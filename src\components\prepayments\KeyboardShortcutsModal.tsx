import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
} from '../ui/dialog';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { ArrowUp, ArrowDown, ArrowLeft, ArrowRight, Command } from 'lucide-react';

interface KeyboardShortcutsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function KeyboardShortcutsModal({ open, onOpenChange }: KeyboardShortcutsModalProps) {
  const shortcuts = [
    {
      category: "Navigation",
      items: [
        {
          keys: [<ArrowUp key="up" className="w-3 h-3" />, <ArrowDown key="down" className="w-3 h-3" />],
          description: "Navigate between invoices / line items"
        },
        {
          keys: [<ArrowDown key="down2" className="w-3 h-3" />],
          description: "Enter line item mode (from expanded invoice)"
        },
        {
          keys: [<<PERSON>Left key="left" className="w-3 h-3" />, <ArrowRight key="right" className="w-3 h-3" />],
          description: "Expand/collapse focused invoice"
        },
        {
          keys: ["Escape"],
          description: "Exit line item mode / Clear all focus and selections"
        }
      ]
    },
    {
      category: "Selection",
      items: [
        {
          keys: ["Space"],
          description: "Select/deselect invoice (invoice mode) or individual line item (line item mode)"
        },
        {
          keys: [<Command key="cmd" className="w-3 h-3" />, "A"],
          description: "Select all line items from focused invoice"
        },
        {
          keys: [<Command key="cmd" className="w-3 h-3" />, "D"],
          description: "Deselect all line items"
        }
      ]
    },
    {
      category: "Actions",
      items: [
        {
          keys: [<Command key="cmd" className="w-3 h-3" />, "S"],
          description: "Save Changes"
        },
        {
          keys: [<Command key="cmd" className="w-3 h-3" />, "Enter"],
          description: "Post Ready (save and post to Xero)"
        },
        {
          keys: [<Command key="cmd" className="w-3 h-3" />, "R"],
          description: "Reset configuration"
        }
      ]
    },
    {
      category: "Help",
      items: [
        {
          keys: ["Shift", "?"],
          description: "Show this keyboard shortcuts dialog"
        }
      ]
    }
  ];

  const renderKeys = (keys: (string | React.ReactElement)[]) => (
    <div className="flex items-center gap-1">
      {keys.map((key, index) => (
        <React.Fragment key={index}>
          {index > 0 && <span className="text-xs text-gray-400">+</span>}
          {typeof key === 'string' ? (
            <kbd className="px-2 py-1 text-xs bg-gray-100 rounded border font-mono">
              {key}
            </kbd>
          ) : (
            <div className="px-2 py-1 bg-gray-100 rounded border flex items-center justify-center">
              {key}
            </div>
          )}
        </React.Fragment>
      ))}
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            ⌨️ Keyboard Shortcuts
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {shortcuts.map((section, sectionIndex) => (
            <div key={section.category}>
              {sectionIndex > 0 && <Separator className="mb-4" />}
              
              <div className="space-y-3">
                <h3 className="text-sm font-semibold text-gray-900 flex items-center gap-2">
                  {section.category}
                  <Badge variant="secondary" className="text-xs">
                    {section.items.length}
                  </Badge>
                </h3>
                
                <div className="space-y-2">
                  {section.items.map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">{item.description}</span>
                      {renderKeys(item.keys)}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 pt-4 border-t">
          <p className="text-xs text-gray-500">
            💡 <strong>Pro tip:</strong> Use <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">Ctrl</kbd> (or <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">Cmd</kbd> on Mac) for actions like Save and Post.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}