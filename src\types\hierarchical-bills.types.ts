import type { InvoiceData, AmortizableLineItem, SupplierData } from './schedule.types';

export interface LineItemNode {
  lineItemId: string;
  description: string;
  lineAmount: number;
  isSelected: boolean;
  tags: string[];
  status: string;
  prepaymentAccountCode: string;
  expenseAccountCode: string | null;
  scheduleId?: string;
  monthlyBreakdown?: Record<string, { amount: number; status: string; journalId?: string; error?: string }>;
}

export interface InvoiceNode {
  invoiceId: string;
  reference: string;
  invoiceDate: string;
  totalAmount: number;
  currencyCode: string;
  isExpanded: boolean;
  isSelected: boolean;
  status: string;
  hasAttachment: boolean;
  attachmentId?: string;
  lineItems: LineItemNode[];
}

export interface SupplierNode {
  supplierId: string;
  supplierName: string;
  totalAmount: number;
  invoiceCount: number;
  isExpanded: boolean;
  isSelected: boolean;
  status: string;
  invoices: InvoiceNode[];
}

export interface HierarchicalBillsData {
  suppliers: SupplierNode[];
}

export interface HierarchicalSelection {
  suppliers: Set<string>;
  invoices: Set<string>;
  lineItems: Set<string>;
}

export interface ExpandedState {
  suppliers: Set<string>;
  invoices: Set<string>;
}

export type SelectionState = 'none' | 'partial' | 'all';

// Utility functions for transforming data
export const transformToHierarchicalData = (suppliers: SupplierData[]): HierarchicalBillsData => {
  return {
    suppliers: suppliers.map(supplier => ({
      supplierId: supplier.supplierId,
      supplierName: supplier.supplierName,
      totalAmount: supplier.invoices.reduce((sum, invoice) => sum + invoice.totalAmount, 0),
      invoiceCount: supplier.invoices.length,
      isExpanded: false,
      isSelected: false,
      status: supplier.overallStatus,
      invoices: supplier.invoices.map(invoice => ({
        invoiceId: invoice.invoiceId,
        reference: invoice.reference,
        invoiceDate: invoice.invoiceDate,
        totalAmount: invoice.totalAmount,
        currencyCode: invoice.currencyCode,
        isExpanded: false,
        isSelected: false,
        status: invoice.overallStatus,
        hasAttachment: invoice.hasAttachment,
        attachmentId: invoice.attachmentId,
        lineItems: invoice.amortizableLineItems.map(lineItem => ({
          lineItemId: lineItem.lineItemId,
          description: lineItem.description,
          lineAmount: lineItem.lineAmount,
          isSelected: false,
          tags: generateLineItemTags(lineItem, invoice),
          status: lineItem.overallStatus,
          prepaymentAccountCode: lineItem.prepaymentAccountCode,
          expenseAccountCode: lineItem.expenseAccountCode,
          scheduleId: lineItem.scheduleId,
          monthlyBreakdown: lineItem.monthlyBreakdown,
        }))
      }))
    }))
  };
};

const generateLineItemTags = (lineItem: AmortizableLineItem, invoice: InvoiceData): string[] => {
  const tags = [];
  
  // Check for multiple accounts across line items in the same invoice
  const accountCodes = new Set(
    invoice.amortizableLineItems
      .filter(item => item.expenseAccountCode)
      .map(item => item.expenseAccountCode)
  );
  
  if (accountCodes.size > 1) {
    tags.push('Multi COA');
  } else if (accountCodes.size === 1) {
    tags.push('Single COA');
  }

  // Check for tracking categories (placeholder logic)
  if (lineItem.description.toLowerCase().includes('tracking')) {
    tags.push('Multi tracking');
  }

  return tags;
};

// Selection state calculation utilities
export const calculateSupplierSelectionState = (supplier: SupplierNode): SelectionState => {
  const selectedInvoices = supplier.invoices.filter(invoice => invoice.isSelected).length;
  if (selectedInvoices === 0) return 'none';
  if (selectedInvoices === supplier.invoices.length) return 'all';
  return 'partial';
};

export const calculateInvoiceSelectionState = (invoice: InvoiceNode): SelectionState => {
  const selectedLineItems = invoice.lineItems.filter(item => item.isSelected).length;
  if (selectedLineItems === 0) return 'none';
  if (selectedLineItems === invoice.lineItems.length) return 'all';
  return 'partial';
};

// Get all selected line item IDs for calculations
export const getSelectedLineItemIds = (suppliers: SupplierNode[]): string[] => {
  const selectedIds: string[] = [];
  
  suppliers.forEach(supplier => {
    supplier.invoices.forEach(invoice => {
      invoice.lineItems.forEach(lineItem => {
        if (lineItem.isSelected) {
          selectedIds.push(lineItem.lineItemId);
        }
      });
    });
  });
  
  return selectedIds;
};

// Get total amount of selected items
export const getSelectedTotalAmount = (suppliers: SupplierNode[]): number => {
  let total = 0;
  
  suppliers.forEach(supplier => {
    supplier.invoices.forEach(invoice => {
      invoice.lineItems.forEach(lineItem => {
        if (lineItem.isSelected) {
          total += lineItem.lineAmount;
        }
      });
    });
  });
  
  return total;
};

// Search filtering utilities
export const filterHierarchyBySearch = (
  suppliers: SupplierNode[], 
  searchTerm: string
): SupplierNode[] => {
  if (!searchTerm) return suppliers;
  
  const term = searchTerm.toLowerCase();
  
  return suppliers.map(supplier => {
    const supplierMatches = supplier.supplierName.toLowerCase().includes(term);
    
    const filteredInvoices = supplier.invoices.map(invoice => {
      const invoiceMatches = invoice.reference.toLowerCase().includes(term) ||
                           invoice.invoiceId.toLowerCase().includes(term);
      
      const filteredLineItems = invoice.lineItems.filter(lineItem =>
        lineItem.description.toLowerCase().includes(term) ||
        lineItem.lineItemId.toLowerCase().includes(term)
      );
      
      // Include invoice if it matches or has matching line items
      if (invoiceMatches || filteredLineItems.length > 0) {
        return {
          ...invoice,
          lineItems: filteredLineItems.length > 0 ? filteredLineItems : invoice.lineItems
        };
      }
      
      return null;
    }).filter(Boolean) as InvoiceNode[];
    
    // Include supplier if it matches or has matching invoices
    if (supplierMatches || filteredInvoices.length > 0) {
      return {
        ...supplier,
        invoices: filteredInvoices.length > 0 ? filteredInvoices : supplier.invoices
      };
    }
    
    return null;
  }).filter(Boolean) as SupplierNode[];
};