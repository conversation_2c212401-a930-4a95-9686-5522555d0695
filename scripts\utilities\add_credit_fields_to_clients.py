#!/usr/bin/env python3
"""
Add credit system fields to existing CLIENTS collection documents.

This script adds the required credit tracking fields to all existing client documents
in Firestore to support the new credit-based billing system for LLM processing.

Fields added:
- credit_balance: Starting credit amount (default: 1000)
- credits_used_total: Total credits used across all services (default: 0)
- credits_used_openai: Credits used for OpenAI processing (default: 0)
- credits_used_mistral: Credits used for Mistral processing (default: 0)
- last_credit_usage: Timestamp of last credit usage (default: null)

Usage:
    python add_credit_fields_to_clients.py [--starting-credits 1000] [--dry-run]

Arguments:
    --starting-credits: Initial credit balance to give each client (default: 1000)
    --dry-run: Preview changes without actually updating documents
"""

import asyncio
import argparse
import sys
import os
import logging
from typing import Dict, Any

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from google.cloud import firestore
from google.cloud.firestore import SERVER_TIMESTAMP

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def add_credit_fields_to_clients(starting_credits: int = 1000, dry_run: bool = False):
    """
    Add credit system fields to all existing client documents.
    
    Args:
        starting_credits: Initial credit balance to assign to each client
        dry_run: If True, only preview changes without updating documents
    """
    try:
        # Initialize Firestore client
        db = firestore.AsyncClient()
        logger.info("Connected to Firestore")
        
        # Get all client documents
        clients_ref = db.collection('CLIENTS')
        clients_snapshot = await clients_ref.get()
        
        if not clients_snapshot:
            logger.warning("No client documents found in CLIENTS collection")
            return
        
        logger.info(f"Found {len(clients_snapshot)} client documents")
        
        # Credit fields to add
        credit_fields = {
            'credit_balance': starting_credits,
            'credits_used_total': 0,
            'credits_used_openai': 0,
            'credits_used_mistral': 0,
            'last_credit_usage': None
        }
        
        if dry_run:
            logger.info("=== DRY RUN MODE - NO CHANGES WILL BE MADE ===")
        
        # Process clients in batches for better performance
        batch_size = 10
        updated_count = 0
        skipped_count = 0
        
        for i in range(0, len(clients_snapshot), batch_size):
            batch_docs = clients_snapshot[i:i + batch_size]
            
            if not dry_run:
                batch = db.batch()
            
            for doc in batch_docs:
                client_data = doc.to_dict()
                client_id = doc.id
                
                # Check if client already has credit fields
                has_credit_fields = any(field in client_data for field in credit_fields.keys())
                
                if has_credit_fields:
                    logger.info(f"Client {client_id} already has credit fields - skipping")
                    skipped_count += 1
                    continue
                
                # Log what we're about to do
                logger.info(f"{'[DRY RUN] ' if dry_run else ''}Adding credit fields to client {client_id}")
                logger.info(f"  - credit_balance: {starting_credits}")
                logger.info(f"  - credits_used_total: 0")
                logger.info(f"  - credits_used_openai: 0") 
                logger.info(f"  - credits_used_mistral: 0")
                logger.info(f"  - last_credit_usage: null")
                
                if not dry_run:
                    # Add to batch update
                    doc_ref = clients_ref.document(client_id)
                    batch.update(doc_ref, credit_fields)
                
                updated_count += 1
            
            # Commit batch if not dry run
            if not dry_run and updated_count > 0:
                await batch.commit()
                logger.info(f"Committed batch update for {len(batch_docs)} clients")
        
        # Summary
        logger.info("=== SUMMARY ===")
        logger.info(f"Total clients found: {len(clients_snapshot)}")
        logger.info(f"Clients {'that would be ' if dry_run else ''}updated: {updated_count}")
        logger.info(f"Clients skipped (already have credit fields): {skipped_count}")
        
        if dry_run:
            logger.info("To apply these changes, run the script without --dry-run")
        else:
            logger.info("Credit fields successfully added to all clients!")
            
    except Exception as e:
        logger.error(f"Error updating client credit fields: {e}")
        raise


async def verify_credit_fields():
    """Verify that all clients now have the required credit fields."""
    try:
        db = firestore.AsyncClient()
        clients_ref = db.collection('CLIENTS')
        clients_snapshot = await clients_ref.get()
        
        required_fields = ['credit_balance', 'credits_used_total', 'credits_used_openai', 'credits_used_mistral', 'last_credit_usage']
        
        logger.info("=== VERIFICATION ===")
        missing_fields_count = 0
        
        for doc in clients_snapshot:
            client_data = doc.to_dict()
            client_id = doc.id
            
            missing_fields = [field for field in required_fields if field not in client_data]
            
            if missing_fields:
                logger.warning(f"Client {client_id} is missing fields: {missing_fields}")
                missing_fields_count += 1
            else:
                logger.info(f"Client {client_id} has all required credit fields ✓")
        
        if missing_fields_count == 0:
            logger.info("All clients have the required credit fields! ✓")
        else:
            logger.error(f"{missing_fields_count} clients are missing credit fields")
            
    except Exception as e:
        logger.error(f"Error during verification: {e}")


def main():
    parser = argparse.ArgumentParser(description='Add credit system fields to existing clients')
    parser.add_argument('--starting-credits', type=int, default=1000,
                       help='Initial credit balance to give each client (default: 1000)')
    parser.add_argument('--dry-run', action='store_true',
                       help='Preview changes without actually updating documents')
    parser.add_argument('--verify', action='store_true',
                       help='Verify that all clients have required credit fields')
    
    args = parser.parse_args()
    
    if args.verify:
        asyncio.run(verify_credit_fields())
    else:
        asyncio.run(add_credit_fields_to_clients(
            starting_credits=args.starting_credits,
            dry_run=args.dry_run
        ))


if __name__ == "__main__":
    main()