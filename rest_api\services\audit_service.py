"""
Audit Service - Business logic for audit log operations
"""
from typing import List, Optional, Dict, Any, Tuple
from google.cloud import firestore
import logging
from datetime import datetime, timezone, timedelta, date

from ..models.audit import (
    AuditLogEntry, AuditLogSummary, AuditLogDetails, 
    AuditEventCategory, AuditEventStatus, AuditStatistics
)
from ..core.firebase_auth import AuthUser

logger = logging.getLogger(__name__)


class AuditService:
    """Service for handling audit log business logic"""
    
    def __init__(self, db):
        self.db = db
    
    async def list_audit_logs_paginated(
        self,
        client_id: Optional[str] = None,
        entity_id: Optional[str] = None,
        user_id: Optional[str] = None,
        firm_id: Optional[str] = None,
        event_category: Optional[AuditEventCategory] = None,
        event_type: Optional[str] = None,
        status: Optional[AuditEventStatus] = None,
        date_from: Optional[date] = None,
        date_to: Optional[date] = None,
        datetime_from: Optional[datetime] = None,
        datetime_to: Optional[datetime] = None,
        min_duration_ms: Optional[int] = None,
        max_duration_ms: Optional[int] = None,
        source_ip: Optional[str] = None,
        error_only: Optional[bool] = None,
        has_details: Optional[bool] = None,
        search_text: Optional[str] = None,
        page: int = 1,
        limit: int = 50,
        sort_by: str = "timestamp",
        sort_order: str = "desc",
        user_client_ids: Optional[List[str]] = None
    ) -> Tuple[List[AuditLogSummary], int, int]:
        """
        List audit logs with pagination and filtering.
        Returns: (audit_logs, total_count, total_pages)
        """
        offset = (page - 1) * limit
        
        # Build base query
        base_query = self.db.collection("AUDIT_LOG")
        
        # Apply basic filters that can be done in Firestore
        current_query = base_query
        
        if client_id:
            current_query = current_query.where(
                filter=firestore.FieldFilter("client_id", "==", client_id)
            )
        elif user_client_ids and len(user_client_ids) > 0:
            # Filter by user's accessible clients
            if len(user_client_ids) == 1:
                current_query = current_query.where(
                    filter=firestore.FieldFilter("client_id", "==", user_client_ids[0])
                )
            else:
                current_query = current_query.where(
                    filter=firestore.FieldFilter("client_id", "in", user_client_ids[:10])  # Firestore limit
                )
        
        if entity_id:
            current_query = current_query.where(
                filter=firestore.FieldFilter("entity_id", "==", entity_id)
            )
        
        if user_id:
            current_query = current_query.where(
                filter=firestore.FieldFilter("user_id", "==", user_id)
            )
        
        if firm_id:
            current_query = current_query.where(
                filter=firestore.FieldFilter("firm_id", "==", firm_id)
            )
        
        if event_category:
            current_query = current_query.where(
                filter=firestore.FieldFilter("event_category", "==", event_category)
            )
        
        if status:
            current_query = current_query.where(
                filter=firestore.FieldFilter("status", "==", status)
            )
        
        # Handle date/time range filters
        if datetime_from:
            current_query = current_query.where(
                filter=firestore.FieldFilter("timestamp", ">=", datetime_from)
            )
        elif date_from:
            datetime_from = datetime.combine(date_from, datetime.min.time()).replace(tzinfo=timezone.utc)
            current_query = current_query.where(
                filter=firestore.FieldFilter("timestamp", ">=", datetime_from)
            )
        
        if datetime_to:
            current_query = current_query.where(
                filter=firestore.FieldFilter("timestamp", "<=", datetime_to)
            )
        elif date_to:
            datetime_to = datetime.combine(date_to, datetime.max.time()).replace(tzinfo=timezone.utc)
            current_query = current_query.where(
                filter=firestore.FieldFilter("timestamp", "<=", datetime_to)
            )
        
        # Apply sorting
        sort_direction = "ASCENDING" if sort_order.lower() == "asc" else "DESCENDING"
        current_query = current_query.order_by(sort_by, direction=sort_direction)
        
        # Get all matching documents for client-side filtering
        all_docs = current_query.stream()
        
        audit_logs_data = []
        async for doc in all_docs:
            audit_data = doc.to_dict()
            audit_data["audit_id"] = doc.id
            
            # Apply client-side filters
            if self._should_include_audit_log(
                audit_data, event_type, min_duration_ms, max_duration_ms,
                source_ip, error_only, has_details, search_text
            ):
                audit_logs_data.append(audit_data)
        
        # Apply pagination
        total_count = len(audit_logs_data)
        total_pages = (total_count + limit - 1) // limit if total_count > 0 else 0
        paginated_data = audit_logs_data[offset:offset + limit]
        
        # Transform to AuditLogSummary models
        audit_logs = []
        for audit_data in paginated_data:
            try:
                audit_summary = await self._transform_to_audit_summary(audit_data)
                audit_logs.append(audit_summary)
            except Exception as e:
                logger.warning(f"Could not transform audit log {audit_data.get('audit_id')}: {e}")
                continue
        
        return audit_logs, total_count, total_pages
    
    def _should_include_audit_log(
        self,
        audit_data: Dict[str, Any],
        event_type: Optional[str],
        min_duration_ms: Optional[int],
        max_duration_ms: Optional[int],
        source_ip: Optional[str],
        error_only: Optional[bool],
        has_details: Optional[bool],
        search_text: Optional[str]
    ) -> bool:
        """Check if audit log should be included based on client-side filters"""
        
        if event_type:
            log_event_type = audit_data.get("event_type", "").lower()
            if event_type.lower() not in log_event_type:
                return False
        
        if min_duration_ms is not None:
            duration = audit_data.get("duration_ms")
            if duration is None or duration < min_duration_ms:
                return False
        
        if max_duration_ms is not None:
            duration = audit_data.get("duration_ms")
            if duration is not None and duration > max_duration_ms:
                return False
        
        if source_ip:
            log_source_ip = audit_data.get("source_ip", "")
            if source_ip not in log_source_ip:
                return False
        
        if error_only:
            status = audit_data.get("status", "").upper()
            if status not in ["FAILURE", "ERROR"]:
                return False
        
        if has_details is not None:
            details = audit_data.get("details")
            has_details_actual = details is not None and bool(details)
            if has_details != has_details_actual:
                return False
        
        if search_text:
            search_lower = search_text.lower()
            searchable_fields = [
                audit_data.get("event_type", ""),
                audit_data.get("error_message", ""),
                str(audit_data.get("details", {}))
            ]
            searchable_text = " ".join(searchable_fields).lower()
            if search_lower not in searchable_text:
                return False
        
        return True
    
    async def _transform_to_audit_summary(self, audit_data: Dict[str, Any]) -> AuditLogSummary:
        """Transform Firestore data to AuditLogSummary model"""
        # Handle legacy field names
        event_category = audit_data.get("event_category") or audit_data.get("eventCategory")
        event_type = audit_data.get("event_type") or audit_data.get("eventType")
        
        # Ensure event_category is a valid enum value
        try:
            if event_category and hasattr(AuditEventCategory, event_category):
                event_category = AuditEventCategory(event_category)
            else:
                # Default to SYSTEM if not found
                event_category = AuditEventCategory.SYSTEM
        except (ValueError, AttributeError):
            event_category = AuditEventCategory.SYSTEM
        
        # Ensure status is a valid enum value
        status = audit_data.get("status", "").upper()
        try:
            if status and hasattr(AuditEventStatus, status):
                status = AuditEventStatus(status)
            else:
                status = AuditEventStatus.INFO
        except (ValueError, AttributeError):
            status = AuditEventStatus.INFO
        
        # Determine if this is an error
        is_error = status in [AuditEventStatus.FAILURE, AuditEventStatus.ERROR]
        
        # Generate summary
        summary = self._generate_event_summary(event_type, audit_data)
        
        # Enrich with user email if user_id is present
        user_email = None
        try:
            if audit_data.get("user_id"):
                user_email = await self._get_user_email(audit_data["user_id"])
        except Exception as e:
            logger.warning(f"Error getting user email for audit summary: {e}")
        
        return AuditLogSummary(
            audit_id=audit_data.get("audit_id"),
            timestamp=audit_data.get("timestamp"),
            event_category=event_category,
            event_type=event_type,
            status=status,
            client_id=audit_data.get("client_id"),
            entity_id=audit_data.get("entity_id"),
            user_id=audit_data.get("user_id"),
            duration_ms=audit_data.get("duration_ms"),
            is_error=is_error,
            summary=summary,
            user_email=user_email
        )
    
    def _generate_event_summary(self, event_type: str, audit_data: Dict[str, Any]) -> str:
        """Generate a human-readable summary for an audit event"""
        try:
            if "SYNC" in event_type:
                if "STARTED" in event_type:
                    return f"Sync operation started"
                elif "SUCCESS" in event_type or "COMPLETED" in event_type:
                    return f"Sync operation completed successfully"
                elif "FAILURE" in event_type or "ERROR" in event_type:
                    return f"Sync operation failed"
                else:
                    return f"Sync operation in progress"
            
            elif "API_REQUEST" in event_type:
                method = audit_data.get("details", {}).get("method", "")
                endpoint = audit_data.get("details", {}).get("endpoint", "")
                return f"API {method} request to {endpoint}"
            
            elif "USER" in event_type:
                if "LOGIN" in event_type:
                    return "User login attempt"
                elif "LOGOUT" in event_type:
                    return "User logged out"
                else:
                    return "User management action"
            
            elif "AMORTIZATION" in event_type:
                return "Amortization processing event"
            
            else:
                return event_type.replace("_", " ").title()
        
        except Exception:
            return event_type or "Unknown event"
    
    async def get_audit_log_by_id(
        self,
        audit_id: str,
        current_user: AuthUser,
        include_related: bool = True
    ) -> Optional[AuditLogDetails]:
        """Get a single audit log entry by ID with optional related entries"""
        try:
            audit_ref = self.db.collection("AUDIT_LOG").document(audit_id)
            audit_doc = await audit_ref.get()
            
            if not audit_doc.exists:
                return None
            
            audit_data = audit_doc.to_dict()
            audit_data["audit_id"] = audit_doc.id
            
            # Check if user has access to this audit log's client
            client_id = audit_data.get("client_id")
            if client_id and not await self._user_has_client_access(current_user, client_id):
                return None
            
            # Transform to AuditLogDetails model
            audit_details = await self._transform_to_audit_details(audit_data)
            
            return audit_details
            
        except Exception as e:
            logger.error(f"Error retrieving audit log {audit_id}: {e}")
            return None
    
    async def _transform_to_audit_details(self, audit_data: Dict[str, Any]) -> AuditLogDetails:
        """Transform Firestore data to AuditLogDetails model with enriched data"""
        # Handle legacy field names
        event_category = audit_data.get("event_category") or audit_data.get("eventCategory")
        event_type = audit_data.get("event_type") or audit_data.get("eventType")
        
        # Ensure event_category is a valid enum value
        try:
            if event_category and hasattr(AuditEventCategory, event_category):
                event_category = AuditEventCategory(event_category)
            else:
                event_category = AuditEventCategory.SYSTEM
        except (ValueError, AttributeError):
            event_category = AuditEventCategory.SYSTEM
        
        # Ensure status is a valid enum value
        status = audit_data.get("status", "").upper()
        try:
            if status and hasattr(AuditEventStatus, status):
                status = AuditEventStatus(status)
            else:
                status = AuditEventStatus.INFO
        except (ValueError, AttributeError):
            status = AuditEventStatus.INFO
        
        # Enrich with related data
        user_email = None
        client_name = None
        entity_name = None
        
        try:
            if audit_data.get("user_id"):
                user_email = await self._get_user_email(audit_data["user_id"])
            
            if audit_data.get("client_id"):
                client_name = await self._get_client_name(audit_data["client_id"])
            
            if audit_data.get("entity_id"):
                entity_name = await self._get_entity_name(audit_data["entity_id"])
        except Exception as e:
            logger.warning(f"Error enriching audit data: {e}")
        
        return AuditLogDetails(
            audit_id=audit_data.get("audit_id"),
            timestamp=audit_data.get("timestamp"),
            event_category=event_category,
            event_type=event_type,
            status=status,
            client_id=audit_data.get("client_id"),
            entity_id=audit_data.get("entity_id"),
            user_id=audit_data.get("user_id"),
            firm_id=audit_data.get("firm_id"),
            source_ip=audit_data.get("source_ip"),
            user_agent=audit_data.get("user_agent"),
            request_id=audit_data.get("request_id"),
            details=audit_data.get("details"),
            error_message=audit_data.get("error_message"),
            duration_ms=audit_data.get("duration_ms"),
            user_email=user_email,
            client_name=client_name,
            entity_name=entity_name
        )
    
    async def _get_user_email(self, user_id: str) -> Optional[str]:
        """Get user email from user ID"""
        try:
            # First check FIRM_USERS collection
            firm_user_query = self.db.collection("FIRM_USERS").where(
                filter=firestore.FieldFilter("user_id", "==", user_id)
            ).limit(1)
            firm_user_docs = firm_user_query.stream()
            
            async for doc in firm_user_docs:
                firm_user_data = doc.to_dict()
                return firm_user_data.get("email")
            
            # If not found in FIRM_USERS, check CLIENT_USERS collection
            client_user_query = self.db.collection("CLIENT_USERS").where(
                filter=firestore.FieldFilter("user_id", "==", user_id)
            ).limit(1)
            client_user_docs = client_user_query.stream()
            
            async for doc in client_user_docs:
                client_user_data = doc.to_dict()
                return client_user_data.get("email")
                
        except Exception as e:
            logger.warning(f"Error getting user email for user_id {user_id}: {e}")
        return None
    
    async def _get_client_name(self, client_id: str) -> Optional[str]:
        """Get client name from client ID"""
        try:
            client_ref = self.db.collection("CLIENTS").document(client_id)
            client_doc = await client_ref.get()
            if client_doc.exists:
                return client_doc.to_dict().get("name")
        except Exception:
            pass
        return None
    
    async def _get_entity_name(self, entity_id: str) -> Optional[str]:
        """Get entity name from entity ID"""
        try:
            entity_ref = self.db.collection("ENTITIES").document(entity_id)
            entity_doc = await entity_ref.get()
            if entity_doc.exists:
                return entity_doc.to_dict().get("entity_name")
        except Exception:
            pass
        return None
    
    async def get_audit_statistics(
        self,
        client_id: Optional[str] = None,
        entity_id: Optional[str] = None,
        date_from: Optional[date] = None,
        date_to: Optional[date] = None,
        current_user: Optional[AuthUser] = None
    ) -> AuditStatistics:
        """Get statistics about audit logs"""
        try:
            base_query = self.db.collection("AUDIT_LOG")
            
            # Apply filters
            if client_id:
                query = base_query.where(filter=firestore.FieldFilter("client_id", "==", client_id))
            elif current_user and current_user.role != "firm_admin":
                # Limit to user's assigned clients
                user_client_ids = current_user.assigned_client_ids or []
                if not user_client_ids:
                    return self._empty_statistics()
                query = base_query.where(filter=firestore.FieldFilter("client_id", "in", user_client_ids[:10]))
            else:
                query = base_query
            
            if entity_id:
                query = query.where(filter=firestore.FieldFilter("entity_id", "==", entity_id))
            
            # Apply date filters
            if date_from:
                datetime_from = datetime.combine(date_from, datetime.min.time()).replace(tzinfo=timezone.utc)
                query = query.where(filter=firestore.FieldFilter("timestamp", ">=", datetime_from))
            
            if date_to:
                datetime_to = datetime.combine(date_to, datetime.max.time()).replace(tzinfo=timezone.utc)
                query = query.where(filter=firestore.FieldFilter("timestamp", "<=", datetime_to))
            
            # Get all audit logs for statistics
            all_docs = query.stream()
            
            stats = {
                "total_events": 0,
                "events_by_category": {},
                "events_by_status": {},
                "error_count": 0,
                "duration_sum": 0,
                "duration_count": 0,
                "event_type_counts": {}
            }
            
            async for doc in all_docs:
                audit_data = doc.to_dict()
                stats["total_events"] += 1
                
                # Count by category
                category = audit_data.get("event_category") or audit_data.get("eventCategory", "UNKNOWN")
                stats["events_by_category"][category] = stats["events_by_category"].get(category, 0) + 1
                
                # Count by status
                status = audit_data.get("status", "UNKNOWN")
                stats["events_by_status"][status] = stats["events_by_status"].get(status, 0) + 1
                
                # Count errors
                if status in ["FAILURE", "ERROR"]:
                    stats["error_count"] += 1
                
                # Track duration
                duration = audit_data.get("duration_ms")
                if duration is not None:
                    stats["duration_sum"] += duration
                    stats["duration_count"] += 1
                
                # Track event types
                event_type = audit_data.get("event_type") or audit_data.get("eventType", "UNKNOWN")
                stats["event_type_counts"][event_type] = stats["event_type_counts"].get(event_type, 0) + 1
            
            # Calculate derived statistics
            error_rate = (stats["error_count"] / stats["total_events"] * 100) if stats["total_events"] > 0 else 0.0
            average_duration = (stats["duration_sum"] / stats["duration_count"]) if stats["duration_count"] > 0 else None
            
            # Get most common event types
            most_common_events = sorted(
                [{"event_type": k, "count": v} for k, v in stats["event_type_counts"].items()],
                key=lambda x: x["count"],
                reverse=True
            )[:10]
            
            return AuditStatistics(
                total_events=stats["total_events"],
                events_by_category=stats["events_by_category"],
                events_by_status=stats["events_by_status"],
                error_rate=error_rate,
                average_duration_ms=average_duration,
                most_common_event_types=most_common_events
            )
            
        except Exception as e:
            logger.error(f"Error getting audit statistics: {e}")
            return self._empty_statistics()
    
    def _empty_statistics(self) -> AuditStatistics:
        """Return empty statistics structure"""
        return AuditStatistics(
            total_events=0,
            events_by_category={},
            events_by_status={},
            error_rate=0.0,
            average_duration_ms=None,
            most_common_event_types=[]
        )
    
    async def _user_has_client_access(self, user: AuthUser, client_id: str) -> bool:
        """Check if user has access to a specific client"""
        if user.role == "firm_admin":
            # Firm admins have access to all clients in their firm
            return True
        
        # Other users only have access to assigned clients
        assigned_client_ids = user.assigned_client_ids or []
        return client_id in assigned_client_ids
    
    async def get_user_client_ids(self, current_user: AuthUser, requested_client_id: Optional[str] = None) -> List[str]:
        """Get list of client IDs the user has access to"""
        if current_user.role == "firm_admin":
            if requested_client_id:
                return [requested_client_id]
            
            # Get all clients for the firm
            clients_query = self.db.collection("CLIENTS").where(
                filter=firestore.FieldFilter("firm_id", "==", current_user.firm_id)
            )
            client_docs = clients_query.stream()
            client_ids = []
            async for doc in client_docs:
                client_ids.append(doc.id)
            return client_ids
        
        # Regular users only get their assigned clients
        user_client_ids = current_user.assigned_client_ids or []
        if requested_client_id and requested_client_id in user_client_ids:
            return [requested_client_id]
        
        return user_client_ids
    
    async def get_available_event_types(self) -> Dict[str, List[str]]:
        """Get available event types grouped by category"""
        try:
            # Get unique event types from recent audit logs
            query = (
                self.db.collection("AUDIT_LOG")
                .order_by("timestamp", direction="DESCENDING")
                .limit(1000)  # Sample recent logs
            )
            docs = query.stream()
            
            event_types_by_category = {}
            
            async for doc in docs:
                audit_data = doc.to_dict()
                category = audit_data.get("event_category") or audit_data.get("eventCategory", "UNKNOWN")
                event_type = audit_data.get("event_type") or audit_data.get("eventType", "UNKNOWN")
                
                if category not in event_types_by_category:
                    event_types_by_category[category] = set()
                
                event_types_by_category[category].add(event_type)
            
            # Convert sets to sorted lists
            return {
                category: sorted(list(event_types))
                for category, event_types in event_types_by_category.items()
            }
            
        except Exception as e:
            logger.error(f"Error getting available event types: {e}")
            return {}
    
    async def delete_old_audit_logs(
        self,
        days_to_keep: int = 90,
        batch_size: int = 100
    ) -> int:
        """Delete audit logs older than specified days (for maintenance)"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_to_keep)
            
            query = (
                self.db.collection("AUDIT_LOG")
                .where(filter=firestore.FieldFilter("timestamp", "<", cutoff_date))
                .limit(batch_size)
            )
            
            docs = query.stream()
            deleted_count = 0
            
            async for doc in docs:
                await doc.reference.delete()
                deleted_count += 1
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error deleting old audit logs: {e}")
            return 0