"""
Payments Endpoint Handler

Handles Payments synchronization from Xero.
Focuses on payment transactions and reconciliation data.
"""

import logging
from typing import List
from datetime import datetime, timezone

from google.cloud import firestore

from context import SyncContext
from utils.sync_helpers import _create_audit_log_entry, _update_firestore_sync_timestamp
from utils.bills_processor import _check_if_current_version_exists

logger = logging.getLogger(__name__)

__all__ = ["sync_payments"]


async def sync_payments(ctx: SyncContext, requested_endpoints: List[str]) -> int:
    """
    Handle Payments synchronization from Xero.
    
    Args:
        ctx: Sync context containing all dependencies
        requested_endpoints: List of requested endpoints for sync
        
    Returns:
        Number of payments processed
        
    Raises:
        Exception: If payments sync fails
    """
    if "Payments" not in requested_endpoints and requested_endpoints:
        return 0
    
    # Check if payments sync is enabled for this entity
    if not ctx.should_sync_endpoint("Payments", True):
        logger.info(f"Payments sync disabled for entity_id: {ctx.entity_id}")
        return 0
    
    try:
        logger.info(f"Starting Payments sync for entity_id: {ctx.entity_id}")
        
        full_sync = "Payments" in ctx.force_full_sync_endpoints
        last_sync_timestamp_utc_str = None
        transaction_sync_start_date = ctx.message_data.get("transactionSyncStartDate")
        
        # Get last sync timestamp for incremental sync
        sync_ts_field = "_system_lastSyncTimestampUtc_Payments"
        last_sync_timestamp_utc_str = ctx.get_entity_setting(sync_ts_field) if not full_sync else None
        
        # Use transaction_sync_start_date only for FIRST sync (when no previous sync timestamp exists)
        use_date_filter = transaction_sync_start_date and not last_sync_timestamp_utc_str
        where_filter = None
        
        if use_date_filter:
            # Parse the date and format for Xero DateTime constructor (FIRST SYNC ONLY)
            try:
                date_obj = datetime.strptime(transaction_sync_start_date, '%Y-%m-%d').date()
                where_filter = f'Date>=DateTime({date_obj.year},{date_obj.month:02d},{date_obj.day:02d},00,00,00)'
                logger.info(f"First Payments sync - using date filter from {transaction_sync_start_date}")
            except ValueError:
                logger.warning(f"Invalid transactionSyncStartDate format: {transaction_sync_start_date}, skipping date filter")
                use_date_filter = False
        
        logger.info(f"Fetching Payments with filter: {where_filter if where_filter else 'None'}")
        payments_data = await ctx.xero_client.get_records(
            "Payments", 
            where_filter=where_filter,
            if_modified_since=last_sync_timestamp_utc_str if not use_date_filter else None
        )
        
        saved_payments_count = 0
        skipped_payments_count = 0
        
        # Process payments in batches
        batch = ctx.db.batch()
        batch_count = 0
        batch_size = ctx.get_entity_setting("batch_size", 50)
        
        for payment in payments_data:
            payment_id = payment.get("PaymentID")
            if not payment_id:
                logger.warning(f"Payment data missing PaymentID for entity {ctx.entity_id}. Skipping: {payment}")
                continue
            
            # CHECK IF WE ALREADY HAVE THE CURRENT VERSION - SKIP PROCESSING IF SO
            new_updated_at = payment.get("UpdatedDateUTC")
            if await _check_if_current_version_exists(ctx.db, payment_id, new_updated_at, ctx.entity_id, "TRANSACTIONS", "payment"):
                skipped_payments_count += 1
                continue  # Skip all processing for this payment - we already have current version
            
            # Create transaction document for payment
            transaction_doc = {
                "transaction_id": payment_id,
                "entity_id": ctx.entity_id,
                "client_id": ctx.client_id,
                "source_system": "XERO",
                "source_system_id": payment_id,
                "document_type": "PAYMENT",
                "transaction_type": payment.get("PaymentType", "PAYMENT"),
                "payment_id": payment_id,
                "date": payment.get("Date"),
                "amount": payment.get("Amount", 0),
                "currency_rate": payment.get("CurrencyRate"),
                "reference": payment.get("Reference"),
                "invoice": payment.get("Invoice", {}),
                "credit_note": payment.get("CreditNote", {}),
                "prepayment": payment.get("Prepayment", {}),
                "overpayment": payment.get("Overpayment", {}),
                "account": payment.get("Account", {}),
                "status": payment.get("Status"),
                "is_reconciled": payment.get("IsReconciled", False),
                "raw_xero_data": payment,
                "created_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP,
                "source_updated_at_utc": payment.get("UpdatedDateUTC"),
            }
            
            # Remove None values
            transaction_doc = {k: v for k, v in transaction_doc.items() if v is not None}
            
            # Add to batch
            transaction_ref = ctx.db.collection("TRANSACTIONS").document(payment_id)
            batch.set(transaction_ref, transaction_doc, merge=True)
            batch_count += 1
            saved_payments_count += 1
            
            # Commit batch when it reaches size limit
            if batch_count >= batch_size:
                await batch.commit()
                batch = ctx.db.batch()
                batch_count = 0
        
        # Commit remaining documents
        if batch_count > 0:
            await batch.commit()
        
        logger.info(f"Payments sync completed for entity_id: {ctx.entity_id} - Processed: {saved_payments_count}, Skipped: {skipped_payments_count}")
        await _update_firestore_sync_timestamp(
            ctx.db, ctx.entity_id, "Payments", datetime.now(timezone.utc).isoformat(), "Payments sync successful"
        )
        await _create_audit_log_entry(
            ctx.db, "SYNC", "PAYMENTS_SYNC_SUCCESS", ctx.client_id, ctx.entity_id, "SUCCESS", 
            {"payments_processed": saved_payments_count, "payments_skipped": skipped_payments_count, "syncJobId": ctx.sync_job_id}
        )
        
        return saved_payments_count
        
    except Exception as e:
        logger.error(f"Payments sync failed for entity_id: {ctx.entity_id}: {e}", exc_info=True)
        await _create_audit_log_entry(
            ctx.db, "SYNC", "PAYMENTS_SYNC_FAILURE", ctx.client_id, ctx.entity_id, "FAILURE",
            {"error": str(e), "syncJobId": ctx.sync_job_id}
        )
        raise