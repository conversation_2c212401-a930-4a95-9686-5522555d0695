# Error Handling Examples

This document provides examples of common error scenarios encountered when using the DRCR API, along with the typical error responses and suggestions for client-side handling.

## 1. Authentication Required (401 Unauthorized)

**Scenario**: Attempting to access a protected endpoint without providing an authentication token.

**Request**:
```
GET /api/v1/clients
```

**Response (HTTP 401)**:
```json
{
  "detail": "Not authenticated"
}
```
*(Note: FastAPI's default non-application specific error for HTTPBearer)*

Or, if caught by our custom middleware/logic with an `app_error_code`:
```json
{
  "detail": {
    "message": "Authentication credentials were not provided or are invalid.",
    "app_error_code": "AUTHENTICATION_REQUIRED"
  }
}
```

**Client-Side Handling**:
*   Prompt the user to log in.
*   Redirect to a login page.
*   If a token was thought to be present, treat it as expired/invalid and initiate a re-authentication flow.

## 2. Invalid or Expired Token (401 Unauthorized)

**Scenario**: Providing an authentication token that is malformed, has an invalid signature, or has expired.

**Request**:
```
GET /api/v1/clients
Authorization: Bearer <invalid_or_expired_token>
```

**Response (HTTP 401)**:
```json
{
  "detail": {
    "message": "The provided authentication token has expired.",
    "app_error_code": "TOKEN_EXPIRED"
  }
}
```
Or for an invalid token:
```json
{
  "detail": {
    "message": "The provided authentication token is invalid.",
    "app_error_code": "TOKEN_INVALID"
  }
}
```

**Client-Side Handling**:
*   Clear the invalid/expired token from local storage.
*   Prompt the user to log in again to obtain a new token.
*   Automated token refresh mechanisms (if implemented using a refresh token) should be triggered.

## 3. Insufficient Permissions (403 Forbidden)

**Scenario**: An authenticated user attempts an action they are not authorized to perform (e.g., a non-admin user trying to access an admin-only endpoint).

**Request (User with 'staff' role attempts to access an admin endpoint)**:
```
GET /api/v1/admin/settings
Authorization: Bearer <valid_staff_user_token>
```

**Response (HTTP 403)**:
```json
{
  "detail": {
    "message": "You do not have sufficient permissions to perform this action. Administrator privileges are required.",
    "app_error_code": "ADMIN_PRIVILEGES_REQUIRED"
  }
}
```

**Client-Side Handling**:
*   Inform the user that they do not have the necessary permissions.
*   Disable or hide UI elements corresponding to actions the user is not permitted to perform based on their role.
*   Do not display sensitive data that they are not authorized to see.

## 4. Resource Not Found (404 Not Found)

**Scenario**: Requesting a resource that does not exist (e.g., a client with an unknown ID).

**Request**:
```
GET /api/v1/clients/non_existent_client_id
Authorization: Bearer <valid_token>
```

**Response (HTTP 404)**:
```json
{
  "detail": {
    "message": "The requested resource was not found.",
    "app_error_code": "RESOURCE_NOT_FOUND",
    "context": {
      "resource_type": "Client",
      "identifier": "non_existent_client_id"
    }
  }
}
```

**Client-Side Handling**:
*   Inform the user that the requested item could not be found.
*   Provide a way for the user to navigate back or search for existing resources.
*   Ensure internal links and navigation point to valid resources.

## 5. Validation Error (422 Unprocessable Entity)

**Scenario**: Submitting a request with invalid data (e.g., missing required fields, incorrect data types).

**Request (Creating a new transaction with a missing required field `type`)**:
```
POST /api/v1/transactions
Authorization: Bearer <valid_token>
Content-Type: application/json

{
  "client_id": "some_client_id",
  "entity_id": "some_entity_id",
  "date_issued": "2023-10-26T10:00:00Z",
  "line_items": [
    { "description": "Service A", "amount": 100 }
  ],
  "total": 100
  // "type" field is missing
}
```

**Response (HTTP 422)**:
```json
{
  "detail": [ // Standard FastAPI validation error format
    {
      "loc": [
        "body",
        "type"
      ],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```
Or, if wrapped by our custom handler to include `app_error_code`:
```json
{
  "detail": {
    "message": "Request validation failed.",
    "app_error_code": "VALIDATION_ERROR",
    "context": {
      "errors": [
        {
          "loc": ["body", "type"],
          "msg": "field required",
          "type": "value_error.missing"
        }
      ]
    }
  }
}
```

**Client-Side Handling**:
*   Display user-friendly validation messages next to the corresponding input fields.
*   Prevent form submission until all validation errors are resolved.
*   Highlight the fields that have errors.

## 6. Application-Specific Logic Error (e.g., 400 Bad Request)

**Scenario**: A logically invalid request that passes schema validation but fails business rule validation (e.g., transaction due date before issue date).

**Request (Creating a transaction with due date before issue date)**:
```
POST /api/v1/transactions
Authorization: Bearer <valid_token>
Content-Type: application/json

{
  "client_id": "client123",
  "entity_id": "entity456",
  "type": "ACCREC_INVOICE",
  "date_issued": "2023-11-01T00:00:00Z",
  "date_due": "2023-10-01T00:00:00Z", // Invalid: due date before issue date
  "line_items": [{"description": "Consulting", "amount": 500}],
  "total": 500
}
```

**Response (HTTP 400)**:
```json
{
  "detail": {
    "message": "Transaction due date cannot be before the issue date.",
    "app_error_code": "INVALID_TRANSACTION_DATES",
    "context": {
      "date_issued": "2023-11-01T00:00:00Z",
      "date_due": "2023-10-01T00:00:00Z"
    }
  }
}
```

**Client-Side Handling**:
*   Display a specific error message to the user explaining the logical inconsistency.
*   Guide the user on how to correct the input.

---

These examples cover some of the most common error scenarios. Client applications should be designed to gracefully handle these and other errors by inspecting the HTTP status code and the `app_error_code` in the response body for more detailed information. 