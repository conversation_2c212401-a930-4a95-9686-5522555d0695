import { toast } from 'sonner';

export function useToast() {
  const showSuccess = (message: string, description?: string) => {
    return toast.success(message, { description });
  };

  const showError = (message: string, description?: string) => {
    return toast.error(message, { description });
  };

  const showWarning = (message: string, description?: string) => {
    return toast.warning(message, { description });
  };

  const showInfo = (message: string, description?: string) => {
    return toast.info(message, { description });
  };

  const showLoading = (message: string, description?: string) => {
    return toast.loading(message, { description });
  };

  const dismiss = (toastId?: string | number) => {
    if (toastId) {
      toast.dismiss(toastId);
    } else {
      toast.dismiss();
    }
  };

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showLoading,
    dismiss,
  };
}