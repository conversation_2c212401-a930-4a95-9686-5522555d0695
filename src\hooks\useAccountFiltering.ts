import { useState, useCallback } from 'react';
import { api } from '@/lib/api';
import { filterAccountsForPrepayments, type FilteredAccounts } from '@/utils/accountFiltering';

export function useAccountFiltering() {
  const [availableAccounts, setAvailableAccounts] = useState<FilteredAccounts>({
    amortization: [],
    expense: []
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadAndFilterAccounts = useCallback(async (clientId: string, entityId: string) => {
    if (!clientId || !entityId || entityId === 'all') {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const accountsData = await api.getEntityAccounts(entityId);
      const accounts = accountsData.accounts;
      console.log('📋 All accounts:', accounts.length);
      
      const filteredAccounts = filterAccountsForPrepayments(accounts);
      
      console.log('🏦 Amortization accounts found:', filteredAccounts.amortization.length, filteredAccounts.amortization);
      console.log('💰 Expense accounts found:', filteredAccounts.expense.length, filteredAccounts.expense);
      
      setAvailableAccounts(filteredAccounts);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load accounts';
      setError(errorMessage);
      console.error('Error loading accounts:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearAccounts = useCallback(() => {
    setAvailableAccounts({ amortization: [], expense: [] });
    setError(null);
  }, []);

  return {
    availableAccounts,
    isLoading,
    error,
    loadAndFilterAccounts,
    clearAccounts
  };
}