"""
Audit Log Models - Pydantic models for audit log data
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum


class AuditEventCategory(str, Enum):
    """Categories of audit events"""
    SYNC = "SYNC"
    USER_MANAGEMENT = "USER_MANAGEMENT"
    ENTITY_MANAGEMENT = "ENTITY_MANAGEMENT"
    AMORTIZATION_PROCESSING = "AMORTIZATION_PROCESSING"
    API_REQUEST = "API_REQUEST"
    AUTHENTICATION = "AUTHENTICATION"
    AUTHORIZATION = "AUTHORIZATION"
    DATA_MODIFICATION = "DATA_MODIFICATION"
    SYSTEM = "SYSTEM"
    ERROR = "ERROR"


class AuditEventStatus(str, Enum):
    """Status of audit events"""
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    INFO = "INFO"
    WARNING = "WARNING"
    STARTED = "STARTED"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"
    ERROR = "ERROR"


class AuditLogEntry(BaseModel):
    """Complete audit log entry model"""
    # Core identifiers
    audit_id: Optional[str] = Field(None, description="Unique audit log entry identifier")
    
    # Timing
    timestamp: datetime = Field(..., description="When the event occurred")
    
    # Event classification
    event_category: AuditEventCategory = Field(..., description="Broad category of the event")
    event_type: str = Field(..., description="Specific type of event within the category")
    status: AuditEventStatus = Field(..., description="Outcome of the event")
    
    # Context identifiers
    client_id: Optional[str] = Field(None, description="Client ID relevant to the event")
    entity_id: Optional[str] = Field(None, description="Entity ID relevant to the event")
    user_id: Optional[str] = Field(None, description="Firebase UID of the user involved")
    firm_id: Optional[str] = Field(None, description="Firm ID relevant to the event")
    
    # Request context
    source_ip: Optional[str] = Field(None, description="IP address of the request origin")
    user_agent: Optional[str] = Field(None, description="User agent string")
    request_id: Optional[str] = Field(None, description="Request ID for tracing")
    
    # Event details
    details: Optional[Dict[str, Any]] = Field(None, description="Additional event-specific data")
    error_message: Optional[str] = Field(None, description="Error message if status indicates failure")
    
    # Duration and performance
    duration_ms: Optional[int] = Field(None, ge=0, description="Event duration in milliseconds")
    
    # Legacy field support
    eventCategory: Optional[str] = Field(None, description="Legacy field for event category")
    eventType: Optional[str] = Field(None, description="Legacy field for event type")
    
    class Config:
        extra = "allow"
        json_schema_extra = {
            "example": {
                "audit_id": "audit_123",
                "timestamp": "2024-01-15T10:30:00Z",
                "event_category": "SYNC",
                "event_type": "XERO_CONTACTS_SYNC_STARTED",
                "status": "STARTED",
                "client_id": "client_456",
                "entity_id": "entity_789",
                "user_id": "user_uid_123",
                "source_ip": "*************",
                "details": {
                    "syncJobId": "sync_job_456",
                    "contacts_count": 150,
                    "estimated_duration": "2-3 minutes"
                },
                "duration_ms": 2500
            }
        }

    @validator('event_type')
    def validate_event_type(cls, v):
        if not v or not v.strip():
            raise ValueError('Event type cannot be empty')
        return v.strip().upper()


class AuditLogSummary(BaseModel):
    """Simplified audit log model for list views"""
    audit_id: Optional[str] = None
    timestamp: datetime
    event_category: AuditEventCategory
    event_type: str
    status: AuditEventStatus
    client_id: Optional[str] = None
    entity_id: Optional[str] = None
    user_id: Optional[str] = None
    duration_ms: Optional[int] = None
    
    # Computed fields
    is_error: bool = Field(False, description="Whether this entry represents an error")
    summary: Optional[str] = Field(None, description="Human-readable summary of the event")
    user_email: Optional[str] = Field(None, description="Email of the user who triggered the event")
    
    class Config:
        extra = "allow"


class AuditLogDetails(BaseModel):
    """Detailed audit log model for single entry views"""
    audit_id: Optional[str] = None
    timestamp: datetime
    event_category: AuditEventCategory
    event_type: str
    status: AuditEventStatus
    
    # All context
    client_id: Optional[str] = None
    entity_id: Optional[str] = None
    user_id: Optional[str] = None
    firm_id: Optional[str] = None
    source_ip: Optional[str] = None
    user_agent: Optional[str] = None
    request_id: Optional[str] = None
    
    # Event data
    details: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    duration_ms: Optional[int] = None
    
    # Enriched data (computed at query time)
    user_email: Optional[str] = Field(None, description="Email of the user who triggered the event")
    client_name: Optional[str] = Field(None, description="Name of the client")
    entity_name: Optional[str] = Field(None, description="Name of the entity")
    
    class Config:
        extra = "allow"


class AuditEventTypeInfo(BaseModel):
    """Information about a specific event type"""
    event_type: str
    category: AuditEventCategory
    description: str
    typical_duration_ms: Optional[int] = None
    severity_level: str = "INFO"  # INFO, WARNING, ERROR, CRITICAL
    
    class Config:
        extra = "allow"


class AuditStatistics(BaseModel):
    """Statistics about audit events"""
    total_events: int = 0
    events_by_category: Dict[str, int] = {}
    events_by_status: Dict[str, int] = {}
    error_rate: float = 0.0
    average_duration_ms: Optional[float] = None
    most_common_event_types: List[Dict[str, Any]] = []
    
    class Config:
        extra = "allow"
        json_schema_extra = {
            "example": {
                "total_events": 1250,
                "events_by_category": {
                    "SYNC": 450,
                    "API_REQUEST": 600,
                    "USER_MANAGEMENT": 150,
                    "ERROR": 50
                },
                "events_by_status": {
                    "SUCCESS": 1100,
                    "FAILURE": 50,
                    "IN_PROGRESS": 100
                },
                "error_rate": 4.0,
                "average_duration_ms": 1250.5,
                "most_common_event_types": [
                    {"event_type": "API_REQUEST_GET", "count": 400},
                    {"event_type": "XERO_SYNC_STARTED", "count": 125}
                ]
            }
        }


# Common event types for validation and documentation
COMMON_SYNC_EVENT_TYPES = [
    "XERO_SYNC_JOB_STARTED",
    "XERO_SYNC_JOB_COMPLETED",
    "CHART_OF_ACCOUNTS_SYNC_STARTED",
    "CHART_OF_ACCOUNTS_SYNC_SUCCESS",
    "CONTACTS_SYNC_STARTED", 
    "CONTACTS_SYNC_SUCCESS",
    "TRANSACTIONS_SYNC_STARTED",
    "TRANSACTIONS_SYNC_SUCCESS",
    "SYNC_FAILURE",
    "SYNC_ERROR"
]

COMMON_API_EVENT_TYPES = [
    "API_REQUEST_GET",
    "API_REQUEST_POST",
    "API_REQUEST_PUT",
    "API_REQUEST_DELETE",
    "API_AUTHENTICATION_SUCCESS",
    "API_AUTHENTICATION_FAILURE",
    "API_AUTHORIZATION_FAILURE"
]

COMMON_USER_EVENT_TYPES = [
    "USER_LOGIN_SUCCESS",
    "USER_LOGIN_FAILURE",
    "USER_LOGOUT",
    "USER_CREATED",
    "USER_UPDATED",
    "USER_DELETED",
    "PASSWORD_RESET_REQUESTED",
    "PASSWORD_RESET_COMPLETED"
]