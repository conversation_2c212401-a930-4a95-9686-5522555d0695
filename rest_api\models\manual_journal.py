"""
Manual Journal Models

Pydantic models for manual journal API operations.
"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime


class MatchConfirmationRequest(BaseModel):
    """Request model for confirming manual journal matches"""
    transaction_id: str = Field(..., description="ID of the transaction to link")
    confidence_override: Optional[str] = Field(None, description="Optional confidence level override")
    user_notes: Optional[str] = Field(None, description="Optional user notes for the match")


class MatchCandidate(BaseModel):
    """Model for a single match candidate in preview results"""
    journal_id: str = Field(..., description="Manual journal ID")
    confidence_score: float = Field(..., description="Numerical confidence score")
    confidence_level: str = Field(..., description="Confidence level (high, medium, low)")
    match_factors: Dict[str, Any] = Field(..., description="Breakdown of matching factors")
    asset_account_code: Optional[str] = Field(None, description="Asset account involved")
    expense_account_code: Optional[str] = Field(None, description="Expense account involved")
    amount: Optional[float] = Field(None, description="Transaction amount")
    journal_date: Optional[str] = Field(None, description="Journal date in ISO format")


class MatchPreviewResponse(BaseModel):
    """Response model for match preview requests"""
    journal_id: str = Field(..., description="Manual journal ID analyzed")
    entity_id: str = Field(..., description="Entity context")
    candidates: List[MatchCandidate] = Field(..., description="List of match candidates")
    preview_metadata: Optional[Dict[str, Any]] = Field(None, description="Preview metadata")


class DetectionStatusResponse(BaseModel):
    """Response model for detection status requests"""
    journal_id: str = Field(..., description="Manual journal ID")
    detected: bool = Field(..., description="Whether detection exists")
    detection_confidence: Optional[str] = Field(None, description="Confidence level if detected")
    flags: Optional[Dict[str, Any]] = Field(None, description="Detection flags")
    asset_account_code: Optional[str] = Field(None, description="Asset account code")
    expense_account_code: Optional[str] = Field(None, description="Expense account code")
    amount: Optional[float] = Field(None, description="Amount")
    journal_date: Optional[str] = Field(None, description="Journal date in ISO format")
    schedule_pattern_id: Optional[str] = Field(None, description="Schedule pattern ID if applicable")
    linked_transaction_id: Optional[str] = Field(None, description="Linked transaction ID if confirmed")
    synthetic_schedule_id: Optional[str] = Field(None, description="Synthetic schedule ID if created")
    created_at: Optional[str] = Field(None, description="Detection creation timestamp")
    has_synthetic_schedule: Optional[bool] = Field(None, description="Whether synthetic schedule exists")
    has_linked_transaction: Optional[bool] = Field(None, description="Whether transaction is linked")
    message: Optional[str] = Field(None, description="Status message if not detected")


class MatchConfirmationResponse(BaseModel):
    """Response model for match confirmation requests"""
    journal_id: str = Field(..., description="Manual journal ID")
    transaction_id: str = Field(..., description="Linked transaction ID")
    confirmed: Optional[bool] = Field(None, description="Whether match was confirmed")
    already_confirmed: Optional[bool] = Field(None, description="Whether already confirmed")
    synthetic_schedule_id: Optional[str] = Field(None, description="Created/existing schedule ID")
    confirmed_by: Optional[str] = Field(None, description="User who confirmed the match")
    confirmed_at: Optional[str] = Field(None, description="Confirmation timestamp")
    message: str = Field(..., description="Status message")