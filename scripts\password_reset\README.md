# Password Reset Scripts

This directory contains all scripts related to testing and debugging the password reset functionality implemented with SendGrid.

## Testing Scripts

### Core Functionality Tests
- `test_password_reset_functionality.py` - Comprehensive unit tests for password reset service
- `test_smart_password_reset.py` - Tests for smart URL detection functionality

### Email Service Tests
- `send_test_email.py` - Direct email sending test with SendGrid
- `simple_sendgrid_test.py` - Basic SendGrid API connectivity test
- `direct_email_test.py` - Direct email testing without service layer
- `sendgrid_example_test.py` - Example SendGrid implementation test

### Environment and Configuration
- `setup_sendgrid_env.py` - Environment setup helper for SendGrid
- `load_env_and_test.py` - Environment loading and testing script
- `debug_sendgrid.py` - SendGrid configuration debugging tool

### Database Setup
- `create_firestore_indexes.py` - Creates required Firestore indexes for password reset
- `create_remaining_indexes.py` - Creates additional required indexes

## Usage

### Running Tests
```bash
# Run comprehensive functionality tests
python scripts/password_reset/test_password_reset_functionality.py

# Test smart URL detection
python scripts/password_reset/test_smart_password_reset.py

# Test email sending
python scripts/password_reset/send_test_email.py
```

### Setup and Configuration
```bash
# Setup SendGrid environment
python scripts/password_reset/setup_sendgrid_env.py

# Debug SendGrid configuration
python scripts/password_reset/debug_sendgrid.py

# Create required Firestore indexes
python scripts/password_reset/create_firestore_indexes.py
```

## Environment Variables Required

- `SENDGRID_API_KEY` - Your SendGrid API key
- `SENDGRID_FROM_EMAIL` - Sender email address
- `SENDGRID_TEMPLATE_ID` - (Optional) SendGrid template ID
- `FRONTEND_URL` - Default frontend URL for password reset links

## Notes

- All scripts assume you have proper Firebase credentials configured
- SendGrid API key must be valid and have email sending permissions
- Firestore indexes must be created before running password reset functionality in production 