# Application-Specific Error Codes

This document provides a detailed list of application-specific error codes used in the DRCR API. These codes are returned in the `app_error_code` field of the standardized error response (see `README.md`) and offer more granular information than standard HTTP status codes.

## General Authentication Errors

These errors relate to issues with user authentication, primarily concerning token validation and Firebase Authentication.

*   **`AUTHENTICATION_REQUIRED`**:
    *   **Description**: Indicates that the request requires authentication, but no valid authentication token was provided. This is often handled by the HTTP Bearer scheme before reaching application logic, resulting in an HTTP 401.
    *   **Typical HTTP Status**: `401 Unauthorized`
*   **`AUTHENTICATION_FAILED`**:
    *   **Description**: A generic authentication failure occurred when the reason is not more specific (e.g., token invalid, token expired).
    *   **Typical HTTP Status**: `401 Unauthorized`
*   **`TOKEN_INVALID`**:
    *   **Description**: The provided authentication token is malformed, its signature is invalid, or it cannot be decoded.
    *   **Typical HTTP Status**: `401 Unauthorized`
*   **`TOKEN_EXPIRED`**:
    *   **Description**: The provided authentication token has expired. The client should obtain a new token.
    *   **Typical HTTP Status**: `401 Unauthorized`
*   **`USER_NOT_FOUND_FIREBASE`**:
    *   **Description**: The User ID (UID) decoded from the token does not correspond to an existing user in Firebase Authentication.
    *   **Typical HTTP Status**: `401 Unauthorized`

## Application-Level User/Role Errors

These errors occur after successful Firebase authentication but relate to issues with the user's profile or roles within the DRCR application itself.

*   **`USER_PROFILE_NOT_FOUND`**:
    *   **Description**: The user authenticated successfully via Firebase, but no corresponding user profile was found in the application's database (e.g., in `FIRM_USERS`).
    *   **Typical HTTP Status**: `403 Forbidden` (as the user is authenticated but not set up in the app)
*   **`ROLE_NOT_ASSIGNED`**:
    *   **Description**: The authenticated user has a profile in the application but has not been assigned a role, preventing access to resources.
    *   **Typical HTTP Status**: `403 Forbidden`

## Authorization / Permission Errors

These errors indicate that the authenticated user does not have the necessary permissions to perform the requested action or access the requested resource.

*   **`FORBIDDEN_ACTION`**:
    *   **Description**: A generic error indicating that the authenticated user has insufficient permissions for the requested action.
    *   **Typical HTTP Status**: `403 Forbidden`
*   **`ADMIN_PRIVILEGES_REQUIRED`**:
    *   **Description**: The requested action requires administrator-level privileges, which the current user does not possess.
    *   **Typical HTTP Status**: `403 Forbidden`
*   **`CLIENT_ACCESS_DENIED`**:
    *   **Description**: The user is not authorized to access or manage data for the specified client.
    *   **Typical HTTP Status**: `403 Forbidden`

## Generic Server & Validation Errors

These codes provide more specific reasons for common HTTP errors.

*   **`INTERNAL_SERVER_ERROR`**:
    *   **Description**: An unexpected error occurred on the server. This is a generic catch-all for unhandled exceptions.
    *   **Typical HTTP Status**: `500 Internal Server Error`
*   **`VALIDATION_ERROR`**:
    *   **Description**: The request payload failed validation. The error response `context` usually contains details about the specific fields and validation rules that failed. This is typically for Pydantic model validation.
    *   **Typical HTTP Status**: `422 Unprocessable Entity`
*   **`RESOURCE_NOT_FOUND`**:
    *   **Description**: The requested resource (e.g., a specific client, entity, or transaction) could not be found.
    *   **Typical HTTP Status**: `404 Not Found`

## Transaction Specific Errors

These errors are specific to operations related to financial transactions.

*   **`TRANSACTION_NOT_FOUND`**:
    *   **Description**: The specified transaction ID does not correspond to an existing transaction.
    *   **Typical HTTP Status**: `404 Not Found`
*   **`TRANSACTION_OPERATION_FORBIDDEN`**:
    *   **Description**: The authenticated user is not permitted to perform the requested operation on the specified transaction (e.g., editing a locked transaction).
    *   **Typical HTTP Status**: `403 Forbidden`
*   **`INVALID_TRANSACTION_PAYLOAD`**:
    *   **Description**: The transaction payload has issues not caught by basic schema validation (e.g., an update request with no actual changes, logical inconsistencies).
    *   **Typical HTTP Status**: `400 Bad Request` or `422 Unprocessable Entity`
*   **`INVALID_ENTITY_FOR_CLIENT`**:
    *   **Description**: The `entity_id` provided in the transaction payload does not belong to the specified `client_id`.
    *   **Typical HTTP Status**: `400 Bad Request` or `409 Conflict`
*   **`TRANSACTION_TOTAL_MISMATCH`**:
    *   **Description**: The sum of line item amounts does not match the provided transaction total, or other inconsistencies in amounts.
    *   **Typical HTTP Status**: `400 Bad Request`
*   **`INVALID_TRANSACTION_DATES`**:
    *   **Description**: Issues with transaction dates, such as `date_due` being before `date_issued`.
    *   **Typical HTTP Status**: `400 Bad Request`

---

*Note: This list is subject to change as the application evolves. Always refer to the latest API documentation for the most current error codes.* 