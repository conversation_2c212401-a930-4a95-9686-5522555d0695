"""
Contact/Counterparty API Routes - REST endpoints for contact/counterparty management
"""
from fastapi import APIRouter, Depends, HTTPException, status, Path, Query, Body
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
import logging

from ..core.firebase_auth import get_current_user, get_firm_admin, AuthUser
from ..dependencies import get_db
from ..models.contact import ContactType
from ..schemas.contact_schemas import (
    ContactListResponse, ContactDetailResponse, ContactCreateRequest, 
    ContactUpdateRequest, ContactCreateResponse, ContactUpdateResponse,
    ContactDeleteResponse, ContactStatsResponse, ContactStatistics
)
from ..services.contact_service import ContactService

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Contacts"])


def get_contact_service(db=Depends(get_db)) -> ContactService:
    """Dependency to get contact service instance"""
    return ContactService(db)


@router.get("/", response_model=ContactListResponse)
async def list_contacts(
    page: int = Query(1, description="Page number", ge=1),
    limit: int = Query(20, description="Items per page", ge=1, le=100),
    sort_by: str = Query("name", description="Field to sort by"),
    sort_order: str = Query("asc", pattern="^(asc|desc)$", description="Sort order"),
    client_id: Optional[str] = Query(None, description="Filter by client ID"),
    entity_id: Optional[str] = Query(None, description="Filter by entity ID"),
    name_filter: Optional[str] = Query(None, description="Filter by contact name (partial match)"),
    contact_type: Optional[ContactType] = Query(None, description="Filter by contact type"),
    email_filter: Optional[str] = Query(None, description="Filter by email (partial match)"),
    source_system: Optional[str] = Query(None, description="Filter by source system"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    has_amortization_settings: Optional[bool] = Query(None, description="Filter by presence of amortization settings"),
    current_user: AuthUser = Depends(get_current_user),
    contact_service: ContactService = Depends(get_contact_service)
):
    """List contacts with pagination and filtering"""
    try:
        # Get user's accessible client IDs
        user_client_ids = await contact_service.get_user_client_ids(current_user, client_id)
        
        if not user_client_ids and client_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User does not have access to the specified client"
            )
        
        contacts, total_count, total_pages = await contact_service.list_contacts_paginated(
            client_id=client_id,
            entity_id=entity_id,
            name_filter=name_filter,
            contact_type=contact_type,
            email_filter=email_filter,
            source_system=source_system,
            is_active=is_active,
            has_amortization_settings=has_amortization_settings,
            page=page,
            limit=limit,
            sort_by=sort_by,
            sort_order=sort_order,
            user_client_ids=user_client_ids if not client_id else None
        )
        
        pagination_meta = {
            "current_page": page,
            "page_size": limit,
            "total_items": total_count,
            "total_pages": total_pages,
            "has_next": page < total_pages,
            "has_previous": page > 1
        }
        
        filters_applied = {
            "client_id": client_id,
            "entity_id": entity_id,
            "name_filter": name_filter,
            "contact_type": contact_type,
            "email_filter": email_filter,
            "source_system": source_system,
            "is_active": is_active,
            "has_amortization_settings": has_amortization_settings
        }
        
        return ContactListResponse(
            contacts=contacts,
            pagination=pagination_meta,
            filters_applied=filters_applied
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing contacts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve contacts"
        )


@router.get("/{contact_id}", response_model=ContactDetailResponse)
async def get_contact(
    contact_id: str = Path(..., description="Contact ID"),
    current_user: AuthUser = Depends(get_current_user),
    contact_service: ContactService = Depends(get_contact_service)
):
    """Get detailed contact information by ID"""
    try:
        contact = await contact_service.get_contact_by_id(
            contact_id=contact_id,
            current_user=current_user,
            include_related_data=True
        )
        
        if not contact:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Contact not found or access denied"
            )
        
        # Get additional related data
        transaction_count = await contact_service._get_transaction_count(contact_id)
        
        return ContactDetailResponse(
            contact=contact,
            transaction_count=transaction_count,
            recent_transactions=None,  # Could be implemented later
            amortization_schedules_count=0  # Could be implemented later
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving contact {contact_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve contact"
        )


@router.post("/", response_model=ContactCreateResponse)
async def create_contact(
    request: ContactCreateRequest = Body(...),
    current_user: AuthUser = Depends(get_firm_admin),  # Only firm admins can create contacts
    contact_service: ContactService = Depends(get_contact_service)
):
    """Create a new contact (firm_admin only)"""
    try:
        contact_id, contact = await contact_service.create_contact(
            contact_data=request.contact_data,
            client_id=request.client_id,
            entity_id=request.entity_id,
            current_user=current_user
        )
        
        # Convert to ContactSummary for response
        contact_summary = {
            "counterparty_id": contact.counterparty_id,
            "client_id": contact.client_id,
            "entity_id": contact.entity_id,
            "name": contact.name,
            "contact_type": contact.contact_type,
            "email_address": contact.email_address,
            "source_system": contact.source_system,
            "is_active": contact.is_active,
            "updated_at": contact.updated_at,
            "has_amortization_settings": bool(contact.amortization_settings),
            "transaction_count": 0
        }
        
        return ContactCreateResponse(
            message="Contact created successfully",
            contact_id=contact_id,
            contact=contact_summary
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating contact: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create contact"
        )


@router.put("/{contact_id}", response_model=ContactUpdateResponse)
async def update_contact(
    contact_id: str = Path(..., description="Contact ID"),
    request: ContactUpdateRequest = Body(...),
    current_user: AuthUser = Depends(get_firm_admin),  # Only firm admins can update contacts
    contact_service: ContactService = Depends(get_contact_service)
):
    """Update an existing contact (firm_admin only)"""
    try:
        updated_contact = await contact_service.update_contact(
            contact_id=contact_id,
            contact_data=request.contact_data,
            current_user=current_user
        )
        
        # Convert to ContactSummary for response
        contact_summary = {
            "counterparty_id": updated_contact.counterparty_id,
            "client_id": updated_contact.client_id,
            "entity_id": updated_contact.entity_id,
            "name": updated_contact.name,
            "contact_type": updated_contact.contact_type,
            "email_address": updated_contact.email_address,
            "source_system": updated_contact.source_system,
            "is_active": updated_contact.is_active,
            "updated_at": updated_contact.updated_at,
            "has_amortization_settings": bool(updated_contact.amortization_settings),
            "transaction_count": await contact_service._get_transaction_count(contact_id)
        }
        
        return ContactUpdateResponse(
            message="Contact updated successfully",
            contact=contact_summary
        )
        
    except ValueError as e:
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
    except Exception as e:
        logger.error(f"Error updating contact {contact_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update contact"
        )


@router.delete("/{contact_id}", response_model=ContactDeleteResponse)
async def delete_contact(
    contact_id: str = Path(..., description="Contact ID"),
    soft_delete: bool = Query(True, description="Whether to soft delete (mark inactive) or hard delete"),
    current_user: AuthUser = Depends(get_firm_admin),  # Only firm admins can delete contacts
    contact_service: ContactService = Depends(get_contact_service)
):
    """Delete a contact (firm_admin only)"""
    try:
        success = await contact_service.delete_contact(
            contact_id=contact_id,
            current_user=current_user,
            soft_delete=soft_delete
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete contact"
            )
        
        action = "deactivated" if soft_delete else "deleted"
        return ContactDeleteResponse(
            message=f"Contact {action} successfully",
            contact_id=contact_id
        )
        
    except ValueError as e:
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        elif "cannot delete" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=str(e)
            )
    except Exception as e:
        logger.error(f"Error deleting contact {contact_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete contact"
        )


@router.get("/stats/summary", response_model=ContactStatsResponse)
async def get_contact_statistics(
    client_id: Optional[str] = Query(None, description="Filter statistics by client ID"),
    entity_id: Optional[str] = Query(None, description="Filter statistics by entity ID"),
    current_user: AuthUser = Depends(get_current_user),
    contact_service: ContactService = Depends(get_contact_service)
):
    """Get contact statistics"""
    try:
        # Check access to client if specified
        if client_id:
            user_client_ids = await contact_service.get_user_client_ids(current_user, client_id)
            if client_id not in user_client_ids:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="User does not have access to the specified client"
                )
        
        stats = await contact_service.get_contact_statistics(
            client_id=client_id,
            entity_id=entity_id,
            current_user=current_user
        )
        
        statistics = ContactStatistics(**stats)
        
        return ContactStatsResponse(
            statistics=statistics,
            generated_at=datetime.now(timezone.utc).isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting contact statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve contact statistics"
        )


# Utility endpoints
@router.get("/enums/contact-types")
async def get_contact_types():
    """Get available contact types"""
    return {
        "contact_types": [
            {"value": contact_type.value, "label": contact_type.value.replace("_", " ").title()}
            for contact_type in ContactType
        ]
    }


@router.get("/enums/source-systems")
async def get_source_systems(
    current_user: AuthUser = Depends(get_current_user),
    contact_service: ContactService = Depends(get_contact_service)
):
    """Get available source systems from existing contacts"""
    try:
        # Get user's accessible client IDs
        user_client_ids = await contact_service.get_user_client_ids(current_user)
        
        # This could be optimized with a more specific query
        contacts, _, _ = await contact_service.list_contacts_paginated(
            user_client_ids=user_client_ids,
            limit=1000  # Get a large sample to find source systems
        )
        
        source_systems = set()
        for contact in contacts:
            if contact.source_system:
                source_systems.add(contact.source_system)
        
        return {
            "source_systems": [
                {"value": system, "label": system.title()}
                for system in sorted(source_systems)
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting source systems: {e}")
        return {"source_systems": []}


# Test endpoints for backward compatibility
@router.get("/test")
async def test_contacts_basic():
    """Basic test endpoint for contacts router"""
    return {"message": "Contacts router test endpoint", "status": "ok"}


@router.get("/test-auth")
async def test_contacts_auth(
    current_user: AuthUser = Depends(get_current_user)
):
    """Test endpoint with authentication"""
    return {
        "message": "Contacts router auth test endpoint",
        "user": current_user.email,
        "firm_id": current_user.firm_id,
        "status": "ok"
    }