# DRCR Frontend Deployment Script
# Deploys the optimized React app to Firebase Hosting

Write-Host "🚀 Starting DRCR Frontend Deployment..." -ForegroundColor Green

# Step 1: Install dependencies (if needed)
Write-Host "📦 Checking dependencies..." -ForegroundColor Yellow
if (!(Test-Path "node_modules")) {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    npm install
}

# Step 2: Build the optimized production version
Write-Host "🔨 Building optimized production bundle..." -ForegroundColor Yellow
npm run build

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed! Please fix errors and try again." -ForegroundColor Red
    exit 1
}

# Step 3: Show build stats
Write-Host "📊 Build completed! Bundle analysis:" -ForegroundColor Green
Get-ChildItem dist -Recurse | Where-Object { !$_.PSIsContainer } | ForEach-Object {
    $size = [math]::Round($_.Length / 1KB, 2)
    Write-Host "  $($_.Name): ${size} KB" -ForegroundColor Cyan
}

# Step 4: Deploy to Firebase
Write-Host "🌐 Deploying to Firebase Hosting..." -ForegroundColor Yellow
firebase deploy --only hosting

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Deployment successful!" -ForegroundColor Green
    Write-Host "🔗 Your optimized app is now live!" -ForegroundColor Green
}
else {
    Write-Host "❌ Deployment failed! Check Firebase CLI setup." -ForegroundColor Red
}

Write-Host "🎉 Deployment process completed!" -ForegroundColor Green 