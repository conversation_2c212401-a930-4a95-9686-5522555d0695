import type { Account } from '@/lib/api';

export interface FilteredAccounts {
  amortization: { code: string; name: string }[];
  expense: { code: string; name: string }[];
}

/**
 * Filters accounts for prepayment use cases
 * @param accounts - Array of normalized Account objects
 * @returns Filtered accounts for amortization and expense dropdowns
 */
export function filterAccountsForPrepayments(accounts: Account[]): FilteredAccounts {
  // Filter prepayment accounts (620-629 range AND name contains 'prepayment')
  const amortizationAccounts = accounts.filter((account: Account) => {
    const code = account.code || '';
    const name = (account.name || '').toLowerCase();
    const codeNum = parseInt(code);
    return (codeNum >= 620 && codeNum <= 629) && name.includes('prepayment') && code.trim() !== '';
  });

  // Filter expense accounts (class = 'EXPENSE' only, not type)
  const expenseAccounts = accounts.filter((account: Account) => {
    const code = account.code || '';
    return account.class === 'EXPENSE' && code.trim() !== '';
  });

  return {
    amortization: amortizationAccounts
      .filter((account: Account) => account.code && account.code.trim() !== '')
      .map((account: Account) => ({
        code: account.code,
        name: account.name
      })),
    expense: expenseAccounts
      .filter((account: Account) => account.code && account.code.trim() !== '')
      .map((account: Account) => ({
        code: account.code,
        name: account.name
      }))
  };
}

/**
 * Filters accounts for entity settings (all expense accounts)
 * @param accounts - Array of normalized Account objects
 * @returns All expense accounts for entity configuration
 */
export function filterAccountsForEntitySettings(accounts: Account[]): Account[] {
  return accounts.filter(acc => {
    const code = acc.code || '';
    return (acc.type === 'EXPENSE' || acc.class === 'EXPENSE') && code.trim() !== '';
  });
}