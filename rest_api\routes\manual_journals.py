"""
Manual Journals Routes

REST API endpoints for manual journal operations, specifically focused on 
prepayment release detection and matching capabilities.

Endpoints:
- GET /manual-journals/{journal_id}/match-preview - Preview prepayment release matching
"""
from fastapi import APIRouter, Depends, HTTPException, status, Path, Query, Body
from typing import Dict, Any, List, Optional
from google.cloud import firestore
from datetime import datetime
import logging

from ..core.firebase_auth import get_current_user, get_firm_user_with_client_access, AuthUser
from ..dependencies import get_db
from ..services.prepayment_release_detector_service import PrepaymentReleaseDetectorService
from ..models.manual_journal import (
    MatchConfirmationRequest,
    MatchPreviewResponse,
    DetectionStatusResponse,
    MatchConfirmationResponse
)

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Manual Journals"])


@router.get("/manual-journals/{journal_id}/match-preview")
async def get_manual_journal_match_preview(
    journal_id: str = Path(..., description="Manual journal ID to analyze"),
    entity_id: str = Query(..., description="Entity ID for context"),
    max_candidates: int = Query(3, ge=1, le=10, description="Maximum candidates to return"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
) -> MatchPreviewResponse:
    """
    Preview prepayment release matching for a manual journal.
    
    This endpoint runs the same detection algorithm as the automated detector
    but does not persist any results. It provides a read-only preview of 
    potential prepayment release patterns for user review.
    
    Security:
    - Requires authentication
    - Validates user access to the entity via client_id lookup from MJ document
    
    Args:
        journal_id: Manual journal to analyze
        entity_id: Entity context for configuration and account codes
        max_candidates: Maximum number of candidates to return (1-10)
        current_user: Authenticated user
        db: Firestore database client
        
    Returns:
        Dictionary containing:
        - journal_id: The analyzed journal ID
        - entity_id: Entity context
        - candidates: List of detection results ranked by confidence
        
    Raises:
        404: Journal not found or user lacks access
        400: Invalid parameters or configuration
        500: Internal processing error
    """
    try:
        logger.info(f"Preview match request for journal {journal_id} in entity {entity_id}")
        
        # First, fetch the manual journal to validate existence and get client_id
        journal_ref = db.collection("MANUAL_JOURNALS").document(journal_id)
        journal_doc = await journal_ref.get()
        
        if not journal_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Manual journal {journal_id} not found"
            )
        
        journal_data = journal_doc.to_dict()
        
        # Validate entity_id matches journal
        journal_entity_id = journal_data.get("entity_id")
        if journal_entity_id != entity_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Journal entity_id {journal_entity_id} does not match requested entity_id {entity_id}"
            )
        
        # Get client_id for authorization
        client_id = journal_data.get("client_id")
        if not client_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Manual journal missing client_id"
            )
        
        # Validate user access to this client/entity
        await get_firm_user_with_client_access(client_id, current_user)
        
        # Initialize detector service
        detector_service = PrepaymentReleaseDetectorService()
        
        # Run preview matching (read-only)
        preview_result = await detector_service.preview_match(
            entity_id=entity_id,
            journal_id=journal_id,
            db=db,
            max_candidates=max_candidates
        )
        
        # Check for errors in preview result
        if "error" in preview_result:
            if "configuration incomplete" in preview_result["error"].lower():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Entity configuration incomplete: {preview_result['error']}"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Preview processing error: {preview_result['error']}"
                )
        
        # Add metadata to response
        preview_result.update({
            "preview_metadata": {
                "requested_by": current_user.user_id,
                "request_timestamp": datetime.utcnow().isoformat(),  # Fix: Use ISO timestamp
                "max_candidates_requested": max_candidates,
                "read_only": True,
                "detection_algorithm_version": "1.0"
            }
        })
        
        logger.info(f"Preview match completed for journal {journal_id}: {len(preview_result.get('candidates', []))} candidates")
        
        return preview_result
        
    except HTTPException:
        # Re-raise HTTP exceptions (already have proper status codes)
        raise
    except Exception as e:
        logger.error(f"Error in manual journal match preview for {journal_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during match preview"
        )


@router.get("/manual-journals/{journal_id}/detection-status")
async def get_manual_journal_detection_status(
    journal_id: str = Path(..., description="Manual journal ID to check"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
) -> DetectionStatusResponse:
    """
    Get detection status for a manual journal.
    
    Returns information about whether this journal has been analyzed by the
    prepayment release detector, including confidence level and linked schedules.
    
    Args:
        journal_id: Manual journal to check
        current_user: Authenticated user
        db: Firestore database client
        
    Returns:
        Dictionary containing detection status and details
        
    Raises:
        404: Journal not found or user lacks access
        500: Internal processing error
    """
    try:
        logger.debug(f"Detection status request for journal {journal_id} by user {current_user.user_id}")
        
        # Fetch the manual journal for authorization
        journal_ref = db.collection("MANUAL_JOURNALS").document(journal_id)
        journal_doc = await journal_ref.get()
        
        if not journal_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Manual journal {journal_id} not found"
            )
        
        journal_data = journal_doc.to_dict()
        client_id = journal_data.get("client_id")
        
        if not client_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Manual journal missing client_id"
            )
        
        # Validate user access
        await get_firm_user_with_client_access(client_id, current_user)
        
        # Check for existing detection
        detection_ref = db.collection("PREPAYMENT_RELEASES_DETECTED").document(journal_id)
        detection_doc = await detection_ref.get()
        
        if detection_doc.exists:
            detection_data = detection_doc.to_dict()
            
            status_result = {
                "journal_id": journal_id,
                "detected": True,
                "detection_confidence": detection_data.get("detection_confidence"),
                "flags": detection_data.get("flags", {}),
                "asset_account_code": detection_data.get("asset_account_code"),
                "expense_account_code": detection_data.get("expense_account_code"),
                "amount": detection_data.get("amount"),
                "journal_date": detection_data.get("journal_date").isoformat() if detection_data.get("journal_date") else None,
                "schedule_pattern_id": detection_data.get("schedule_pattern_id"),
                "linked_transaction_id": detection_data.get("linked_transaction_id"),
                "synthetic_schedule_id": detection_data.get("synthetic_schedule_id"),
                "created_at": detection_data.get("created_at").isoformat() if hasattr(detection_data.get("created_at"), 'isoformat') else str(detection_data.get("created_at")) if detection_data.get("created_at") else None,
                "has_synthetic_schedule": bool(detection_data.get("synthetic_schedule_id")),
                "has_linked_transaction": bool(detection_data.get("linked_transaction_id"))
            }
        else:
            status_result = {
                "journal_id": journal_id,
                "detected": False,
                "detection_confidence": None,
                "message": "No prepayment release detection found for this journal"
            }
        
        return status_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting detection status for journal {journal_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error getting detection status"
        )


@router.post("/manual-journals/{journal_id}/confirm-match")
async def confirm_manual_journal_match(
    journal_id: str = Path(..., description="Manual journal ID"),
    match_data: MatchConfirmationRequest = Body(..., description="Match confirmation data"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
) -> MatchConfirmationResponse:
    """
    Confirm a prepayment release match and create synthetic schedule.
    
    This endpoint creates a synthetic amortization schedule for a confirmed
    manual journal → transaction match. It should be called after the user
    reviews a preview and confirms the match.
    
    Args:
        journal_id: Manual journal ID
        match_data: Match confirmation data including transaction_id
        current_user: Authenticated user
        db: Firestore database client
        
    Returns:
        Dictionary with confirmation status and created schedule ID
        
    Raises:
        404: Journal or transaction not found
        400: Invalid match data or already confirmed
        500: Internal processing error
    """
    try:
        logger.info(f"Match confirmation request for journal {journal_id} by user {current_user.user_id}")
        
        # Extract validated data from Pydantic model
        transaction_id = match_data.transaction_id
        
        # Fetch and validate journal
        journal_ref = db.collection("MANUAL_JOURNALS").document(journal_id)
        journal_doc = await journal_ref.get()
        
        if not journal_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Manual journal {journal_id} not found"
            )
        
        journal_data = journal_doc.to_dict()
        client_id = journal_data.get("client_id")
        
        # Validate user access
        await get_firm_user_with_client_access(client_id, current_user)
        
        # Check if already confirmed (idempotency)
        detection_ref = db.collection("PREPAYMENT_RELEASES_DETECTED").document(journal_id)
        detection_doc = await detection_ref.get()
        
        if detection_doc.exists:
            detection_data = detection_doc.to_dict()
            existing_schedule_id = detection_data.get("synthetic_schedule_id")
            
            if existing_schedule_id:
                return {
                    "journal_id": journal_id,
                    "transaction_id": transaction_id,
                    "already_confirmed": True,
                    "synthetic_schedule_id": existing_schedule_id,
                    "message": "Match already confirmed with existing synthetic schedule"
                }
        
        # Create synthetic schedule
        detector_service = PrepaymentReleaseDetectorService()
        schedule_id = await detector_service.create_synthetic_schedule(
            mj_id=journal_id,
            bill_id=transaction_id,
            db=db
        )
        
        if not schedule_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create synthetic schedule"
            )
        
        logger.info(f"Created synthetic schedule {schedule_id} for journal {journal_id} → transaction {transaction_id}")
        
        return {
            "journal_id": journal_id,
            "transaction_id": transaction_id,
            "confirmed": True,
            "synthetic_schedule_id": schedule_id,
            "confirmed_by": current_user.user_id,
            "confirmed_at": datetime.utcnow().isoformat(),  # Fix: Use ISO timestamp
            "message": "Match confirmed and synthetic schedule created"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error confirming match for journal {journal_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during match confirmation"
        )