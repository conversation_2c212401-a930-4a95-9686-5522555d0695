from typing import Dict, Any, List, Optional, Union
from datetime import date, datetime, timezone
import re

from ..models import invoice as invoice_models, schedule as schedule_models, attachment as attachment_models # Added attachment_models
from ..models.invoice import InvoiceStatus, InvoiceType, LineItem, Contact # Specific imports
from ..models.schedule import ScheduleStatus # Import ScheduleStatus if needed for direct mapping value checks
from ..models.attachment import AttachmentSourceSystem # Import AttachmentSourceSystem
from .field_access import get_field, get_date_issued, get_date_due

# Placeholder for a logger, can be configured later
import logging
logger = logging.getLogger(__name__)

def _parse_date(date_input: Optional[Union[str, datetime, Any]]) -> Optional[date]:
    """
    Parse date from various input formats: strings, datetime objects, or Firestore timestamps.
    
    Args:
        date_input: Can be a string, datetime object, Firestore DatetimeWithNanoseconds, or None
        
    Returns:
        date object or None if parsing fails
    """
    if not date_input:
        return None
        
    try:
        # Handle datetime objects (including Firestore DatetimeWithNanoseconds)
        if isinstance(date_input, datetime):
            return date_input.date()
        
        # Handle objects with datetime-like attributes (Firestore Timestamp)
        if hasattr(date_input, 'date') and callable(getattr(date_input, 'date')):
            return date_input.date()
            
        # Handle string inputs
        if isinstance(date_input, str):
            # Handle Xero's /Date(timestamp)/ format first
            if date_input.startswith("/Date("):
                ts_match = re.search(r'(\d+)', date_input)
                if ts_match:
                    timestamp_ms = int(ts_match.group(1))
                    return datetime.fromtimestamp(timestamp_ms / 1000, tz=timezone.utc).date()
            
            # Attempt to parse ISO format date (YYYY-MM-DD) or datetime and take date part
            if 'T' in date_input:
                return datetime.fromisoformat(date_input.replace("Z", "+00:00")).date()
            return datetime.strptime(date_input, "%Y-%m-%d").date()
            
    except (ValueError, AttributeError, TypeError) as e:
        logger.warning(f"Could not parse date input: {date_input} (type: {type(date_input)}, error: {e})")
        return None

def firestore_transaction_to_invoice_model(
    transaction_id: str, transaction_data: Dict[str, Any]
) -> Optional[invoice_models.Invoice]:
    """
    Transforms a Firestore transaction document (expected to be an invoice/bill from Xero)
    into an invoice_models.Invoice Pydantic model.
    """
    if not transaction_data:
        return None

    # Handle both nested Xero format and flattened Firestore format
    raw_xero_data = transaction_data.get("data", {}) # The original Xero payload (if nested)
    if not raw_xero_data:
        # If no nested data, use the transaction_data itself (flattened format)
        raw_xero_data = transaction_data

    # --- Basic Information & ID ---
    # The main ID for the API model should be the Firestore document ID
    api_invoice_id = transaction_id
    source_system_id = transaction_data.get("sourceSystemId") or transaction_data.get("source_system_id")

    # --- Status Mapping ---
    # Xero Statuses: DRAFT, SUBMITTED, AUTHORISED, PAID, VOIDED, DELETED, ARCHIVED
    # API InvoiceStatus Enums: DRAFT, SUBMITTED, AUTHORIZED, PAID, VOIDED, DELETED
    xero_status = raw_xero_data.get("Status") or transaction_data.get("status") # From Xero data or flattened
    api_status = None
    if xero_status:
        status_map = {
            "DRAFT": InvoiceStatus.DRAFT,
            "SUBMITTED": InvoiceStatus.SUBMITTED,
            "AUTHORISED": InvoiceStatus.AUTHORIZED, # Note Xero 'AUTHORISED' vs API 'AUTHORIZED'
            "PAID": InvoiceStatus.PAID,
            "VOIDED": InvoiceStatus.VOIDED,
            "DELETED": InvoiceStatus.DELETED,
            # ARCHIVED in Xero might map to VOIDED or a new status if needed
        }
        api_status = status_map.get(xero_status.upper())
        if not api_status:
            logger.warning(f"Unknown Xero status '{xero_status}' for transaction {transaction_id}. Setting to DRAFT.")
            api_status = InvoiceStatus.DRAFT # Default or handle as error

    # --- Type Mapping ---
    # Xero Types: ACCREC (Invoice), ACCPAY (Bill)
    # API InvoiceType Enums: INVOICE, BILL
    xero_type = raw_xero_data.get("Type") or transaction_data.get("transaction_type") # From Xero data or flattened
    api_type = None
    if xero_type:
        type_map = {
            "ACCREC": InvoiceType.INVOICE,
            "ACCPAY": InvoiceType.BILL,
        }
        api_type = type_map.get(xero_type.upper())
        if not api_type:
            logger.warning(f"Unknown Xero type '{xero_type}' for transaction {transaction_id}. Transformation will be skipped.")
            return None

    # --- Dates ---
    # Use canonical field access to handle both Xero format and flattened Firestore format
    date_issued_value = get_date_issued(raw_xero_data) or get_date_issued(transaction_data)
    date_due_value = get_date_due(raw_xero_data) or get_date_due(transaction_data)
    
    api_date_issued = _parse_date(date_issued_value)
    api_date_due = _parse_date(date_due_value)

    if not api_date_issued: # Date issued is usually mandatory
        logger.warning(f"Missing or invalid date_issued for transaction {transaction_id}. Skipping.")
        return None

    # --- Contact ---
    # Handle both nested Xero format and flattened Firestore format
    xero_contact_info = raw_xero_data.get("Contact", {})
    api_contact_id = (xero_contact_info.get("ContactID") or 
                     transaction_data.get("contact_id")) # Will be None if not present

    # --- Line Items ---
    api_line_items: List[LineItem] = []
    xero_line_items = raw_xero_data.get("LineItems", []) or transaction_data.get("line_items", [])
    for idx, xero_item in enumerate(xero_line_items):
        try:
            line_item_id_xero = xero_item.get("LineItemID")

            # Refined LineItem ID parsing to ensure an int for Pydantic model
            parsed_line_item_id = idx # Default to index
            if line_item_id_xero is not None:
                if isinstance(line_item_id_xero, int):
                    parsed_line_item_id = line_item_id_xero
                elif isinstance(line_item_id_xero, str) and line_item_id_xero.isdigit():
                    try:
                        parsed_line_item_id = int(line_item_id_xero)
                    except ValueError:
                        logger.warning(f"Could not parse LineItemID '{line_item_id_xero}' to int for transaction {transaction_id}. Using index {idx} as fallback.")
                        # parsed_line_item_id remains idx
                else: # It's a non-digit string (e.g., GUID) or other type
                    logger.debug(f"Non-integer LineItemID '{line_item_id_xero}' for transaction {transaction_id}. Using index {idx} as fallback.")
                    # parsed_line_item_id remains idx
            
            api_line_items.append(
                LineItem(
                    id=parsed_line_item_id, # Ensure this is an int
                    description=xero_item.get("Description", ""),
                    quantity=float(xero_item.get("Quantity", 0)),
                    unit_amount=float(xero_item.get("UnitAmount", 0)),
                )
            )
        except (ValueError, TypeError) as e:
            logger.warning(f"Skipping line item for transaction {transaction_id} due to parsing error: {e}. Data: {xero_item}")
            continue

    # --- Amounts ---
    # Handle both Xero format and flattened Firestore format
    subtotal = raw_xero_data.get("SubTotal") or transaction_data.get("subtotal")
    tax_total = raw_xero_data.get("TotalTax") or transaction_data.get("tax_total")
    total = raw_xero_data.get("Total") or transaction_data.get("total_amount")

    # --- System and other fields from Firestore doc if needed ---
    created_at_ts = (transaction_data.get("syncTimestamp") or 
                    transaction_data.get("created_at"))
    updated_at_ts = (transaction_data.get("syncTimestamp") or 
                    transaction_data.get("updated_at")) 

    # Convert Firestore timestamp to datetime if necessary.
    # Fallback to datetime.now() ensures the field is always populated,
    # which might be desired over Optional[datetime] if a timestamp is always expected.
    api_created_at = created_at_ts if isinstance(created_at_ts, datetime) else datetime.now(timezone.utc) 
    api_updated_at = updated_at_ts if isinstance(updated_at_ts, datetime) else datetime.now(timezone.utc) 

    try:
        # Add contact_name to metadata for frontend use
        enhanced_metadata = transaction_data.get("prepayment_analysis", {})
        if isinstance(enhanced_metadata, dict):
            enhanced_metadata = enhanced_metadata.copy()
        else:
            enhanced_metadata = {}
        
        # Add contact name to metadata
        contact_name = (xero_contact_info.get("Name") or 
                       transaction_data.get("contact_name", "Unknown Supplier"))
        enhanced_metadata["contact_name"] = contact_name

        # Extract attachment data
        has_attachments = transaction_data.get("has_attachments")
        attachment_id = transaction_data.get("attachment_id")

        invoice_obj = invoice_models.Invoice(
            id=api_invoice_id,
            document_number=(raw_xero_data.get("InvoiceNumber") or raw_xero_data.get("BillNumber") or 
                           transaction_data.get("document_number")),
            status=api_status or InvoiceStatus.DRAFT, 
            type=api_type, 
            date_issued=api_date_issued,
            date_due=api_date_due,
            # Handle both currency formats
            currency=(raw_xero_data.get("CurrencyCode") or 
                     transaction_data.get("currency_code", "USD")),
            contact_id=api_contact_id,
            line_items=api_line_items,
            subtotal=float(subtotal) if subtotal is not None else None,
            tax_total=float(tax_total) if tax_total is not None else None,
            total=float(total) if total is not None else None,
            source_id=source_system_id,
            source=transaction_data.get("source_system", "Xero"),
            amount_paid=float(raw_xero_data.get("AmountPaid") or transaction_data.get("amount_paid", 0.0)),
            amount_due=float(raw_xero_data.get("AmountDue") or transaction_data.get("amount_due", 0.0)),
            created_at=api_created_at,
            updated_at=api_updated_at,
            notes=(raw_xero_data.get("Reference") or transaction_data.get("reference")),
            metadata=enhanced_metadata,
            # Include attachment data
            has_attachments=has_attachments,
            attachment_id=attachment_id
        )
        return invoice_obj
    except Exception as e: # Catch Pydantic validation errors or others
        logger.error(f"Failed to create Invoice Pydantic model for transaction {transaction_id}: {e}", exc_info=True)
        logger.debug(f"Data causing Pydantic error: Transaction ID: {transaction_id}, Raw Xero Data: {raw_xero_data}, Processed API fields: {api_status}, {api_type}, {api_date_issued}, {api_date_due}")

        return None

def firestore_to_schedule_model(
    schedule_id: str, schedule_data: Dict[str, Any]
) -> Optional[schedule_models.Schedule]:
    """
    Transforms a Firestore AMORTIZATION_SCHEDULES document 
    into a schedule_models.Schedule Pydantic model.
    """
    if not schedule_data:
        return None

    try:
        # Convert date strings or timestamps to date/datetime objects
        entry_date_val = schedule_data.get("entry_date")
        parsed_entry_date = None
        if isinstance(entry_date_val, str):
            parsed_entry_date = _parse_date(entry_date_val) 
        elif isinstance(entry_date_val, datetime):
            parsed_entry_date = entry_date_val.date()
        elif isinstance(entry_date_val, date):
            parsed_entry_date = entry_date_val
        
        # Don't return early - we have fallback logic below for amortizationStartDate

        # Fallback to datetime.now() for created_at/updated_at ensures these fields are always populated.
        created_at_val = schedule_data.get("created_at")
        parsed_created_at = datetime.now(timezone.utc) 
        if isinstance(created_at_val, datetime):
            parsed_created_at = created_at_val
        elif isinstance(created_at_val, str):
            try: parsed_created_at = datetime.fromisoformat(created_at_val.replace("Z", "+00:00"))
            except ValueError: pass 

        updated_at_val = schedule_data.get("updated_at")
        parsed_updated_at = datetime.now(timezone.utc) 
        if isinstance(updated_at_val, datetime):
            parsed_updated_at = updated_at_val
        elif isinstance(updated_at_val, str):
            try: parsed_updated_at = datetime.fromisoformat(updated_at_val.replace("Z", "+00:00"))
            except ValueError: pass 

        # Ensure status is a valid ScheduleStatus enum member
        raw_status = schedule_data.get("status")
        api_status = schedule_models.ScheduleStatus.ERROR # Default to ERROR

        if raw_status:
            normalized_status = raw_status.lower().strip() # Normalize to lowercase and strip whitespace
            
            if normalized_status == schedule_models.ScheduleStatus.PENDING_CONFIGURATION.value:
                api_status = schedule_models.ScheduleStatus.PENDING_CONFIGURATION
            elif normalized_status == schedule_models.ScheduleStatus.PROPOSED.value or normalized_status == "pending":
                api_status = schedule_models.ScheduleStatus.PROPOSED
            elif normalized_status == "pending_confirmation":
                api_status = schedule_models.ScheduleStatus.CONFIRMED
            elif normalized_status == schedule_models.ScheduleStatus.CONFIRMED.value:
                api_status = schedule_models.ScheduleStatus.CONFIRMED
            elif normalized_status == schedule_models.ScheduleStatus.POSTED.value:
                api_status = schedule_models.ScheduleStatus.POSTED
            elif normalized_status == schedule_models.ScheduleStatus.CANCELLED.value:
                api_status = schedule_models.ScheduleStatus.CANCELLED
            elif normalized_status == schedule_models.ScheduleStatus.ERROR.value: # Explicitly check for "error" string as input
                api_status = schedule_models.ScheduleStatus.ERROR
            # Add any other direct string mappings to enum members if necessary
            else:
                logger.warning(f"Unknown or unhandled status '{raw_status}' for schedule {schedule_id}. Defaulting to ERROR.")
                # api_status remains ERROR (the default set at the beginning)
        else:
             logger.warning(f"Missing status for schedule {schedule_id}. Setting to ERROR.")
             # api_status remains ERROR (the default set at the beginning)

        # Handle both field name variations
        current_transaction_id_for_model = schedule_data.get("transaction_id") or schedule_data.get("transactionId")
        
        # Handle amount field variations
        amount_value = schedule_data.get("originalAmount") or schedule_data.get("amount", 0.0)
        
        # For master schedules without individual entry dates, use the start date
        if not parsed_entry_date:
            start_date_val = schedule_data.get("amortizationStartDate")
            if isinstance(start_date_val, datetime):
                parsed_entry_date = start_date_val.date()
            elif isinstance(start_date_val, str):
                try:
                    parsed_entry_date = datetime.fromisoformat(start_date_val.replace("Z", "+00:00")).date()
                except ValueError:
                    pass
        
        # If still no entry date, skip this schedule
        if not parsed_entry_date:
            logger.warning(f"No valid entry_date or amortizationStartDate for schedule {schedule_id}. Skipping.")
            return None

        # Include monthly entries if present
        monthly_entries = schedule_data.get("monthlyEntries", [])
        
        schedule_obj = schedule_models.Schedule(
            id=schedule_id,
            transaction_id=current_transaction_id_for_model,
            line_item_id=schedule_data.get("line_item_id"),
            status=api_status,
            entry_date=parsed_entry_date,
            amount=float(amount_value), # Use originalAmount or amount
            # Default currency to USD if not present; this is a business/system decision.
            currency=schedule_data.get("currency", "USD"), 
            description=schedule_data.get("description"),
            account_code=schedule_data.get("amortizationAccountCode") or schedule_data.get("account_code"),
            expense_account_code=schedule_data.get("expenseAccountCode") or schedule_data.get("expense_account_code"),
            journal_id_external=schedule_data.get("journal_id_external"),
            journal_link_external=schedule_data.get("journal_link_external"),
            monthly_entries=monthly_entries if monthly_entries else None,
            created_at=parsed_created_at,
            updated_at=parsed_updated_at,
        )
        return schedule_obj
    except Exception as e: # Catch Pydantic validation errors or others
        logger.error(f"Failed to create Schedule Pydantic model for schedule {schedule_id}: {e}", exc_info=True)
        logger.debug(f"Data causing Pydantic error for schedule: ID: {schedule_id}, Raw Data: {schedule_data}")
        return None

def firestore_to_attachment_model(
    attachment_id: str, attachment_data: Dict[str, Any]
) -> Optional[attachment_models.Attachment]:
    """
    Transforms a Firestore ATTACHMENTS document 
    into an attachment_models.Attachment Pydantic model.
    """
    if not attachment_data:
        return None

    try:
        # Fallback to datetime.now() for created_at ensures this field is always populated.
        created_at_val = attachment_data.get("created_at") or attachment_data.get("upload_date")
        parsed_created_at = datetime.now(timezone.utc) 
        if isinstance(created_at_val, datetime):
            parsed_created_at = created_at_val
        elif isinstance(created_at_val, str):
            try: 
                parsed_created_at = datetime.fromisoformat(created_at_val.replace("Z", "+00:00"))
            except ValueError:
                 try: # Try parsing a different common format like from Xero (e.g., /Date(1628881030000+0000)/)
                    if isinstance(created_at_val, str) and created_at_val.startswith("/Date("):
                        ts_match = re.search(r'\d+', created_at_val)
                        if ts_match:
                            parsed_created_at = datetime.fromtimestamp(int(ts_match.group(0)) / 1000, tz=timezone.utc)
                    else: raise ValueError # Reraise if not the Xero format
                 except ValueError:
                    logger.warning(f"Could not parse created_at string '{created_at_val}' for attachment {attachment_id}. Using default.")
                    pass # Keep default
        elif isinstance(created_at_val, (int, float)): # Handle timestamp numbers
            try:
                parsed_created_at = datetime.fromtimestamp(created_at_val, tz=timezone.utc)
            except ValueError:
                logger.warning(f"Could not parse created_at timestamp {created_at_val} for attachment {attachment_id}. Using default.")

        raw_source_system = attachment_data.get("source_system") or attachment_data.get("source")
        api_source_system = AttachmentSourceSystem.UNKNOWN
        if raw_source_system and isinstance(raw_source_system, str):
            try:
                normalized_raw_system_value = raw_source_system.title() # e.g., "xero" -> "Xero"
                if normalized_raw_system_value in AttachmentSourceSystem._value2member_map_:
                    api_source_system = AttachmentSourceSystem(normalized_raw_system_value)
                elif raw_source_system in AttachmentSourceSystem._value2member_map_: # Original value, e.g. "Xero"
                    api_source_system = AttachmentSourceSystem(raw_source_system)
                elif raw_source_system.upper() in AttachmentSourceSystem._member_names_: # Key, e.g. "XERO"
                    api_source_system = AttachmentSourceSystem[raw_source_system.upper()]
                else:
                    # If none of the above, it's still considered invalid for our enum values/keys
                    raise ValueError(f"Cannot map '{raw_source_system}' to AttachmentSourceSystem")
            except (ValueError, KeyError):
                logger.warning(f"Invalid or unmappable source_system '{raw_source_system}' for attachment {attachment_id}. Defaulting to UNKNOWN.")
                # api_source_system remains UNKNOWN
        elif raw_source_system: # Not a string, but present
             logger.warning(f"Non-string source_system value '{raw_source_system}' for attachment {attachment_id}. Defaulting to UNKNOWN.")
             # api_source_system remains UNKNOWN
        
        attachment_obj = attachment_models.Attachment(
            id=attachment_id,
            transaction_id=attachment_data.get("transaction_id") or attachment_data.get("parent_id"), # Common alternatives
            file_name=attachment_data.get("file_name") or attachment_data.get("FileName") or "unknown_file",
            content_type=attachment_data.get("content_type") or attachment_data.get("MimeType") or "application/octet-stream",
            size_bytes=int(attachment_data.get("size_bytes") or attachment_data.get("ContentLength", 0)),
            gcs_path=attachment_data.get("gcs_path"),
            download_url=attachment_data.get("download_url") or attachment_data.get("Url"), # Xero uses "Url"
            source_system=api_source_system,
            external_id=attachment_data.get("external_id") or attachment_data.get("AttachmentID"), # Xero uses "AttachmentID"
            created_at=parsed_created_at,
        )
        return attachment_obj
    except Exception as e:
        logger.error(f"Failed to create Attachment Pydantic model for attachment {attachment_id}: {e}", exc_info=True)
        logger.debug(f"Data causing Pydantic error for attachment: ID: {attachment_id}, Raw Data: {attachment_data}")
        return None

# Future transformers can be added below (e.g., for Contacts, Accounts if needed by API) 