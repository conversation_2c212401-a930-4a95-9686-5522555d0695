import { api } from '../lib/api';

export interface FirmDetails {
  firm_id: string;
  name: string;
  status: string;
  subscription_tier?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: any;
  settings?: any;
  created_at?: string;
  updated_at?: string;
}

export class FirmService {
  /**
   * Get firm details by firm ID
   * This will call the backend to fetch firm information from Firestore
   */
  static async getFirmDetails(firmId: string): Promise<FirmDetails | null> {
    try {
      // Call backend endpoint to get firm details
      // The backend should implement GET /firms/{firmId} endpoint
      const response = await api.get<FirmDetails>(`/firms/${firmId}`);
      return response;
    } catch (error) {
      console.error('Error fetching firm details:', error);
      return null;
    }
  }

  /**
   * Get firm name with fallback logic
   * Returns the firm name or a fallback if not available
   */
  static async getFirmName(firmId: string | undefined): Promise<string> {
    if (!firmId) {
      return 'DRCR'; // Fallback to app name if no firm ID
    }

    try {
      const firmDetails = await this.getFirmDetails(firmId);
      if (firmDetails?.name) {
        return firmDetails.name;
      }
    } catch (error) {
      console.error('Error getting firm name:', error);
    }

    // Fallback to a formatted firm ID
    return `Firm ${firmId.slice(-8)}`; // Show last 8 characters of firm ID
  }

  /**
   * Cache for firm names to avoid repeated API calls
   */
  private static firmNameCache = new Map<string, { name: string; timestamp: number }>();
  private static CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Get firm name with caching
   */
  static async getFirmNameCached(firmId: string | undefined): Promise<string> {
    if (!firmId) {
      return 'DRCR';
    }

    // Check cache first
    const cached = this.firmNameCache.get(firmId);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.name;
    }

    // Fetch from API
    const firmName = await this.getFirmName(firmId);

    // Cache the result
    this.firmNameCache.set(firmId, {
      name: firmName,
      timestamp: Date.now()
    });

    return firmName;
  }

  /**
   * Clear the firm name cache (useful when switching firms)
   */
  static clearCache(): void {
    this.firmNameCache.clear();
  }
}
