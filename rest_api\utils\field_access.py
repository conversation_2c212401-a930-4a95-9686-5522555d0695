from typing import Any, Dict, Optional

# Hard-coded aliases for known variations – extend as needed during MVP
FIELD_ALIASES: Dict[str, str] = {
    # Account codes
    "AccountCode": "account_code",
    "accountCode": "account_code",

    # Date fields
    "journalDate": "journal_date",
    "Date": "date_issued",        # Xero's main date field maps to date_issued for transactions
    "DateString": "date_issued",  # Alternative Xero date format
    "DueDate": "date_due",        # Xero due date field
    "DueDateString": "date_due",  # Alternative Xero due date format
    "UpdatedDateUTC": "updated_date_utc",

    # Line item arrays
    "JournalLines": "lines",
    "journal_lines": "lines",
    "LineItems": "line_items",

    # Amount fields
    "LineAmount": "line_amount",
    "Total": "total_amount",
}


def get_field(data: Dict[str, Any], canonical_name: str, default: Any = None) -> Any:
    """Return a value from *data* using the canonical field name.

    Looks for the canonical key first, then any known aliases.  Falls back to
    *default* when the field is missing.
    """
    if canonical_name in data:
        return data[canonical_name]

    # Look for any alias that maps to our canonical key
    for alias, canonical in FIELD_ALIASES.items():
        if canonical == canonical_name and alias in data:
            return data[alias]

    return default


def get_account_code(line_data: Dict[str, Any]) -> Optional[str]:
    """Convenience wrapper for accessing an account code on a line item."""
    return get_field(line_data, "account_code")


def get_journal_lines(journal_data: Dict[str, Any]) -> list:
    """Returns the list of journal lines using canonical naming."""
    return get_field(journal_data, "lines", [])


def get_date_issued(data: Dict[str, Any]) -> Any:
    """Convenience wrapper for accessing date_issued field with alias resolution."""
    return get_field(data, "date_issued")


def get_date_due(data: Dict[str, Any]) -> Any:
    """Convenience wrapper for accessing date_due field with alias resolution."""
    return get_field(data, "date_due")