"""
Sync Routes - Handle long-running sync operations
Extracted heavy processing from Cloud Functions to leverage Cloud Run's longer timeouts
"""
from fastapi import APIRouter, Depends, HTTPException, status, Path, BackgroundTasks, Body, Request
from typing import Dict, Any, List, Optional
from google.cloud import firestore
import logging
import uuid
import asyncio
import time
from datetime import datetime, timezone
import re

from ..core.firebase_auth import get_current_user, get_firm_user_with_client_access, AuthUser
from ..dependencies import get_db
from ..utils.audit import create_audit_log_entry

# Import heavy processing modules at top to prevent blocking during execution
from drcr_shared_logic.clients.xero_client import XeroApiClient
from ..services.llm_processing_service import (
    fetch_and_process_attachments,
    perform_combined_prepayment_analysis
)
from ..services.bills_processing_service import create_amortization_schedules, build_transaction_document
from ..services.sync_helpers import _update_firestore_sync_timestamp

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Sync Operations"])


def parse_xero_date(date_string: str) -> Optional[str]:
    """Parse Xero date format /Date(timestamp)/ to ISO format"""
    if not date_string:
        return None
    
    try:
        # Handle Xero's /Date(timestamp)/ format
        if isinstance(date_string, str) and date_string.startswith("/Date("):
            ts_match = re.search(r'(\d+)', date_string)
            if ts_match:
                timestamp_ms = int(ts_match.group(1))
                dt = datetime.fromtimestamp(timestamp_ms / 1000, tz=timezone.utc)
                return dt.isoformat()
        
        # Return as-is if already in ISO format or other recognized format
        return date_string
    except Exception as e:
        logger.warning(f"Failed to parse Xero date '{date_string}': {e}")
        return None


@router.get("/health")
async def sync_health_check():
    """Health check endpoint for Cloud Run during long-running sync operations"""
    return {"status": "healthy", "service": "sync_operations"}


@router.post("/process-heavy/{entity_id}")
async def process_heavy_sync(
    entity_id: str = Path(...),
    background_tasks: BackgroundTasks = None,
    sync_data: Dict[str, Any] = Body(...),
    request: Request = None,
    db = Depends(get_db)
):
    """
    Start heavy sync processing for an entity (OCR, LLM analysis, amortization schedules)
    This endpoint handles the processing that was too slow for Cloud Functions
    """
    try:
        # Verify entity exists and user has access
        entity_ref = db.collection("ENTITIES").document(entity_id)
        entity_doc = await entity_ref.get()

        if not entity_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entity not found"
            )

        entity_data = entity_doc.to_dict()
        client_id = entity_data.get("client_id")
        base_currency = entity_data.get("base_currency", "USD")

        if not client_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Entity missing client_id"
            )

        # For internal service calls, verify this is from Cloud Function
        source = request.headers.get("X-Sync-Source") if request else None
        if source != "cloud-function":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="This endpoint is for internal service calls only"
            )

        # Extract sync parameters
        sync_job_id = sync_data.get("sync_job_id", str(uuid.uuid4()))
        bills_to_process = sync_data.get("bills_to_process", [])
        entity_settings = sync_data.get("entity_settings", {})
        run_prepayment_detector = bool(sync_data.get("run_prepayment_detector", True))

        # Create sync job record for progress tracking
        sync_job_ref = db.collection("SYNC_JOBS").document(sync_job_id)
        await sync_job_ref.set({
            "sync_job_id": sync_job_id,
            "entity_id": entity_id,
            "client_id": client_id,
            "stage": "heavy_processing",
            "status": "processing",
            "progress_percent": 0,
            "total_bills": len(bills_to_process),
            "processed_bills": 0,
            "failed_bills": 0,
            "started_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP,
            "started_by": "cloud-function"
        })

        # Start DETACHED background processing (not tied to HTTP request lifecycle)
        # This prevents the 300-second request timeout from killing the background task
        asyncio.create_task(heavy_sync_processor(
            db=db,
            entity_id=entity_id,
            client_id=client_id,
            sync_job_id=sync_job_id,
            bills_to_process=bills_to_process,
            entity_settings=entity_settings,
            base_currency=base_currency,
            user_id="cloud-function",
            run_prepayment_detector=run_prepayment_detector
        ))
        
        # Log that we're returning immediately while detached task runs independently
        logger.info(f"HTTP request returning immediately for sync job {sync_job_id} - DETACHED background processing will continue independently")

        # Create audit log entry
        await create_audit_log_entry(
            db=db,
            event_category="SYNC",
            event_type="HEAVY_PROCESSING_STARTED",
            client_id=client_id,
            entity_id=entity_id,
            status="SUCCESS",
            details={
                "sync_job_id": sync_job_id,
                "bills_count": len(bills_to_process),
                "processing_type": "heavy_sync"
            },
            user_id="cloud-function"
        )

        return {
            "message": "Heavy sync processing started",
            "sync_job_id": sync_job_id,
            "status": "processing",
            "estimated_duration_minutes": max(1, len(bills_to_process) * 0.5),  # Rough estimate
            "progress_endpoint": f"/sync/status/{sync_job_id}"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start heavy sync processing for entity {entity_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start heavy sync processing"
        )


@router.get("/status/{sync_job_id}")
async def get_sync_status(
    sync_job_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Get sync job status and progress"""
    try:
        sync_job_ref = db.collection("SYNC_JOBS").document(sync_job_id)
        sync_job_doc = await sync_job_ref.get()

        if not sync_job_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Sync job not found"
            )

        sync_job_data = sync_job_doc.to_dict()
        client_id = sync_job_data.get("client_id")

        # Check client access
        await get_firm_user_with_client_access(client_id, current_user)

        response_data = {
            "sync_job_id": sync_job_id,
            "status": sync_job_data.get("status"),
            "stage": sync_job_data.get("stage"),
            "progress_percent": sync_job_data.get("progress_percent", 0),
            "total_bills": sync_job_data.get("total_bills", 0),
            "processed_bills": sync_job_data.get("processed_bills", 0),
            "failed_bills": sync_job_data.get("failed_bills", 0),
            "started_at": sync_job_data.get("started_at"),
            "completed_at": sync_job_data.get("completed_at"),
            "error_details": sync_job_data.get("error_details"),
            "estimated_completion": sync_job_data.get("estimated_completion")
        }
        
        # Include detector fields if they exist
        if "detector_detections" in sync_job_data:
            response_data["detector_detections"] = sync_job_data.get("detector_detections", 0)
        if "detector_completed_at" in sync_job_data:
            response_data["detector_completed_at"] = sync_job_data.get("detector_completed_at")
        if "detector_error" in sync_job_data:
            response_data["detector_error"] = sync_job_data.get("detector_error")
            
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get sync status for job {sync_job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get sync status"
        )


@router.get("/entity/{entity_id}/jobs")
async def get_entity_sync_jobs(
    entity_id: str = Path(...),
    limit: int = 10,
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Get recent sync jobs for an entity"""
    try:
        # Verify entity exists and user has access
        entity_ref = db.collection("ENTITIES").document(entity_id)
        entity_doc = await entity_ref.get()

        if not entity_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entity not found"
            )

        entity_data = entity_doc.to_dict()
        client_id = entity_data.get("client_id")

        # Check client access
        await get_firm_user_with_client_access(client_id, current_user)

        # Query sync jobs for this entity
        sync_jobs_query = db.collection("SYNC_JOBS").where(
            filter=firestore.FieldFilter("entity_id", "==", entity_id)
        ).order_by("started_at", direction=firestore.Query.DESCENDING).limit(limit)

        sync_jobs = []
        async for job_doc in sync_jobs_query.stream():
            job_data = job_doc.to_dict()
            sync_jobs.append({
                "sync_job_id": job_doc.id,
                "status": job_data.get("status"),
                "stage": job_data.get("stage"),
                "progress_percent": job_data.get("progress_percent", 0),
                "total_bills": job_data.get("total_bills", 0),
                "processed_bills": job_data.get("processed_bills", 0),
                "failed_bills": job_data.get("failed_bills", 0),
                "started_at": job_data.get("started_at"),
                "completed_at": job_data.get("completed_at"),
                "started_by": job_data.get("started_by")
            })

        return {
            "entity_id": entity_id,
            "sync_jobs": sync_jobs,
            "total_jobs": len(sync_jobs)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get sync jobs for entity {entity_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get sync jobs"
        )


@router.get("/entity/{entity_id}/processing-summary")
async def get_entity_processing_summary(
    entity_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Get summary of bill processing status for an entity"""
    try:
        # Verify entity exists and user has access
        entity_ref = db.collection("ENTITIES").document(entity_id)
        entity_doc = await entity_ref.get()

        if not entity_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entity not found"
            )

        entity_data = entity_doc.to_dict()
        client_id = entity_data.get("client_id")

        # Check client access
        await get_firm_user_with_client_access(client_id, current_user)

        # Count bills by processing status
        transactions_query = db.collection("TRANSACTIONS").where(
            filter=firestore.FieldFilter("entity_id", "==", entity_id)
        ).where(
            filter=firestore.FieldFilter("document_type", "==", "BILL")
        )

        status_counts = {
            "metadata_loaded": 0,
            "completed": 0,
            "failed": 0,
            "total": 0
        }

        prepayment_counts = {
            "with_prepayments": 0,
            "without_prepayments": 0,
            "amortization_schedules": 0
        }

        async for transaction_doc in transactions_query.stream():
            transaction_data = transaction_doc.to_dict()
            status_counts["total"] += 1
            
            processing_status = transaction_data.get("processing_status", "unknown")
            if processing_status in status_counts:
                status_counts[processing_status] += 1
            
            has_prepayments = transaction_data.get("has_potential_prepayments", False)
            if has_prepayments:
                prepayment_counts["with_prepayments"] += 1
            else:
                prepayment_counts["without_prepayments"] += 1
            
            if transaction_data.get("has_amortization_schedules", False):
                prepayment_counts["amortization_schedules"] += 1

        return {
            "entity_id": entity_id,
            "processing_summary": status_counts,
            "prepayment_summary": prepayment_counts,
            "last_updated": datetime.now(timezone.utc).isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get processing summary for entity {entity_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get processing summary"
        )


async def _process_prepayment_transaction(
    db: firestore.AsyncClient,
    xero_client,
    transaction_ref,
    transaction_data: Dict[str, Any],
    bill: Dict[str, Any],
    bill_id: str,
    entity_settings: Dict[str, Any],
    entity_id: str,
    client_id: str,
    gl_prepayment_lines: List[Dict[str, Any]],
    base_currency: str
) -> tuple[int, int]:
    """
    Process a single transaction with prepayment analysis and schedule generation.
    Copied from working Cloud Function bills_processor.py line 401-481.
    Returns: (saved_count, processed_prepayments_count)
    """
    processed_prepayments_count = 0
    
    try:
        # Phase 1: Analysis
        processed_attachments = []
        if entity_settings.get("enable_llm_prepayment_detection", True):
            processed_attachments = await fetch_and_process_attachments(
                xero_client=xero_client,
                transaction_id=bill_id,
                transaction_data=bill,
                entity_settings=entity_settings,
                entity_id=entity_id,
                db=db,
                file_name_for_logging=f"bill_{bill_id}",
                enable_concurrent_processing=entity_settings.get("enable_concurrent_attachment_processing", True)
            )
        
        combined_analysis = await perform_combined_prepayment_analysis(
            bill, gl_prepayment_lines, processed_attachments, entity_settings
        )
        logger.info(f"Completed prepayment analysis for bill {bill_id}: {combined_analysis['recommended_action']}")
        
        # Phase 2: Schedule creation (only if analysis succeeded)
        if combined_analysis["recommended_action"] == "create_amortization_schedule":
            processed_prepayments_count = await create_amortization_schedules(
                db=db,
                bill=bill,
                bill_id=bill_id,
                combined_analysis=combined_analysis,
                entity_settings=entity_settings,
                entity_id=entity_id,
                client_id=client_id,
                gl_prepayment_lines=gl_prepayment_lines,
                base_currency=base_currency
            )
        
        # Build transaction document using the same structure as Cloud Function
        # Parse Xero date formats properly to prevent parsing errors
        standardized_bill = {
            "date_issued": parse_xero_date(bill.get("Date")),
            "date_due": parse_xero_date(bill.get("DueDate")),
            "metadata": {
                "xero_url": f"https://go.xero.com/AccountsPayable/View.aspx?InvoiceID={bill_id}"
            }
        }
        
        # Determine if has potential prepayments (copied from Cloud Function logic)
        prepayment_asset_codes = entity_settings.get("prepayment_asset_account_codes", [])
        gl_prepayment_lines_check = [li for li in bill.get("LineItems", []) if li.get("AccountCode") in prepayment_asset_codes]
        has_potential_prepayments = (len(gl_prepayment_lines_check) > 0 or entity_settings.get("enable_llm_prepayment_detection", True))
        
        # Build base transaction document like Cloud Function
        transaction_doc = await build_transaction_document(
            bill=bill,
            standardized_bill=standardized_bill,
            bill_id=bill_id,
            entity_id=entity_id,
            client_id=client_id,
            has_potential_prepayments=has_potential_prepayments,
            metadata_only=False
        )
        
        # Update with analysis results (overwrite the pending analysis)
        transaction_doc.update({
            "prepayment_analysis": combined_analysis,
            "has_amortization_schedules": combined_analysis["recommended_action"] == "create_amortization_schedule",
            "attachments_processed": len(processed_attachments),
            "llm_processing_completed": combined_analysis["llm_based_analysis_completed"],
            "processing_status": "completed",
            "heavy_processing_completed_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        })
        
    except Exception as e_analysis:
        logger.error(f"Error in prepayment analysis for bill {bill_id}: {e_analysis}", exc_info=True)
        # Create fallback analysis like Cloud Function
        fallback_analysis = {
            "gl_based_analysis_completed": True,
            "llm_based_analysis_completed": False,
            "has_prepayment_line_items": False,
            "detection_methods": [],
            "prepayment_line_items": [],
            "llm_detections": [],
            "confidence_score": 0.0,
            "recommended_action": "analysis_failed",
            "error": str(e_analysis)
        }
        # Build consistent fallback transaction document (same structure as success path)
        standardized_bill = {
            "date_issued": parse_xero_date(bill.get("Date")),
            "date_due": parse_xero_date(bill.get("DueDate")),
            "metadata": {
                "xero_url": f"https://go.xero.com/AccountsPayable/View.aspx?InvoiceID={bill_id}"
            }
        }
        
        prepayment_asset_codes = entity_settings.get("prepayment_asset_account_codes", [])
        gl_prepayment_lines_check = [li for li in bill.get("LineItems", []) if li.get("AccountCode") in prepayment_asset_codes]
        has_potential_prepayments = (len(gl_prepayment_lines_check) > 0 or entity_settings.get("enable_llm_prepayment_detection", True))
        
        transaction_doc = await build_transaction_document(
            bill=bill,
            standardized_bill=standardized_bill,
            bill_id=bill_id,
            entity_id=entity_id,
            client_id=client_id,
            has_potential_prepayments=has_potential_prepayments,
            metadata_only=False
        )
        
        # Update with failure-specific fields (ensure same structure as success)
        transaction_doc.update({
            "prepayment_analysis": fallback_analysis,
            "has_amortization_schedules": False,
            "attachments_processed": 0,
            "llm_processing_completed": False,
            "processing_status": "failed",
            "processing_error": str(e_analysis),
            "heavy_processing_completed_at": firestore.SERVER_TIMESTAMP,  # Include this field for consistency
            "updated_at": firestore.SERVER_TIMESTAMP
        })
    
    # Single atomic write for prepayment transaction like Cloud Function
    try:
        await transaction_ref.set(transaction_doc, merge=True)
        logger.info(f"Successfully wrote prepayment transaction {bill_id} with complete data")
        return 1, processed_prepayments_count
    except Exception as e_write:
        logger.error(f"Failed to write prepayment transaction {bill_id}: {e_write}", exc_info=True)
        return 0, 0


async def heavy_sync_processor(
    db: firestore.AsyncClient,
    entity_id: str,
    client_id: str,
    sync_job_id: str,
    bills_to_process: List[Dict[str, Any]],
    entity_settings: Dict[str, Any],
    base_currency: str,
    user_id: str,
    run_prepayment_detector: bool = True
):
    """
    Background task to process bills with heavy OCR/LLM analysis
    This runs independently of the HTTP request lifecycle
    """
    logger.info(f"Starting heavy sync processing for entity {entity_id}, job {sync_job_id} with {len(bills_to_process)} bills")
    
    # Mark sync job as processing immediately to prevent timeout issues
    try:
        sync_job_ref = db.collection("SYNC_JOBS").document(sync_job_id)
        await sync_job_ref.update({
            "status": "processing",
            "last_heartbeat": firestore.SERVER_TIMESTAMP,
            "background_task_started": True,
            "updated_at": firestore.SERVER_TIMESTAMP
        })
        logger.info(f"Background task started successfully for sync job {sync_job_id}")
    except Exception as e_heartbeat:
        logger.error(f"Failed to update sync job heartbeat: {e_heartbeat}")
    
    try:

        # Initialize Xero client ONCE and reuse for all bills (critical performance fix)
        xero_client = await XeroApiClient.create(
            platform_org_id=entity_id,
            tenant_id=client_id
        )
        logger.info(f"Initialized XeroApiClient for entity {entity_id} - will reuse for all {len(bills_to_process)} bills")

        processed_count = 0
        failed_count = 0
        processed_prepayments_count = 0  # Fix missing variable initialization
        total_bills = len(bills_to_process)
        detector_detections = 0
        bills_with_existing_releases = set()
        skipped_bills_with_releases = 0

        # Run prepayment release detection FIRST to find existing release journals
        try:
            logger.info(f"Starting prepayment release detection for entity {entity_id}")
            
            # Import the service (local import to avoid circular dependencies)
            from ..services.prepayment_release_detector_service import PrepaymentReleaseDetectorService
            
            detector_service = PrepaymentReleaseDetectorService()
            detector_detections = await detector_service.run_for_entity(entity_id, db)
            
            # Update sync job with detector results
            sync_job_ref = db.collection("SYNC_JOBS").document(sync_job_id)
            await sync_job_ref.update({
                "detector_detections": detector_detections,
                "detector_completed_at": firestore.SERVER_TIMESTAMP,
                "bills_to_skip_with_releases": len(bills_with_existing_releases),
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            
            # Query for bills that already have release journals
            if detector_detections > 0:
                releases_query = db.collection("PREPAYMENT_RELEASES_DETECTED").where(
                    filter=firestore.FieldFilter("entity_id", "==", entity_id)
                )
                
                async for release_doc in releases_query.stream():
                    release_data = release_doc.to_dict()
                    linked_transaction_id = release_data.get("linked_transaction_id")
                    if linked_transaction_id:
                        bills_with_existing_releases.add(linked_transaction_id)
                
                logger.info(f"Found {len(bills_with_existing_releases)} bills with existing release journals")
            
            logger.info(f"Prepayment detector created {detector_detections} detections for entity {entity_id}")
            
        except Exception as e_detector:
            logger.error(f"Prepayment detection failed for entity {entity_id}: {e_detector}", exc_info=True)
            
            # Create specific detector failure audit log
            try:
                await create_audit_log_entry(
                    db=db,
                    event_category="SYNC",
                    event_type="DETECTOR_FAILED",
                    client_id=client_id,
                    entity_id=entity_id,
                    status="FAILURE",
                    details={
                        "sync_job_id": sync_job_id,
                        "detector_error": str(e_detector),
                        "error_type": type(e_detector).__name__
                    },
                    user_id=user_id
                )
            except Exception as e_audit:
                logger.error(f"Failed to create detector failure audit log: {e_audit}")

        # Process bills sequentially like Cloud Function (working pattern)
        if len(bills_with_existing_releases) > 0:
            logger.info(f"[PERF] Processing {total_bills} bills sequentially ({len(bills_with_existing_releases)} will be skipped due to existing releases)")
        else:
            logger.info(f"[PERF] Processing {total_bills} bills sequentially to respect Xero rate limits")
        
        for i, bill_data in enumerate(bills_to_process):
            bill_id = bill_data.get("transaction_id")
            
            try:
                bill_start_time = time.time()
                logger.info(f"[PERF] Starting bill {bill_id} ({i+1}/{total_bills}) for heavy analysis")
                
                # Update progress with heartbeat - fix off-by-one error so it reaches 100%
                progress_percent = int(((i + 1) / total_bills) * 100) if total_bills > 0 else 0
                await _update_sync_progress(
                    db, sync_job_id, progress_percent, processed_count, failed_count, heartbeat=True
                )

                # Skip if already processed (safety check)
                transaction_ref = db.collection("TRANSACTIONS").document(bill_id)
                transaction_doc = await transaction_ref.get()
                
                if not transaction_doc.exists:
                    logger.warning(f"Bill {bill_id} not found in TRANSACTIONS collection, skipping")
                    failed_count += 1
                    continue

                transaction_data = transaction_doc.to_dict()
                
                # Check if already processed
                processing_status = transaction_data.get("processing_status")
                if processing_status == "completed":
                    logger.info(f"Bill {bill_id} already processed, skipping")
                    processed_count += 1
                    continue
                
                # Skip bills that already have release journals (detected by prepayment detector)
                if bill_id in bills_with_existing_releases:
                    logger.info(f"Skipping bill {bill_id} - already has release journal detected")
                    await transaction_ref.update({
                        "processing_status": "skipped",
                        "skip_reason": "existing_release_journal_detected",
                        "updated_at": firestore.SERVER_TIMESTAMP
                    })
                    processed_count += 1
                    skipped_bills_with_releases += 1
                    continue

                # Extract bill data from Firestore (saved during metadata stage)
                bill = transaction_data.get("raw_xero_data", {})
                if not bill:
                    logger.warning(f"No raw Xero data found for bill {bill_id}, skipping heavy processing")
                    failed_count += 1
                    continue

                # Apply filtering logic consistent with Cloud Function processing
                excluded_account_codes = set(entity_settings.get("excluded_pnl_account_codes", []))
                scanning_threshold = entity_settings.get("scanning_amount_threshold", 100.0)
                bill_total = float(bill.get("Total", 0))
                
                # Check if bill should be filtered out (only if ALL line items are excluded)
                if excluded_account_codes:
                    bill_line_items = bill.get("LineItems", [])
                    if bill_line_items:  # Only check if there are line items
                        non_excluded_line_items = [li for li in bill_line_items if li.get("AccountCode") not in excluded_account_codes]
                        if not non_excluded_line_items:
                            # All line items use excluded account codes
                            excluded_codes_used = [li.get("AccountCode") for li in bill_line_items]
                            logger.info(f"Skipping bill {bill_id} - all line items use excluded account codes: {excluded_codes_used}")
                            await transaction_ref.update({
                                "processing_status": "skipped",
                                "skip_reason": "all_line_items_excluded",
                                "excluded_codes": excluded_codes_used,
                                "updated_at": firestore.SERVER_TIMESTAMP
                            })
                            processed_count += 1
                            continue
                
                # Check scanning threshold
                if bill_total < scanning_threshold:
                    logger.info(f"Skipping bill {bill_id} - total {bill_total} below scanning threshold {scanning_threshold}")
                    await transaction_ref.update({
                        "processing_status": "skipped",
                        "skip_reason": "below_scanning_threshold",
                        "updated_at": firestore.SERVER_TIMESTAMP
                    })
                    processed_count += 1
                    continue

                # Determine if this bill has potential prepayments
                prepayment_asset_codes = entity_settings.get("prepayment_asset_account_codes", [])
                gl_prepayment_lines = [
                    li for li in bill.get("LineItems", []) 
                    if li.get("AccountCode") in prepayment_asset_codes
                ]
                has_potential_prepayments = (
                    len(gl_prepayment_lines) > 0 or 
                    entity_settings.get("enable_llm_prepayment_detection", True)
                )

                if not has_potential_prepayments:
                    # Mark as completed without heavy processing
                    await transaction_ref.update({
                        "processing_status": "completed",
                        "updated_at": firestore.SERVER_TIMESTAMP,
                        "processing_note": "No prepayments detected - skipped heavy analysis"
                    })
                    processed_count += 1
                    continue

                # Heavy processing using exact Cloud Function pattern
                saved_count, prep_count = await _process_prepayment_transaction(
                    db=db,
                    xero_client=xero_client,
                    transaction_ref=transaction_ref,
                    transaction_data=transaction_data,
                    bill=bill,
                    bill_id=bill_id,
                    entity_settings=entity_settings,
                    entity_id=entity_id,
                    client_id=client_id,
                    gl_prepayment_lines=gl_prepayment_lines,
                    base_currency=base_currency
                )
                
                # Use actual saved_count, not hardcoded increment
                processed_count += saved_count
                processed_prepayments_count += prep_count
                bill_duration = time.time() - bill_start_time
                logger.info(f"[PERF] Completed heavy processing for bill {bill_id} in {bill_duration:.2f}s total")
                
                # Optimized delay between bills - balance throughput vs rate limits
                await asyncio.sleep(0.3)  # Reduced from 1.0s for better performance

            except Exception as e_bill:
                logger.error(f"Error processing bill {bill_id}: {e_bill}", exc_info=True)
                failed_count += 1
                
                # Mark bill as failed
                try:
                    await transaction_ref.update({
                        "processing_status": "failed",
                        "processing_error": str(e_bill),
                        "updated_at": firestore.SERVER_TIMESTAMP
                    })
                except Exception as e_update:
                    logger.error(f"Failed to update bill {bill_id} status to failed: {e_update}")


        # Final progress update with skipped count
        await _update_sync_progress(
            db, sync_job_id, 100, processed_count, failed_count, completed=True, skipped_count=skipped_bills_with_releases
        )
        
        # Update sync job with final filtering statistics
        if run_prepayment_detector:
            try:
                sync_job_ref = db.collection("SYNC_JOBS").document(sync_job_id)
                await sync_job_ref.update({
                    "bills_skipped_with_releases": skipped_bills_with_releases,
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
            except Exception as e_sync_update:
                logger.warning(f"Failed to update sync job with skipped count: {e_sync_update}")

        # Create completion audit log
        audit_details = {
            "sync_job_id": sync_job_id,
            "total_bills": total_bills,
            "processed_bills": processed_count,
            "failed_bills": failed_count,
            "processing_type": "heavy_sync"
        }
        
        # Include detector results if detector was run
        if run_prepayment_detector:
            audit_details["detector_detections"] = detector_detections
            audit_details["detector_enabled"] = True
            audit_details["skipped_bills_with_releases"] = skipped_bills_with_releases
        
        await create_audit_log_entry(
            db=db,
            event_category="SYNC",
            event_type="HEAVY_PROCESSING_COMPLETED",
            client_id=client_id,
            entity_id=entity_id,
            status="SUCCESS" if failed_count == 0 else "PARTIAL_SUCCESS",
            details=audit_details,
            user_id=user_id
        )

        # Log comprehensive summary including detector filtering
        summary_msg = f"Heavy sync processing completed for entity {entity_id}: {processed_count} processed, {failed_count} failed"
        if run_prepayment_detector:
            summary_msg += f", {detector_detections} release journals detected, {skipped_bills_with_releases} bills skipped (existing releases)"
        logger.info(summary_msg)

    except Exception as e_main:
        logger.error(f"Critical error in heavy sync processing for entity {entity_id}: {e_main}", exc_info=True)
        
        # Mark sync job as failed
        try:
            sync_job_ref = db.collection("SYNC_JOBS").document(sync_job_id)
            await sync_job_ref.update({
                "status": "failed",
                "error_details": str(e_main),
                "completed_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP
            })

            # Create failure audit log
            await create_audit_log_entry(
                db=db,
                event_category="SYNC",
                event_type="HEAVY_PROCESSING_FAILED",
                client_id=client_id,
                entity_id=entity_id,
                status="FAILURE",
                details={
                    "sync_job_id": sync_job_id,
                    "error": str(e_main),
                    "processing_type": "heavy_sync"
                },
                user_id=user_id
            )
        except Exception as e_cleanup:
            logger.error(f"Failed to update sync job status after failure: {e_cleanup}")


async def _update_sync_progress(
    db: firestore.AsyncClient,
    sync_job_id: str,
    progress_percent: int,
    processed_count: int,
    failed_count: int,
    completed: bool = False,
    heartbeat: bool = False,
    skipped_count: int = 0
):
    """Update sync job progress in Firestore with optional heartbeat"""
    try:
        sync_job_ref = db.collection("SYNC_JOBS").document(sync_job_id)
        update_data = {
            "progress_percent": progress_percent,
            "processed_bills": processed_count,
            "failed_bills": failed_count,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        
        # Include skipped count if provided
        if skipped_count > 0:
            update_data["bills_skipped_with_releases"] = skipped_count
        
        if heartbeat:
            update_data["last_heartbeat"] = firestore.SERVER_TIMESTAMP
            update_data["background_task_active"] = True
        
        if completed:
            update_data.update({
                "status": "completed" if failed_count == 0 else "completed_with_errors",
                "completed_at": firestore.SERVER_TIMESTAMP,
                "background_task_active": False
            })
        
        await sync_job_ref.update(update_data)
        
    except Exception as e:
        logger.warning(f"Failed to update sync progress for job {sync_job_id}: {e}")