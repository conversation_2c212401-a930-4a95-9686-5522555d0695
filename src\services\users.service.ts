import { AuthService } from './auth.service';
import { api } from '@/lib/api';

export interface User {
  user_id: string;
  email: string;
  display_name?: string;
  role: 'firm_admin' | 'firm_staff';
  status: 'active' | 'inactive' | 'invited' | 'suspended';
  assigned_client_ids: string[];
  created_at?: string;
  updated_at?: string;
  last_login?: string;
  last_sign_in?: string;
  email_verified: boolean;
  document_id: string;
}

export interface UserDetails extends User {
  assigned_clients: Array<{
    client_id: string;
    name: string;
    status: string;
  }>;
  firebase_data: {
    email_verified: boolean;
    disabled: boolean;
    creation_time?: string;
    last_sign_in_time?: string;
    last_refresh_time?: string;
    provider_data?: Array<{
      provider_id: string;
      uid: string;
      email: string;
    }>;
  };
}

export interface UsersListResponse {
  users: User[];
  total_count: number;
  firm_id: string;
}

export interface UpdateUserRoleRequest {
  role: 'firm_admin' | 'firm_staff';
  assigned_client_ids: string[];
}

export interface UpdateUserStatusRequest {
  status: 'active' | 'inactive' | 'invited' | 'suspended';
}

export interface InviteUserRequest {
  email: string;
  role: 'firm_admin' | 'firm_staff';
  display_name?: string;
  assigned_client_ids?: string[];
}

export class UsersService {
  /**
   * Get authorization headers with Firebase token
   */
  private static async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await AuthService.getCurrentUserToken();
    if (!token) {
      throw new Error('No authentication token available');
    }

    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  }

  /**
   * Handle API errors and convert them to user-friendly messages
   */
  private static handleApiError(error: any): Error {
    if (error.response?.data?.detail) {
      return new Error(error.response.data.detail);
    }
    if (error.message) {
      return new Error(error.message);
    }
    return new Error('An unexpected error occurred');
  }

  /**
   * List all users in the current firm
   */
  static async listUsers(): Promise<UsersListResponse> {
    try {
      return await api.get<UsersListResponse>('/auth/users');
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Get detailed information about a specific user
   */
  static async getUserDetails(userId: string): Promise<UserDetails> {
    try {
      return await api.get<UserDetails>(`/auth/users/${userId}`);
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Update a user's role and client assignments
   */
  static async updateUserRole(userId: string, data: UpdateUserRoleRequest): Promise<void> {
    try {
      await api.put(`/auth/users/${userId}/role`, data);
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Update a user's status (activate/deactivate)
   */
  static async updateUserStatus(userId: string, data: UpdateUserStatusRequest): Promise<void> {
    try {
      await api.put(`/auth/users/${userId}/status`, data);
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Remove a user from the firm
   */
  static async removeUser(userId: string): Promise<void> {
    try {
      await api.delete(`/auth/users/${userId}`);
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Invite a new user to the firm
   */
  static async inviteUser(data: InviteUserRequest): Promise<void> {
    try {
      await api.post('/auth/invite-user', data);
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Resend invitation email to a user with 'invited' status
   */
  static async resendInvite(userId: string): Promise<void> {
    try {
      await api.post(`/auth/users/${userId}/resend-invite`);
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }
} 