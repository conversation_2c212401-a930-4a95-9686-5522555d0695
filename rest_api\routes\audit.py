"""
Audit Log API Routes - REST endpoints for audit log management and querying
"""
from fastapi import APIRouter, Depends, HTTPException, status, Path, Query, Body
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone, date, timedelta
import logging

from ..core.firebase_auth import get_current_user, get_firm_admin, AuthUser
from ..dependencies import get_db
from ..models.audit import AuditEventCategory, AuditEventStatus
from ..schemas.audit_schemas import (
    AuditLogListResponse, AuditLogDetailResponse, AuditLogStatsResponse,
    AuditEventTypesResponse, AuditLogExportRequest, AuditLogExportResponse,
    AuditBulkActionRequest, AuditBulkActionResponse
)
from ..services.audit_service import AuditService

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Audit Logs"])


def get_audit_service(db=Depends(get_db)) -> AuditService:
    """Dependency to get audit service instance"""
    return AuditService(db)


@router.get("/", response_model=AuditLogListResponse)
async def list_audit_logs(
    page: int = Query(1, description="Page number", ge=1),
    limit: int = Query(50, description="Items per page", ge=1, le=200),
    sort_by: str = Query("timestamp", description="Field to sort by"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="Sort order (desc for newest first)"),
    client_id: Optional[str] = Query(None, description="Filter by client ID"),
    entity_id: Optional[str] = Query(None, description="Filter by entity ID"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    firm_id: Optional[str] = Query(None, description="Filter by firm ID"),
    event_category: Optional[AuditEventCategory] = Query(None, description="Filter by event category"),
    event_type: Optional[str] = Query(None, description="Filter by event type (partial match)"),
    status: Optional[AuditEventStatus] = Query(None, description="Filter by event status"),
    date_from: Optional[date] = Query(None, description="Filter events from this date"),
    date_to: Optional[date] = Query(None, description="Filter events to this date"),
    min_duration_ms: Optional[int] = Query(None, ge=0, description="Filter events with minimum duration"),
    max_duration_ms: Optional[int] = Query(None, ge=0, description="Filter events with maximum duration"),
    source_ip: Optional[str] = Query(None, description="Filter by source IP address"),
    error_only: Optional[bool] = Query(None, description="Show only error events"),
    has_details: Optional[bool] = Query(None, description="Filter by presence of details"),
    search_text: Optional[str] = Query(None, description="Search in event type, details, or error message"),
    current_user: AuthUser = Depends(get_current_user),
    audit_service: AuditService = Depends(get_audit_service)
):
    """List audit logs with pagination and filtering"""
    try:
        # Get user's accessible client IDs
        user_client_ids = await audit_service.get_user_client_ids(current_user, client_id)
        
        if not user_client_ids and client_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User does not have access to the specified client"
            )
        
        # Apply firm filter for non-admin users
        if current_user.role != "firm_admin" and not firm_id:
            firm_id = current_user.firm_id
        
        audit_logs, total_count, total_pages = await audit_service.list_audit_logs_paginated(
            client_id=client_id,
            entity_id=entity_id,
            user_id=user_id,
            firm_id=firm_id,
            event_category=event_category,
            event_type=event_type,
            status=status,
            date_from=date_from,
            date_to=date_to,
            min_duration_ms=min_duration_ms,
            max_duration_ms=max_duration_ms,
            source_ip=source_ip,
            error_only=error_only,
            has_details=has_details,
            search_text=search_text,
            page=page,
            limit=limit,
            sort_by=sort_by,
            sort_order=sort_order,
            user_client_ids=user_client_ids if not client_id else None
        )
        
        # Calculate pagination metadata
        oldest_entry = None
        newest_entry = None
        if audit_logs:
            timestamps = [log.timestamp for log in audit_logs if log.timestamp]
            if timestamps:
                oldest_entry = min(timestamps)
                newest_entry = max(timestamps)
        
        pagination_meta = {
            "current_page": page,
            "page_size": limit,
            "total_items": total_count,
            "total_pages": total_pages,
            "has_next": page < total_pages,
            "has_previous": page > 1,
            "oldest_entry": oldest_entry,
            "newest_entry": newest_entry
        }
        
        filters_applied = {
            "client_id": client_id,
            "entity_id": entity_id,
            "user_id": user_id,
            "firm_id": firm_id,
            "event_category": event_category,
            "event_type": event_type,
            "status": status,
            "date_from": date_from,
            "date_to": date_to,
            "min_duration_ms": min_duration_ms,
            "max_duration_ms": max_duration_ms,
            "source_ip": source_ip,
            "error_only": error_only,
            "has_details": has_details,
            "search_text": search_text
        }
        
        return AuditLogListResponse(
            audit_logs=audit_logs,
            pagination=pagination_meta,
            filters_applied=filters_applied
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing audit logs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve audit logs"
        )


@router.get("/{audit_id}", response_model=AuditLogDetailResponse)
async def get_audit_log(
    audit_id: str = Path(..., description="Audit log ID"),
    include_related: bool = Query(True, description="Include related audit entries"),
    current_user: AuthUser = Depends(get_current_user),
    audit_service: AuditService = Depends(get_audit_service)
):
    """Get detailed audit log information by ID"""
    try:
        audit_log = await audit_service.get_audit_log_by_id(
            audit_id=audit_id,
            current_user=current_user,
            include_related=include_related
        )
        
        if not audit_log:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Audit log not found or access denied"
            )
        
        # Get related entries if requested
        related_entries = []
        if include_related and audit_log.request_id:
            # Find other audit logs with the same request_id
            try:
                related_logs, _, _ = await audit_service.list_audit_logs_paginated(
                    # Use request_id in search_text for now
                    search_text=audit_log.request_id,
                    limit=20,
                    user_client_ids=await audit_service.get_user_client_ids(current_user)
                )
                related_entries = [log for log in related_logs if log.audit_id != audit_id]
            except Exception as e:
                logger.warning(f"Error fetching related audit entries: {e}")
        
        return AuditLogDetailResponse(
            audit_log=audit_log,
            related_entries=related_entries if related_entries else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving audit log {audit_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve audit log"
        )


@router.get("/stats/summary", response_model=AuditLogStatsResponse)
async def get_audit_statistics(
    client_id: Optional[str] = Query(None, description="Filter statistics by client ID"),
    entity_id: Optional[str] = Query(None, description="Filter statistics by entity ID"),
    date_from: Optional[date] = Query(None, description="Statistics from this date"),
    date_to: Optional[date] = Query(None, description="Statistics to this date"),
    current_user: AuthUser = Depends(get_current_user),
    audit_service: AuditService = Depends(get_audit_service)
):
    """Get audit log statistics"""
    try:
        # Check access to client if specified
        if client_id:
            user_client_ids = await audit_service.get_user_client_ids(current_user, client_id)
            if client_id not in user_client_ids:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="User does not have access to the specified client"
                )
        
        stats = await audit_service.get_audit_statistics(
            client_id=client_id,
            entity_id=entity_id,
            date_from=date_from,
            date_to=date_to,
            current_user=current_user
        )
        
        time_range = None
        if date_from or date_to:
            time_range = {
                "from": date_from.isoformat() if date_from else None,
                "to": date_to.isoformat() if date_to else None
            }
        
        return AuditLogStatsResponse(
            statistics=stats,
            generated_at=datetime.now(timezone.utc),
            time_range=time_range
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting audit statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve audit statistics"
        )


@router.get("/metadata/event-types", response_model=AuditEventTypesResponse)
async def get_event_types(
    current_user: AuthUser = Depends(get_current_user),
    audit_service: AuditService = Depends(get_audit_service)
):
    """Get available audit event types and categories"""
    try:
        events_by_category = await audit_service.get_available_event_types()
        
        # Flatten event types
        all_event_types = []
        for category_events in events_by_category.values():
            all_event_types.extend(category_events)
        
        return AuditEventTypesResponse(
            event_types=sorted(list(set(all_event_types))),
            event_categories=list(AuditEventCategory),
            event_statuses=list(AuditEventStatus),
            events_by_category=events_by_category
        )
        
    except Exception as e:
        logger.error(f"Error getting event types: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve event types"
        )


@router.post("/export", response_model=AuditLogExportResponse)
async def export_audit_logs(
    request: AuditLogExportRequest = Body(...),
    current_user: AuthUser = Depends(get_firm_admin),  # Only firm admins can export
    audit_service: AuditService = Depends(get_audit_service)
):
    """Export audit logs (firm_admin only)"""
    try:
        # This is a placeholder implementation
        # In a real system, you would:
        # 1. Validate the export request
        # 2. Generate the export file asynchronously
        # 3. Store it in cloud storage
        # 4. Return a download URL
        
        # For now, return a mock response
        export_id = f"export_{int(datetime.now().timestamp())}"
        
        return AuditLogExportResponse(
            export_id=export_id,
            download_url=f"https://storage.googleapis.com/exports/audit_export_{export_id}.{request.format}",
            expires_at=datetime.now(timezone.utc) + timedelta(hours=24),
            record_count=0,  # Would be calculated from actual export
            file_size_bytes=0  # Would be calculated from actual export
        )
        
    except Exception as e:
        logger.error(f"Error exporting audit logs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export audit logs"
        )


@router.post("/bulk-actions", response_model=AuditBulkActionResponse)
async def bulk_audit_actions(
    request: AuditBulkActionRequest = Body(...),
    current_user: AuthUser = Depends(get_firm_admin),  # Only firm admins can perform bulk actions
    audit_service: AuditService = Depends(get_audit_service)
):
    """Perform bulk actions on audit logs (firm_admin only)"""
    try:
        # This is a placeholder implementation
        # In a real system, you would:
        # 1. Validate each audit log ID
        # 2. Check permissions
        # 3. Perform the requested action
        # 4. Return results
        
        processed_count = len(request.audit_ids)
        failed_count = 0
        errors = []
        
        if request.action == "delete":
            action_msg = "Bulk delete completed"
        elif request.action == "archive":
            action_msg = "Bulk archive completed"
        elif request.action == "export":
            action_msg = "Bulk export initiated"
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported action: {request.action}"
            )
        
        return AuditBulkActionResponse(
            message=f"{action_msg} successfully",
            processed_count=processed_count,
            failed_count=failed_count,
            errors=errors if errors else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error performing bulk audit action: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform bulk action"
        )


@router.delete("/cleanup")
async def cleanup_old_audit_logs(
    days_to_keep: int = Query(90, ge=30, le=365, description="Number of days to keep audit logs"),
    dry_run: bool = Query(True, description="Perform a dry run without actually deleting"),
    current_user: AuthUser = Depends(get_firm_admin),  # Only firm admins can cleanup
    audit_service: AuditService = Depends(get_audit_service)
):
    """Cleanup old audit logs (firm_admin only)"""
    try:
        if dry_run:
            # In a real implementation, you would count the logs that would be deleted
            return {
                "message": f"Dry run: Would delete audit logs older than {days_to_keep} days",
                "logs_to_delete": 0,  # Would be calculated
                "dry_run": True
            }
        else:
            deleted_count = await audit_service.delete_old_audit_logs(
                days_to_keep=days_to_keep,
                batch_size=100
            )
            
            return {
                "message": f"Successfully deleted {deleted_count} old audit logs",
                "deleted_count": deleted_count,
                "days_kept": days_to_keep,
                "dry_run": False
            }
        
    except Exception as e:
        logger.error(f"Error cleaning up audit logs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cleanup audit logs"
        )


# Utility endpoints
@router.get("/enums/categories")
async def get_audit_categories():
    """Get available audit event categories"""
    return {
        "categories": [
            {"value": category.value, "label": category.value.replace("_", " ").title()}
            for category in AuditEventCategory
        ]
    }


@router.get("/enums/statuses")
async def get_audit_statuses():
    """Get available audit event statuses"""
    return {
        "statuses": [
            {"value": status_value.value, "label": status_value.value.replace("_", " ").title()}
            for status_value in AuditEventStatus
        ]
    }


# Test endpoints
@router.get("/test")
async def test_audit_basic():
    """Basic test endpoint for audit router"""
    return {"message": "Audit router test endpoint", "status": "ok"}


@router.get("/test-auth")
async def test_audit_auth(
    current_user: AuthUser = Depends(get_current_user)
):
    """Test endpoint with authentication"""
    return {
        "message": "Audit router auth test endpoint",
        "user": current_user.email,
        "firm_id": current_user.firm_id,
        "status": "ok"
    }