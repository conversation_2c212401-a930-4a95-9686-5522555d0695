import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Loader2, 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft,
  Settings,
  AlertCircle,
  TrendingUp,
  DollarSign,
  Filter,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { toast } from 'sonner';
import { ApiClient } from '@/lib/api';

const apiClient = new ApiClient();

// Helper function to generate cutoff key for date filtering
const getCutoffKey = (dateRangeMonths: number): string => {
  const now = new Date();
  const cutoffDate = new Date(now.getFullYear(), now.getMonth() - dateRangeMonths, 1);
  return cutoffDate.toISOString().slice(0, 7); // YYYY-MM format
};

interface BillAggregates {
  aggregates: {
    [month: string]: {
      by_account: {
        [accountCode: string]: {
          bills: number;
          with_attachments: number;
          total_amount: number;
        };
      };
      by_supplier: {
        [supplier: string]: {
          bills: number;
          with_attachments: number;
          total_amount: number;
        };
      };
      total_bills: number;
      total_with_attachments: number;
      total_amount: number;
    };
  };
  account_classifications: {
    prepayment_candidates: string[];
    exclude_recommended: string[];
    classic_exclusions: string[];
    revenue_accounts: string[];
    fixed_asset_accounts: string[];
    bank_accounts: string[];
    expense_accounts: string[];
    all_accounts: {
      [accountCode: string]: {
        name: string;
        type: string;
        class: string;
      };
    };
  };
  date_range: {
    start_date: string;
    end_date: string;
    months: number;
  };
  total_bills: number;
  total_with_attachments: number;
}

interface WizardFilters {
  dateRangeMonths: number;
  amountThreshold: number;
  thresholdCurrency: string;
  excludedAccountCodes: Set<string>;
  categoryToggles: {
    prepaymentCandidates: boolean;
    expenseAccounts: boolean;
    excludeRevenue: boolean;
    excludeFixedAssets: boolean;
    excludeBankAccounts: boolean;
    excludeClassicExclusions: boolean;
  };
  expandedCategories: Set<string>;
}

interface CostEstimation {
  totalBillsWithAttachments: number;
  estimatedCredits: { min: number; max: number };
  monthlyAverage: number;
  monthlyCredits: { min: number; max: number };
}

interface SetupData {
  enable_ai_scanning: boolean;
  sync_settings: {
    sync_start_date: string;
    sync_frequency: string;
    auto_sync_enabled: boolean;
    sync_invoices: boolean;
    sync_bills: boolean;
    sync_payments: boolean;
    sync_bank_transactions: boolean;
    sync_journal_entries: boolean;
    sync_spend_money: boolean;
  };
  account_settings: {
    prepayment_asset_accounts: string[];
    excluded_accounts: string[];
    base_currency: string;
  };
}

export function EnhancedEntitySetupWizard() {
  const { clientId, entityId } = useParams<{ clientId: string; entityId: string }>();
  const navigate = useNavigate();
  
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [billAggregates, setBillAggregates] = useState<BillAggregates | null>(null);
  const [accountClassificationsLoaded, setAccountClassificationsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Ref to track component mount status
  const isMountedRef = useRef(true);
  
  // Flag to track if initial data has been loaded (avoids redundant fetches on object reference changes)
  const hasLoadedOnceRef = useRef(false);
  
  // Polling state for handling race condition with Cloud Function sync
  const [isPolling, setIsPolling] = useState(false);
  const [pollingAttempt, setPollingAttempt] = useState(0);
  const [pollingStartTime, setPollingStartTime] = useState<number | null>(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const pollingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const elapsedTimeIntervalRef = useRef<NodeJS.Timeout | null>(null);
  
  // Wizard filtering state
  const [filters, setFilters] = useState<WizardFilters>({
    dateRangeMonths: 12,
    amountThreshold: 20,
    thresholdCurrency: 'GBP', // Will be updated from entity data
    excludedAccountCodes: new Set(),
    categoryToggles: {
      prepaymentCandidates: true,
      expenseAccounts: true,
      excludeRevenue: true,
      excludeFixedAssets: true,
      excludeBankAccounts: true,
      excludeClassicExclusions: true,
    },
    expandedCategories: new Set()
  });

  // Setup data for final submission
  const [setupData, setSetupData] = useState<SetupData>({
    enable_ai_scanning: true,
    sync_settings: {
      sync_start_date: '',
      sync_frequency: 'daily',
      auto_sync_enabled: true,
      sync_invoices: false,
      sync_bills: true,
      sync_payments: false,
      sync_bank_transactions: false,
      sync_journal_entries: true,
      sync_spend_money: false,
    },
    account_settings: {
      prepayment_asset_accounts: [] as string[],
      excluded_accounts: [] as string[],
      base_currency: 'USD',
    }
  });

  // Helper function to detect if COA data is empty (indicating sync hasn't completed)
  const isCoaDataEmpty = (data: unknown): boolean => {
    if (!data || typeof data !== 'object') return true;
    const typedData = data as any;
    if (!typedData.account_classifications) return true;
    
    const classifications = typedData.account_classifications;
    
    // Check if all key arrays are empty or missing
    const keyArrays = [
      'prepayment_candidates',
      'expense_accounts', 
      'revenue_accounts',
      'all_accounts'
    ];
    
    const isEmpty = keyArrays.every(key => {
      const value = classifications[key];
      if (key === 'all_accounts') {
        return !value || typeof value !== 'object' || Object.keys(value).length === 0;
      }
      return !Array.isArray(value) || value.length === 0;
    });
    
    return isEmpty;
  };

  // Polling function with exponential backoff
  const pollForCoaData = async (attempt: number = 0): Promise<void> => {
    const maxAttempts = 4; // 2s, 4s, 8s, 16s = 30s total
    const baseDelay = 2000; // Start with 2 seconds
    const maxDelay = 16000; // Cap at 16 seconds
    
    try {
      console.log(`🔄 [POLL:${entityId}] Attempt ${attempt + 1}/${maxAttempts} - elapsed: ${elapsedTime}s`);
      
      const data = await apiClient.getEntityBillAggregates(entityId!, filters.dateRangeMonths, filters.amountThreshold, true); // Disable cache during polling
      
      if (!isMountedRef.current) return;
      
      // Production monitoring: log data quality metrics
      const accountCount = Object.keys((data as any)?.account_classifications?.all_accounts || {}).length;
      const candidateCount = (data as any)?.account_classifications?.prepayment_candidates?.length || 0;
      console.log(`📊 [POLL:${entityId}] Data quality - accounts: ${accountCount}, candidates: ${candidateCount}`);
      
      // Check if we got meaningful data
      if (!isCoaDataEmpty(data)) {
        const totalElapsed = Math.round((Date.now() - (pollingStartTime || Date.now())) / 1000);
        console.log(`✅ [SUCCESS:${entityId}] COA data loaded after ${totalElapsed}s, ${attempt + 1} attempts`);
        
        // Production monitoring: successful polling metrics
        console.log(`📊 [METRICS:${entityId}] Polling success - attempts: ${attempt + 1}, total_time: ${totalElapsed}s, data_quality: ${accountCount > 0 ? 'good' : 'empty'}`);
        
        await processApiResponse(data);
        
        // Clear polling state
        setIsPolling(false);
        setPollingAttempt(0);
        setPollingStartTime(null);
        setElapsedTime(0);
        if (pollingTimeoutRef.current) {
          clearTimeout(pollingTimeoutRef.current);
          pollingTimeoutRef.current = null;
        }
        if (elapsedTimeIntervalRef.current) {
          clearInterval(elapsedTimeIntervalRef.current);
          elapsedTimeIntervalRef.current = null;
        }
        return;
      }
      
      // If we've reached max attempts, show error
      if (attempt >= maxAttempts - 1) {
        const totalElapsed = Math.round((Date.now() - (pollingStartTime || Date.now())) / 1000);
        console.warn(`⚠️ [TIMEOUT:${entityId}] Max polling attempts reached after ${totalElapsed}s - data still empty`);
        
        // Production monitoring: timeout metrics
        console.log(`🚨 [METRICS:${entityId}] Polling timeout - attempts: ${maxAttempts}, total_time: ${totalElapsed}s, final_account_count: ${accountCount}`);
        
        setError('Chart of Accounts data is still loading. The entity may still be syncing. Please refresh the page in a few moments.');
        setIsPolling(false);
        setPollingAttempt(0);
        setPollingStartTime(null);
        setElapsedTime(0);
        if (elapsedTimeIntervalRef.current) {
          clearInterval(elapsedTimeIntervalRef.current);
          elapsedTimeIntervalRef.current = null;
        }
        return;
      }
      
      // Schedule next polling attempt with exponential backoff + jitter
      const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
      const jitter = Math.random() * 500; // 0-500ms jitter
      const totalDelay = delay + jitter;
      
      console.log(`⏳ [RETRY:${entityId}] Data empty, next attempt in ${Math.round(totalDelay)}ms (attempt ${attempt + 2}/${maxAttempts})`);
      
      setPollingAttempt(attempt + 1);
      
      pollingTimeoutRef.current = setTimeout(() => {
        if (isMountedRef.current) {
          pollForCoaData(attempt + 1);
        }
      }, totalDelay);
      
    } catch (error: unknown) {
      console.error(`❌ [ERROR:${entityId}] Polling attempt ${attempt + 1} failed:`, error);
      
      // Production monitoring: error tracking
      console.log(`🚨 [METRICS:${entityId}] Polling error - attempt: ${attempt + 1}, error: ${error instanceof Error ? error.message : 'unknown'}`);
      
      if (attempt >= maxAttempts - 1) {
        const totalElapsed = Math.round((Date.now() - (pollingStartTime || Date.now())) / 1000);
        console.error(`🚨 [FAILURE:${entityId}] Max polling attempts reached after ${totalElapsed}s, giving up`);
        
        // Production monitoring: failure metrics
        console.log(`🚨 [METRICS:${entityId}] Polling failure - attempts: ${maxAttempts}, total_time: ${totalElapsed}s, final_error: ${error instanceof Error ? error.message : 'unknown'}`);
        
        setError('Failed to load Chart of Accounts data. Please refresh the page.');
        setIsPolling(false);
        setPollingAttempt(0);
        setPollingStartTime(null);
        setElapsedTime(0);
        if (elapsedTimeIntervalRef.current) {
          clearInterval(elapsedTimeIntervalRef.current);
          elapsedTimeIntervalRef.current = null;
        }
      } else {
        // Continue polling on error
        const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
        pollingTimeoutRef.current = setTimeout(() => {
          if (isMountedRef.current) {
            pollForCoaData(attempt + 1);
          }
        }, delay);
        setPollingAttempt(attempt + 1);
      }
    }
  };

  // Process API response with validation
  const processApiResponse = async (data: unknown): Promise<void> => {
    // Defensive validation of API response structure
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid API response: expected object');
    }
    
    const typedData = data as any;
    
    if (!typedData.account_classifications) {
      console.warn('Missing account_classifications in API response, using empty structure');
      typedData.account_classifications = {
        prepayment_candidates: [],
        exclude_recommended: [],
        classic_exclusions: [],
        revenue_accounts: [],
        fixed_asset_accounts: [],
        bank_accounts: [],
        expense_accounts: [],
        all_accounts: {}
      };
    }
    
    if (!typedData.account_classifications.all_accounts || typeof typedData.account_classifications.all_accounts !== 'object') {
      console.warn('Missing or invalid all_accounts in account_classifications, using empty object');
      typedData.account_classifications.all_accounts = {};
    }
    
    // Validate required arrays exist
    const requiredArrays = [
      'revenue_accounts', 'fixed_asset_accounts', 'bank_accounts', 
      'classic_exclusions', 'prepayment_candidates', 'expense_accounts'
    ];
    
    requiredArrays.forEach(arrayName => {
      if (!Array.isArray(typedData.account_classifications[arrayName])) {
        console.warn(`Missing or invalid ${arrayName} in account_classifications, using empty array`);
        typedData.account_classifications[arrayName] = [];
      }
    });
    
    if (isMountedRef.current) {
      setBillAggregates(typedData as BillAggregates);
      hasLoadedOnceRef.current = true;
    }
  };

  const loadBillAggregates = useCallback(async () => {
    try {
      if (isMountedRef.current) {
        setLoading(true);
        setError(null);
      }
      
      const data = await apiClient.getEntityBillAggregates(entityId!, filters.dateRangeMonths, filters.amountThreshold, false);
      
      // Check if component is still mounted before proceeding
      if (!isMountedRef.current) {
        return;
      }
      
      // Check if COA data is empty (race condition detected)
      if (isCoaDataEmpty(data)) {
        const accountCount = Object.keys((data as any)?.account_classifications?.all_accounts || {}).length;
        console.log(`🚨 [RACE:${entityId}] Empty COA detected - accounts: ${accountCount}, starting polling...`);
        
        // Production monitoring: race condition detection
        console.log(`📊 [METRICS:${entityId}] Race condition detected - immediate_account_count: ${accountCount}, filters: ${JSON.stringify({dateRange: filters.dateRangeMonths, threshold: filters.amountThreshold})}`);
        
        // Start polling if not already polling
        if (!isPolling) {
          setIsPolling(true);
          setPollingAttempt(0);
          const startTime = Date.now();
          setPollingStartTime(startTime);
          setElapsedTime(0);
          
          console.log(`🔄 [START:${entityId}] Polling initiated at ${new Date(startTime).toISOString()}`);
          
          // Start elapsed time tracking
          elapsedTimeIntervalRef.current = setInterval(() => {
            if (isMountedRef.current) {
              setElapsedTime(Math.round((Date.now() - startTime) / 1000));
            }
          }, 1000);
          
          // Start polling with a delay to avoid immediate retry
          pollingTimeoutRef.current = setTimeout(() => {
            if (isMountedRef.current) {
              pollForCoaData(0);
            }
          }, 2000); // Start first poll after 2 seconds
        }
        return;
      }
      
      // Process successful response
      const accountCount = Object.keys((data as any)?.account_classifications?.all_accounts || {}).length;
      console.log(`✅ [DIRECT:${entityId}] COA data loaded immediately - accounts: ${accountCount}`);
      
      // Production monitoring: immediate success
      console.log(`📊 [METRICS:${entityId}] Direct load success - account_count: ${accountCount}, no_polling_needed: true`);
      
      await processApiResponse(data);
      
    } catch (error: unknown) {
      console.error(`❌ [LOAD_ERROR:${entityId}] Failed to load bill aggregates:`, error);
      
      // Production monitoring: load error tracking
      console.log(`🚨 [METRICS:${entityId}] Load error - error: ${error instanceof Error ? error.message : 'unknown'}, was_polling: ${isPolling}`);
      
      if (isMountedRef.current) {
        setError('Failed to load bill data. Please try again.');
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [entityId, filters.dateRangeMonths, filters.amountThreshold]);

  useEffect(() => {
    if (entityId) {
      loadBillAggregates();
    }
  }, [entityId]);

  // Cleanup effect to mark component as unmounted and clear polling timeout
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (pollingTimeoutRef.current) {
        clearTimeout(pollingTimeoutRef.current);
        pollingTimeoutRef.current = null;
      }
      if (elapsedTimeIntervalRef.current) {
        clearInterval(elapsedTimeIntervalRef.current);
        elapsedTimeIntervalRef.current = null;
      }
    };
  }, []);

  // Separate effect for amount threshold and date range changes (debounced)
  useEffect(() => {
    // Only debounce if we've loaded data at least once
    if (entityId && hasLoadedOnceRef.current) {
      const timeoutId = setTimeout(() => {
        loadBillAggregates();
      }, 500); // 500ms debounce
      
      return () => clearTimeout(timeoutId);
    }
  }, [filters.amountThreshold, filters.dateRangeMonths, entityId]);

  // Separate effect to handle billAggregates data initialization
  useEffect(() => {
    if (!billAggregates?.account_classifications) {
      setAccountClassificationsLoaded(false);
      return;
    }

    // Memoized initial exclusions to avoid creating new Sets on every render
    const initialExclusions = new Set([
      ...billAggregates.account_classifications.revenue_accounts,
      ...billAggregates.account_classifications.fixed_asset_accounts,
      ...billAggregates.account_classifications.bank_accounts,
      ...billAggregates.account_classifications.classic_exclusions,
    ]);

    // Set initial filters based on data
    setFilters(prev => ({
      ...prev,
      excludedAccountCodes: initialExclusions
    }));

    // Set setup data defaults
    setSetupData(prev => ({
      ...prev,
      sync_settings: {
        ...prev.sync_settings,
        sync_start_date: billAggregates.date_range?.start_date || prev.sync_settings.sync_start_date,
      },
      account_settings: {
        ...prev.account_settings,
        prepayment_asset_accounts: billAggregates.account_classifications.prepayment_candidates || [],
        excluded_accounts: Array.from(initialExclusions),
      }
    }));

    setAccountClassificationsLoaded(true);
  }, [billAggregates]); // Trigger when billAggregates changes

  // Real-time cost calculation based on current filters
  const costEstimation = useMemo((): CostEstimation => {
    if (!billAggregates) {
      return {
        totalBillsWithAttachments: 0,
        estimatedCredits: { min: 0, max: 0 },
        monthlyAverage: 0,
        monthlyCredits: { min: 0, max: 0 }
      };
    }

    let totalBillsWithAttachments = 0;
    const monthlyTotals: number[] = [];

    // Calculate cutoff date for filtering
    const cutoffKey = getCutoffKey(filters.dateRangeMonths);

    // Calculate totals based on current filters
    Object.entries(billAggregates.aggregates).forEach(([month, monthData]) => {
      // Filter by date range - only include months within selected range
      if (month < cutoffKey) {
        return;
      }

      let monthBillsWithAttachments = 0;

      // Check each account in this month
      Object.entries(monthData.by_account).forEach(([accountCode, accountData]) => {
        // Skip if account is excluded
        if (filters.excludedAccountCodes.has(accountCode)) {
          return;
        }

        monthBillsWithAttachments += accountData.with_attachments;
      });

      totalBillsWithAttachments += monthBillsWithAttachments;
      monthlyTotals.push(monthBillsWithAttachments);
    });

    // Calculate monthly average
    const monthlyAverage = monthlyTotals.length > 0 
      ? monthlyTotals.reduce((sum, val) => sum + val, 0) / monthlyTotals.length 
      : 0;

    // Estimate credits (1-3 pages per bill average)
    const estimatedCredits = {
      min: Math.round(totalBillsWithAttachments * 1.5), // 1.5 pages average
      max: Math.round(totalBillsWithAttachments * 2.5)  // 2.5 pages average
    };

    const monthlyCredits = {
      min: Math.round(monthlyAverage * 1.5),
      max: Math.round(monthlyAverage * 2.5)
    };

    return {
      totalBillsWithAttachments,
      estimatedCredits,
      monthlyAverage,
      monthlyCredits
    };
  }, [billAggregates, filters]);

  const toggleCategoryExpansion = (categoryKey: string) => {
    setFilters(prev => {
      const newExpanded = new Set(prev.expandedCategories);
      if (newExpanded.has(categoryKey)) {
        newExpanded.delete(categoryKey);
      } else {
        newExpanded.add(categoryKey);
      }
      return { ...prev, expandedCategories: newExpanded };
    });
  };

  const handleAccountToggle = (accountCode: string, isIncluded: boolean) => {
    setFilters(prev => {
      const newExclusions = new Set(prev.excludedAccountCodes);
      if (isIncluded) {
        newExclusions.delete(accountCode);
      } else {
        newExclusions.add(accountCode);
      }
      return { ...prev, excludedAccountCodes: newExclusions };
    });
  };

  const getAccountsInCategory = (categoryKey: string): Array<{code: string; name: string; type: string}> => {
    // Early return if no data available
    if (!billAggregates?.account_classifications) return [];
    
    const classifications = billAggregates.account_classifications;
    
    // Ensure all_accounts exists and is an object
    if (!classifications.all_accounts || typeof classifications.all_accounts !== 'object') {
      console.warn('Missing or invalid all_accounts in account_classifications');
      return [];
    }
    
    let accountCodes: string[] = [];
    
    switch (categoryKey) {
      case 'prepaymentCandidates':
        accountCodes = Array.isArray(classifications.prepayment_candidates) ? classifications.prepayment_candidates : [];
        break;
      case 'expenseAccounts':
        accountCodes = Array.isArray(classifications.expense_accounts) ? classifications.expense_accounts : [];
        break;
      case 'excludeRevenue':
        accountCodes = Array.isArray(classifications.revenue_accounts) ? classifications.revenue_accounts : [];
        break;
      case 'excludeFixedAssets':
        accountCodes = Array.isArray(classifications.fixed_asset_accounts) ? classifications.fixed_asset_accounts : [];
        break;
      case 'excludeBankAccounts':
        accountCodes = Array.isArray(classifications.bank_accounts) ? classifications.bank_accounts : [];
        break;
      case 'excludeClassicExclusions':
        accountCodes = Array.isArray(classifications.classic_exclusions) ? classifications.classic_exclusions : [];
        break;
      default:
        return [];
    }
    
    // Filter out invalid account codes and map to account objects
    return accountCodes
      .filter(code => typeof code === 'string' && code.trim() !== '')
      .map(code => ({
        code,
        name: classifications.all_accounts[code]?.name || 'Unknown Account',
        type: classifications.all_accounts[code]?.type || 'Unknown'
      }));
  };

  const getCategorySelectionStatus = (categoryKey: string, isExcludeCategory: boolean = false) => {
    const accounts = getAccountsInCategory(categoryKey);
    if (accounts.length === 0) return { selected: 0, total: 0, isIndeterminate: false };
    
    let selectedCount = 0;
    accounts.forEach(account => {
      const isExcluded = filters.excludedAccountCodes.has(account.code);
      if (isExcludeCategory) {
        if (isExcluded) selectedCount++;
      } else {
        if (!isExcluded) selectedCount++;
      }
    });
    
    return {
      selected: selectedCount,
      total: accounts.length,
      isIndeterminate: selectedCount > 0 && selectedCount < accounts.length
    };
  };

  const handleCategoryToggle = (category: keyof WizardFilters['categoryToggles']) => {
    if (!billAggregates) return;

    const newToggles = { ...filters.categoryToggles, [category]: !filters.categoryToggles[category] };
    let newExclusions = new Set(filters.excludedAccountCodes);

    // Update exclusions based on category toggles
    const classifications = billAggregates.account_classifications;
    
    if (category === 'excludeRevenue') {
      if (newToggles.excludeRevenue) {
        classifications.revenue_accounts.forEach(code => newExclusions.add(code));
      } else {
        classifications.revenue_accounts.forEach(code => newExclusions.delete(code));
      }
    }

    if (category === 'excludeFixedAssets') {
      if (newToggles.excludeFixedAssets) {
        classifications.fixed_asset_accounts.forEach(code => newExclusions.add(code));
      } else {
        classifications.fixed_asset_accounts.forEach(code => newExclusions.delete(code));
      }
    }

    if (category === 'excludeBankAccounts') {
      if (newToggles.excludeBankAccounts) {
        classifications.bank_accounts.forEach(code => newExclusions.add(code));
      } else {
        classifications.bank_accounts.forEach(code => newExclusions.delete(code));
      }
    }

    if (category === 'excludeClassicExclusions') {
      if (newToggles.excludeClassicExclusions) {
        classifications.classic_exclusions.forEach(code => newExclusions.add(code));
      } else {
        classifications.classic_exclusions.forEach(code => newExclusions.delete(code));
      }
    }

    setFilters(prev => ({
      ...prev,
      categoryToggles: newToggles,
      excludedAccountCodes: newExclusions
    }));
  };

  const handleNext = () => {
    // Basic validation before proceeding
    if (currentStep === 1) {
      // No specific validation required for Step 1
    } else if (currentStep === 2) {
      // Ensure at least one data type is selected for sync
      const hasDataTypesSelected = Object.entries(setupData.sync_settings).some(([key, value]) => 
        key.startsWith('sync_') && value === true
      );
      if (!hasDataTypesSelected) {
        toast.error('Please select at least one data type to sync');
        return;
      }
    }
    
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleCompleteSetup = async () => {
    try {
      setSubmitting(true);
      
      // Validate that at least one data type is selected
      const hasDataTypesSelected = Object.entries(setupData.sync_settings).some(([key, value]) => 
        key.startsWith('sync_') && value === true
      );
      if (!hasDataTypesSelected) {
        toast.error('Please select at least one data type to sync');
        setSubmitting(false);
        return;
      }
      
      // Update setup data with current filter selections
      const finalSetupData = {
        ...setupData,
        enable_ai_scanning: setupData.enable_ai_scanning,
        account_settings: {
          ...setupData.account_settings,
          excluded_accounts: Array.from(filters.excludedAccountCodes),
        }
      };
      
      await apiClient.completeEntitySetup(entityId!, finalSetupData);
      
      toast.success('Entity setup completed successfully!');
      navigate(`/clients/${clientId}/entities`);
      
    } catch (error: unknown) {
      console.error('Failed to complete setup:', error);
      toast.error('Failed to complete setup. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const renderAccountCategory = (
    categoryKey: string, 
    categoryLabel: string, 
    isExcludeCategory: boolean = false,
    showRecommendedBadge: boolean = false
  ) => {
    const accounts = getAccountsInCategory(categoryKey);
    const selectionStatus = getCategorySelectionStatus(categoryKey, isExcludeCategory);
    const isExpanded = filters.expandedCategories.has(categoryKey);
    
    return (
      <div key={categoryKey} className="border rounded-lg p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {isExcludeCategory ? (
              <Switch
                id={categoryKey}
                checked={filters.categoryToggles[categoryKey as keyof typeof filters.categoryToggles]}
                onCheckedChange={() => handleCategoryToggle(categoryKey as keyof WizardFilters['categoryToggles'])}
              />
            ) : (
              <Checkbox 
                id={categoryKey}
                checked={selectionStatus.selected === selectionStatus.total && selectionStatus.total > 0}
                ref={(el) => {
                  if (el && 'indeterminate' in el) {
                    (el as any).indeterminate = selectionStatus.isIndeterminate;
                  }
                }}
                onCheckedChange={(checked) => {
                  const accounts = getAccountsInCategory(categoryKey);
                  accounts.forEach(account => {
                    handleAccountToggle(account.code, !!checked);
                  });
                }}
              />
            )}
            <label htmlFor={categoryKey} className="text-sm font-medium">
              {categoryLabel}
            </label>
            {showRecommendedBadge && (
              <Badge variant="secondary" className="text-xs">Recommended</Badge>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-xs text-muted-foreground">
              {isExcludeCategory 
                ? `${selectionStatus.selected}/${selectionStatus.total} excluded`
                : `${selectionStatus.selected}/${selectionStatus.total} selected`
              }
            </span>
            {accounts.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => toggleCategoryExpansion(categoryKey)}
                className="h-6 w-6 p-0"
              >
                {isExpanded ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )}
              </Button>
            )}
          </div>
        </div>
        
        {/* Expanded account list */}
        {isExpanded && accounts.length > 0 && (
          <div className="mt-3 space-y-2 pl-6 border-l-2 border-muted">
            {accounts.map((account) => {
              const isIncluded = !filters.excludedAccountCodes.has(account.code);
              const displayIncluded = isExcludeCategory ? !isIncluded : isIncluded;
              
              return (
                <div key={account.code} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`account-${account.code}`}
                      checked={displayIncluded}
                      onCheckedChange={(checked) => {
                        if (isExcludeCategory) {
                          handleAccountToggle(account.code, !checked);
                        } else {
                          handleAccountToggle(account.code, !!checked);
                        }
                      }}
                    />
                    <label htmlFor={`account-${account.code}`} className="text-xs">
                      <span className="font-mono text-muted-foreground">{account.code}</span>
                      <span className="ml-2">{account.name}</span>
                    </label>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {account.type}
                  </Badge>
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          {isPolling ? (
            <div className="space-y-2">
              <p className="text-muted-foreground">Waiting for entity sync to complete...</p>
              <div className="text-sm text-muted-foreground">
                <p>Attempt {pollingAttempt + 1} of 4</p>
                <p>Elapsed time: {elapsedTime}s</p>
                <p className="text-xs mt-2 max-w-md mx-auto">
                  🚨 Your entity is still syncing with the accounting system. This usually takes about 30 seconds for new connections.
                </p>
              </div>
            </div>
          ) : (
            <p className="text-muted-foreground">Analyzing your bills and accounts...</p>
          )}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              Setup Error
            </CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => navigate(`/clients/${clientId}/entities`)}>
              Back to Entities
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!billAggregates) {
    return null;
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold mb-2">Setup Entity - Interactive Cost Estimation</h1>
        <p className="text-muted-foreground">
          Configure your entity settings and see real-time cost estimates
        </p>
      </div>

      {/* Progress Indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          {[1, 2, 3].map((step) => (
            <div 
              key={step}
              className={`flex items-center ${step < 3 ? 'flex-1' : ''}`}
            >
              <div className={`
                w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                ${currentStep >= step 
                  ? 'bg-primary text-primary-foreground' 
                  : 'bg-muted text-muted-foreground'
                }
              `}>
                {currentStep > step ? <CheckCircle className="h-4 w-4" /> : step}
              </div>
              {step < 3 && (
                <div className={`h-0.5 flex-1 mx-4 ${
                  currentStep > step ? 'bg-primary' : 'bg-muted'
                }`} />
              )}
            </div>
          ))}
        </div>
        <div className="flex justify-between text-sm text-muted-foreground">
          <span>Interactive Cost Estimation</span>
          <span>Sync Settings</span>
          <span>Complete</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        
        {/* Left Column - Configuration (Step 1) */}
        {currentStep === 1 && (
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Configure Scanning Scope
                </CardTitle>
                <CardDescription>
                  Adjust settings to see real-time cost estimates
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                
                {/* Date Range Slider */}
                <div>
                  <label className="text-sm font-medium mb-3 block">
                    Date Range: {filters.dateRangeMonths} months
                  </label>
                  <Slider
                    value={[filters.dateRangeMonths]}
                    onValueChange={([value]) => setFilters(prev => ({ ...prev, dateRangeMonths: value }))}
                    min={3}
                    max={24}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>3 months</span>
                    <span>24 months</span>
                  </div>
                </div>

                {/* Amount Threshold */}
                <div>
                  <label className="text-sm font-medium mb-3 block">
                    Amount Threshold: Only scan bills above {filters.thresholdCurrency === 'GBP' ? '£' : filters.thresholdCurrency === 'USD' ? '$' : '€'}{filters.amountThreshold}
                  </label>
                  <div className="flex gap-3 items-center">
                    <select 
                      value={filters.thresholdCurrency}
                      onChange={(e) => setFilters(prev => ({ ...prev, thresholdCurrency: e.target.value }))}
                      className="px-3 py-2 border rounded-md text-sm"
                    >
                      <option value="GBP">£ GBP</option>
                      <option value="USD">$ USD</option>
                      <option value="EUR">€ EUR</option>
                    </select>
                    <input
                      type="number"
                      value={filters.amountThreshold}
                      onChange={(e) => setFilters(prev => ({ ...prev, amountThreshold: parseFloat(e.target.value) || 0 }))}
                      min="0"
                      step="5"
                      className="flex-1 px-3 py-2 border rounded-md text-sm"
                    />
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    💡 Recommended: {filters.thresholdCurrency === 'GBP' ? '£20' : filters.thresholdCurrency === 'USD' ? '$20' : '€25'} for most businesses to exclude petty expenses
                  </p>
                </div>

                <Separator />

                {/* Account Categories - Hierarchical View */}
                <div>
                  <h3 className="text-sm font-medium mb-3">Account Categories</h3>
                  
                  {!accountClassificationsLoaded ? (
                    /* Loading skeleton for account categories */
                    <div className="space-y-3">
                      <div className="space-y-3">
                        <h4 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                          Include in Scanning
                        </h4>
                        {/* Skeleton for include categories */}
                        {[1, 2].map((i) => (
                          <div key={`include-skeleton-${i}`} className="border rounded-lg p-3 animate-pulse">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <div className="w-4 h-4 bg-gray-300 rounded"></div>
                                <div className="h-4 bg-gray-300 rounded w-32"></div>
                              </div>
                              <div className="h-3 bg-gray-300 rounded w-16"></div>
                            </div>
                          </div>
                        ))}
                      </div>
                      
                      <div className="h-px bg-gray-200"></div>
                      
                      <div className="space-y-3">
                        <h4 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                          Exclude from Scanning
                        </h4>
                        {/* Skeleton for exclude categories */}
                        {[1, 2, 3, 4].map((i) => (
                          <div key={`exclude-skeleton-${i}`} className="border rounded-lg p-3 animate-pulse">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <div className="w-4 h-4 bg-gray-300 rounded"></div>
                                <div className="h-4 bg-gray-300 rounded w-40"></div>
                              </div>
                              <div className="h-3 bg-gray-300 rounded w-20"></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    /* Actual account categories content */
                    <div className="space-y-3">
                      
                      {/* Include Categories */}
                      <div className="space-y-3">
                        <h4 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                          Include in Scanning
                        </h4>
                        
                        {renderAccountCategory('prepaymentCandidates', 'Prepayment Candidates', false, true)}
                        {renderAccountCategory('expenseAccounts', 'Other Expense Accounts', false, false)}
                      </div>

                      <Separator />

                      {/* Exclude Categories */}
                      <div className="space-y-3">
                        <h4 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                          Exclude from Scanning
                        </h4>
                        
                        {renderAccountCategory('excludeRevenue', 'Revenue & Sales', true, false)}
                        {renderAccountCategory('excludeFixedAssets', 'Fixed Assets', true, false)}
                        {renderAccountCategory('excludeBankAccounts', 'Bank & Cash Accounts', true, false)}
                        {renderAccountCategory('excludeClassicExclusions', 'Travel, Entertainment & Bank Fees', true, false)}
                      </div>
                    </div>
                  )}
                </div>

              </CardContent>
            </Card>
          </div>
        )}

        {/* Step 2 & 3 content would go here... */}
        {currentStep === 2 && (
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Sync Configuration
                </CardTitle>
                <CardDescription>
                  Configure how and when to synchronize data from Xero
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">

                {/* Sync Frequency */}
                <div>
                  <label className="text-sm font-medium mb-3 block">Sync Frequency</label>
                  <div className="space-y-3">
                    {[
                      { value: 'daily', label: 'Daily', description: 'Sync every day automatically (recommended)' },
                      { value: 'weekly', label: 'Weekly', description: 'Sync once per week' },
                      { value: 'manual', label: 'Manual', description: 'Only sync when you trigger it manually' }
                    ].map((option) => (
                      <div key={option.value} className="flex items-start space-x-3">
                        <input
                          type="radio"
                          id={`sync-${option.value}`}
                          name="sync_frequency"
                          value={option.value}
                          checked={setupData.sync_settings.sync_frequency === option.value}
                          onChange={(e) => setSetupData(prev => ({
                            ...prev,
                            sync_settings: { ...prev.sync_settings, sync_frequency: e.target.value }
                          }))}
                          className="mt-1"
                        />
                        <div className="flex-1">
                          <label htmlFor={`sync-${option.value}`} className="text-sm font-medium cursor-pointer">
                            {option.label}
                          </label>
                          <p className="text-xs text-muted-foreground">{option.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* Data Types to Sync */}
                <div>
                  <label className="text-sm font-medium mb-3 block">Data Types to Sync</label>
                  <div className="grid grid-cols-2 gap-4">
                    {[
                      { key: 'sync_bills', label: 'Bills', description: 'Purchase invoices and bills', enabled: true },
                      { key: 'sync_journal_entries', label: 'Journal Entries', description: 'Manual journal entries', enabled: true },
                      { key: 'sync_invoices', label: 'Sales Invoices', description: 'Customer invoices', enabled: false },
                      { key: 'sync_payments', label: 'Payments', description: 'Bill and invoice payments', enabled: false },
                      { key: 'sync_bank_transactions', label: 'Bank Transactions', description: 'Bank feeds and transactions', enabled: false },
                      { key: 'sync_spend_money', label: 'Spend Money', description: 'Direct expense transactions', enabled: false }
                    ].map((item) => (
                      <div key={item.key} className="flex items-start space-x-3 p-3 border rounded-lg opacity-70">
                        <Checkbox
                          id={item.key}
                          checked={setupData.sync_settings[item.key as keyof typeof setupData.sync_settings] as boolean}
                          disabled
                        />
                        <div className="flex-1">
                          <label htmlFor={item.key} className="text-sm font-medium">
                            {item.label}
                          </label>
                          <p className="text-xs text-muted-foreground">{item.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* Auto-sync Settings */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <label className="text-sm font-medium">Auto-sync Enabled</label>
                      <p className="text-xs text-muted-foreground">Enable automatic background synchronization</p>
                    </div>
                    <Switch
                      checked={setupData.sync_settings.auto_sync_enabled}
                      onCheckedChange={(checked) => setSetupData(prev => ({
                        ...prev,
                        sync_settings: { ...prev.sync_settings, auto_sync_enabled: checked }
                      }))}
                    />
                  </div>
                </div>

              </CardContent>
            </Card>
          </div>
        )}

        {currentStep === 3 && (
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  Review & Complete
                </CardTitle>
                <CardDescription>
                  Review your configuration and complete the entity setup
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">

                {/* Configuration Summary */}
                <div>
                  <h3 className="text-sm font-medium mb-4">Configuration Summary</h3>
                  
                  {/* Step 1 Summary */}
                  <div className="space-y-4">
                    <div className="p-4 bg-muted/50 rounded-lg">
                      <h4 className="text-sm font-medium mb-2">📊 Cost Estimation Settings</h4>
                      <div className="grid grid-cols-2 gap-4 text-xs">
                        <div>
                          <span className="text-muted-foreground">Date Range:</span>
                          <span className="ml-2 font-medium">{filters.dateRangeMonths} months</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Amount Threshold:</span>
                          <span className="ml-2 font-medium">
                            {filters.thresholdCurrency === 'GBP' ? '£' : filters.thresholdCurrency === 'USD' ? '$' : '€'}{filters.amountThreshold}
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Excluded Accounts:</span>
                          <span className="ml-2 font-medium">{filters.excludedAccountCodes.size} accounts</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">AI Scanning:</span>
                          <span className="ml-2 font-medium">{setupData.enable_ai_scanning ? 'Enabled' : 'Disabled'}</span>
                        </div>
                      </div>
                    </div>

                    {/* Step 2 Summary */}
                    <div className="p-4 bg-muted/50 rounded-lg">
                      <h4 className="text-sm font-medium mb-2">⚙️ Sync Configuration</h4>
                      <div className="grid grid-cols-2 gap-4 text-xs">
                        <div>
                          <span className="text-muted-foreground">Frequency:</span>
                          <span className="ml-2 font-medium capitalize">{setupData.sync_settings.sync_frequency}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Auto-sync:</span>
                          <span className="ml-2 font-medium">{setupData.sync_settings.auto_sync_enabled ? 'Enabled' : 'Disabled'}</span>
                        </div>
                      </div>
                      <div className="mt-2">
                        <span className="text-muted-foreground text-xs">Data Types:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {[
                            { key: 'sync_bills', label: 'Bills' },
                            { key: 'sync_invoices', label: 'Invoices' },
                            { key: 'sync_payments', label: 'Payments' },
                            { key: 'sync_bank_transactions', label: 'Bank' },
                            { key: 'sync_journal_entries', label: 'Journals' },
                            { key: 'sync_spend_money', label: 'Spend Money' }
                          ].filter(item => setupData.sync_settings[item.key as keyof typeof setupData.sync_settings])
                           .map(item => (
                            <Badge key={item.key} variant="secondary" className="text-xs">
                              {item.label}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* What Happens Next */}
                <div>
                  <h3 className="text-sm font-medium mb-3">What Happens Next</h3>
                  <div className="space-y-3">
                    <div className="flex items-start space-x-3">
                      <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium">1</div>
                      <div>
                        <p className="text-sm font-medium">Initial Data Sync</p>
                        <p className="text-xs text-muted-foreground">
                          We'll sync your selected data types from Xero (estimated 5-15 minutes)
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium">2</div>
                      <div>
                        <p className="text-sm font-medium">AI Document Processing</p>
                        <p className="text-xs text-muted-foreground">
                          {setupData.enable_ai_scanning 
                            ? 'AI will scan bill attachments for prepayment opportunities'
                            : 'AI scanning disabled - you can enable this later in settings'
                          }
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium">3</div>
                      <div>
                        <p className="text-sm font-medium">Ongoing Synchronization</p>
                        <p className="text-xs text-muted-foreground">
                          {setupData.sync_settings.auto_sync_enabled 
                            ? `Automatic ${setupData.sync_settings.sync_frequency} sync will keep your data up to date`
                            : 'Manual sync only - trigger syncs when needed'
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Ready to Complete */}
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Ready to complete setup!</strong> Your entity will be configured with the settings above and the initial sync will begin immediately.
                  </AlertDescription>
                </Alert>

              </CardContent>
            </Card>
          </div>
        )}

        {/* Right Column - Live Cost Estimation */}
        <div className="lg:col-span-1">
          <div className="sticky top-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Cost Estimation
                </CardTitle>
                <CardDescription>Updates in real-time</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                
                {!accountClassificationsLoaded ? (
                  /* Loading skeleton for cost estimation */
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 rounded-lg border border-blue-200 animate-pulse">
                      <h3 className="font-medium text-blue-900 mb-2">Historical Scan</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-blue-700">Bills with attachments:</span>
                          <div className="h-4 bg-blue-300 rounded w-8"></div>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-blue-700">Estimated cost:</span>
                          <div className="h-4 bg-blue-300 rounded w-16"></div>
                        </div>
                        <div className="h-3 bg-blue-300 rounded w-24"></div>
                      </div>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg border border-green-200 animate-pulse">
                      <h3 className="font-medium text-green-900 mb-2">Monthly Ongoing</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-green-700">Average monthly bills:</span>
                          <div className="h-4 bg-green-300 rounded w-8"></div>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-green-700">Monthly cost:</span>
                          <div className="h-4 bg-green-300 rounded w-16"></div>
                        </div>
                        <div className="h-3 bg-green-300 rounded w-20"></div>
                      </div>
                    </div>
                  </div>
                ) : (
                  /* Actual cost estimation content */
                  <>
                    {/* Historical Scan Cost */}
                    <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <h3 className="font-medium text-blue-900 mb-2">Historical Scan</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-blue-700">Bills with attachments:</span>
                          <span className="font-medium text-blue-900">{costEstimation.totalBillsWithAttachments}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-blue-700">Estimated cost:</span>
                          <span className="font-medium text-blue-900">
                            {costEstimation.estimatedCredits.min}-{costEstimation.estimatedCredits.max} credits
                          </span>
                        </div>
                        <p className="text-xs text-blue-600">
                          Based on {filters.dateRangeMonths} months of data
                        </p>
                      </div>
                    </div>
                    {/* Monthly Ongoing Cost */}
                    <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                      <h3 className="font-medium text-green-900 mb-2">Monthly Ongoing</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-green-700">Average monthly bills:</span>
                          <span className="font-medium text-green-900">{Math.round(costEstimation.monthlyAverage)}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-green-700">Monthly cost:</span>
                          <span className="font-medium text-green-900">
                            {costEstimation.monthlyCredits.min}-{costEstimation.monthlyCredits.max} credits
                          </span>
                        </div>
                        <p className="text-xs text-green-600">
                          Last {filters.dateRangeMonths} months average
                        </p>
                      </div>
                    </div>

                    {/* AI Scanning Toggle */}
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">Enable AI Scanning</span>
                        <Switch
                          checked={setupData.enable_ai_scanning}
                          onCheckedChange={(checked) => setSetupData(prev => ({ ...prev, enable_ai_scanning: checked }))}
                        />
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {setupData.enable_ai_scanning 
                          ? "AI will scan documents for prepayments" 
                          : "You can enable this later in settings"
                        }
                      </p>
                    </div>

                    {/* Amount Threshold Impact */}
                    {filters.amountThreshold > 0 && (
                      <Alert>
                        <Filter className="h-4 w-4" />
                        <AlertDescription className="text-xs">
                          <strong>Amount Filter:</strong> Bills below {filters.thresholdCurrency === 'GBP' ? '£' : filters.thresholdCurrency === 'USD' ? '$' : '€'}{filters.amountThreshold} excluded. 
                          Cost estimates reflect filtered data.
                        </AlertDescription>
                      </Alert>
                    )}
                  </>
                )}

                {/* Cost Methodology */}
                <Alert>
                  <TrendingUp className="h-4 w-4" />
                  <AlertDescription className="text-xs">
                    <strong>Methodology:</strong> 1 credit per page. Most bills are 1-3 pages. Monthly estimate based on historical average.
                  </AlertDescription>
                </Alert>

              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between mt-8">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 1}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => navigate(`/clients/${clientId}/entities`)}
          >
            Cancel
          </Button>

          {currentStep < 3 ? (
            <Button onClick={handleNext}>
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button 
              onClick={handleCompleteSetup}
              disabled={submitting}
            >
              {submitting ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <CheckCircle className="h-4 w-4 mr-2" />
              )}
              Complete Setup
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}