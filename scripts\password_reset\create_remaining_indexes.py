#!/usr/bin/env python3
"""
Script to show remaining Firestore indexes needed for password reset functionality
"""

def show_remaining_indexes():
    """Show the remaining indexes that need to be created"""
    
    print("✅ Index 1 COMPLETED: password_reset_tokens (email + created_at)")
    print("\n🔄 REMAINING INDEXES TO CREATE:\n")
    
    print("Index 2: Collection: password_reset_tokens")
    print("   Fields to add:")
    print("   - user_id (Ascending)")
    print("   - used (Ascending)")
    print("   - __name__ (Ascending) [automatically added]")
    print()
    
    print("Index 3: Collection: password_reset_tokens")
    print("   Fields to add:")
    print("   - token (Ascending)")
    print("   - used (Ascending)")
    print("   - __name__ (Ascending) [automatically added]")
    print()
    
    print("📝 HOW TO CREATE:")
    print("1. Go to: https://console.firebase.google.com/project/drcr-d660a/firestore/indexes")
    print("2. Click 'Add index' button")
    print("3. Select Collection: password_reset_tokens")
    print("4. Add the fields as shown above")
    print("5. Set Query scope to 'Collection'")
    print("6. Click 'Create index'")
    print()
    print("⏱️  Each index takes 2-5 minutes to build")
    print("🔄 You can create both indexes simultaneously")

if __name__ == "__main__":
    show_remaining_indexes() 