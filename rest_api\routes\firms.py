from fastapi import APIRouter, Depends, HTTPException, status, Path
from typing import Dict, Any
from google.cloud import firestore
import logging

from ..core.firebase_auth import get_current_user, AuthUser
from ..dependencies import get_db
from ..models.auth import FirmModel

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/{firm_id}")
async def get_firm_details(
    firm_id: str = Path(..., description="Firm ID"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Get firm details by firm ID.
    Users can only access details for their own firm.
    """
    try:
        # Check if user has access to this firm
        if current_user.firm_id != firm_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only access your own firm's details"
            )
        
        # Get firm document from Firestore
        firm_ref = db.collection("FIRMS").document(firm_id)
        firm_doc = await firm_ref.get()
        
        if not firm_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Firm not found"
            )
        
        firm_data = firm_doc.to_dict()
        
        # Return firm details
        return {
            "firm_id": firm_data.get("firm_id"),
            "name": firm_data.get("name"),
            "status": firm_data.get("status", "active"),
            "subscription_tier": firm_data.get("subscription_tier", "standard"),
            "contact_email": firm_data.get("contact_email"),
            "contact_phone": firm_data.get("contact_phone"),
            "address": firm_data.get("address"),
            "settings": firm_data.get("settings"),
            "created_at": firm_data.get("created_at"),
            "updated_at": firm_data.get("updated_at")
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error fetching firm details for firm_id {firm_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve firm details"
        )


@router.get("/{firm_id}/summary")
async def get_firm_summary(
    firm_id: str = Path(..., description="Firm ID"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Get firm summary with basic statistics.
    Users can only access summary for their own firm.
    """
    try:
        # Check if user has access to this firm
        if current_user.firm_id != firm_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only access your own firm's summary"
            )
        
        # Get firm document
        firm_ref = db.collection("FIRMS").document(firm_id)
        firm_doc = await firm_ref.get()
        
        if not firm_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Firm not found"
            )
        
        firm_data = firm_doc.to_dict()
        
        # Get basic statistics
        # Count clients
        clients_query = db.collection("CLIENTS").where(filter=firestore.FieldFilter("firm_id", "==", firm_id))
        clients_docs = [doc async for doc in clients_query.stream()]
        client_count = len(clients_docs)
        
        # Count users
        users_query = db.collection("FIRM_USERS").where(filter=firestore.FieldFilter("firm_id", "==", firm_id))
        users_docs = [doc async for doc in users_query.stream()]
        user_count = len(users_docs)
        
        # Count entities (across all clients)
        entities_count = 0
        for client_doc in clients_docs:
            client_id = client_doc.id
            entities_query = db.collection("ENTITIES").where(filter=firestore.FieldFilter("client_id", "==", client_id))
            entities_docs = [doc async for doc in entities_query.stream()]
            entities_count += len(entities_docs)
        
        return {
            "firm_id": firm_data.get("firm_id"),
            "name": firm_data.get("name"),
            "status": firm_data.get("status", "active"),
            "subscription_tier": firm_data.get("subscription_tier", "standard"),
            "statistics": {
                "client_count": client_count,
                "user_count": user_count,
                "entity_count": entities_count
            },
            "created_at": firm_data.get("created_at"),
            "updated_at": firm_data.get("updated_at")
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error fetching firm summary for firm_id {firm_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve firm summary"
        )
