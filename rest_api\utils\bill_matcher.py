"""
Multi-Signal Bill Matching Utility

Implements fuzzy matching logic to link manual journals to originating bills
using multiple signals: company names, account codes, amounts, and dates.
"""
import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from rapidfuzz import fuzz
from .field_access import get_field
# Currency conversion now uses stored exchange rates from bills
from ..config.detector_config import (
    USE_SUBTOTAL_FOR_MATCHING,
    NAME_MATCHING_MIN_LENGTH_RATIO
)

logger = logging.getLogger(__name__)

# Stop words to ignore when extracting company names
COMPANY_STOP_WORDS = {
    'expense', 'charged', 'from', 'prepayment', 'to', 'p&l', 'amortization', 
    'amortisation', 'entry', 'jan', 'feb', 'mar', 'apr', 'may', 'jun',
    'jul', 'aug', 'sep', 'oct', 'nov', 'dec', 'january', 'february',
    'march', 'april', 'june', 'july', 'august', 'september', 'october',
    'november', 'december', '2024', '2025', '2026', '2027', '2028'
}

@dataclass
class MatchFeatures:
    """Features used for scoring manual journal to bill matches"""
    name_similarity: float = 0.0
    account_overlap: float = 0.0
    amount_ratio: float = 0.0
    date_validity: bool = False
    days_gap: int = 0
    has_exact_reference: bool = False
    extracted_tokens: List[str] = None
    match_reason: str = ""

@dataclass
class MatchResult:
    """Result of matching a manual journal to a bill"""
    bill_id: str
    confidence_score: float
    features: MatchFeatures
    should_link: bool = False

class BillMatcher:
    """Multi-signal matcher for linking manual journals to bills"""
    
    def __init__(self, 
                 name_weight: float = 0.4,
                 account_weight: float = 0.3, 
                 amount_weight: float = 0.2,
                 date_weight: float = 0.1,
                 min_confidence: float = 0.75,
                 max_days_gap: int = 545):  # ~18 months
        """
        Initialize matcher with configurable weights and thresholds
        
        Args:
            name_weight: Weight for company name similarity (0-1)
            account_weight: Weight for account code overlap (0-1)
            amount_weight: Weight for amount relationship (0-1)
            date_weight: Weight for date validity (0-1)
            min_confidence: Minimum confidence score to create link (0-1)
            max_days_gap: Maximum days between bill and manual journal
            
        Note: Currency conversion now uses stored exchange rates from bill data.
        """
        self.name_weight = name_weight
        self.account_weight = account_weight
        self.amount_weight = amount_weight
        self.date_weight = date_weight
        self.min_confidence = min_confidence
        self.max_days_gap = max_days_gap
        
        # Currency conversion now uses stored exchange rates from bills
        
        # Normalize weights to sum to 1.0
        total_weight = name_weight + account_weight + amount_weight + date_weight
        if total_weight != 1.0:
            self.name_weight /= total_weight
            self.account_weight /= total_weight
            self.amount_weight /= total_weight
            self.date_weight /= total_weight
    
    def extract_company_tokens(self, narration: str) -> List[str]:
        """
        Extract potential company names/tokens from manual journal narration
        
        Args:
            narration: Manual journal narration text
            
        Returns:
            List of extracted company tokens
        """
        if not narration:
            return []
        
        # Convert to lowercase for processing
        text = narration.lower()
        
        # Remove common punctuation and split into words
        text = re.sub(r'[^\w\s&]', ' ', text)
        words = text.split()
        
        # Filter out stop words and short words
        tokens = []
        for word in words:
            if (len(word) >= 3 and 
                word not in COMPANY_STOP_WORDS and
                not word.isdigit() and
                not re.match(r'\d+', word)):
                tokens.append(word)
        
        # Look for multi-word company names (2-3 consecutive tokens)
        company_tokens = []
        
        # Add individual tokens
        company_tokens.extend(tokens)
        
        # Add bigrams (2-word combinations)
        for i in range(len(tokens) - 1):
            bigram = f"{tokens[i]} {tokens[i+1]}"
            company_tokens.append(bigram)
        
        # Add trigrams for longer company names
        for i in range(len(tokens) - 2):
            trigram = f"{tokens[i]} {tokens[i+1]} {tokens[i+2]}"
            company_tokens.append(trigram)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_tokens = []
        for token in company_tokens:
            if token not in seen:
                seen.add(token)
                unique_tokens.append(token)
        
        return unique_tokens
    
    def compute_name_similarity(self, mj_tokens: List[str], bill_contact_name: str) -> float:
        """
        Compute similarity between extracted tokens and bill contact name
        
        Args:
            mj_tokens: Extracted company tokens from manual journal
            bill_contact_name: Contact name from bill
            
        Returns:
            Similarity score (0-1)
        """
        if not mj_tokens or not bill_contact_name:
            return 0.0
        
        bill_name_lower = bill_contact_name.lower()
        max_similarity = 0.0
        
        for token in mj_tokens:
            # Try exact substring match first
            if token in bill_name_lower:
                max_similarity = max(max_similarity, 1.0)
                continue
            
            # Special handling for common company name variations
            # Handle cases like "Zoom" -> "ZoomInfo", "LinkedIn" -> "LinkedIn Corporation"
            if len(token) >= 4:
                # Check if token is a prefix of any word in the bill name
                for bill_word in bill_name_lower.split():
                    if bill_word.startswith(token):
                        # Add length ratio guard to prevent false positives like "Box" -> "Dropbox"
                        length_ratio = len(token) / len(bill_word)
                        if length_ratio >= NAME_MATCHING_MIN_LENGTH_RATIO:
                            max_similarity = max(max_similarity, 0.95)  # High score for valid prefix match
                            break
                    elif token.startswith(bill_word) and len(bill_word) >= 4:
                        # Reverse prefix check with length ratio guard
                        length_ratio = len(bill_word) / len(token)
                        if length_ratio >= NAME_MATCHING_MIN_LENGTH_RATIO:
                            max_similarity = max(max_similarity, 0.90)  # Good score for valid reverse prefix
                            break
            
            # Use fuzzy matching
            similarity = fuzz.partial_ratio(token, bill_name_lower) / 100.0
            max_similarity = max(max_similarity, similarity)
        
        return max_similarity
    
    def _convert_currency_for_comparison(self, amount: float, from_currency: str, to_currency: str, bill: Dict[str, Any]) -> float:
        """
        Convert currency for bill matching comparison using stored exchange rates.
        
        Args:
            amount: Amount to convert
            from_currency: Source currency code (e.g., 'GBP')
            to_currency: Target currency code (e.g., 'USD')
            bill: Bill data containing stored exchange rate
            
        Returns:
            Converted amount for comparison purposes
            
        Note: Uses shared fx_utils for currency conversion logic.
        """
        # Import here to avoid circular imports
        from rest_api.utils.fx_utils import convert_to_base_currency_from_bill
        
        # TODO: Get entity base currency from settings instead of assuming GBP
        # Should be: base_currency = entity_settings.get('base_currency', 'GBP')
        # Requires refactoring bill matcher to accept entity_settings parameter
        # For now, assume GBP as base currency (works for current use case)
        base_currency = 'GBP'
        
        # Handle direct conversion to base currency
        if to_currency == base_currency:
            return convert_to_base_currency_from_bill(amount, from_currency, base_currency, bill)
        
        # Handle conversion from base currency to bill currency
        if from_currency == base_currency:
            # Get the exchange rate and invert it
            stored_rate = bill.get('currency_rate')
            rate_source = bill.get('currency_rate_source', 'unknown')
            
            if stored_rate and stored_rate > 0:
                try:
                    # Invert the rate for base -> bill currency conversion
                    inverted_rate = 1.0 / stored_rate
                    from rest_api.utils.fx_utils import convert_to_base_currency
                    return convert_to_base_currency(amount, from_currency, to_currency, inverted_rate, rate_source)
                except ZeroDivisionError:
                    logger.warning(f"Cannot invert zero exchange rate for {from_currency} -> {to_currency}")
        
        # For other cases, use original logic or return amount unchanged
        logger.warning(f"No conversion available for {from_currency} -> {to_currency}. Returning original amount.")
        return amount
    
    def compute_account_overlap(self, mj_accounts: List[str], bill_accounts: List[str], 
                               asset_accounts: set) -> float:
        """
        Compute account code overlap score with asset account weighting
        
        Args:
            mj_accounts: Account codes from manual journal lines
            bill_accounts: Account codes from bill line items
            asset_accounts: Set of asset/prepayment account codes
            
        Returns:
            Account overlap score (0-1)
        """
        if not mj_accounts or not bill_accounts:
            return 0.0
        
        mj_set = set(mj_accounts)
        bill_set = set(bill_accounts)
        
        # Calculate overlap
        overlap = mj_set.intersection(bill_set)
        if not overlap:
            return 0.0
        
        # Weight asset accounts higher
        asset_overlap = overlap.intersection(asset_accounts)
        regular_overlap = overlap - asset_overlap
        
        # Asset accounts get 1.0 score, regular accounts get 0.7
        score = 0.0
        if asset_overlap:
            score += len(asset_overlap) * 1.0
        if regular_overlap:
            score += len(regular_overlap) * 0.7
        
        # Normalize by overlap count to reward strong matches
        # Use overlap size as denominator to avoid penalizing decisive asset matches
        overlap_count = len(overlap)
        return min(score / overlap_count, 1.0)
    
    def compute_amount_relationship(self, mj_amount: float, bill_amount: float) -> float:
        """
        Compute amount relationship score looking for rational fractions
        
        Args:
            mj_amount: Manual journal amount (absolute value)
            bill_amount: Bill total amount
            
        Returns:
            Amount relationship score (0-1)
        """
        if not mj_amount or not bill_amount or abs(bill_amount) < 1e-6:
            return 0.0
        
        # Make ratio symmetric (always >= 1.0)
        ratio = max(abs(mj_amount), abs(bill_amount)) / min(abs(mj_amount), abs(bill_amount))
        
        # Check for exact match
        if abs(ratio - 1.0) < 0.02:  # Within 2%
            return 1.0
        
        # Check for common rational fractions (monthly, quarterly, etc.)
        # Include both directions now that ratio is symmetric
        common_fractions = [
            (12.0, 0.9),   # Monthly (mj = 1/12 of bill)
            (6.0, 0.9),    # Bi-monthly  
            (4.0, 0.9),    # Quarterly
            (3.0, 0.9),    # Tri-annual
            (2.0, 0.9),    # Semi-annual or double payment
            (1.5, 0.8),    # 1.5x relationship
            (1.33, 0.8),   # 4/3 relationship
            (1.25, 0.8),   # 5/4 relationship
        ]
        
        for target_ratio, score in common_fractions:
            if abs(ratio - target_ratio) < 0.05:  # Within 5%
                return score
        
        # Linear scaling for partial matches
        if 1.0 <= ratio <= 20.0:  # Reasonable range
            # Scale linearly: ratio 1.0 → 1.0, ratio 20.0 → 0.1
            linear_score = max(0.1, 1.0 - (ratio - 1.0) / 19.0 * 0.9)
            return linear_score
        
        return 0.0
    
    def compute_date_validity(self, mj_date: datetime, bill_date: datetime) -> Tuple[bool, int]:
        """
        Check if manual journal date is valid relative to bill date
        
        Args:
            mj_date: Manual journal date
            bill_date: Bill date
            
        Returns:
            Tuple of (is_valid, days_gap)
        """
        if not mj_date or not bill_date:
            return False, 0
        
        # Convert to date objects if they're datetime
        if hasattr(mj_date, 'date'):
            mj_date = mj_date.date()
        if hasattr(bill_date, 'date'):
            bill_date = bill_date.date()
        
        days_gap = (mj_date - bill_date).days
        
        # Manual journal should be after bill date but not too far in future
        is_valid = 0 <= days_gap <= self.max_days_gap
        
        return is_valid, days_gap
    
    def compute_features(self, manual_journal: Dict[str, Any], bill: Dict[str, Any], 
                        asset_accounts: set) -> MatchFeatures:
        """
        Compute all matching features for a manual journal and bill pair
        
        Args:
            manual_journal: Manual journal data
            bill: Bill/transaction data
            asset_accounts: Set of asset account codes
            
        Returns:
            MatchFeatures object with all computed features
        """
        features = MatchFeatures()
        
        # Get journal and bill IDs for debugging
        mj_id = manual_journal.get('journal_id') or manual_journal.get('ManualJournalID', 'unknown')
        bill_id = bill.get('transaction_id') or bill.get('id', 'unknown')
        
        logger.debug(f"=== FEATURE COMPUTATION DEBUG: MJ {mj_id} vs Bill {bill_id} ===")
        
        # Extract data with detailed logging
        narration = manual_journal.get('narration', '') or manual_journal.get('Narration', '')
        mj_date_raw = manual_journal.get('date') or manual_journal.get('journalDate')
        bill_contact = bill.get('contact_name', '')
        bill_date_raw = bill.get('date_issued')
        
        logger.debug(f"Raw data extraction:")
        logger.debug(f"  MJ narration: '{narration}'")
        logger.debug(f"  MJ date raw: {mj_date_raw} (type: {type(mj_date_raw)})")
        logger.debug(f"  Bill contact: '{bill_contact}'")
        logger.debug(f"  Bill date raw: {bill_date_raw} (type: {type(bill_date_raw)})")
        
        # Manual journal accounts
        mj_lines = manual_journal.get('journal_lines', []) or manual_journal.get('JournalLines', [])
        mj_accounts = [line.get('AccountCode', '') for line in mj_lines if line.get('AccountCode')]
        
        logger.debug(f"MJ lines data:")
        logger.debug(f"  Raw lines count: {len(mj_lines)}")
        logger.debug(f"  Raw lines: {mj_lines}")
        logger.debug(f"  Extracted accounts: {mj_accounts}")
        
        # Bill accounts  
        bill_lines = bill.get('line_items', [])
        bill_accounts = [line.get('AccountCode', '') for line in bill_lines if line.get('AccountCode')]
        
        logger.debug(f"Bill lines data:")
        logger.debug(f"  Raw lines count: {len(bill_lines)}")
        logger.debug(f"  Raw lines: {bill_lines}")
        logger.debug(f"  Extracted accounts: {bill_accounts}")
        logger.debug(f"  Asset accounts for comparison: {asset_accounts}")
        
        # Extract company tokens
        features.extracted_tokens = self.extract_company_tokens(narration)
        logger.debug(f"Company tokens extracted: {features.extracted_tokens}")
        
        # Compute name similarity
        features.name_similarity = self.compute_name_similarity(features.extracted_tokens, bill_contact)
        logger.debug(f"Name similarity computed: {features.name_similarity}")
        
        # Compute account overlap
        features.account_overlap = self.compute_account_overlap(mj_accounts, bill_accounts, asset_accounts)
        logger.debug(f"Account overlap computed: {features.account_overlap}")
        
        # Compute amount relationship with currency conversion
        # Calculate amount from journal lines since 'amount' field is often None
        # Use the maximum absolute line amount to avoid double counting
        mj_amount_raw = 0
        for line in manual_journal.get('journal_lines', []):
            line_amount = abs(line.get('LineAmount', 0) or 0)
            mj_amount_raw = max(mj_amount_raw, line_amount)
        
        if mj_amount_raw == 0:
            mj_amount_raw = manual_journal.get('amount', 0)  # Fallback to amount field
        
        # Use subtotal (pre-tax) or total based on configuration
        if USE_SUBTOTAL_FOR_MATCHING:
            bill_amount_raw = bill.get('subtotal', 0) or bill.get('total_amount', 0)  # Fallback to total if subtotal missing
            amount_type = "subtotal"
        else:
            bill_amount_raw = bill.get('total_amount', 0)
            amount_type = "total"
        
        # Get currency codes
        mj_currency = manual_journal.get('currency', 'GBP')  # Default to GBP for manual journals
        bill_currency = bill.get('currency_code', 'GBP')
        
        logger.debug(f"Amount data:")
        logger.debug(f"  MJ amount raw: {mj_amount_raw} (currency: {mj_currency}) (type: {type(mj_amount_raw)})")
        logger.debug(f"  Bill {amount_type} raw: {bill_amount_raw} (currency: {bill_currency}) (type: {type(bill_amount_raw)})")
        logger.debug(f"  Using {amount_type} for matching (USE_SUBTOTAL_FOR_MATCHING={USE_SUBTOTAL_FOR_MATCHING})")
        
        # Apply currency conversion if needed
        mj_amount_converted = self._convert_currency_for_comparison(mj_amount_raw, mj_currency, bill_currency, bill)
        
        logger.debug(f"  MJ amount converted: {mj_amount_converted} ({mj_currency} -> {bill_currency})")
        
        features.amount_ratio = self.compute_amount_relationship(mj_amount_converted, bill_amount_raw)
        logger.debug(f"Amount ratio computed: {features.amount_ratio}")
        
        # Compute date validity with enhanced debugging
        mj_date_parsed = None
        bill_date_parsed = None
        
        if mj_date_raw and bill_date_raw:
            try:
                # Parse MJ date
                if isinstance(mj_date_raw, str):
                    mj_date_parsed = datetime.fromisoformat(mj_date_raw.replace('Z', '+00:00'))
                elif hasattr(mj_date_raw, 'date'):  # Firestore timestamp or datetime
                    mj_date_parsed = mj_date_raw if isinstance(mj_date_raw, datetime) else mj_date_raw.date()
                else:
                    mj_date_parsed = mj_date_raw
                    
                # Parse bill date
                if isinstance(bill_date_raw, str):
                    bill_date_parsed = datetime.fromisoformat(bill_date_raw.replace('Z', '+00:00'))
                elif hasattr(bill_date_raw, 'date'):  # Firestore timestamp or datetime
                    bill_date_parsed = bill_date_raw if isinstance(bill_date_raw, datetime) else bill_date_raw.date()
                else:
                    bill_date_parsed = bill_date_raw
                
                logger.debug(f"Date parsing results:")
                logger.debug(f"  MJ date parsed: {mj_date_parsed} (type: {type(mj_date_parsed)})")
                logger.debug(f"  Bill date parsed: {bill_date_parsed} (type: {type(bill_date_parsed)})")
                
                features.date_validity, features.days_gap = self.compute_date_validity(mj_date_parsed, bill_date_parsed)
                logger.debug(f"Date validity: {features.date_validity}, days gap: {features.days_gap}")
                
            except Exception as e:
                logger.error(f"Date parsing error: {e}")
                features.date_validity = False
                features.days_gap = 0
        else:
            logger.debug(f"Date validation skipped - missing dates (MJ: {bool(mj_date_raw)}, Bill: {bool(bill_date_raw)})")
            features.date_validity = False
            features.days_gap = 0
        
        # Check for exact reference (fallback to legacy logic)
        bill_ref = bill.get('reference', '') or bill.get('document_number', '')
        logger.debug(f"Reference check: bill_ref='{bill_ref}', narration='{narration}'")
        
        if bill_ref and bill_ref.lower() in narration.lower():
            features.has_exact_reference = True
            logger.debug(f"Exact reference match found!")
        
        logger.debug(f"=== FINAL FEATURES: name={features.name_similarity:.3f}, account={features.account_overlap:.3f}, amount={features.amount_ratio:.3f}, date={features.date_validity} ===")
        
        return features
    
    def score_match(self, features: MatchFeatures) -> float:
        """
        Compute final confidence score from features
        
        Args:
            features: Computed match features
            
        Returns:
            Confidence score (0-1)
        """
        # Exact reference gets maximum score
        if features.has_exact_reference:
            return 1.0
        
        # Guard: Require at least one non-name feature to prevent matches based solely on name similarity
        # This prevents false positives from common company names
        has_non_name_feature = (
            features.account_overlap > 0.0 or 
            features.amount_ratio > 0.0 or 
            features.date_validity
        )
        if not has_non_name_feature:
            return 0.0
        
        # Weighted combination of features
        score = (
            features.name_similarity * self.name_weight +
            features.account_overlap * self.account_weight +
            features.amount_ratio * self.amount_weight +
            (1.0 if features.date_validity else 0.0) * self.date_weight
        )
        
        # Bonus for very high name similarity
        if features.name_similarity >= 0.9:
            score += 0.1
        
        # Penalty for invalid dates
        if not features.date_validity:
            score *= 0.8
        
        return min(score, 1.0)
    
    def find_best_match(self, manual_journal: Dict[str, Any], candidate_bills: List[Dict[str, Any]], 
                       asset_accounts: set) -> Optional[MatchResult]:
        """
        Find the best matching bill for a manual journal
        
        Args:
            manual_journal: Manual journal data
            candidate_bills: List of candidate bills to match against
            asset_accounts: Set of asset account codes
            
        Returns:
            MatchResult if match found above threshold, None otherwise
        """
        if not candidate_bills:
            return None
        
        best_match = None
        best_score = 0.0
        
        journal_id = manual_journal.get('journal_id') or manual_journal.get('ManualJournalID')
        
        for bill in candidate_bills:
            bill_id = bill.get('transaction_id') or bill.get('id')
            
            # Compute features
            features = self.compute_features(manual_journal, bill, asset_accounts)
            
            # Score the match
            score = self.score_match(features)
            
            # Log detailed features for debugging
            logger.debug(f"Matching MJ {journal_id} to Bill {bill_id}:")
            logger.debug(f"  Name similarity: {features.name_similarity:.3f} (tokens: {features.extracted_tokens})")
            logger.debug(f"  Account overlap: {features.account_overlap:.3f}")
            logger.debug(f"  Amount ratio: {features.amount_ratio:.3f}")
            logger.debug(f"  Date valid: {features.date_validity} (gap: {features.days_gap} days)")
            logger.debug(f"  Exact reference: {features.has_exact_reference}")
            logger.debug(f"  Final score: {score:.3f}")
            
            if score > best_score:
                best_score = score
                features.match_reason = f"Best of {len(candidate_bills)} candidates"
                best_match = MatchResult(
                    bill_id=bill_id,
                    confidence_score=score,
                    features=features,
                    should_link=score >= self.min_confidence
                )
        
        if best_match:
            logger.info(f"Best match for MJ {journal_id}: Bill {best_match.bill_id} "
                       f"(score: {best_match.confidence_score:.3f}, "
                       f"link: {best_match.should_link})")
        
        return best_match