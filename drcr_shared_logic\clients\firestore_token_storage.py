import json
import logging
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from google.cloud import firestore
from cryptography.fernet import Fernet
import base64
import os

class FirestoreTokenStorage:
    """
    Cost-effective token storage using Firestore with encryption.
    Much cheaper than Secret Manager for frequently accessed OAuth tokens.
    """

    def __init__(self, gcp_project_id: str, encryption_key: Optional[str] = None):
        self.gcp_project_id = gcp_project_id
        self.db = firestore.Client(project=gcp_project_id)
        self.logger = logging.getLogger(__name__)

        # Initialize encryption
        if encryption_key:
            self.cipher = Fernet(encryption_key.encode())
        else:
            # Generate or get encryption key from environment
            key = os.getenv('TOKEN_ENCRYPTION_KEY')
            if not key:
                # Generate a new key (store this securely!)
                key = Fernet.generate_key().decode()
                self.logger.warning(f"Generated new encryption key. Store this securely: {key}")
            self.cipher = Fernet(key.encode())

    def _encrypt_data(self, data: str) -> str:
        """Encrypt sensitive token data"""
        return self.cipher.encrypt(data.encode()).decode()

    def _decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive token data"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()

    async def save_tokens(self, app_tenant_id: str, platform_org_id: str,
                         access_token: str, refresh_token: str,
                         expires_at: datetime) -> bool:
        """
        Save OAuth tokens to Firestore with encryption.

        Args:
            app_tenant_id: Your application's tenant ID
            platform_org_id: Platform organization ID (e.g., Xero tenant ID)
            access_token: OAuth access token
            refresh_token: OAuth refresh token
            expires_at: Token expiration datetime
        """
        try:
            # Encrypt sensitive data
            encrypted_access_token = self._encrypt_data(access_token)
            encrypted_refresh_token = self._encrypt_data(refresh_token)

            token_data = {
                'access_token': encrypted_access_token,
                'refresh_token': encrypted_refresh_token,
                'expires_at': expires_at,
                'updated_at': datetime.now(timezone.utc),
                'platform': 'xero'  # For future multi-platform support
            }

            # Store in Firestore: oauth_tokens/{app_tenant_id}/platforms/{platform_org_id}
            doc_ref = (self.db.collection('oauth_tokens')
                      .document(app_tenant_id)
                      .collection('platforms')
                      .document(platform_org_id))

            # Use merge=True for faster updates if document exists
            doc_ref.set(token_data, merge=True)

            self.logger.info(f"Successfully saved encrypted tokens to Firestore for {app_tenant_id}/{platform_org_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to save tokens to Firestore: {e}", exc_info=True)
            return False

    async def load_tokens(self, app_tenant_id: str, platform_org_id: str) -> Optional[Dict[str, Any]]:
        """
        Load and decrypt OAuth tokens from Firestore.

        Returns:
            Dict with 'access_token', 'refresh_token', 'expires_at' or None if not found
        """
        try:
            doc_ref = (self.db.collection('oauth_tokens')
                      .document(app_tenant_id)
                      .collection('platforms')
                      .document(platform_org_id))

            doc = doc_ref.get()

            if not doc.exists:
                self.logger.info(f"No tokens found in Firestore for {app_tenant_id}/{platform_org_id}")
                return None

            data = doc.to_dict()

            # Decrypt sensitive data
            access_token = self._decrypt_data(data['access_token'])
            refresh_token = self._decrypt_data(data['refresh_token'])

            return {
                'access_token': access_token,
                'refresh_token': refresh_token,
                'expires_at': data['expires_at']
            }

        except Exception as e:
            self.logger.error(f"Failed to load tokens from Firestore: {e}", exc_info=True)
            return None

    async def delete_tokens(self, app_tenant_id: str, platform_org_id: str) -> bool:
        """Delete tokens from Firestore"""
        try:
            doc_ref = (self.db.collection('oauth_tokens')
                      .document(app_tenant_id)
                      .collection('platforms')
                      .document(platform_org_id))

            doc_ref.delete()

            self.logger.info(f"Successfully deleted tokens from Firestore for {app_tenant_id}/{platform_org_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to delete tokens from Firestore: {e}", exc_info=True)
            return False

    async def list_connected_platforms(self, app_tenant_id: str) -> list:
        """List all connected platforms for a tenant"""
        try:
            platforms_ref = (self.db.collection('oauth_tokens')
                           .document(app_tenant_id)
                           .collection('platforms'))

            docs = platforms_ref.stream()

            platforms = []
            for doc in docs:
                data = doc.to_dict()
                platforms.append({
                    'platform_org_id': doc.id,
                    'platform': data.get('platform', 'unknown'),
                    'updated_at': data.get('updated_at'),
                    'expires_at': data.get('expires_at')
                })

            return platforms

        except Exception as e:
            self.logger.error(f"Failed to list platforms from Firestore: {e}", exc_info=True)
            return []