# 🚨 URGENT: Backend API Changes - Action Required

## Critical Changes Made to Backend (2025-06-22)

### 1. **Schedule Status System Simplified**

**BREAKING CHANGES:**
- ❌ **REMOVED:** `pending_review` status → **Replace with `proposed`**
- ❌ **REMOVED:** `pending_confirmation` status → **Remove from all filters**  
- ❌ **REMOVED:** `POST /schedules/{scheduleId}/post-ready` endpoint → **Use `/confirm` instead**

**New Status Flow:**
```
pending_configuration → proposed → confirmed → posted
```

### 2. **API Field Names Fixed**

**NOW USE THESE SNAKE_CASE FIELD NAMES:**
```typescript
// ✅ CORRECT - Use these field names
{
  account_code: "1200",
  expense_account_code: "6100", 
  amount: 15000,
  start_date: "2025-01-01",
  end_date: "2025-12-31",
  description: "Updated description"
}

// ❌ OLD - Don't use these anymore
{
  amortizationAccountCode: "1200",  // Wrong
  expenseAccountCode: "6100",       // Wrong
  originalAmount: 15000,            // Wrong
  notes: "description"              // Wrong
}
```

### 3. **New Fields Added**

**Add these to your Schedule interface:**
```typescript
interface Schedule {
  // ... existing fields
  
  // NEW FIELDS:
  calculation_method?: "day_based" | "equal_monthly";
  detection_method?: "llm_only" | "gl_coding";
}
```

### 4. **Updated Endpoints**

**Schedule Metadata Update (account codes, description only):**
```typescript
// ✅ NEW WAY - Metadata only, does NOT change monthly entries
PUT /schedules/{scheduleId}
Body: {
  account_code?: string;
  expense_account_code?: string;
  description?: string;
  // ❌ DON'T use amount, start_date, end_date here - use /recalculate instead
}
```

**✅ NEW FUNCTIONALITY ADDED:**
```typescript
// ✅ NEW ENDPOINT - Update individual monthly entry amounts!
PUT /schedules/{scheduleId}/entries/{entryIndex}
Body: {
  amount: number;  // New amount for this specific month
}
Response: {
  success: boolean;
  message: string;
  updated_entry: {
    entry_index: number;
    amount: number;
    month_date: string;
    status: string;
  }
}

// Safe alternative to destructive recalculation:
// PUT /schedules/{scheduleId}/recalculate  // ⚠️ Still overwrites everything
```

**Schedule Actions:**
```typescript
// ✅ NEW WAY - Use confirm instead of post-ready
POST /schedules/{scheduleId}/confirm

// ❌ OLD WAY - This endpoint no longer exists
POST /schedules/{scheduleId}/post-ready  // 404 ERROR
```

### 5. **Status Filter Updates**

**Update your status filter arrays:**
```typescript
// ✅ NEW STATUS FILTERS
const actionableStatuses = ['pending_configuration', 'proposed'];

const allStatuses = [
  'pending_configuration',
  'proposed',           // ← NEW (replaces pending_review)
  'confirmed', 
  'posted',
  'skipped',
  'cancelled', 
  'error'
];

// ❌ REMOVE THESE
// 'pending_review'      ← Remove completely
// 'pending_confirmation' ← Remove completely
```

### 6. **LLM Detection Changes**

**Use detection_method instead of status:**
```typescript
// ✅ NEW WAY
const needsReview = schedule.detection_method === 'llm_only';
if (needsReview) {
  showWarning("⚠️ AI Detected - Please Review Carefully");
}

// ❌ OLD WAY
const needsReview = schedule.status === 'pending_review'; // Wrong
```

### 7. **Calculation Method Display**

**Show the new calculation method:**
```typescript
const methodLabel = schedule.calculation_method === 'day_based' 
  ? 'Day-based Distribution' 
  : 'Equal Monthly Distribution';
```

---

## 🔧 Required Frontend Changes Checklist

### **Immediate Fixes (API Calls):**
- [ ] Replace `/post-ready` with `/confirm` in BillsAmortizationPage
- [ ] Replace `/post-ready` with `/confirm` in PrepaymentsPage  
- [ ] Update schedule update API calls to use snake_case field names
- [ ] Remove `pending_confirmation` from all status filters
- [x] **RESOLVED:** Monthly entry update endpoint now available!
  - ✅ Use `PUT /schedules/{id}/entries/{index}` for individual monthly amount updates
  - ⚠️ Continue using `/recalculate` only for full schedule changes (amount, dates, method)
  - Update frontend to use new endpoint instead of workarounds

### **Status System Updates:**
- [ ] Replace `pending_review` with `proposed` in status filters
- [ ] Update status dropdown options
- [ ] Update dashboard status counts
- [ ] Update transaction filtering logic

### **Interface Updates:**
- [ ] Add `calculation_method` and `detection_method` to Schedule interface
- [ ] Update field names in forms to use snake_case
- [ ] Add calculation method display in schedule details
- [ ] Replace status-based LLM warnings with detection_method-based warnings

### **Testing:**
- [ ] Test schedule creation flow
- [ ] Test schedule editing with new field names  
- [ ] Test status transitions (proposed → confirmed → posted)
- [ ] Test LLM detection warnings
- [ ] Verify no 404 errors on `/post-ready` calls

---

## 📚 Full API Reference

See complete API documentation in `/CLAUDE.md` file for:
- All available endpoints
- Request/response examples  
- TypeScript interfaces
- Error handling
- Authentication setup

**Questions?** Check the updated API documentation in `CLAUDE.md` or ask the backend team.