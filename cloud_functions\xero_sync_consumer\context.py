"""
Shared Sync Context

Provides a shared context object containing all sync dependencies.
Used by all endpoint handlers for consistency.
"""

import logging
from typing import Dict, Any, List
from dataclasses import dataclass

from google.cloud import firestore
from drcr_shared_logic.clients.xero_client import XeroApiClient

from settings import Settings

logger = logging.getLogger(__name__)


@dataclass
class SyncContext:
    """Context object containing all sync dependencies and configuration."""
    
    # Core dependencies
    db: firestore.AsyncClient
    xero_client: XeroApiClient
    settings: Settings
    
    # Entity information
    entity_id: str
    client_id: str
    raw_entity_settings: Dict[str, Any]  # Direct from Firestore
    entity_settings: Dict[str, Any]      # Merged with global settings
    
    # Sync job information
    message_data: Dict[str, Any]
    force_full_sync_endpoints: List[str]
    sync_job_id: str
    
    # State tracking
    processed_prepayments_count: int = 0
    heavy_stage_triggered: bool = False
    
    def __post_init__(self):
        """Post-initialization to merge settings."""
        # Merge global settings with entity-specific overrides
        self.entity_settings = self.settings.get_entity_settings(self.raw_entity_settings)
        
        if self.settings.DEBUG:
            logger.debug(f"SyncContext initialized for entity {self.entity_id}")
    
    def get_entity_setting(self, key: str, default=None):
        """Get an entity setting with fallback to default."""
        return self.entity_settings.get(key, default)
    
    def use_two_stage_processing(self) -> bool:
        """Check if two-stage processing is enabled for this entity."""
        return self.get_entity_setting("_system_enableTwoStageSync", True)
    
    def get_scanning_threshold(self) -> float:
        """Get the scanning amount threshold for this entity."""
        return self.get_entity_setting("scanning_amount_threshold", 100.0)
    
    def should_sync_endpoint(self, endpoint_name: str, default: bool = True) -> bool:
        """Check if a specific endpoint should be synced based on entity settings."""
        setting_key = f"sync{endpoint_name}"
        return self.get_entity_setting(setting_key, default)