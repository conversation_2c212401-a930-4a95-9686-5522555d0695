import os
import logging
from typing import Dict, Any, Optional, List

from google.cloud import firestore
from drcr_shared_logic.clients.xero_client import XeroApiClient
from drcr_shared_logic.document_processor import (
    extract_data_with_openai_direct,
    is_direct_openai_attempt_sufficient,
    process_pdf_with_mistral_ocr,
    process_image_with_mistral_ocr,
    extract_structured_data_with_openai,
    post_process_extracted_invoice_data,
)

# Import helpers from the same package - consistent relative imports
from .sync_helpers import _upload_to_gcs, get_other_secret

logger = logging.getLogger(__name__)

async def _process_attachment_with_llm_fallback(
    attachment_bytes: bytes,
    mime_type: str,
    xero_transaction_data: Dict[str, Any],
    file_name_for_logging: str = "document",
    entity_id: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """
    Processes attachment with LLM using fallback logic:
    1. Try OpenAI direct extraction
    2. If validation fails, fall back to Mistral OCR + structured extraction
    """
    # Initialize fallback tracking variables
    fallback_reason = None
    openai_attempt_made = False
    openai_failure_details = None
    
    try:
        logger.info(f"Attempting OpenAI direct extraction for {file_name_for_logging}")
        openai_result = await extract_data_with_openai_direct(
            attachment_bytes, mime_type, file_name_for_logging, entity_id
        )
        openai_attempt_made = True
        
        if openai_result:
            validated_data = await post_process_extracted_invoice_data(
                openai_result, xero_transaction_data, file_name_for_logging
            )
            if is_direct_openai_attempt_sufficient(validated_data):
                logger.info(f"OpenAI direct extraction successful for {file_name_for_logging}")
                
                # NOW charge OpenAI credits since we know it worked and no fallback needed
                if entity_id:
                    from drcr_shared_logic.document_processor import get_client_id_from_entity, check_and_deduct_credits, get_document_page_count
                    client_id = await get_client_id_from_entity(entity_id)
                    if client_id:
                        page_count = get_document_page_count(attachment_bytes, mime_type)
                        credit_result = await check_and_deduct_credits(client_id, page_count, "openai", "direct_extraction")
                        
                        # Add billing info to validated_data
                        validated_data["_system_billing_info"] = {
                            "page_count": page_count,
                            "total_credits_deducted": credit_result.get("credits_deducted", 0),
                            "services_used": [credit_result],
                            "extraction_method": "openai_direct"
                        }
                
                validated_data["_system_extraction_method"] = "openai_direct"
                return validated_data
            else:
                # OpenAI extracted data but failed validation
                fallback_reason = "openai_validation_failed"
                
                # Get specific validation failure reason
                total_validation = validated_data.get("System_TotalValidation", {})
                if total_validation.get("match") is False:
                    fallback_reason = "openai_total_mismatch"
                    openai_failure_details = f"LLM: {total_validation.get('llm_extracted_total')}, Xero: {total_validation.get('xero_total_amount')}"
                
                # Check for missing critical fields
                critical_fields = ["InvoiceNumber", "Date", "Total"]
                missing_fields = [field for field in critical_fields if not validated_data.get(field)]
                if missing_fields:
                    fallback_reason = "openai_missing_fields"
                    openai_failure_details = f"Missing: {', '.join(missing_fields)}"
                
                logger.info(f"OpenAI direct extraction failed validation for {file_name_for_logging}: {fallback_reason}")
        else:
            # OpenAI returned None/empty result
            fallback_reason = "openai_extraction_failed"
            openai_failure_details = "OpenAI returned null or empty result"
            logger.info(f"OpenAI direct extraction returned no data for {file_name_for_logging}")
        
        logger.info(f"Falling back to Mistral OCR for {file_name_for_logging} (reason: {fallback_reason})")
        mistral_api_key = get_other_secret("MISTRAL_API_KEY")
        if not mistral_api_key:
            logger.warning(f"Mistral API key not available for fallback processing of {file_name_for_logging}")
            return None
        
        if mime_type == "application/pdf":
            mistral_result = await process_pdf_with_mistral_ocr(
                attachment_bytes, mistral_api_key, file_name_for_logging, entity_id
            )
        else:
            mistral_result = await process_image_with_mistral_ocr(
                attachment_bytes, mistral_api_key, file_name_for_logging, entity_id
            )
        
        if mistral_result and mistral_result.get("text"):
            # Mistral OCR already charged 1 credit per page (includes OpenAI structured extraction)
            page_count = mistral_result.get("_system_billing_info", {}).get("page_count", 1)
            
            # OpenAI structured extraction after OCR: NO ADDITIONAL CHARGE
            openai_billing = []
            if entity_id:
                from drcr_shared_logic.document_processor import get_client_id_from_entity, check_and_deduct_credits
                client_id = await get_client_id_from_entity(entity_id)
                if client_id:
                    # This should charge 0 credits (included in Mistral OCR cost)
                    openai_credit_result = await check_and_deduct_credits(client_id, page_count, "openai", "structured_extraction")
                    openai_billing.append(openai_credit_result)
            
            structured_data = await extract_structured_data_with_openai(
                mistral_result["text"], file_name_for_logging=file_name_for_logging
            )
            if structured_data:
                validated_data = await post_process_extracted_invoice_data(
                    structured_data, xero_transaction_data, file_name_for_logging
                )
                if validated_data:
                    logger.info(f"Mistral OCR + OpenAI structured extraction successful for {file_name_for_logging}")
                    
                    # Combine billing information from both services
                    mistral_billing = mistral_result.get("_system_billing_info", {})
                    all_services = mistral_billing.get("services_used", []) + openai_billing
                    
                    combined_billing = {
                        "page_count": mistral_billing.get("page_count", 1),
                        "total_credits_deducted": sum(s.get("credits_deducted", 0) for s in all_services),
                        "services_used": all_services,
                        "extraction_method": "mistral_ocr_structured"
                    }
                    
                    validated_data["_system_extraction_method"] = "mistral_ocr_structured"
                    validated_data["_system_ocr_text"] = mistral_result["text"][:1000]
                    validated_data["_system_billing_info"] = combined_billing
                    
                    # Add fallback tracking information
                    validated_data["_system_fallback_info"] = {
                        "fallback_reason": fallback_reason,
                        "openai_attempt_made": openai_attempt_made,
                        "openai_failure_details": openai_failure_details
                    }
                    
                    return validated_data
        
        logger.warning(f"Both OpenAI direct and Mistral OCR failed for {file_name_for_logging}")
        return None
        
    except Exception as e_process:
        # If we hit an exception before trying OpenAI, record that
        if not openai_attempt_made:
            fallback_reason = "openai_api_failure"
            openai_failure_details = f"Exception before OpenAI attempt: {str(e_process)}"
        
        logger.error(f"Error in LLM processing for {file_name_for_logging}: {e_process}", exc_info=True)
        return None

async def _fetch_and_process_attachments(
    xero_client: XeroApiClient,
    transaction_id: str,
    transaction_data: Dict[str, Any],
    entity_settings: Dict[str, Any],
    entity_id: str,
    db: firestore.AsyncClient, # Added db client for Firestore operations
    file_name_for_logging: str = "document"
) -> List[Dict[str, Any]]:
    """
    Fetches attachments from Xero and processes them with LLM for data extraction.
    Returns list of processed attachment data.
    """
    processed_attachments = []
    
    if not entity_settings.get("enable_llm_prepayment_detection", True):
        logger.info(f"LLM prepayment detection disabled for entity {entity_id}")
        return processed_attachments
    
    try:
        document_type = transaction_data.get("Type", "")
        if document_type == "ACCPAY":
            xero_transaction_type = "Bills"
        elif document_type == "SPEND":
            xero_transaction_type = "BankTransactions"
        else:
            xero_transaction_type = "Invoices" 
        
        logger.info(f"Fetching attachments for {xero_transaction_type}/{transaction_id}")
        attachments = await xero_client.get_attachments(transaction_id, xero_transaction_type)
        
        if not attachments:
            logger.info(f"No attachments found for {xero_transaction_type}/{transaction_id}")
            return processed_attachments
        
        logger.info(f"Found {len(attachments)} attachments for {xero_transaction_type}/{transaction_id}")
        
        for attachment in attachments:
            try:
                attachment_id = attachment.get("AttachmentID")
                file_name = attachment.get("FileName", "unknown")
                mime_type = attachment.get("MimeType", "application/octet-stream")
                file_size = attachment.get("ContentLength", 0)
                
                max_size_mb = entity_settings.get("max_attachment_size_mb", 30)
                if file_size > max_size_mb * 1024 * 1024:
                    logger.warning(f"Attachment {attachment_id} ({file_name}) is too large: {file_size} bytes")
                    continue
                
                supported_types = entity_settings.get("supported_attachment_types", [
                    "application/pdf", "image/jpeg", "image/png", "image/jpg"
                ])
                if mime_type not in supported_types:
                    logger.info(f"Attachment {attachment_id} ({file_name}) type {mime_type} not supported for LLM processing")
                    continue
                
                logger.info(f"Downloading attachment {attachment_id} ({file_name})")
                attachment_bytes = await xero_client.download_attachment(transaction_id, attachment_id, xero_transaction_type)
                
                if not attachment_bytes:
                    logger.warning(f"Failed to download attachment {attachment_id}")
                    continue
                
                processed_data = await _process_attachment_with_llm_fallback(
                    attachment_bytes, mime_type, transaction_data, f"{file_name_for_logging}_{file_name}", entity_id
                )
                
                if processed_data:
                    gcs_bucket = os.getenv("GCS_BUCKET_NAME", "drcr-attachments")  # Default bucket name
                    gcs_path = f"attachments/{entity_id}/{transaction_id}/{attachment_id}"
                    
                    try:
                        # Always attempt to upload to GCS
                        await _upload_to_gcs(gcs_bucket, gcs_path, attachment_bytes, mime_type)
                        processed_data["gcs_path"] = gcs_path
                        logger.info(f"Successfully uploaded attachment {attachment_id} to GCS: gs://{gcs_bucket}/{gcs_path}")
                    except Exception as e_gcs:
                        logger.error(f"Failed to upload attachment {attachment_id} to GCS: {e_gcs}")
                        # Still save the attachment record but without GCS path
                        processed_data["gcs_path"] = None
                    
                    # Extract billing information
                    billing_info = processed_data.get("_system_billing_info", {})
                    fallback_info = processed_data.get("_system_fallback_info", {})
                    
                    attachment_doc = {
                        "transaction_id": transaction_id,
                        "entity_id": entity_id,
                        "source_system": "XERO",
                        "external_id": attachment_id,
                        "file_name": file_name,
                        "mime_type": mime_type,
                        "file_size": file_size,
                        "gcs_path": processed_data.get("gcs_path"),  # Will be None if upload failed
                        "gcs_upload_status": "success" if processed_data.get("gcs_path") else "failed",
                        "llm_processed": True,
                        "llm_extraction_data": processed_data,
                        # Add billing summary
                        "page_count": billing_info.get("page_count", 1),
                        "billing_summary": {
                            "total_credits_deducted": billing_info.get("total_credits_deducted", 0),
                            "services_used": billing_info.get("services_used", []),
                            "extraction_method": billing_info.get("extraction_method", "unknown"),
                            "billing_processed_at": firestore.SERVER_TIMESTAMP
                        },
                        # Add fallback tracking information
                        "fallback_reason": fallback_info.get("fallback_reason"),
                        "openai_attempt_made": fallback_info.get("openai_attempt_made", False),
                        "openai_failure_details": fallback_info.get("openai_failure_details"),
                        "created_at": firestore.SERVER_TIMESTAMP,
                        "updated_at": firestore.SERVER_TIMESTAMP
                    }
                    
                    await db.collection("ATTACHMENTS").document(attachment_id).set(attachment_doc, merge=True)
                    processed_attachments.append(processed_data)
                    logger.info(f"Successfully processed attachment {attachment_id} ({file_name})")
                else:
                    logger.warning(f"Failed to process attachment {attachment_id} ({file_name}) with LLM")
                    
            except Exception as e_attachment:
                logger.error(f"Error processing attachment {attachment.get('AttachmentID', 'unknown')}: {e_attachment}", exc_info=True)
                continue
        
        logger.info(f"Successfully processed {len(processed_attachments)} attachments for {xero_transaction_type}/{transaction_id}")
        
    except Exception as e_fetch:
        logger.error(f"Error fetching attachments for transaction {transaction_id}: {e_fetch}", exc_info=True)
    
    return processed_attachments

async def _perform_combined_prepayment_analysis(
    transaction_data: Dict[str, Any],
    gl_prepayment_lines: List[Dict[str, Any]],
    processed_attachments: List[Dict[str, Any]],
    entity_settings: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Performs combined prepayment analysis using both GL coding and LLM extraction.
    Returns comprehensive prepayment analysis results.
    """
    analysis = {
        "gl_based_analysis_completed": True,
        "llm_based_analysis_completed": len(processed_attachments) > 0,
        "has_prepayment_line_items": len(gl_prepayment_lines) > 0,
        "detection_methods": [],
        "prepayment_line_items": [],
        "llm_detections": [],
        "confidence_score": 0.0,
        "recommended_action": "no_prepayment"
    }
    
    if gl_prepayment_lines:
        analysis["detection_methods"].append("GL_CODING")
        analysis["prepayment_line_items"] = [
            {
                "line_item_id": line.get("LineItemID"),
                "account_code": line.get("AccountCode"),
                "amount": line.get("LineAmount"),
                "description": line.get("Description"),
                "detection_method": "GL_CODING"
            } for line in gl_prepayment_lines
        ]
        analysis["confidence_score"] += 0.8
    
    llm_detected_prepayments = []
    best_service_period = None
    highest_confidence = 0.0
    
    for attachment_data in processed_attachments:
        if attachment_data.get("System_IsPrepayment"):
            llm_detected_prepayments.append({
                "extraction_method": attachment_data.get("_system_extraction_method"),
                "is_prepayment": attachment_data.get("System_IsPrepayment"),
                "prepayment_reason": attachment_data.get("System_PrepaymentReason"),
                "service_start_date": attachment_data.get("ExpectedServiceStartDate"),
                "service_end_date": attachment_data.get("ExpectedServiceEndDate"),
                "service_period_source": attachment_data.get("_system_servicePeriodSource"),
                "confidence": attachment_data.get("_system_servicePeriodInferenceConfidence", "unknown"),
                "total_validation": attachment_data.get("System_TotalValidation", {})
            })
            
            confidence_map = {"high": 0.9, "medium": 0.6, "low": 0.3}
            current_confidence = confidence_map.get(
                attachment_data.get("_system_servicePeriodInferenceConfidence", "low"), 0.3
            )
            
            if current_confidence > highest_confidence:
                highest_confidence = current_confidence
                best_service_period = {
                    "start_date": attachment_data.get("ExpectedServiceStartDate"),
                    "end_date": attachment_data.get("ExpectedServiceEndDate"),
                    "source": attachment_data.get("_system_servicePeriodSource"),
                    "confidence": attachment_data.get("_system_servicePeriodInferenceConfidence")
                }
    
    if llm_detected_prepayments:
        analysis["detection_methods"].append("LLM_ANALYSIS")
        analysis["llm_detections"] = llm_detected_prepayments
        analysis["llm_based_analysis_completed"] = True  # Update completion flag when we have actual detections
        analysis["confidence_score"] += highest_confidence
        analysis["best_service_period"] = best_service_period
    
    if gl_prepayment_lines and llm_detected_prepayments:
        analysis["recommended_action"] = "create_amortization_schedule"
        analysis["confidence_score"] = min(analysis["confidence_score"], 1.0)
    elif gl_prepayment_lines:
        analysis["recommended_action"] = "create_amortization_schedule"
    elif llm_detected_prepayments and highest_confidence >= 0.6:
        # Create amortization schedule for high-confidence LLM detections
        # These will be marked as "llm_detected" for review
        analysis["recommended_action"] = "create_amortization_schedule"
    else:
        analysis["recommended_action"] = "no_prepayment"
    
    return analysis 