from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import time
import logging
import os
from contextlib import asynccontextmanager

# Import routers
from rest_api.routes.auth import router as auth_router
from rest_api.routes.clients import router as clients_router
from rest_api.routes.entities import router as entities_router
from rest_api.routes.firms import router as firms_router
from rest_api.routes.xero import router as xero_router
from rest_api.routes.transactions import router as transactions_router
from rest_api.routes.reports import router as reports_router
from rest_api.routes.schedules import router as schedules_router
from rest_api.routes.invoices import router as invoices_router
from rest_api.routes.attachments import router as attachments_router
from rest_api.routes.contacts import router as contacts_router
from rest_api.routes.audit import router as audit_router
from rest_api.routes.sync import router as sync_router
from rest_api.routes.manual_journals import router as manual_journals_router

# Import dependencies
from rest_api.dependencies import get_global_db_client

logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events"""
    # Startup
    logger.info("Starting DRCR Backend API...")
    
    # Initialize global database client
    try:
        db_client = get_global_db_client()
        logger.info("Global database client initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database client: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down DRCR Backend API...")

# Create FastAPI app with lifespan manager
app = FastAPI(
    title="DRCR Backend API",
    description="Backend API for the DRCR (Debit Reconciliation) system",
    version="1.0.0",
    lifespan=lifespan,
    # Performance optimizations
    docs_url="/docs" if os.getenv("API_DEBUG", "false").lower() == "true" else None,
    redoc_url="/redoc" if os.getenv("API_DEBUG", "false").lower() == "true" else None,
    openapi_url="/openapi.json" if os.getenv("API_DEBUG", "false").lower() == "true" else None
)

# Add compression middleware (should be added early)
app.add_middleware(
    GZipMiddleware, 
    minimum_size=1000,  # Only compress responses larger than 1KB
    compresslevel=6     # Balance between compression ratio and speed
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",  # Vite dev server
        "http://localhost:5174",  # Alternative Vite port
        "http://localhost:3000",  # Alternative dev server
        "https://drcr-d660a.web.app",  # Firebase hosting
        "https://drcr-d660a.firebaseapp.com"  # Firebase hosting alternative
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Performance monitoring middleware
@app.middleware("http")
async def performance_middleware(request: Request, call_next):
    """Add performance monitoring and caching headers"""
    start_time = time.time()
    
    # Add request ID for tracing
    request_id = f"{int(start_time * 1000)}-{hash(str(request.url)) % 10000}"
    
    response = await call_next(request)
    
    # Calculate processing time
    process_time = time.time() - start_time
    
    # Add performance headers
    response.headers["X-Process-Time"] = str(process_time)
    response.headers["X-Request-ID"] = request_id
    
    # Add caching headers for GET requests
    if request.method == "GET":
        # Cache static data for longer periods
        if any(path in str(request.url) for path in ["/health", "/auth/me"]):
            response.headers["Cache-Control"] = "public, max-age=60"  # 1 minute
        elif any(path in str(request.url) for path in ["/clients/summary", "/entities"]):
            response.headers["Cache-Control"] = "public, max-age=30"  # 30 seconds
        else:
            response.headers["Cache-Control"] = "public, max-age=10"  # 10 seconds default
    else:
        # No caching for mutations
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
    
    # Add security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    # Allow framing for attachment endpoints only
    if "/attachments/" not in str(request.url):
        response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    
    # Log slow requests
    if process_time > 1.0:  # Log requests taking more than 1 second
        logger.warning(f"Slow request: {request.method} {request.url} took {process_time:.2f}s")
    
    return response

# Health check endpoint (optimized)
@app.get("/health")
async def health_check():
    """Fast health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": int(time.time()),
        "version": "1.0.0"
    }

# Include routers with optimized prefixes
app.include_router(auth_router, prefix="/auth", tags=["Authentication"])
app.include_router(clients_router, prefix="/clients", tags=["Clients"])
app.include_router(entities_router, prefix="/entities", tags=["Entities"])
app.include_router(firms_router, prefix="/firms", tags=["Firms"])
app.include_router(xero_router, prefix="/xero", tags=["Xero Integration"])
app.include_router(transactions_router, prefix="/transactions", tags=["Transactions"])
app.include_router(reports_router, prefix="/reports", tags=["Reports"])
app.include_router(schedules_router, prefix="/schedules", tags=["Schedules"])
app.include_router(invoices_router, prefix="/invoices", tags=["Invoices"])
app.include_router(attachments_router, prefix="/attachments", tags=["Attachments"])
app.include_router(contacts_router, prefix="/contacts", tags=["Contacts"])
app.include_router(audit_router, prefix="/audit", tags=["Audit Logs"])
app.include_router(sync_router, prefix="/sync", tags=["Sync Operations"])
app.include_router(manual_journals_router, tags=["Manual Journals"])

# Global exception handler for better error responses
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler with performance logging"""
    logger.error(f"Unhandled exception in {request.method} {request.url}: {str(exc)}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "detail": "Internal server error",
            "request_id": request.headers.get("X-Request-ID", "unknown"),
            "timestamp": int(time.time())
        }
    )

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with basic API information"""
    return {
        "message": "DRCR Backend API",
        "version": "1.0.0",
        "docs": "/docs" if os.getenv("API_DEBUG", "false").lower() == "true" else "Documentation disabled in production",
        "health": "/health"
    } 