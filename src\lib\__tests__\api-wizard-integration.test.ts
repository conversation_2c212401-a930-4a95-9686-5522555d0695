import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ApiClient } from '../api';

// Mock axios
vi.mock('axios', () => ({
  default: {
    create: vi.fn(() => ({
      defaults: { baseURL: 'http://localhost:8081' },
      interceptors: {
        request: { use: vi.fn() },
        response: { use: vi.fn() },
      },
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
    })),
  },
}));

// Mock Firebase auth
vi.mock('../firebase', () => ({
  auth: {
    currentUser: {
      getIdToken: vi.fn().mockResolvedValue('mock-token'),
    },
  },
}));

describe('API Client Wizard Integration', () => {
  let apiClient: ApiClient;
  let mockAxiosInstance: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    const axios = require('axios');
    mockAxiosInstance = axios.default.create();
    
    apiClient = new ApiClient();
  });

  describe('getEntityWizardAnalysis', () => {
    it('calls the correct endpoint with proper parameters', async () => {
      const mockResponse = {
        entity_id: 'test-entity-123',
        entity_name: 'Test Company Ltd',
        organization_info: {
          name: 'Test Company Ltd',
          base_currency: 'GBP',
        },
        bills_analysis: {
          bills_with_attachments: 25,
          total_bills: 150,
          scanning_cost_estimate: 'Variable (1 credit per page - most bills are 1-3 pages)',
          expected_prepayments: '5-8 items',
          financial_year_start: '2024-04-01',
        },
        accounts_analysis: {
          prepayment_asset_accounts: [
            { code: '1200', name: 'Prepaid Insurance', recommended: true },
          ],
          relevant_expense_accounts: [
            { code: '6200', name: 'Insurance Expense' },
          ],
          suggested_exclusions: [
            { code: '6300', name: 'Bank Fees' },
          ],
        },
        recommendations: {
          sync_start_date: '2024-04-01',
          sync_frequency: 'daily',
          enable_ai_scanning: true,
          selected_asset_accounts: ['1200'],
          excluded_accounts: ['6300'],
          base_currency: 'GBP',
        },
      };

      mockAxiosInstance.get.mockResolvedValue({ data: mockResponse });

      const result = await apiClient.getEntityWizardAnalysis('test-entity-123');

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/entities/test-entity-123/analysis/wizard', undefined);
      expect(result).toEqual(mockResponse);
    });

    it('handles API errors gracefully', async () => {
      const mockError = new Error('Network error');
      mockAxiosInstance.get.mockRejectedValue(mockError);

      await expect(apiClient.getEntityWizardAnalysis('test-entity-123')).rejects.toThrow('Network error');
    });

    it('validates response structure', async () => {
      const mockResponse = {
        entity_id: 'test-entity-123',
        entity_name: 'Test Company Ltd',
        organization_info: { name: 'Test Company Ltd', base_currency: 'GBP' },
        bills_analysis: {
          bills_with_attachments: 25,
          total_bills: 150,
          scanning_cost_estimate: 'Variable (1 credit per page)',
          expected_prepayments: '5-8 items',
          financial_year_start: '2024-04-01',
        },
        accounts_analysis: {
          prepayment_asset_accounts: [],
          relevant_expense_accounts: [],
          suggested_exclusions: [],
        },
        recommendations: {
          sync_start_date: '2024-04-01',
          sync_frequency: 'daily',
          enable_ai_scanning: true,
          selected_asset_accounts: [],
          excluded_accounts: [],
          base_currency: 'GBP',
        },
      };

      mockAxiosInstance.get.mockResolvedValue({ data: mockResponse });

      const result = await apiClient.getEntityWizardAnalysis('test-entity-123');

      // Validate required fields exist
      expect(result).toHaveProperty('entity_id');
      expect(result).toHaveProperty('entity_name');
      expect(result).toHaveProperty('organization_info');
      expect(result).toHaveProperty('bills_analysis');
      expect(result).toHaveProperty('accounts_analysis');
      expect(result).toHaveProperty('recommendations');

      // Validate nested structures
      expect(result.bills_analysis).toHaveProperty('bills_with_attachments');
      expect(result.bills_analysis).toHaveProperty('scanning_cost_estimate');
      expect(result.recommendations).toHaveProperty('enable_ai_scanning');
      expect(result.recommendations).toHaveProperty('sync_start_date');
    });
  });

  describe('completeEntitySetup', () => {
    it('sends setup data to correct endpoint', async () => {
      const setupData = {
        enable_ai_scanning: true,
        sync_settings: {
          sync_start_date: '2024-04-01',
          sync_frequency: 'daily',
          auto_sync_enabled: true,
          sync_invoices: true,
          sync_bills: true,
          sync_payments: true,
          sync_bank_transactions: true,
          sync_journal_entries: true,
          sync_spend_money: true,
        },
        account_settings: {
          prepayment_asset_accounts: ['1200', '1201'],
          excluded_accounts: ['6300'],
          default_expense_account: '6200',
          base_currency: 'GBP',
        },
      };

      const mockResponse = {
        message: 'Entity setup completed successfully',
        entity_id: 'test-entity-123',
      };

      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse });

      const result = await apiClient.completeEntitySetup('test-entity-123', setupData);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/entities/test-entity-123/setup/complete',
        setupData,
        undefined
      );
      expect(result).toEqual(mockResponse);
    });

    it('validates setup data structure', async () => {
      const invalidSetupData = {
        // Missing required fields
        enable_ai_scanning: true,
      };

      // This should be caught by TypeScript, but let's test runtime behavior
      mockAxiosInstance.post.mockResolvedValue({ data: { message: 'Success', entity_id: 'test' } });

      // The API client should still call the endpoint even with incomplete data
      // The backend will handle validation
      await apiClient.completeEntitySetup('test-entity-123', invalidSetupData as any);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/entities/test-entity-123/setup/complete',
        invalidSetupData,
        undefined
      );
    });

    it('handles setup completion errors', async () => {
      const setupData = {
        enable_ai_scanning: true,
        sync_settings: {
          sync_start_date: '2024-04-01',
          sync_frequency: 'daily',
          auto_sync_enabled: true,
          sync_invoices: true,
          sync_bills: true,
          sync_payments: true,
          sync_bank_transactions: true,
          sync_journal_entries: true,
          sync_spend_money: true,
        },
        account_settings: {
          prepayment_asset_accounts: ['1200'],
          excluded_accounts: [],
          base_currency: 'GBP',
        },
      };

      const mockError = new Error('Setup failed');
      mockAxiosInstance.post.mockRejectedValue(mockError);

      await expect(apiClient.completeEntitySetup('test-entity-123', setupData)).rejects.toThrow('Setup failed');
    });
  });

  describe('connectSelectedXeroOrganization', () => {
    it('returns is_new_connection flag', async () => {
      const mockResponse = {
        message: 'Successfully connected to organization',
        entity: {
          entity_id: 'test-entity-123',
          entity_name: 'Test Company Ltd',
          requires_configuration: false,
          is_new_connection: true,
        },
      };

      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse });

      const result = await apiClient.connectSelectedXeroOrganization('client-123', 'tenant-456');

      expect(result.entity).toHaveProperty('is_new_connection');
      expect(result.entity.is_new_connection).toBe(true);
    });

    it('handles reconnection scenario', async () => {
      const mockResponse = {
        message: 'Successfully reconnected to organization',
        entity: {
          entity_id: 'existing-entity-789',
          entity_name: 'Existing Company Ltd',
          requires_configuration: false,
          is_new_connection: false,
        },
      };

      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse });

      const result = await apiClient.connectSelectedXeroOrganization('client-123', 'tenant-456');

      expect(result.entity.is_new_connection).toBe(false);
    });
  });

  describe('Type Safety', () => {
    it('enforces correct TypeScript interfaces', () => {
      // These should compile without errors if types are correct
      const wizardAnalysis: Parameters<typeof apiClient.getEntityWizardAnalysis>[0] = 'entity-123';
      
      const setupData: Parameters<typeof apiClient.completeEntitySetup>[1] = {
        enable_ai_scanning: true,
        sync_settings: {
          sync_start_date: '2024-01-01',
          sync_frequency: 'daily',
          auto_sync_enabled: true,
          sync_invoices: true,
          sync_bills: true,
          sync_payments: true,
          sync_bank_transactions: true,
          sync_journal_entries: true,
          sync_spend_money: true,
        },
        account_settings: {
          prepayment_asset_accounts: ['1200'],
          excluded_accounts: [],
          base_currency: 'USD',
        },
      };

      // If this compiles, TypeScript interfaces are correct
      expect(wizardAnalysis).toBe('entity-123');
      expect(setupData.enable_ai_scanning).toBe(true);
    });
  });

  describe('Error Response Handling', () => {
    it('handles 401 unauthorized errors', async () => {
      const mockError = {
        response: {
          status: 401,
          data: { detail: 'Unauthorized' },
        },
      };

      mockAxiosInstance.get.mockRejectedValue(mockError);

      await expect(apiClient.getEntityWizardAnalysis('test-entity')).rejects.toEqual(mockError);
    });

    it('handles 404 entity not found errors', async () => {
      const mockError = {
        response: {
          status: 404,
          data: { detail: 'Entity not found' },
        },
      };

      mockAxiosInstance.get.mockRejectedValue(mockError);

      await expect(apiClient.getEntityWizardAnalysis('nonexistent-entity')).rejects.toEqual(mockError);
    });

    it('handles 500 server errors', async () => {
      const mockError = {
        response: {
          status: 500,
          data: { detail: 'Internal server error' },
        },
      };

      mockAxiosInstance.post.mockRejectedValue(mockError);

      const setupData = {
        enable_ai_scanning: true,
        sync_settings: {
          sync_start_date: '2024-01-01',
          sync_frequency: 'daily',
          auto_sync_enabled: true,
          sync_invoices: true,
          sync_bills: true,
          sync_payments: true,
          sync_bank_transactions: true,
          sync_journal_entries: true,
          sync_spend_money: true,
        },
        account_settings: {
          prepayment_asset_accounts: [],
          excluded_accounts: [],
          base_currency: 'USD',
        },
      };

      await expect(apiClient.completeEntitySetup('test-entity', setupData)).rejects.toEqual(mockError);
    });
  });
});