# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo

# Google Cloud
.gcloud/
*.log

# Terraform
.terraform/
*.tfstate
*.tfstate.backup
*.tfvars

# OS specific
.DS_Store
Thumbs.db

# Project specific
credentials/
*.pem
*.key

# Test files and directories
tests/
local_utils/
examples/
*_test.py
test_*.py
*_test.ps1
test_*.ps1
direct_test.py
local_test.py
firebase_id_token.txt
id_token.txt
xero_lifecycle_test.log
coa_message.json
hello.py
organize_tests.ps1
drcr-d660a-firebase-adminsdk-fbsvc-f1d2dc57df.json