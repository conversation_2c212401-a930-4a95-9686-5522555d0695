name: Deploy Cloud Functions

on:
  push:
    branches:
      - main
      
jobs:
  deploy-xero-sync-consumer:
    runs-on: ubuntu-latest
    
    steps:
    # Clone repository to default working directory ($GITHUB_WORKSPACE)
    - name: Checkout code
      uses: actions/checkout@v3
      
    # Google Cloud authentication
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    # Configure Google Cloud SDK
    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
      with:
        project_id: drcr-d660a

    # Enable required APIs
    - name: Enable required APIs
      run: |
        gcloud services enable cloudfunctions.googleapis.com
        gcloud services enable cloudbuild.googleapis.com
        gcloud services enable pubsub.googleapis.com
        gcloud services enable eventarc.googleapis.com
        
    # Prepare deployment directory with modular structure
    - name: Prepare deployment directory
      run: |
        echo "📂 Preparing deployment directory for modular architecture..."
        
        # Create a clean deployment directory
        mkdir -p ./deploy_temp
        
        # Copy the entire xero_sync_consumer directory (preserves modular structure)
        cp -r ./cloud_functions/xero_sync_consumer/* ./deploy_temp/
        
        # Copy shared libraries to deployment directory
        cp -r ./drcr_shared_logic ./deploy_temp/
        cp -r ./rest_api ./deploy_temp/
        
        echo "📂 Deployment directory structure:"
        ls -la ./deploy_temp/
        echo "📂 Utils directory:"
        ls -la ./deploy_temp/utils/
        echo "📂 Endpoints directory:" 
        ls -la ./deploy_temp/endpoints/
            
        echo "✅ Deployment directory prepared successfully!"
        
    # Deploy Xero Sync Consumer Cloud Function
    - name: Deploy Xero Sync Consumer
      working-directory: deploy_temp
      env:
        IS_CLOUD_FUNCTION: "1"
      run: |
        gcloud functions deploy xero-sync-consumer \
          --runtime python39 \
          --trigger-topic xero-sync-topic \
          --entry-point xero_sync_consumer \
          --memory 512MB \
          --timeout 540s \
          --region europe-west2 \
          --no-gen2 \
          --max-instances 10 \
          --set-env-vars \
          GCP_PROJECT_ID=${{ vars.GCP_PROJECT_ID }},GCP_REGION=europe-west2,GCS_BUCKET_NAME=dvcd-dev-bucket,LOAD_SECRETS_FROM_SECRET_MANAGER=false,PUBSUB_TOPIC_XERO_SYNC=${{ vars.PUBSUB_TOPIC_XERO_SYNC }},PUBSUB_SUBSCRIPTION_XERO_SYNC=xero-sync-subscription,IS_CLOUD_FUNCTION=1,OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }},MISTRAL_API_KEY=${{ secrets.MISTRAL_API_KEY }},XERO_CLIENT_ID=${{ vars.XERO_CLIENT_ID }},XERO_CLIENT_SECRET=${{ secrets.XERO_CLIENT_SECRET }},TOKEN_ENCRYPTION_KEY=${{ secrets.TOKEN_ENCRYPTION_KEY }},FASTAPI_BASE_URL=${{ vars.FASTAPI_BASE_URL }}
          
    # Deploy Scheduled Sync Processor Cloud Function
    - name: Deploy Scheduled Sync Processor
      run: |
        cd cloud_functions/scheduled_sync_processor
        gcloud functions deploy scheduled-sync-processor \
          --runtime python39 \
          --trigger-topic scheduled-sync-topic \
          --entry-point scheduled_sync_processor \
          --memory 512MB \
          --timeout 540s \
          --region europe-west2 \
          --no-gen2 \
          --max-instances 5 \
          --set-env-vars GCP_PROJECT_ID=${{ vars.GCP_PROJECT_ID }},GCS_BUCKET_NAME=dvcd-dev-bucket,PUBSUB_TOPIC_XERO_SYNC=${{ vars.PUBSUB_TOPIC_XERO_SYNC }},IS_CLOUD_FUNCTION=1,OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }},MISTRAL_API_KEY=${{ secrets.MISTRAL_API_KEY }},XERO_CLIENT_ID=${{ vars.XERO_CLIENT_ID }},XERO_CLIENT_SECRET=${{ secrets.XERO_CLIENT_SECRET }},TOKEN_ENCRYPTION_KEY=${{ secrets.TOKEN_ENCRYPTION_KEY }},FASTAPI_BASE_URL=${{ vars.FASTAPI_BASE_URL }}
          
    # Create Pub/Sub subscription for testing
    - name: Create Pub/Sub subscription
      run: |
        gcloud pubsub subscriptions create xero-sync-subscription \
          --topic=xero-sync-topic \
          --ack-deadline=600 || echo "Subscription already exists"
          
    # Verify deployment
    - name: Verify Cloud Functions deployment
      run: |
        echo "Checking deployed functions..."
        gcloud functions list --regions=europe-west2 --filter="name:(xero-sync-consumer)"
        
        echo "✅ Cloud Functions deployment completed!"