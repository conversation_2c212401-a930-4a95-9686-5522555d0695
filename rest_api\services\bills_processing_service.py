"""
Bills Processing Service for FastAPI

Handles bills processing including transaction document building and amortization schedule creation.
Migrated from Cloud Functions to avoid import path issues.
"""
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from google.cloud import firestore

from ..services.amortization_service import AmortizationService

logger = logging.getLogger(__name__)


async def build_transaction_document(
    bill: Dict[str, Any],
    standardized_bill: Dict[str, Any], 
    bill_id: str,
    entity_id: str,
    client_id: str,
    has_potential_prepayments: bool,
    metadata_only: bool = False
) -> Dict[str, Any]:
    """Build the base transaction document structure."""
    
    transaction_doc = {
        # Core identifiers
        "transaction_id": bill_id,
        "entity_id": entity_id,
        "client_id": client_id,
        "source_system": "XERO",
        "source_system_id": bill_id,

        # Document classification
        "document_type": "BILL",
        "transaction_type": "ACCPAY",
        "document_number": bill.get("InvoiceNumber"),
        "reference": bill.get("Reference"),
        "status": bill.get("Status"),

        # Dates (use standardized format)
        "date_issued": standardized_bill.get("date_issued"),
        "date_due": standardized_bill.get("date_due"),
        "source_updated_at_utc": bill.get("UpdatedDateUTC"),

        # Financial amounts
        "subtotal": bill.get("SubTotal"),
        "tax_total": bill.get("TotalTax"),
        "total_amount": bill.get("Total"),
        "amount_due": bill.get("AmountDue"),
        "amount_paid": bill.get("AmountPaid"),
        "currency_code": bill.get("CurrencyCode"),

        # Contact information
        "contact_id": bill.get("Contact", {}).get("ContactID") if bill.get("Contact") else None,
        "contact_name": bill.get("Contact", {}).get("Name") if bill.get("Contact") else None,

        # Line items (essential for prepayment analysis)
        "line_items": bill.get("LineItems", []),

        # System fields
        "has_amortization_schedules": False,
        "prepayment_analysis": {
            "gl_based_analysis_completed": False,
            "llm_based_analysis_completed": False,
            "has_prepayment_line_items": has_potential_prepayments,
            "detection_methods": [],
            "prepayment_line_items": [],
            "llm_detections": [],
            "confidence_score": 0.0,
            "recommended_action": "pending_analysis" if has_potential_prepayments else "no_prepayment"
        },
        "attachments_processed": 0,
        "llm_processing_completed": False,

        # Two-stage processing support
        "processing_status": "metadata_loaded" if metadata_only else "pending",
        "raw_xero_data": bill if metadata_only else None,  # Store for heavy processing

        # Timestamps
        "created_at": firestore.SERVER_TIMESTAMP,
        "updated_at": firestore.SERVER_TIMESTAMP
    }
    
    return transaction_doc


async def create_amortization_schedules(
    db: firestore.AsyncClient,
    bill: Dict[str, Any],
    bill_id: str,
    combined_analysis: Dict[str, Any],
    entity_settings: Dict[str, Any],
    entity_id: str,
    client_id: str,
    gl_prepayment_lines: List[Dict[str, Any]]
) -> int:
    """Create amortization schedules based on analysis results."""
    processed_count = 0
    amortization_service = AmortizationService()
    
    try:
        # Determine if this is GL-based or LLM-only detection
        has_gl_detection = len(combined_analysis.get("prepayment_line_items", [])) > 0
        has_llm_detection = len(combined_analysis.get("llm_detections", [])) > 0
        
        if has_gl_detection:
            # Traditional GL-based processing
            for prepayment_line in gl_prepayment_lines:
                try:
                    best_period = combined_analysis.get("best_service_period")
                    service_start_date, service_end_date = (
                        best_period.get("start_date"), 
                        best_period.get("end_date")
                    ) if best_period and best_period.get("confidence") in ["high", "medium"] else (None, None)
                    
                    schedule_id = await _generate_and_save_amortization_schedule(
                        db=db,
                        bill_id=bill_id,
                        line_item=prepayment_line,
                        master_period_start_date=entity_settings.get("_system_masterPeriodStartDate"),
                        master_period_end_date=entity_settings.get("_system_masterPeriodEndDate"),
                        entity_settings=entity_settings,
                        client_id=client_id,
                        entity_id=entity_id,
                        bill=bill,
                        service_start_date=service_start_date,
                        service_end_date=service_end_date,
                        amortization_service=amortization_service,
                        is_llm_detected=False
                    )
                    if schedule_id:
                        processed_count += 1
                        logger.info(f"Generated amortization schedule {schedule_id} for bill {bill_id}")
                except Exception as e_schedule:
                    logger.error(f"Error generating amortization schedule for bill {bill_id}: {e_schedule}")
                    
        elif has_llm_detection:
            # LLM-only detection: use original line items as expense accounts
            logger.info(f"Processing LLM-only prepayment detection for bill {bill_id}")
            for line_item in bill.get("LineItems", []):
                try:
                    best_period = combined_analysis.get("best_service_period")
                    service_start_date, service_end_date = (
                        best_period.get("start_date"), 
                        best_period.get("end_date")
                    ) if best_period and best_period.get("confidence") in ["high", "medium"] else (None, None)
                    
                    schedule_id = await _generate_and_save_amortization_schedule(
                        db=db,
                        bill_id=bill_id,
                        line_item=line_item,
                        master_period_start_date=entity_settings.get("_system_masterPeriodStartDate"),
                        master_period_end_date=entity_settings.get("_system_masterPeriodEndDate"),
                        entity_settings=entity_settings,
                        client_id=client_id,
                        entity_id=entity_id,
                        bill=bill,
                        service_start_date=service_start_date,
                        service_end_date=service_end_date,
                        amortization_service=amortization_service,
                        is_llm_detected=True
                    )
                    if schedule_id:
                        processed_count += 1
                        logger.info(f"Generated LLM-detected amortization schedule {schedule_id} for bill {bill_id}")
                except Exception as e_schedule:
                    logger.error(f"Error generating LLM amortization schedule for bill {bill_id}: {e_schedule}")
                    
    except Exception as e_create:
        logger.error(f"Error creating amortization schedules for bill {bill_id}: {e_create}")
    
    return processed_count


async def _generate_and_save_amortization_schedule(
    db: firestore.AsyncClient,
    bill_id: str,
    line_item: Dict[str, Any],
    master_period_start_date: Optional[str],
    master_period_end_date: Optional[str],
    entity_settings: Dict[str, Any],
    client_id: str,
    entity_id: str,
    bill: Dict[str, Any],
    service_start_date: Optional[str],
    service_end_date: Optional[str],
    amortization_service: AmortizationService,
    is_llm_detected: bool = False
) -> Optional[str]:
    """
    Generate and save an amortization schedule for a line item.
    Copied from Cloud Function implementation to ensure consistency.
    """
    from datetime import datetime, timezone, date
    
    line_item_id = line_item.get("LineItemID")
    logger.info(
        f"Attempting to generate amortization schedule for Bill: {bill_id}, LineItem: {line_item_id}"
    )

    if not client_id:
        logger.warning(
            f"Cannot generate schedule for Bill {bill_id}, Line {line_item_id}: missing clientId."
        )
        return None

    prepayment_asset_codes = entity_settings.get("prepayment_asset_account_codes", [])
    
    if is_llm_detected:
        # For LLM-only detections, use the original account code as expense account
        # and use the first prepayment asset account for the asset side
        if not prepayment_asset_codes:
            logger.warning(
                f"Cannot generate LLM schedule for Bill {bill_id}, Line {line_item_id}: No prepayment_asset_account_codes defined in entity settings."
            )
            return None
        amortization_account_code = prepayment_asset_codes[0]
        
        # Extract expense account from the original line item
        original_line_item_account_code = line_item.get("AccountCode")
        if not original_line_item_account_code:
            logger.warning(
                f"Cannot generate LLM schedule for Bill {bill_id}, Line {line_item_id}: Missing AccountCode on line item."
            )
            return None
        
        # Use the original line item account code as the expense account
        expense_account_code = original_line_item_account_code
        
        # Validate that expense account is not the same as asset account
        if expense_account_code in prepayment_asset_codes:
            logger.warning(
                f"LLM-detected prepayment for Bill {bill_id}, Line {line_item_id}: Original AccountCode {expense_account_code} is a prepayment asset account. This suggests GL-based detection should have been used instead."
            )
            # For now, still proceed but log this as unusual
        
        logger.info(f"LLM-detected prepayment: Using asset account {amortization_account_code}, expense account {expense_account_code} (from original line item)")
    else:
        # Traditional GL-based logic
        amortization_account_code = (
            prepayment_asset_codes[0] if prepayment_asset_codes else None
        )
        if not amortization_account_code:
            logger.warning(
                f"Cannot generate schedule for Bill {bill_id}, Line {line_item_id}: No prepayment_asset_account_codes defined in entity settings for {entity_id}."
            )
            return None

        # Determine the initial expense account code from the line item
        original_line_item_account_code = line_item.get("AccountCode")
        resolved_expense_account_code = original_line_item_account_code # Start with this

        if not resolved_expense_account_code:
            logger.warning(
                f"Cannot generate schedule for Bill {bill_id}, Line {line_item_id}: Missing AccountCode on line item."
            )
            return None

        # Check if the line item is coded to a prepayment asset account
        if original_line_item_account_code in prepayment_asset_codes:
            logger.info(
                f"Bill {bill_id}, Line {line_item_id}: Line item AccountCode {original_line_item_account_code} is a prepayment asset account. Attempting to use vendor default expense account."
            )
            contact_data = bill.get("Contact") # Contact field in Xero Bill is a summary
            if contact_data and isinstance(contact_data, dict):
                contact_id_from_bill = contact_data.get("ContactID") # This is the Xero ContactID
                if contact_id_from_bill:
                    try:
                        # Read from top-level COUNTERPARTIES using Xero ContactID as document ID
                        contact_doc_ref = db.collection("COUNTERPARTIES").document(contact_id_from_bill)
                        contact_doc = await contact_doc_ref.get()
                        if contact_doc.exists:
                            contact_firestore_data = contact_doc.to_dict()
                            # Ensure the fetched counterparty belongs to the same entity_id and client_id for integrity
                            if contact_firestore_data.get("entity_id") != entity_id or contact_firestore_data.get("client_id") != client_id:
                                logger.error(f"Bill {bill_id}, Line {line_item_id}: Fetched Counterparty {contact_id_from_bill} does not match expected entity_id/client_id. Data integrity issue? Skipping schedule.")
                                return None
                            vendor_default_expense_code = contact_firestore_data.get("_system_defaultAmortizationExpenseAccountCode")
                            if vendor_default_expense_code:
                                logger.info(f"Bill {bill_id}, Line {line_item_id}: Found vendor default expense account {vendor_default_expense_code} for Contact {contact_id_from_bill}.")
                                # CRITICAL: Ensure the default expense account itself is not a prepayment asset account
                                if vendor_default_expense_code in prepayment_asset_codes:
                                    logger.error(f"Bill {bill_id}, Line {line_item_id}: Vendor default expense account {vendor_default_expense_code} for Contact {contact_id_from_bill} is itself a prepayment asset account. Misconfiguration. Cannot generate schedule.")
                                    return None # Misconfiguration
                                resolved_expense_account_code = vendor_default_expense_code
                            else:
                                logger.warning(f"Bill {bill_id}, Line {line_item_id}: Contact {contact_id_from_bill} exists but has no '_system_defaultAmortizationExpenseAccountCode' defined. Cannot generate schedule for this GL-coded prepayment line.")
                                return None
                        else:
                            logger.warning(f"Bill {bill_id}, Line {line_item_id}: Counterparty document for ID {contact_id_from_bill} not found in COUNTERPARTIES. Cannot determine vendor default expense account.")
                            return None
                    except Exception as e_contact_fetch:
                        logger.error(f"Bill {bill_id}, Line {line_item_id}: Error fetching counterparty {contact_id_from_bill} from COUNTERPARTIES: {e_contact_fetch}", exc_info=True)
                        return None
                else:
                    logger.warning(f"Bill {bill_id}, Line {line_item_id}: ContactID not found on parent bill. Cannot determine vendor default expense account.")
                    return None
            else:
                logger.warning(f"Bill {bill_id}, Line {line_item_id}: Contact data not found or invalid on parent bill. Cannot determine vendor default expense account.")
                return None
        
        # Final check on the resolved expense account code (could be original or from vendor default)
        if not resolved_expense_account_code: # Should be caught by earlier checks, but as a safeguard
            logger.error(f"Bill {bill_id}, Line {line_item_id}: Resolved expense_account_code is missing before proceeding. This should not happen.")
            return None
        
        if resolved_expense_account_code in prepayment_asset_codes:
            logger.warning(
                f"Bill {bill_id}, Line {line_item_id}: Final resolved AccountCode {resolved_expense_account_code} is a prepayment asset account. Cannot generate schedule. (This might occur if a non-GL line was passed or default was misconfigured)"
            )
            return None

        # Assign to the variable name used by the rest of the function
        expense_account_code = resolved_expense_account_code

    total_amount_to_amortize = line_item.get("LineAmount")
    if total_amount_to_amortize is None or total_amount_to_amortize == 0:
        logger.info(
            f"Bill {bill_id}, Line {line_item_id}: Amount to amortize is zero or None. No schedule generated."
        )
        return None

    # Parse dates from strings to date objects
    def _parse_date_string_to_date(date_str: str) -> Optional[date]:
        try:
            return datetime.fromisoformat(date_str.replace('Z', '+00:00')).date()
        except:
            try:
                return datetime.strptime(date_str, '%Y-%m-%d').date()
            except:
                return None

    # Determine service period: Use LLM-extracted dates if available, otherwise fall back to master period
    if master_period_start_date and master_period_end_date:
        start_date = _parse_date_string_to_date(master_period_start_date)
        end_date = _parse_date_string_to_date(master_period_end_date)
    else:
        # Default master period
        start_date = date.today()
        end_date = date(start_date.year + 1, start_date.month, start_date.day)
    
    service_period_source = "master_period"
    
    if service_start_date and service_end_date:
        try:
            llm_start = _parse_date_string_to_date(service_start_date)
            llm_end = _parse_date_string_to_date(service_end_date)
            
            if llm_start and llm_end and llm_start <= llm_end:
                start_date = llm_start
                end_date = llm_end
                service_period_source = "llm_extracted"
                logger.info(f"Bill {bill_id}, Line {line_item_id}: Using LLM-extracted service period: {service_start_date} to {service_end_date}")
            else:
                logger.warning(f"Bill {bill_id}, Line {line_item_id}: Invalid LLM service dates ({service_start_date} to {service_end_date}), falling back to master period")
        except Exception as e_date_parse:
            logger.warning(f"Bill {bill_id}, Line {line_item_id}: Error parsing LLM service dates: {e_date_parse}, falling back to master period")
    
    line_item_description = line_item.get("Description", "N/A")

    # Prepayment duration check using > 32 days
    if start_date and end_date:  # Ensure dates are valid
        num_days_in_period = (end_date - start_date).days + 1  # Inclusive day count
        if num_days_in_period <= 32:
            logger.info(
                f"Bill {bill_id}, Line {line_item_id}: Period length ({num_days_in_period} days) is <= 32 days. Does not qualify as prepayment. No schedule generated."
            )
            return None
        else:
            logger.info(
                f"Bill {bill_id}, Line {line_item_id}: Period length ({num_days_in_period} days) qualifies as prepayment. Proceeding with schedule generation using {service_period_source} dates."
            )
    else:
        logger.warning(
            f"Bill {bill_id}, Line {line_item_id}: Missing start or end date for duration check. Skipping schedule."
        )
        return None

    # Use AmortizationService to calculate the schedule
    try:
        schedule_preview = amortization_service.calculate_preview(
            amount=total_amount_to_amortize,
            start_date=start_date,
            end_date=end_date,
            entity_settings=entity_settings
        )
        
        logger.info(
            f"Bill {bill_id}, Line {line_item_id}: Amount €{total_amount_to_amortize}, "
            f"Method: {schedule_preview['calculation_method']}, Periods: {schedule_preview['total_months']}"
        )
        
        # Convert the preview format to the database format expected by existing code
        monthly_entries = []
        for entry in schedule_preview['monthly_entries']:
            # Parse the ISO date string back to datetime for database storage
            month_date_str = entry['month_date']
            month_date = datetime.fromisoformat(month_date_str).replace(tzinfo=timezone.utc)
            
            monthly_entries.append({
                "monthDate": month_date,
                "amount": entry['amount'],
                "status": "proposed",
                "postedJournalId": None,
                "postedJournalLineId": None,
                "matchConfidence": None,
                "lastActionByUserId": None,
                "lastActionTimestamp": None,
                "postingError": None,
            })
        
        actual_periods = schedule_preview['total_months']
        
    except Exception as e:
        logger.error(
            f"Bill {bill_id}, Line {line_item_id}: Failed to calculate amortization schedule: {e}"
        )
        return None
    
    if actual_periods == 0:
        logger.warning(
            f"Calculated zero periods for amortization between {start_date} and {end_date} for Bill {bill_id}, Line {line_item_id}. Skipping schedule."
        )
        return None

    schedule_id_val = str(uuid.uuid4())
    schedule_data = {
        "schedule_id": schedule_id_val,
        "transaction_id": bill_id,
        "line_item_id": line_item_id,
        "entity_id": entity_id,
        "client_id": client_id,
        "status": "proposed",
        "originalAmount": total_amount_to_amortize,
        "amortizationStartDate": datetime(
            start_date.year, start_date.month, start_date.day, tzinfo=timezone.utc
        ),
        "amortizationEndDate": datetime(
            end_date.year, end_date.month, end_date.day, tzinfo=timezone.utc
        ),
        "numberOfPeriods": actual_periods,
        "periodType": "monthly",
        "amortizationAccountCode": amortization_account_code,
        "expenseAccountCode": expense_account_code,
        "description": f"Amortization for: {line_item_description[:100]}",
        "monthlyEntries": monthly_entries,
        "calculation_method": schedule_preview['calculation_method'],
        "is_llm_detected": is_llm_detected,
        "detection_method": "llm_only" if is_llm_detected else "gl_coding",
        "created_at": firestore.SERVER_TIMESTAMP,
        "updated_at": firestore.SERVER_TIMESTAMP,
    }
    schedule_data = {k: v for k, v in schedule_data.items() if v is not None} # Clean None values

    try:
        schedule_doc_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id_val)
        await schedule_doc_ref.set(schedule_data)
        logger.info(
            f"Firestore 'set' command completed for schedule {schedule_id_val} in top-level AMORTIZATION_SCHEDULES. Attempting to verify..."
        )

        # Try to read it back immediately
        verify_doc = await schedule_doc_ref.get()
        if verify_doc.exists:
            logger.info(
                f"VERIFIED: Successfully saved and read back AMORTIZATION_SCHEDULE {schedule_id_val} from top-level collection for Bill {bill_id}, Line {line_item_id}."
            )
        else:
            logger.error(
                f"VERIFICATION FAILED: AMORTIZATION_SCHEDULE {schedule_id_val} NOT FOUND after 'set' command to top-level collection for Bill {bill_id}, Line {line_item_id}."
            )
            return None

        # Update parent transaction
        parent_transaction_ref = db.collection("TRANSACTIONS").document(bill_id)
        try:
            # First, check if the document exists
            parent_doc = await parent_transaction_ref.get()
            if parent_doc.exists:
                # Document exists, use update
                await parent_transaction_ref.update({
                    "_system_amortizationScheduleIDs": firestore.ArrayUnion([schedule_id_val]),
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
                logger.info(f"Successfully updated existing TRANSACTIONS document {bill_id} with amortization schedule {schedule_id_val}")
            else:
                # Document doesn't exist, use set with merge=True to create it with minimal data
                logger.warning(f"TRANSACTIONS document {bill_id} does not exist yet. Creating with amortization schedule reference.")
                await parent_transaction_ref.set({
                    "transaction_id": bill_id,
                    "entity_id": entity_id,
                    "client_id": client_id,
                    "_system_amortizationScheduleIDs": [schedule_id_val],
                    "created_at": firestore.SERVER_TIMESTAMP,
                    "updated_at": firestore.SERVER_TIMESTAMP
                }, merge=True)
                logger.info(f"Successfully created TRANSACTIONS document {bill_id} with amortization schedule {schedule_id_val}")
        except Exception as e_transaction_update:
            logger.error(f"Failed to update/create TRANSACTIONS document {bill_id} with amortization schedule {schedule_id_val}: {e_transaction_update}", exc_info=True)
            logger.warning(f"Continuing with schedule creation despite transaction update failure for {bill_id}")

        return schedule_id_val
    except Exception as e_fs_save_sched:
        logger.error(
            f"Failed to save or verify AMORTIZATION_SCHEDULE {schedule_id_val} at top-level collection for Bill {bill_id}, Line {line_item_id}: {e_fs_save_sched}",
            exc_info=True,
        )
        return None