# DRCR API Guide

Welcome to the DRCR API Guide! This guide provides developers with the information needed to understand and effectively interact with the DRCR (Debits, Credits, Reconciliations) REST API.

The DRCR API allows you to programmatically manage accounting firms, clients, entities, transactions, and related financial data, as well as integrate amortization and reconciliation workflows.

## Design Principles

The DRCR API is designed with the following principles in mind:

*   **RESTful**: The API adheres to REST principles, utilizing standard HTTP methods, status codes, and resource-oriented URLs.
*   **Stateless**: Each request from a client to the server must contain all the information needed to understand the request. The server does not store any client session state.
*   **Consistent**: API endpoints, request/response structures, and error handling follow consistent patterns to provide a predictable developer experience.
*   **Secure**: Authentication and authorization are enforced on all relevant endpoints to protect data integrity and access.
*   **Resource-Oriented**: The API is organized around resources (e.g., `clients`, `transactions`) that can be created, retrieved, updated, and deleted.

## Base URL and Versioning

All API endpoints are relative to a base URL, which includes a version number.

*   **Base URL Structure**: `https://<your-api-domain>/api/v1/`
*   **Current Version**: `v1`

API versioning is handled through the URL path. Future major versions of the API will use a new version identifier (e.g., `/api/v2/`).

## Authentication

All API requests that access protected resources must be authenticated. The DRCR API uses JSON Web Tokens (JWTs) issued by Firebase Authentication.

For detailed information on how to obtain and use authentication tokens, please refer to the [Authentication Guide](./authentication.md).

## Request & Response Format

*   **Format**: The API exclusively uses JSON for request and response bodies.
*   **Content-Type**: Ensure your requests have the `Content-Type: application/json` header when sending a JSON body.
*   **Accept Header**: While not strictly enforced for all endpoints, it's good practice to include an `Accept: application/json` header in your requests.

## Standard HTTP Methods

The API utilizes standard HTTP methods for resource manipulation:

*   **`GET`**: Retrieve a resource or a list of resources.
*   **`POST`**: Create a new resource.
*   **`PUT`**: Update an existing resource entirely.
*   **`PATCH`**: Partially update an existing resource (Note: specific PATCH support may vary by endpoint; PUT is more common for full updates).
*   **`DELETE`**: Delete a resource.

## Idempotency

*   `GET`, `PUT`, `DELETE` requests are generally idempotent. Making the same request multiple times will have the same effect as making it once.
*   `POST` requests are typically not idempotent. Making the same `POST` request multiple times may result in the creation of multiple resources unless specific idempotency key mechanisms are implemented and documented per endpoint.

## Pydantic Models

The API leverages Pydantic models for:
*   Defining clear data structures for request and response bodies.
*   Automatic request data validation.
*   Data serialization and deserialization.

These models are a key part of the OpenAPI/Swagger documentation, providing precise schemas for API interactions.

## Error Handling

The API uses standard HTTP status codes and a consistent JSON error response format. For detailed information, please refer to the [Error Handling Documentation](../error_handling/README.md).

## Navigation

*   [Authentication Guide](./authentication.md)
*   [Request Patterns (Pagination, Filtering, Sorting)](./request_patterns.md)
*   [Endpoint Groups Overview](./endpoints.md)
*   [Router Configuration Guide](./router_configuration.md)
*   **OpenAPI/Swagger Documentation**: For detailed endpoint specifications, request/response schemas, and interactive testing, please refer to the auto-generated OpenAPI documentation available at `/docs` (Swagger UI) or `/redoc` on the API server.

---

This overview should provide a solid foundation for working with the DRCR API. Subsequent documents in this guide will delve into specific aspects in more detail.