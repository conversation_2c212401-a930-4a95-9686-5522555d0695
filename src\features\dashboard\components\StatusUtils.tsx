import React from 'react';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON>Circle, 
  Bell, 
  AlertTriangle, 
  Unlink, 
  Loader2,
  Clock
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { ScheduleStatus } from '@/types/schedule.types';
import { EntityStatusBadge, getEntityStatusInfo } from '@/components/ui/entity-status-badge';
import type { ClientSummary, EntitySummary, StatusInfo } from '../types';

// Helper function to check if client should have action_needed status based on schedule counts
export const shouldClientHaveActionNeededStatus = (client: ClientSummary): boolean => {
  const counts = client.schedule_status_counts;
  if (!counts) return false;
  
  const actionNeededCount = (counts.pending_configuration || 0) + 
                           (counts.proposed || 0);
  
  return actionNeededCount > 0;
};

export const getClientStatusInfo = (status: ClientSummary['overall_status']): StatusInfo => {
  switch (status) {
    case 'ok': 
      return { 
        badgeClass: 'bg-green-100 text-green-700 border border-green-200', 
        icon: <CheckCircle className="h-3 w-3" />, 
        text: 'OK' 
      };
    case 'action_needed': 
      return { 
        badgeClass: 'bg-yellow-50 text-yellow-800 border border-yellow-300', 
        icon: <Bell className="h-3 w-3" />, 
        text: 'Action Needed' 
      };
    case 'error': 
      return { 
        badgeClass: 'bg-red-100 text-red-700 border border-red-200', 
        icon: <AlertTriangle className="h-3 w-3" />, 
        text: 'Error' 
      };
    default: 
      return { 
        badgeClass: 'bg-gray-100 text-gray-700 border border-gray-200', 
        icon: null, 
        text: 'Unknown' 
      };
  }
};

// Re-export the shared entity status function for backward compatibility
export { getEntityStatusInfo } from '@/components/ui/entity-status-badge';

interface StatusBadgeProps {
  statusInfo: StatusInfo;
  tooltipContent?: React.ReactNode;
}

export function StatusBadge({ statusInfo, tooltipContent }: StatusBadgeProps) {
  return (
    <TooltipProvider delayDuration={300}>
      <Tooltip>
        <TooltipTrigger asChild>
          <span>
            <Badge variant="outline" className={statusInfo.badgeClass}>
              {statusInfo.icon}
              <span className="ml-1">{statusInfo.text}</span>
            </Badge>
          </span>
        </TooltipTrigger>
        {(statusInfo.tooltip || tooltipContent) && (
          <TooltipContent>
            <p>{statusInfo.tooltip || tooltipContent}</p>
          </TooltipContent>
        )}
      </Tooltip>
    </TooltipProvider>
  );
}

// Wrapper component for EntityStatusBadge to match StatusBadge interface
export function EntityStatusBadgeWrapper({ entity }: { entity: EntitySummary }) {
  return <EntityStatusBadge entity={entity} />;
}

// Helper function to convert API EntitySummary to types EntitySummary format for compatibility
export function normalizeEntitySummary(entity: any): any {
  return {
    ...entity,
    status: entity.status || entity.connection_status || 'unknown'
  };
}

interface ClientDetailsProps {
  client: ClientSummary;
}

export function ClientDetails({ client }: ClientDetailsProps) {
  const details: React.ReactNode[] = [];
  
  // Tooltip for Pending items
  if ((client.pending_items_count || 0) > 0) {
    const scheduleStatusCounts = client.schedule_status_counts;
    const pendingConfig = scheduleStatusCounts?.pending_configuration || 0;
    const pendingReview = scheduleStatusCounts?.proposed || 0;
    
    let tooltipContent = `${client.pending_items_count} prepayment schedule(s) require attention across all entities.`;
    
    // Add breakdown if available
    if (scheduleStatusCounts && (pendingConfig + pendingReview > 0)) {
      const breakdown = [];
      if (pendingConfig > 0) breakdown.push(`${pendingConfig} need configuration`);
      if (pendingReview > 0) breakdown.push(`${pendingReview} need review`);
      
      if (breakdown.length > 0) {
        tooltipContent += ` Breakdown: ${breakdown.join(', ')}.`;
      }
    }
    
    details.push(
      <TooltipProvider key="pending-tp" delayDuration={100}>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="text-yellow-700 flex items-center text-xs mr-2 cursor-default">
              <Bell className="h-3 w-3 mr-1" /> {client.pending_items_count} pending
            </span>
          </TooltipTrigger>
          <TooltipContent>
            <p>{tooltipContent}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
  
  // Tooltip for Error count
  if ((client.error_count || 0) > 0) {
    const errorEntities = client.entities
      ?.filter(e => e.connection_status === 'error' || e.connection_status === 'disconnected')
      .map(e => e.entity_name)
      .join(', ');
    
    const scheduleErrors = client.schedule_status_counts?.error || 0;
    
    let errorTooltip = `${client.error_count} ${client.error_count === 1 ? 'entity' : 'entities'} with connection issues`;
    if (errorEntities) {
      errorTooltip += `: ${errorEntities}`;
    }
    if (scheduleErrors > 0) {
      errorTooltip += `. Also ${scheduleErrors} amortization schedule${scheduleErrors === 1 ? '' : 's'} with errors`;
    }
    errorTooltip += '.';
    
    details.push(
      <TooltipProvider key="error-tp" delayDuration={100}>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="text-red-600 flex items-center text-xs mr-2 cursor-default">
              <AlertTriangle className="h-3 w-3 mr-1" /> {client.error_count} errors
            </span>
          </TooltipTrigger>
          <TooltipContent>
            <p>{errorTooltip}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
  
  if (details.length === 0 && client.overall_status === 'ok') {
    details.push(
      <span key="ok" className="text-gray-500 flex items-center text-xs mr-2"> - </span>
    );
  }
  
  return <div className="flex flex-wrap items-center gap-1">{details}</div>;
} 