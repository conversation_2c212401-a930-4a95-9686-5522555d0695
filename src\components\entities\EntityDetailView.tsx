import React, { useState, useEffect, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  Building2, 
  Users, 
  FileText, 
  AlertCircle,
  DollarSign,
  Mail,
  Phone,
  Calendar,
  Clock,
  User
} from 'lucide-react';
import { EntitiesService } from '@/services/entities.service';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { DashboardPagination } from '@/features/dashboard/components/DashboardPagination';
import type { EntityResponse } from '@/types/entity.types';
import type { Account } from '@/lib/api';
import type { 
  ContactSummary, 
  ContactFilters, 
  ContactListResponse 
} from '@/types/contact.types';
import type { 
  AuditLogSummary, 
  AuditLogFilters, 
  AuditLogListResponse 
} from '@/types/audit.types';


export function EntityDetailView() {
  const { entityId } = useParams();
  
  // State
  const [entity, setEntity] = useState<EntityResponse | null>(null);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [contacts, setContacts] = useState<ContactSummary[]>([]);
  const [auditLogs, setAuditLogs] = useState<AuditLogSummary[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('accounts');
  const [error, setError] = useState<string | null>(null);
  
  // Pagination state
  const [contactsPagination, setContactsPagination] = useState<ContactListResponse['pagination'] | null>(null);
  const [auditPagination, setAuditPagination] = useState<AuditLogListResponse['pagination'] | null>(null);
  const [contactsLoading, setContactsLoading] = useState(false);
  const [auditLoading, setAuditLoading] = useState(false);
  
  // Filter state
  const [contactFilters, setContactFilters] = useState<ContactFilters>({ page: 1, limit: 20, sort_by: 'name', sort_order: 'asc' });
  const [auditFilters, setAuditFilters] = useState<AuditLogFilters>({ page: 1, limit: 50, sort_by: 'timestamp', sort_order: 'desc' });

  const loadEntityData = async () => {
    if (!entityId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      // Load entity details
      const entityData = await EntitiesService.getEntity(entityId);
      setEntity(entityData);
      
      // Load accounts and sort by code
      const accountsData = await EntitiesService.getChartOfAccounts(entityId);
      const sortedAccounts = accountsData.sort((a, b) => {
        // Handle null/undefined codes
        const codeA = a.code || '';
        const codeB = b.code || '';
        // Sort by account code (alphanumeric)
        return codeA.localeCompare(codeB, undefined, { numeric: true });
      });
      setAccounts(sortedAccounts);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load entity data');
    } finally {
      setIsLoading(false);
    }
  };

  // Load contacts data
  const loadContacts = useCallback(async () => {
    if (!entityId) return;
    
    try {
      setContactsLoading(true);
      const response = await EntitiesService.getContacts(entityId, contactFilters);
      setContacts(response.contacts);
      setContactsPagination(response.pagination);
    } catch (err) {
      console.error('Error loading contacts:', err);
    } finally {
      setContactsLoading(false);
    }
  }, [entityId, contactFilters]);

  // Load audit logs data
  const loadAuditLogs = useCallback(async () => {
    if (!entityId) return;
    
    try {
      setAuditLoading(true);
      const response = await EntitiesService.getAuditLogs(entityId, auditFilters);
      setAuditLogs(response.audit_logs);
      setAuditPagination(response.pagination);
    } catch (err) {
      console.error('Error loading audit logs:', err);
    } finally {
      setAuditLoading(false);
    }
  }, [entityId, auditFilters]);

  // Load entity data
  useEffect(() => {
    if (entityId) {
      loadEntityData();
    }
  }, [entityId]);

  // Load contacts data when tab is active or filters change
  useEffect(() => {
    if (activeTab === 'contacts' && entityId) {
      loadContacts();
    }
  }, [activeTab, loadContacts]);

  // Load audit logs data when tab is active or filters change
  useEffect(() => {
    if (activeTab === 'audit' && entityId) {
      loadAuditLogs();
    }
  }, [activeTab, loadAuditLogs]);


  const getConnectionStatusBadge = (status?: string) => {
    const variants: Record<string, { className: string; label: string }> = {
      active: { className: 'bg-green-100 text-green-700 border border-green-200', label: 'Active' },
      disconnected: { className: 'bg-gray-100 text-gray-500 border border-gray-200', label: 'Disconnected' },
      error: { className: 'bg-red-100 text-red-700 border border-red-200', label: 'Error' },
      pending: { className: 'bg-yellow-50 text-yellow-800 border border-yellow-300', label: 'Pending' },
      syncing: { className: 'bg-blue-100 text-blue-700 border border-blue-200', label: 'Syncing' },
    };

    const config = variants[status || 'disconnected'];
    return (
      <Badge variant="outline" className={config.className}>
        {config.label}
      </Badge>
    );
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDuration = (durationMs?: number) => {
    if (!durationMs) return '-';
    if (durationMs < 1000) return `${durationMs}ms`;
    if (durationMs < 60000) return `${(durationMs / 1000).toFixed(1)}s`;
    return `${(durationMs / 60000).toFixed(1)}m`;
  };

  const getContactTypeBadge = (type?: string) => {
    if (!type) return null;
    
    const variants: Record<string, string> = {
      vendor: 'bg-blue-100 text-blue-700 border-blue-200',
      customer: 'bg-green-100 text-green-700 border-green-200',
      supplier: 'bg-purple-100 text-purple-700 border-purple-200',
      employee: 'bg-orange-100 text-orange-700 border-orange-200',
      other: 'bg-gray-100 text-gray-700 border-gray-200'
    };
    
    return (
      <Badge variant="outline" className={variants[type.toLowerCase()] || variants.other}>
        {type.charAt(0).toUpperCase() + type.slice(1)}
      </Badge>
    );
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, string> = {
      success: 'bg-green-100 text-green-700 border-green-200',
      failed: 'bg-red-100 text-red-700 border-red-200',
      in_progress: 'bg-blue-100 text-blue-700 border-blue-200',
      warning: 'bg-yellow-100 text-yellow-700 border-yellow-200',
      completed: 'bg-green-100 text-green-700 border-green-200',
      cancelled: 'bg-gray-100 text-gray-700 border-gray-200'
    };
    
    return (
      <Badge variant="outline" className={variants[status.toLowerCase()] || variants.cancelled}>
        {status.replace('_', ' ')}
      </Badge>
    );
  };

  // Page change handlers
  const handleContactsPageChange = (page: number) => {
    setContactFilters(prev => ({ ...prev, page }));
  };

  const handleAuditPageChange = (page: number) => {
    setAuditFilters(prev => ({ ...prev, page }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!entity) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>Entity not found</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold">{entity.entity_name}</h1>
        <p className="text-gray-500">Entity Data Explorer</p>
      </div>

      {/* Entity Overview */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              {entity.entity_name}
            </span>
            <div className="flex items-center gap-2">
              <Badge variant="outline">{entity.type?.toUpperCase()}</Badge>
              {getConnectionStatusBadge(entity.connection_details?.status)}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Last sync: {formatDate(entity.last_sync)}
            </div>
            {entity.sync_status && (
              <div className="flex items-center gap-2">
                {entity.sync_status.is_syncing ? (
                  <>
                    <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                    <span className="text-sm text-blue-600">{entity.sync_status.user_message}</span>
                    {entity.sync_status.progress_percentage !== undefined && (
                      <span className="text-xs text-gray-500">({entity.sync_status.progress_percentage}%)</span>
                    )}
                  </>
                ) : (
                  <span className="text-sm text-green-600">{entity.sync_status.user_message}</span>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Data Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="accounts" className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Chart of Accounts
          </TabsTrigger>
          <TabsTrigger value="contacts" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Contacts
          </TabsTrigger>
          <TabsTrigger value="audit" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Audit Log
          </TabsTrigger>
        </TabsList>

        {/* Chart of Accounts Tab */}
        <TabsContent value="accounts">
          <Card>
            <CardHeader>
              <CardTitle>Chart of Accounts</CardTitle>
              <CardDescription>
                All accounts synced from {entity.type?.toUpperCase()} for this entity
              </CardDescription>
            </CardHeader>
            <CardContent>
              {accounts.length === 0 ? (
                <div className="text-center py-8">
                  <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No accounts found</h3>
                  <p className="text-gray-500 mb-4">
                    No chart of accounts data has been synced yet.
                  </p>
                  <p className="text-gray-500">No data available yet.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Code</TableHead>
                        <TableHead>Name</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Class</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {accounts.map((account) => (
                        <TableRow key={account.account_id || account.code}>
                          <TableCell className="font-mono">{account.code}</TableCell>
                          <TableCell>{account.name}</TableCell>
                          <TableCell>
                            <Badge variant="outline">{account.type}</Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">{account.class}</Badge>
                          </TableCell>
                          <TableCell>
                            <Badge 
                              variant={account.status === 'ACTIVE' ? 'default' : 'secondary'}
                            >
                              {account.status}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Contacts Tab */}
        <TabsContent value="contacts">
          <Card>
            <CardHeader>
              <CardTitle>Contacts & Counterparties</CardTitle>
              <CardDescription>
                All contacts synced from {entity.type?.toUpperCase()} for this entity
              </CardDescription>
            </CardHeader>
            <CardContent>
              {contactsLoading ? (
                <div className="flex items-center justify-center h-32">
                  <LoadingSpinner />
                </div>
              ) : contacts.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No contacts found</h3>
                  <p className="text-gray-500 mb-4">
                    No contacts have been synced for this entity yet.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Email</TableHead>
                          <TableHead>Source</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Transactions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {contacts.map((contact) => (
                          <TableRow key={contact.counterparty_id}>
                            <TableCell className="font-medium">
                              <div className="flex items-center gap-2">
                                <User className="h-4 w-4 text-gray-400" />
                                {contact.name}
                              </div>
                            </TableCell>
                            <TableCell>
                              {getContactTypeBadge(contact.contact_type)}
                            </TableCell>
                            <TableCell>
                              {contact.email_address ? (
                                <div className="flex items-center gap-2">
                                  <Mail className="h-4 w-4 text-gray-400" />
                                  <span className="text-sm">{contact.email_address}</span>
                                </div>
                              ) : (
                                <span className="text-gray-400">-</span>
                              )}
                            </TableCell>
                            <TableCell>
                              <Badge variant="secondary">
                                {contact.source_system || 'Manual'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge variant={contact.is_active ? 'default' : 'secondary'}>
                                {contact.is_active ? 'Active' : 'Inactive'}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <Badge variant="outline">
                                {contact.transaction_count}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                  
                  {/* Contacts Pagination */}
                  <DashboardPagination
                    pagination={contactsPagination}
                    isLoading={contactsLoading}
                    onPageChange={handleContactsPageChange}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Audit Log Tab */}
        <TabsContent value="audit">
          <Card>
            <CardHeader>
              <CardTitle>Audit Log</CardTitle>
              <CardDescription>
                Recent activity and sync history for this entity
              </CardDescription>
            </CardHeader>
            <CardContent>
              {auditLoading ? (
                <div className="flex items-center justify-center h-32">
                  <LoadingSpinner />
                </div>
              ) : auditLogs.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No audit logs found</h3>
                  <p className="text-gray-500 mb-4">
                    No audit activity has been recorded for this entity yet.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Event</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>User</TableHead>
                          <TableHead>Timestamp</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {auditLogs.map((log) => (
                          <TableRow key={log.audit_id}>
                            <TableCell className="font-medium">
                              <div className="flex items-center gap-2">
                                <FileText className="h-4 w-4 text-gray-400" />
                                <span className="font-mono text-sm">{log.event_type}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className="font-mono text-xs">
                                {log.event_category}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {getStatusBadge(log.status)}
                            </TableCell>
                            <TableCell>
                              {log.user_email ? (
                                <div className="flex items-center gap-2">
                                  <User className="h-4 w-4 text-gray-400" />
                                  <span className="text-sm">{log.user_email}</span>
                                </div>
                              ) : (
                                <span className="text-gray-400">System</span>
                              )}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4 text-gray-400" />
                                <span className="text-sm">{formatDateTime(log.timestamp)}</span>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                  
                  {/* Audit Logs Pagination */}
                  <DashboardPagination
                    pagination={auditPagination}
                    isLoading={auditLoading}
                    onPageChange={handleAuditPageChange}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}