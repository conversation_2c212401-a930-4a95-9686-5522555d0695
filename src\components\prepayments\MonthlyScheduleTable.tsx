import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '../ui/dropdown-menu';
import { Calendar, Edit, MoreHorizontal, FileText } from 'lucide-react';

interface MonthlyScheduleEntry {
  period: number;
  date: string;
  amount: number;
  status: 'proposed' | 'confirmed' | 'posted';
  runningBalance: number;
}

interface MonthlyScheduleTableProps {
  scheduleEntries: MonthlyScheduleEntry[];
  onEditEntry: (period: number, updates: Partial<MonthlyScheduleEntry>) => void;
  currencyCode?: string;
}

export function MonthlyScheduleTable({
  scheduleEntries,
  onEditEntry,
  currencyCode = 'GBP',
}: MonthlyScheduleTableProps) {
  const [editingPeriod, setEditingPeriod] = useState<number | null>(null);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: currencyCode,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'posted':
        return <Badge className="bg-green-100 text-green-700 border-green-200">Posted</Badge>;
      case 'confirmed':
        return <Badge className="bg-blue-100 text-blue-700 border-blue-200">Confirmed</Badge>;
      default:
        return <Badge variant="secondary">Proposed</Badge>;
    }
  };

  const getTotalAmount = () => {
    return scheduleEntries.reduce((sum, entry) => sum + entry.amount, 0);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'posted':
        return 'text-green-600';
      case 'confirmed':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };


  // Local amount change handler - no auto-save
  const handleAmountChange = (period: number, newAmount: number) => {
    onEditEntry(period, { amount: newAmount });
  };

  if (scheduleEntries.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Monthly Schedule
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-lg font-medium mb-2">No Schedule Generated</p>
            <p className="text-sm">Configure amortization settings to generate a monthly schedule</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Monthly Schedule
          </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Summary Row */}
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">
              Total Schedule: {scheduleEntries.length} periods
            </span>
            <span className="font-bold">
              {formatCurrency(getTotalAmount())}
            </span>
          </div>
        </div>

        {/* Schedule Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-20">Period</TableHead>
                <TableHead>Date</TableHead>
                <TableHead className="text-right">Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Running Balance</TableHead>
                <TableHead className="w-20">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {scheduleEntries.map((entry) => (
                <TableRow 
                  key={entry.period}
                  className={`hover:bg-gray-50 ${entry.status === 'posted' ? 'bg-green-50' : ''}`}
                >
                  <TableCell className="font-medium">
                    Month {entry.period}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      {formatDate(entry.date)}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="relative">
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        value={entry.amount}
                        onChange={(e) => {
                          const newAmount = parseFloat(e.target.value) || 0;
                          handleAmountChange(entry.period, newAmount);
                        }}
                        className="h-8 text-right font-mono max-w-24"
                        disabled={entry.status === 'posted'}
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(entry.status)}
                  </TableCell>
                  <TableCell className={`text-right font-mono ${getStatusColor(entry.status)}`}>
                    {formatCurrency(entry.runningBalance)}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-8 w-8 p-0"
                          disabled={entry.status === 'posted'}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem 
                          onClick={() => onEditEntry(entry.period, { status: 'confirmed' })}
                          disabled={entry.status === 'posted' || entry.status === 'confirmed'}
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          Mark Confirmed
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => console.log('View details', entry.period)}
                        >
                          <FileText className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}