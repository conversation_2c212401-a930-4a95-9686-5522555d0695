from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import date, datetime
from enum import Enum

class InvoiceStatus(str, Enum):
    """Enum for invoice status."""
    DRAFT = "draft"
    SUBMITTED = "submitted"
    AUTHORIZED = "authorized"
    PAID = "paid"
    VOIDED = "voided"
    DELETED = "deleted"

class InvoiceType(str, Enum):
    """Enum for invoice type."""
    INVOICE = "invoice"
    BILL = "bill"

class Contact(BaseModel):
    """Model for a contact (customer or vendor)."""
    id: str
    name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None

class LineItemBase(BaseModel):
    description: str
    quantity: float
    unit_amount: float

class LineItemCreate(LineItemBase):
    pass

class LineItem(LineItemBase):
    id: int # Or str, depending on your DB
    # If this model is created from an ORM object:
    class Config:
        from_attributes = True # Changed from orm_mode

class InvoiceBase(BaseModel):
    """Base model for invoices."""
    document_number: Optional[str] = None
    status: InvoiceStatus = InvoiceStatus.DRAFT
    type: InvoiceType
    date_issued: date
    date_due: Optional[date] = None
    currency: str = "GBP"
    contact_id: Optional[str] = None
    subtotal: Optional[float] = None
    tax_total: Optional[float] = None
    total: Optional[float] = None
    notes: Optional[str] = None
    line_items: List[LineItemCreate]

class InvoiceCreate(InvoiceBase):
    """Model for creating an invoice."""
    pass

class InvoiceUpdate(BaseModel):
    """Model for updating an invoice."""
    document_number: Optional[str] = None
    status: Optional[InvoiceStatus] = None
    date_issued: Optional[date] = None
    date_due: Optional[date] = None
    currency: Optional[str] = None
    contact_id: Optional[str] = None
    line_items: Optional[List[LineItemCreate]] = None
    notes: Optional[str] = None

class Invoice(InvoiceBase):
    """Complete invoice model."""
    id: str = Field(..., description="Unique identifier for the invoice (Firestore Document ID)")
    # client_id: Optional[str] = Field(None, description="ID of the client this transaction belongs to") # If linking to a Clients collection
    # entity_id: Optional[str] = Field(None, description="ID of the entity this transaction belongs to") # If linking to an Entities collection
    
    # contact: Optional[Contact] = Field(None, description="Full contact details") # Alternative: Embed full contact
    contact_id: Optional[str] = Field(None, description="Identifier for the contact (e.g., Xero ContactID or internal contact ID)") # Changed to Optional

    line_items: List[LineItem] = Field(default_factory=list)
    source: str = "manual"
    source_id: Optional[str] = None
    amount_paid: float = 0.0
    amount_due: Optional[float] = None
    created_at: datetime
    updated_at: datetime
    metadata: Optional[Dict[str, Any]] = None
    
    # Attachment fields for frontend
    has_attachments: Optional[bool] = Field(None, description="Whether this transaction has attachments")
    attachment_id: Optional[str] = Field(None, description="Primary attachment ID for viewing")
    
    # If this model is created from an ORM object:
    class Config:
        from_attributes = True # Changed from orm_mode
