from pydantic import BaseModel, Field
from typing import Optional, List, Any
from datetime import datetime

class ErrorDetail(BaseModel):
    loc: Optional[List[str]] = Field(None, title="Location", description="Location of the error (e.g., [\"body\", \"field_name\"])")
    msg: str = Field(..., title="Message", description="Error message specific to this location")
    type: Optional[str] = Field(None, title="Error Type", description="Type of error (e.g., value_error.missing)")
    # ctx: Optional[Dict[str, Any]] = Field(None, title="Error Context", description="Additional context for the error") # Optional, from Pydantic's ValidationError

class ErrorResponse(BaseModel):
    status_code: int = Field(..., description="HTTP status code.")
    detail: str = Field(..., description="A human-readable message providing more details about the error.")
    code: Optional[str] = Field(None, description="An application-specific error code (e.g., INVALID_INPUT, RESOURCE_NOT_FOUND).")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Timestamp of when the error occurred (UTC).")
    path: Optional[str] = Field(None, description="The request path that caused the error.")
    validation_errors: Optional[List[ErrorDetail]] = Field(None, description="A list of specific validation errors, if applicable.")

    class Config:
        from_attributes = True 