#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to pull actual Xero invoice data for testing prepayment logic.
This script will:
1. Connect to an existing Xero entity
2. Pull recent invoices (ACCPAY - bills/purchases)
3. Process them through the document extraction pipeline
4. Generate amortization schedules for prepayments
5. Show the results for testing

Usage:
    python pull_xero_invoices_for_testing.py
"""

import os
import sys
import json
import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List

# Add project root to path
project_root = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from dotenv import load_dotenv
load_dotenv()

from google.cloud import firestore

# Import our shared logic
from drcr_shared_logic.clients.xero_client import XeroApiClient
from cloud_functions.xero_sync_consumer.main import xero_sync_consumer

# Configure logging
logging.basicConfig(
    level=logging.INFO, 
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Configuration - using existing entity from check_sync_data.py
ENTITY_ID = '9101f43d-170a-4564-a043-d029f010e55c'  # Xero tenant ID
CLIENT_ID = '49687fc2-556d-4116-b441-505771881a01'  # Our client ID
GCP_PROJECT_ID = os.getenv('GCP_PROJECT_ID', 'drcr-d660a')

class InvoiceDataPuller:
    def __init__(self):
        self.db = firestore.Client(project=GCP_PROJECT_ID)
        self.xero_client = None
        
    async def initialize_xero_client(self) -> bool:
        """Initialize the Xero client for the entity."""
        try:
            self.xero_client = XeroApiClient(
                entity_id=ENTITY_ID,
                client_id=CLIENT_ID,
                project_id=GCP_PROJECT_ID
            )
            
            # Test the connection
            connection_status = await self.xero_client.check_connection_status()
            if connection_status.get('is_connected'):
                logger.info(f"✅ Successfully connected to Xero entity: {ENTITY_ID}")
                return True
            else:
                logger.error(f"❌ Xero entity not connected: {connection_status}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to initialize Xero client: {e}")
            return False
    
    async def check_entity_settings(self) -> Dict[str, Any]:
        """Check and display current entity settings."""
        try:
            settings_ref = self.db.collection('ENTITY_SETTINGS').document(ENTITY_ID)
            settings_doc = settings_ref.get()
            
            if settings_doc.exists:
                settings = settings_doc.to_dict()
                logger.info("📋 Current Entity Settings:")
                logger.info(f"  Client ID: {settings.get('clientId', 'N/A')}")
                logger.info(f"  Prepayment Asset Accounts: {settings.get('prepaymentAssetAccountCodes', [])}")
                logger.info(f"  Excluded P&L Accounts: {settings.get('excludedPnlAccountCodes', [])}")
                logger.info(f"  Last Invoice Sync: {settings.get('_system_lastSyncTimestampUtc_Invoices', 'Never')}")
                return settings
            else:
                logger.warning("⚠️ No entity settings found - will use defaults")
                return {}
                
        except Exception as e:
            logger.error(f"❌ Error checking entity settings: {e}")
            return {}
    
    async def pull_recent_invoices(self, days_back: int = 30, limit: int = 10) -> List[Dict[str, Any]]:
        """Pull recent invoices from Xero."""
        try:
            # Calculate date filter
            since_date = datetime.now(timezone.utc) - timedelta(days=days_back)
            since_date_str = since_date.strftime('%Y-%m-%dT%H:%M:%S')
            
            logger.info(f"🔍 Pulling invoices from last {days_back} days (since {since_date_str})...")
            
            # Fetch invoices using the Xero client
            invoices_response = await self.xero_client.get_invoices(
                invoice_type="ACCPAY",  # Bills/Purchase invoices
                updated_date_utc=since_date_str,
                page_size=limit
            )
            
            if invoices_response and 'Invoices' in invoices_response:
                invoices = invoices_response['Invoices']
                logger.info(f"✅ Found {len(invoices)} invoices")
                
                # Display summary
                for i, invoice in enumerate(invoices[:5]):  # Show first 5
                    logger.info(f"  {i+1}. {invoice.get('InvoiceNumber', 'N/A')} - "
                              f"{invoice.get('Contact', {}).get('Name', 'N/A')} - "
                              f"${invoice.get('Total', 0)}")
                
                return invoices
            else:
                logger.warning("⚠️ No invoices found or empty response")
                return []
                
        except Exception as e:
            logger.error(f"❌ Error pulling invoices: {e}")
            return []
    
    async def trigger_invoice_sync_via_pubsub(self) -> bool:
        """Trigger a full invoice sync using the pub/sub message format."""
        try:
            logger.info("🚀 Triggering invoice sync via cloud function...")
            
            # Create the sync message
            sync_message = {
                "platformOrgId": ENTITY_ID,  # This is the Xero tenant ID
                "tenantId": CLIENT_ID,       # This is our client ID
                "syncJobId": f"test-sync-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
                "endpoints": ["Invoices"],
                "forceFullSyncEndpoints": ["Invoices"],
                "targetDate": datetime.now().strftime('%Y-%m-%d'),
                "reason": "manual_testing_prepayments"
            }
            
            # Create the event structure for the cloud function
            import base64
            message_json = json.dumps(sync_message)
            message_bytes = message_json.encode('utf-8')
            message_b64 = base64.b64encode(message_bytes).decode('utf-8')
            
            event = {
                "data": message_b64
            }
            
            # Create a simple context
            class SimpleContext:
                def __init__(self):
                    self.timestamp = datetime.now(timezone.utc).isoformat()
                    self.event_id = f"test-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
            
            context = SimpleContext()
            
            logger.info(f"📤 Sync message: {sync_message}")
            
            # Call the cloud function directly
            await xero_sync_consumer(event, context)
            
            logger.info("✅ Invoice sync completed!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error triggering sync: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def check_synced_transactions(self) -> List[Dict[str, Any]]:
        """Check what transactions were synced to Firestore."""
        try:
            logger.info("📊 Checking synced transactions...")
            
            # Query transactions for our entity
            transactions_query = (
                self.db.collection('TRANSACTIONS')
                .where(filter=firestore.FieldFilter("entityId", "==", ENTITY_ID))
                .order_by('dateUtc', direction=firestore.Query.DESCENDING)
                .limit(10)
            )
            
            transactions = list(transactions_query.stream())
            logger.info(f"Found {len(transactions)} transactions in Firestore")
            
            transaction_data = []
            for doc in transactions:
                data = doc.to_dict()
                transaction_data.append(data)
                
                # Display summary
                logger.info(f"  📄 {data.get('reference', 'N/A')} - "
                          f"{data.get('contact', {}).get('name', 'N/A')} - "
                          f"${data.get('total', 0)} - "
                          f"Prepayment: {data.get('_system_isPrepaymentByLLM', False) or data.get('_system_isPrepaymentByGLCoding', False)}")
                
                # Check if it has amortization schedules
                schedule_ids = data.get('_system_amortizationScheduleIDs', [])
                if schedule_ids:
                    logger.info(f"    🗓️ Has {len(schedule_ids)} amortization schedule(s)")
            
            return transaction_data
            
        except Exception as e:
            logger.error(f"❌ Error checking transactions: {e}")
            return []
    
    async def check_amortization_schedules(self) -> List[Dict[str, Any]]:
        """Check amortization schedules that were generated."""
        try:
            logger.info("📅 Checking amortization schedules...")
            
            # Query schedules for our entity
            schedules_query = (
                self.db.collection('AMORTIZATION_SCHEDULES')
                .where(filter=firestore.FieldFilter("entityId", "==", ENTITY_ID))
                .order_by('createdAt', direction=firestore.Query.DESCENDING)
                .limit(5)
            )
            
            schedules = list(schedules_query.stream())
            logger.info(f"Found {len(schedules)} amortization schedules")
            
            schedule_data = []
            for doc in schedules:
                data = doc.to_dict()
                schedule_data.append(data)
                
                # Display summary
                logger.info(f"  📅 Schedule ID: {doc.id}")
                logger.info(f"     Transaction: {data.get('transactionId', 'N/A')}")
                logger.info(f"     Original Amount: ${data.get('originalAmount', 0)}")
                logger.info(f"     Period: {data.get('amortizationStartDate')} to {data.get('amortizationEndDate')}")
                logger.info(f"     Monthly Entries: {len(data.get('monthlyEntries', []))}")
                
                # Show first few monthly entries
                monthly_entries = data.get('monthlyEntries', [])
                for i, entry in enumerate(monthly_entries[:3]):
                    logger.info(f"       {i+1}. {entry.get('monthDate')} - ${entry.get('amount', 0)} - {entry.get('status', 'N/A')}")
            
            return schedule_data
            
        except Exception as e:
            logger.error(f"❌ Error checking schedules: {e}")
            return []

async def main():
    """Main function to pull and process invoice data."""
    print("🚀 DRCR Prepayment Testing - Invoice Data Puller")
    print("=" * 60)
    
    puller = InvoiceDataPuller()
    
    # Step 1: Initialize Xero connection
    print("\n📡 Step 1: Connecting to Xero...")
    if not await puller.initialize_xero_client():
        print("❌ Failed to connect to Xero. Please check your entity connection.")
        return
    
    # Step 2: Check entity settings
    print("\n⚙️ Step 2: Checking entity settings...")
    settings = await puller.check_entity_settings()
    
    # Step 3: Check what we already have
    print("\n📊 Step 3: Checking existing data...")
    existing_transactions = await puller.check_synced_transactions()
    existing_schedules = await puller.check_amortization_schedules()
    
    # Step 4: Ask user if they want to pull fresh data
    print(f"\n🤔 Found {len(existing_transactions)} existing transactions and {len(existing_schedules)} schedules.")
    
    if len(existing_transactions) > 0:
        response = input("Do you want to pull fresh invoice data? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("✅ Using existing data for testing.")
            return
    
    # Step 5: Trigger fresh sync
    print("\n🔄 Step 5: Pulling fresh invoice data...")
    sync_success = await puller.trigger_invoice_sync_via_pubsub()
    
    if sync_success:
        print("\n⏳ Waiting a moment for processing to complete...")
        await asyncio.sleep(5)  # Give it time to process
        
        # Step 6: Check results
        print("\n📈 Step 6: Checking results...")
        new_transactions = await puller.check_synced_transactions()
        new_schedules = await puller.check_amortization_schedules()
        
        print(f"\n✅ Sync complete! Found {len(new_transactions)} transactions and {len(new_schedules)} schedules.")
        
        # Summary for prepayment testing
        prepayment_count = sum(1 for t in new_transactions 
                             if t.get('_system_isPrepaymentByLLM') or t.get('_system_isPrepaymentByGLCoding'))
        
        print(f"\n🎯 Prepayment Testing Summary:")
        print(f"   📄 Total Transactions: {len(new_transactions)}")
        print(f"   💰 Identified Prepayments: {prepayment_count}")
        print(f"   📅 Amortization Schedules: {len(new_schedules)}")
        
        if prepayment_count > 0:
            print(f"\n✅ Great! You now have {prepayment_count} prepayments to test with.")
            print("   You can now test the prepayment logic, journal generation, etc.")
        else:
            print(f"\n⚠️ No prepayments were identified. You may need to:")
            print("   - Check entity settings for prepayment asset account codes")
            print("   - Look for invoices with longer service periods")
            print("   - Test with invoices that have attachments for LLM processing")
    
    print("\n🏁 Invoice data pulling complete!")

if __name__ == "__main__":
    asyncio.run(main()) 