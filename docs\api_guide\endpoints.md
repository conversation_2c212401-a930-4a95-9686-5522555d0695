# API Endpoint Groups Overview

The DRCR API is organized into several groups of endpoints, each corresponding to a major resource or functional area of the application. This document provides a high-level overview of these groups and their primary purposes.

For detailed information on each specific endpoint, including URL structures, request/response schemas (Pydantic models), required parameters, and example usage, please refer to the comprehensive **OpenAPI/Swagger documentation** available at the `/docs` (Swagger UI) or `/redoc` path of your API deployment.

> **Note**: All API endpoints follow the router configuration best practices outlined in the [Router Configuration Guide](./router_configuration.md). This ensures consistent URL paths throughout the API and prevents common issues like duplicate prefixes.

## Core Resource Endpoints

These endpoints manage the fundamental entities within the DRCR system.

*   **Firms (`/firms`)**
    *   **Purpose**: Manage accounting firms registered on the DRCR platform.
    *   **Key Operations**: Retrieving firm details. (Creation and updates might be admin-restricted or handled through a separate onboarding process).
*   **Firm Users (`/users` or `/firm_users`)**
    *   **Purpose**: Manage user accounts associated with firms, their roles, and assignments.
    *   **Key Operations**: Creating firm users, retrieving user details, updating user roles/assignments, managing user status.
*   **Clients (`/clients`)**
    *   **Purpose**: Manage the clients (businesses or individuals) that accounting firms serve.
    *   **Key Operations**: Creating new clients, retrieving client lists and individual client details, updating client information, deleting clients.
*   **Entities (`/entities`)**
    *   **Purpose**: Manage the specific business entities (e.g., Xero organizations, QuickBooks companies) linked to clients, for which financial data is processed.
    *   **Key Operations**: Creating new entities (linking to external platforms), retrieving entity lists and details, updating entity information, managing connection status.
    *   **Important Endpoints**:
        *   `GET /clients/{client_id}/entities` - List entities for a specific client
        *   `GET /entities/{entity_id}` - Get individual entity details
        *   `PUT /entities/{entity_id}/settings` - Update entity configuration settings
        *   `GET /entities/{entity_id}/connection/status` - Check connection status
        *   `POST /entities/{entity_id}/connection/disconnect` - Disconnect entity from external platform
    *   **Data Structure Notes**:
        *   **Backend Response**: Returns nested `connection_details` object containing connection status and metadata
        *   **Frontend Requirements**: Expects flattened `connection_status` field for UI compatibility
        *   **Data Transformation**: Service layer handles transformation between backend structure and frontend interface
        *   **Status Mapping**: Backend "active" status maps to frontend "connected" status
        *   **Field Names**: Uses `entity_name` (not `name`) and lowercase `type` values

### **Entity Data Transformation Example**

**Backend API Response:**
```json
{
  "entity_id": "8ead108d-f6a2-41e4-b2a8-962024248a66",
  "client_id": "c8cc6cf1-920c-497e-b2cd-cef4cccf4005",
  "entity_name": "Arua Test Prod",
  "type": "xero",
  "status": "active",
  "connection_details": {
    "status": "active",
    "connected_by": "firebase_uid",
    "xero_tenant_id": "8ead108d-f6a2-41e4-b2a8-962024248a66"
  }
}
```

**Frontend Interface (`EntitySummary`):**
```typescript
{
  entity_id: "8ead108d-f6a2-41e4-b2a8-962024248a66",
  entity_name: "Arua Test Prod",
  type: "xero",
  status: "active",
  connection_status: "connected", // Transformed from connection_details.status
  last_sync?: "2024-12-01T10:30:00Z",
  error_message?: null
}
```

*   **Entity Settings (`/entity_settings`)**
    *   **Purpose**: Manage configuration settings and synchronization preferences specific to each entity.
    *   **Key Operations**: Retrieving entity settings, updating settings like default accounts or auto-sync preferences.

## Financial & Operational Endpoints

These endpoints handle the core financial data and operational workflows.

*   **Transactions (`/transactions`)**
    *   **Purpose**: Manage financial transactions (invoices, bills, payments) sourced from external systems or created via the API.
    *   **Key Operations**: Creating new transactions, retrieving transaction lists (with filtering and pagination), fetching individual transaction details, updating transactions, deleting transactions.
*   **Counterparties (`/counterparties`)**
    *   **Purpose**: Manage contacts, vendors, and customers associated with transactions.
    *   **Key Operations**: Creating counterparties, retrieving counterparty lists and details, updating counterparty information.
*   **Amortization Schedules (`/amortization_schedules`)**
    *   **Purpose**: Manage amortization schedules for transaction line items.
    *   **Key Operations**: Creating schedules (often system-generated based on transactions), retrieving schedule details, confirming or modifying schedules, viewing monthly amortization entries.
*   **Proposed Journals (`/proposed_journals`)**
    *   **Purpose**: Manage system-generated journal entries based on due amortization schedule entries, awaiting review or posting.
    *   **Key Operations**: Retrieving lists of proposed journals, fetching individual journal details, approving journals for posting, checking posting status.
*   **Reports (`/reports`)**
    *   **Purpose**: Generate and retrieve financial reports and dashboards.
    *   **Key Operations**:
        *   **Dashboard (`/reports/dashboard`)**: Get summary information about transactions for a client, including counts and totals for pending review, approved, and current month transactions.
        *   **Amortization Report (`/reports/amortization`)**: Get detailed information about amortization schedules for a client, including monthly totals and transaction-level details.

## Authentication & Utility Endpoints

*   **Auth (`/auth/me`)**
    *   **Purpose**: Retrieves the profile of the currently authenticated user based on their token, including firm name.
    *   **Key Operations**: Retrieving current authenticated user's profile with firm details.
*   **Health Check (`/health`)**
    *   **Purpose**: Provides a simple endpoint to check the operational status of the API.
    *   **Key Operations**: Returns a success status if the API is running.

## Firm Management Endpoints

*   **Firms (`/firms/{firm_id}`)**
    *   **Purpose**: Retrieve detailed information about a specific firm.
    *   **Key Operations**: Get firm details, contact information, settings, and metadata.
    *   **Security**: Users can only access their own firm's data.
*   **Firm Summary (`/firms/{firm_id}/summary`)**
    *   **Purpose**: Get firm overview with basic statistics.
    *   **Key Operations**: Retrieve firm summary with client count, user count, and entity count.
    *   **Security**: Users can only access their own firm's summary.

## Xero Specific Endpoints (if applicable)

*   **Xero Connections (`/xero/connections` or similar)**
    *   **Purpose**: Manage the OAuth connection lifecycle with Xero if not fully abstracted. May include endpoints for initiating connections or handling callbacks.
    *   **Key Operations**: Initiating Xero OAuth flow, handling redirect callbacks, disconnecting Xero tenants.

---

This overview serves as a starting point. The OpenAPI/Swagger documentation is the definitive source for all endpoint details, including specific paths, HTTP methods, request bodies, response schemas, and required authentication/authorization.