import React, { useState } from 'react';

// --- Shadcn/UI & Lucide Imports ---
import { Button } from '@/components/ui/button';
import {
    DraggableDialog,
    DraggableDialogContent,
    DraggableDialogDescription,
    DraggableDialogFooter,
    DraggableDialogHeader,
    DraggableDialogTitle,
    DraggableDialogClose,
} from '@/components/ui/draggable-dialog';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from '@/components/ui/separator';
import {
    Loader2,
    X,
    Link as LinkIcon,
    AlertCircle,
    Building,
} from 'lucide-react';

// --- Component Props ---
interface ConnectNewEntityModalProps {
    isOpen: boolean;
    onClose: () => void;
    clientId: string; // The client to which the new entity will belong
    clientName?: string; // Optional: To display in the modal
    // Function to call when user clicks "Connect Xero/QBO"
    // This function would handle the redirect to the respective OAuth URL
    onInitiateConnection: (clientId: string, provider: 'xero' | 'qbo') => void;
    isConnecting?: boolean; // Optional: To show loading state on buttons
    connectionError?: string | null; // Optional: To display connection initiation errors
}

// --- Connect New Entity Modal Component ---
export function ConnectNewEntityModal({
    isOpen,
    onClose,
    clientId,
    clientName,
    onInitiateConnection,
    isConnecting = false,
    connectionError = null,
}: ConnectNewEntityModalProps) {

    const handleConnectClick = (provider: 'xero' | 'qbo') => {
        if (isConnecting) return; // Prevent double clicks
        onInitiateConnection(clientId, provider);
    };

    return (
        <DraggableDialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DraggableDialogContent>
                <DraggableDialogHeader>
                    <DraggableDialogTitle>Connect New Accounting Entity</DraggableDialogTitle>
                    <DraggableDialogDescription>
                        Connect a new Xero or QuickBooks Online entity for {clientName || `Client ID: ${clientId}`}.
                    </DraggableDialogDescription>
                </DraggableDialogHeader>

                {/* Connection Options */}
                <div className="py-6 space-y-4">
                     <p className="text-sm text-muted-foreground">
                        Select the accounting software you want to connect:
                    </p>

                    {/* Option 1: Xero */}
                    <Button
                        variant="outline"
                        className="w-full justify-start h-12 text-base"
                        onClick={() => handleConnectClick('xero')}
                        disabled={isConnecting}
                    >
                        {isConnecting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Building className="mr-2 h-5 w-5 text-blue-600"/>}
                        Connect Xero
                    </Button>

                     {/* Option 2: QuickBooks Online */}
                     <Button
                        variant="outline"
                        className="w-full justify-start h-12 text-base"
                        onClick={() => handleConnectClick('qbo')}
                        disabled={isConnecting}
                    >
                         {isConnecting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Building className="mr-2 h-5 w-5 text-green-600"/>}
                        Connect QuickBooks Online
                    </Button>

                    {/* Display Connection Error */}
                    {connectionError && (
                        <Alert variant="destructive" className="mt-4">
                            <AlertCircle className="h-4 w-4" />
                            <AlertTitle>Connection Error</AlertTitle>
                            <AlertDescription>{connectionError}</AlertDescription>
                        </Alert>
                    )}
                </div>

                {/* Footer with Close Button */}
                <DraggableDialogFooter className="sm:justify-end">
                    <DraggableDialogClose asChild>
                        <Button type="button" variant="secondary">
                            Cancel
                        </Button>
                    </DraggableDialogClose>
                </DraggableDialogFooter>
            </DraggableDialogContent>
        </DraggableDialog>
    );
} 