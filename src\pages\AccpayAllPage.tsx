import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/auth.store';
import { useAccpaySelections } from '../store/navigation.store';
import { useFirmName } from '../hooks/useFirmName';
import { useToast } from '../hooks/useToast';
import { useAccountFiltering } from '../hooks/useAccountFiltering';
import { useBulkCreateModal } from '../hooks/useBulkCreateModal';
import { AppSidebar } from '../components/layout/AppSidebar';
import { TransactionTable } from '../components/transactions/TransactionTable';
import { TransactionFilters } from '../components/transactions/TransactionFilters';
import { api } from '@/lib/api';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '../components/ui/breadcrumb';
import { Separator } from '../components/ui/separator';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '../components/ui/sidebar';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { BulkEditScheduleModal } from '@/components/prepayments/BulkEditScheduleModal';

// Types based on the actual API response structure
interface Transaction {
  id: string;
  document_number: string;
  type: string;
  contact_id: string;
  date_issued: string;
  date_due: string;
  total: number;
  amount_due: number;
  amount_paid: number;
  currency: string;
  status: string;
  has_amortization_schedules?: boolean;
  metadata: {
    contact_name: string;
    recommended_action: string;
    confidence_score: number;
    has_prepayment_line_items: boolean;
    gl_based_analysis_completed: boolean;
    llm_based_analysis_completed: boolean;
    llm_detections: any[];
    detection_methods: any[];
    prepayment_line_items: any[];
  };
  line_items: Array<{
    id: string | number;
    description: string;
    amount?: number;
    unit_amount: number;
    account_code?: string;
    account_id?: string;
    quantity: number;
    tax_amount?: number;
    tax_type?: string;
  }>;
  notes: string;
  source: string;
  source_id: string;
  subtotal: number;
  tax_total: number;
  attachment_id?: string;
  has_attachments?: boolean;
  created_at: string;
  updated_at: string;
}

interface Client {
  client_id: string;
  name: string;
}

interface Entity {
  entity_id: string;
  entity_name: string;
  type: string;
  connection_status: string;
  last_sync?: string;
}

// Fetch functions
const fetchClients = async (): Promise<Client[]> => {
  const response = await api.getClients();
  return response.clients.map(client => ({
    client_id: client.client_id,
    name: client.name
  }));
};

const fetchEntities = async (clientId: string): Promise<Entity[]> => {
  const response = await api.getEntitiesForClient(clientId);
  return response.entities.map(entity => ({
    entity_id: entity.entity_id,
    entity_name: entity.entity_name,
    type: entity.type || 'unknown',
    connection_status: entity.connection_status || 'unknown',
    last_sync: entity.last_sync
  }));
};

const fetchTransactions = async (
  clientId: string,
  entityId: string | 'all',
  page: number = 1,
  limit: number = 20,
  searchTerm: string = ''
): Promise<{
  transactions: Transaction[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}> => {
  const filters: any = {
    client_id: clientId,
    transaction_type: 'ACCPAY',
    page,
    limit,
  };

  if (entityId !== 'all') {
    filters.entity_id = entityId;
  }

  const response = await api.getTransactions(filters);
  
  // Filter by search term if provided
  let filteredTransactions = response.transactions;
  if (searchTerm) {
    const searchLower = searchTerm.toLowerCase();
    filteredTransactions = response.transactions.filter(transaction =>
      transaction.metadata?.contact_name?.toLowerCase().includes(searchLower) ||
      transaction.document_number?.toLowerCase().includes(searchLower) ||
      transaction.line_items?.some(item => 
        item.description?.toLowerCase().includes(searchLower)
      )
    );
  }

  return {
    transactions: filteredTransactions,
    total: searchTerm ? filteredTransactions.length : response.total,
    page: response.page,
    limit: response.limit,
    total_pages: searchTerm ? Math.ceil(filteredTransactions.length / limit) : response.total_pages,
  };
};

export function AccpayAllPage() {
  const navigate = useNavigate();
  const { firmName, isLoading: firmNameLoading } = useFirmName();
  const showToast = useToast();
  const { selectedClientId, selectedEntityId, setClientId, setEntityId } = useAccpaySelections();
  const { availableAccounts, loadAndFilterAccounts } = useAccountFiltering();
  
  const loadAccountsForModal = useCallback(async () => {
    if (selectedClientId && selectedEntityId && selectedEntityId !== 'all') {
      await loadAndFilterAccounts(selectedClientId, selectedEntityId);
    }
  }, [selectedClientId, selectedEntityId, loadAndFilterAccounts]);
  
  const { 
    isOpen: bulkModalOpen, 
    selectedTransaction, 
    isLoading: bulkLoading, 
    error: bulkError,
    openModal: openBulkModal,
    closeModal: closeBulkModal,
    handleSave: handleBulkSave,
    handleConfirm: handleBulkConfirm,
    handleSkip: handleBulkSkip
  } = useBulkCreateModal(loadAccountsForModal);

  const [clients, setClients] = useState<Client[]>([]);
  const [entities, setEntities] = useState<Entity[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [totalItems, setTotalItems] = useState<number>(0);

  const [isLoadingClients, setIsLoadingClients] = useState<boolean>(false);
  const [isLoadingEntities, setIsLoadingEntities] = useState<boolean>(false);
  const [isLoadingTransactions, setIsLoadingTransactions] = useState<boolean>(false);

  // Load clients on mount
  useEffect(() => {
    setIsLoadingClients(true);
    fetchClients()
      .then(fetchedClients => {
        setClients(fetchedClients);
        if (!selectedClientId && fetchedClients.length > 0) {
          setClientId(fetchedClients[0].client_id);
        }
      })
      .catch(error => {
        console.error('Error fetching clients:', error);
        showToast.showError('Failed to fetch clients');
      })
      .finally(() => setIsLoadingClients(false));
  }, [selectedClientId, setClientId]);

  // Load entities when client changes
  useEffect(() => {
    if (selectedClientId) {
      setIsLoadingEntities(true);
      setEntities([]);
      setTransactions([]);
      setCurrentPage(1);
      
      fetchEntities(selectedClientId)
        .then(fetchedEntities => {
          setEntities(fetchedEntities);
          if (selectedEntityId !== 'all' && !fetchedEntities.find(e => e.entity_id === selectedEntityId)) {
            setEntityId('all');
          }
        })
        .catch(error => {
          console.error('Error fetching entities:', error);
          showToast.showError('Failed to fetch entities');
        })
        .finally(() => setIsLoadingEntities(false));
    }
  }, [selectedClientId]);

  // Load transactions when client, entity, page, or search changes
  useEffect(() => {
    if (selectedClientId) {
      setIsLoadingTransactions(true);
      
      fetchTransactions(selectedClientId, selectedEntityId, currentPage, 20, searchTerm)
        .then(response => {
          setTransactions(response.transactions);
          setTotalPages(response.total_pages);
          setTotalItems(response.total);
        })
        .catch(error => {
          console.error('Error fetching transactions:', error);
          showToast.showError('Failed to fetch transactions');
        })
        .finally(() => setIsLoadingTransactions(false));
    }
  }, [selectedClientId, selectedEntityId, currentPage, searchTerm]);

  // Reset page when search or entity changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedEntityId]);

  const handleCreatePrepayment = (transaction: Transaction) => {
    openBulkModal(transaction);
  };

  const handleBulkSaveWithRefresh = async (bulkEditData: any) => {
    try {
      await handleBulkSave(bulkEditData);
      // Refresh transactions data on success
      setIsLoadingTransactions(true);
      try {
        const response = await fetchTransactions(selectedClientId, selectedEntityId, currentPage, 20, searchTerm);
        setTransactions(response.transactions);
        setTotalPages(response.total_pages);
        setTotalItems(response.total);
      } catch (error) {
        console.error('Error refreshing transactions:', error);
      } finally {
        setIsLoadingTransactions(false);
      }
    } catch (error) {
      // handleBulkSave already shows error, just log here
      console.error('Save failed:', error);
    }
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset className="flex-1 overflow-hidden">
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate('/dashboard');
                  }}
                  className="cursor-pointer"
                >
                  {firmNameLoading ? 'Loading...' : firmName}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage>ACCPAY</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </header>

        <div className="flex-1 overflow-auto">
          <div className="space-y-4 h-full flex flex-col p-4">
            <TransactionFilters
              clients={clients}
              entities={entities}
              selectedClientId={selectedClientId}
              selectedEntityId={selectedEntityId}
              searchTerm={searchTerm}
              isLoadingClients={isLoadingClients}
              isLoadingEntities={isLoadingEntities}
              onClientChange={setClientId}
              onEntityChange={setEntityId}
              onSearchChange={setSearchTerm}
            />

            <TransactionTable
              transactions={transactions}
              isLoading={isLoadingTransactions}
              onCreatePrepayment={handleCreatePrepayment}
            />

            {/* Pagination */}
            {!isLoadingTransactions && totalPages > 1 && (
              <div className="flex items-center justify-between py-4 flex-shrink-0">
                <div className="text-sm text-muted-foreground">
                  Page {currentPage} of {totalPages} ({totalItems} items)
                </div>
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                        className={currentPage <= 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>

                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                      if (pageNum > totalPages) return null;
                      
                      return (
                        <PaginationItem key={pageNum}>
                          <PaginationLink
                            onClick={() => setCurrentPage(pageNum)}
                            isActive={pageNum === currentPage}
                            className="cursor-pointer"
                          >
                            {pageNum}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    })}

                    <PaginationItem>
                      <PaginationNext
                        onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                        className={currentPage >= totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </div>
        </div>
      </SidebarInset>

      {/* Bulk Schedule Creation Modal */}
      {selectedTransaction && (
        <BulkEditScheduleModal
          isOpen={bulkModalOpen}
          onClose={closeBulkModal}
          mode="create"
          invoiceData={selectedTransaction}
          availableAmortizationAccounts={availableAccounts.amortization}
          availableExpenseAccounts={availableAccounts.expense}
          onBulkSave={handleBulkSaveWithRefresh}
          onBulkConfirm={handleBulkConfirm}
          onBulkSkip={handleBulkSkip}
          isSaving={bulkLoading}
          saveError={bulkError}
        />
      )}
    </SidebarProvider>
  );
}