"""
Sync Helpers - Utility functions for sync operations
Migrated from Cloud Functions to avoid import path issues in FastAPI background tasks.
"""
import os
import logging
import asyncio
import re
import uuid
from typing import Any, Optional, Dict
from google.cloud import firestore

logger = logging.getLogger(__name__)
GCP_PROJECT_ID = os.getenv("GCP_PROJECT_ID")


def _normalize_to_float(value: Any) -> Optional[float]:
    """
    Normalize various input types to float, handling None, empty strings, and type conversion.
    """
    if value is None:
        return None
    try:
        normalized_str = re.sub(r"[^\d.\-]", "", str(value))
        if not normalized_str or normalized_str == "-":
            return None
        return float(normalized_str)
    except (ValueError, TypeError):
        logger.warning(f"Could not normalize '{value}' to float.")
        return None


async def _upload_to_gcs(
    bucket_name: str,
    destination_blob_name: str,
    file_bytes: bytes,
    content_type: Optional[str] = None,
):
    """Uploads bytes to a GCS bucket using synchronous client (3.x compatible)."""
    try:
        # Use synchronous client which works reliably in 3.x
        from google.cloud import storage
        
        # Create synchronous client (works in both 2.x and 3.x)
        client = storage.Client()
        bucket = client.bucket(bucket_name)
        blob = bucket.blob(destination_blob_name)
        
        # Upload using synchronous method (blocking but reliable)
        blob.upload_from_string(
            data=file_bytes,
            content_type=content_type or "application/octet-stream"
        )
        
        logger.info(f"Successfully uploaded to gs://{bucket_name}/{destination_blob_name}")
        
    except Exception as e:
        logger.error(
            f"Failed to upload gs://{bucket_name}/{destination_blob_name} to GCS: {e}",
            exc_info=True,
        )
        raise


def get_other_secret(secret_name_in_secret_manager: str) -> Optional[str]:
    """Fetches a generic secret from environment."""
    secret_value = os.getenv(secret_name_in_secret_manager.upper().replace("-", "_"))
    logger.info(
        f"Attempting to get secret: {secret_name_in_secret_manager}. Found: {'Yes' if secret_value else 'No'}"
    )
    if not secret_value:
        logger.warning(
            f"Secret {secret_name_in_secret_manager} not found in environment."
        )
    return secret_value


async def _create_audit_log_entry(
    db: firestore.AsyncClient,
    event_category: str,
    event_type: str,
    client_id: Optional[str],
    entity_id: str,
    status: str, # e.g., "SUCCESS", "FAILURE", "STARTED", "INFO"
    details: Dict[str, Any],
    user_id: Optional[str] = None,
    source_ip: Optional[str] = None, # Optional: for user-initiated actions via API
):
    """Creates an audit log entry in Firestore."""
    try:
        log_entry_id = str(uuid.uuid4())
        log_data = {
            "timestamp": firestore.SERVER_TIMESTAMP,
            "eventCategory": event_category, # e.g., "SYNC", "USER_MANAGEMENT", "SYSTEM"
            "eventType": event_type,         # e.g., "XERO_SYNC_JOB_STARTED", "USER_LOGIN_SUCCESS"
            "clientId": client_id,
            "entityId": entity_id,
            "userId": user_id,
            "sourceIp": source_ip,
            "status": status,
            "details": details, # A dictionary for structured information
        }
        # Remove None fields to keep documents clean
        log_data = {k: v for k, v in log_data.items() if v is not None}

        audit_log_ref = db.collection("AUDIT_LOG").document(log_entry_id)
        await audit_log_ref.set(log_data)
        logger.info(f"Audit log created: {event_category} - {event_type} for entity {entity_id}. ID: {log_entry_id}")
    except Exception as e_audit:
        logger.error(f"Failed to create audit log for {event_category} - {event_type} for entity {entity_id}: {e_audit}", exc_info=True)


async def _update_firestore_sync_timestamp(
    db: firestore.AsyncClient,
    entity_id: str, # Changed from platform_org_id
    sync_endpoint: str,
    timestamp_to_set: str,
    reason: str,
):
    """Updates the specific last sync timestamp in ENTITY_SETTINGS for an entity and endpoint."""
    try:
        entity_settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id) # Use entity_id
        sync_timestamp_field_name = f"_system_lastSyncTimestampUtc_{sync_endpoint.replace('/', '_').lstrip('_')}"
        update_data = {
            sync_timestamp_field_name: timestamp_to_set,
            "_system_lastSyncTimestampUtc_reason": reason,
            "_system_updatedAt": firestore.SERVER_TIMESTAMP,
        }
        await entity_settings_ref.set(update_data, merge=True)
        logger.info(
            f"Successfully updated sync timestamp field {sync_timestamp_field_name} to {timestamp_to_set} for entity {entity_id} due to: {reason}."
        )
    except Exception as e_fs_ts_update:
        logger.error(
            f"Failed to update {sync_timestamp_field_name} for entity {entity_id} to {timestamp_to_set}: {e_fs_ts_update}",
            exc_info=True,
        )