"""
Audit Schemas - Request/response schemas for audit log API endpoints with pagination
"""
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from ..models.audit import (
    AuditLogEntry, AuditLogSummary, AuditLogDetails, 
    AuditEventCategory, AuditEventStatus, AuditStatistics
)


class AuditLogFilters(BaseModel):
    """Filters for audit log queries"""
    client_id: Optional[str] = Field(None, description="Filter by client ID")
    entity_id: Optional[str] = Field(None, description="Filter by entity ID")
    user_id: Optional[str] = Field(None, description="Filter by user ID")
    firm_id: Optional[str] = Field(None, description="Filter by firm ID")
    
    # Event classification filters
    event_category: Optional[AuditEventCategory] = Field(None, description="Filter by event category")
    event_type: Optional[str] = Field(None, description="Filter by event type (partial match)")
    status: Optional[AuditEventStatus] = Field(None, description="Filter by event status")
    
    # Time range filters
    date_from: Optional[date] = Field(None, description="Filter events from this date (inclusive)")
    date_to: Optional[date] = Field(None, description="Filter events to this date (inclusive)")
    datetime_from: Optional[datetime] = Field(None, description="Filter events from this datetime (inclusive)")
    datetime_to: Optional[datetime] = Field(None, description="Filter events to this datetime (inclusive)")
    
    # Duration filters
    min_duration_ms: Optional[int] = Field(None, ge=0, description="Filter events with minimum duration")
    max_duration_ms: Optional[int] = Field(None, ge=0, description="Filter events with maximum duration")
    
    # Content filters
    source_ip: Optional[str] = Field(None, description="Filter by source IP address")
    error_only: Optional[bool] = Field(None, description="Show only error events")
    has_details: Optional[bool] = Field(None, description="Filter by presence of details")
    
    # Text search
    search_text: Optional[str] = Field(None, description="Search in event type, details, or error message")
    
    class Config:
        json_schema_extra = {
            "example": {
                "client_id": "client_123",
                "entity_id": "entity_456",
                "event_category": "SYNC",
                "status": "SUCCESS",
                "date_from": "2024-01-01",
                "date_to": "2024-01-31",
                "min_duration_ms": 1000,
                "error_only": False,
                "search_text": "xero sync"
            }
        }

    @validator('date_to')
    def validate_date_range(cls, v, values):
        if v and 'date_from' in values and values['date_from']:
            if v < values['date_from']:
                raise ValueError('date_to must be after date_from')
        return v

    @validator('datetime_to')
    def validate_datetime_range(cls, v, values):
        if v and 'datetime_from' in values and values['datetime_from']:
            if v < values['datetime_from']:
                raise ValueError('datetime_to must be after datetime_from')
        return v

    @validator('max_duration_ms')
    def validate_duration_range(cls, v, values):
        if v and 'min_duration_ms' in values and values['min_duration_ms']:
            if v < values['min_duration_ms']:
                raise ValueError('max_duration_ms must be greater than min_duration_ms')
        return v


class AuditPaginationParams(BaseModel):
    """Pagination parameters for audit log queries"""
    page: int = Field(1, ge=1, description="Page number (1-based)")
    limit: int = Field(50, ge=1, le=200, description="Number of items per page (max 200 for audit logs)")
    sort_by: Optional[str] = Field("timestamp", description="Field to sort by")
    sort_order: Optional[str] = Field("desc", pattern="^(asc|desc)$", description="Sort order (desc for newest first)")
    
    class Config:
        json_schema_extra = {
            "example": {
                "page": 1,
                "limit": 50,
                "sort_by": "timestamp",
                "sort_order": "desc"
            }
        }


class AuditPaginationMeta(BaseModel):
    """Pagination metadata for audit log responses"""
    current_page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")
    total_items: int = Field(..., description="Total number of items")
    total_pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_previous: bool = Field(..., description="Whether there is a previous page")
    
    # Additional metadata for audit logs
    oldest_entry: Optional[datetime] = Field(None, description="Timestamp of oldest entry in result set")
    newest_entry: Optional[datetime] = Field(None, description="Timestamp of newest entry in result set")
    
    class Config:
        json_schema_extra = {
            "example": {
                "current_page": 1,
                "page_size": 50,
                "total_items": 2500,
                "total_pages": 50,
                "has_next": True,
                "has_previous": False,
                "oldest_entry": "2024-01-01T00:00:00Z",
                "newest_entry": "2024-01-31T23:59:59Z"
            }
        }


class AuditLogListResponse(BaseModel):
    """Response model for paginated audit log list"""
    audit_logs: List[AuditLogSummary] = Field(..., description="List of audit log entries")
    pagination: AuditPaginationMeta = Field(..., description="Pagination metadata")
    filters_applied: Optional[AuditLogFilters] = Field(None, description="Filters that were applied")
    
    class Config:
        json_schema_extra = {
            "example": {
                "audit_logs": [
                    {
                        "audit_id": "audit_123",
                        "timestamp": "2024-01-15T10:30:00Z",
                        "event_category": "SYNC",
                        "event_type": "XERO_CONTACTS_SYNC_STARTED",
                        "status": "SUCCESS",
                        "client_id": "client_456",
                        "entity_id": "entity_789",
                        "duration_ms": 2500,
                        "is_error": False,
                        "summary": "Xero contacts sync started successfully"
                    }
                ],
                "pagination": {
                    "current_page": 1,
                    "page_size": 50,
                    "total_items": 2500,
                    "total_pages": 50,
                    "has_next": True,
                    "has_previous": False
                }
            }
        }


class AuditLogDetailResponse(BaseModel):
    """Response model for single audit log entry detail"""
    audit_log: AuditLogDetails = Field(..., description="Complete audit log entry information")
    
    # Related entries
    related_entries: Optional[List[AuditLogSummary]] = Field(
        None, 
        description="Related audit entries (same request_id or sync job)"
    )
    
    class Config:
        json_schema_extra = {
            "example": {
                "audit_log": {
                    "audit_id": "audit_123",
                    "timestamp": "2024-01-15T10:30:00Z",
                    "event_category": "SYNC",
                    "event_type": "XERO_CONTACTS_SYNC_STARTED",
                    "status": "SUCCESS",
                    "client_id": "client_456",
                    "entity_id": "entity_789",
                    "user_id": "user_uid_123",
                    "details": {
                        "syncJobId": "sync_job_456",
                        "contacts_count": 150
                    },
                    "duration_ms": 2500,
                    "user_email": "<EMAIL>",
                    "client_name": "ABC Company",
                    "entity_name": "ABC Xero Org"
                },
                "related_entries": []
            }
        }


class AuditLogStatsResponse(BaseModel):
    """Response model for audit log statistics"""
    statistics: AuditStatistics = Field(..., description="Audit log statistics")
    generated_at: datetime = Field(..., description="When the statistics were generated")
    time_range: Optional[Dict[str, Any]] = Field(None, description="Time range for the statistics")
    
    class Config:
        json_schema_extra = {
            "example": {
                "statistics": {
                    "total_events": 1250,
                    "events_by_category": {
                        "SYNC": 450,
                        "API_REQUEST": 600,
                        "ERROR": 50
                    },
                    "events_by_status": {
                        "SUCCESS": 1100,
                        "FAILURE": 50
                    },
                    "error_rate": 4.0,
                    "average_duration_ms": 1250.5
                },
                "generated_at": "2024-01-15T10:30:00Z",
                "time_range": {
                    "from": "2024-01-01T00:00:00Z",
                    "to": "2024-01-31T23:59:59Z"
                }
            }
        }


class AuditEventTypesResponse(BaseModel):
    """Response model for available audit event types"""
    event_types: List[str] = Field(..., description="Available event types")
    event_categories: List[AuditEventCategory] = Field(..., description="Available event categories")
    event_statuses: List[AuditEventStatus] = Field(..., description="Available event statuses")
    
    # Grouped by category for better UX
    events_by_category: Dict[str, List[str]] = Field(..., description="Event types grouped by category")
    
    class Config:
        json_schema_extra = {
            "example": {
                "event_types": [
                    "XERO_SYNC_JOB_STARTED",
                    "CONTACTS_SYNC_SUCCESS",
                    "API_REQUEST_GET",
                    "USER_LOGIN_SUCCESS"
                ],
                "event_categories": ["SYNC", "API_REQUEST", "USER_MANAGEMENT"],
                "event_statuses": ["SUCCESS", "FAILURE", "STARTED"],
                "events_by_category": {
                    "SYNC": [
                        "XERO_SYNC_JOB_STARTED",
                        "CONTACTS_SYNC_SUCCESS"
                    ],
                    "API_REQUEST": [
                        "API_REQUEST_GET",
                        "API_REQUEST_POST"
                    ]
                }
            }
        }


class AuditLogExportRequest(BaseModel):
    """Request model for exporting audit logs"""
    filters: Optional[AuditLogFilters] = Field(None, description="Filters to apply for export")
    format: str = Field("csv", pattern="^(csv|json|xlsx)$", description="Export format")
    include_details: bool = Field(True, description="Whether to include event details")
    max_records: int = Field(10000, ge=1, le=50000, description="Maximum number of records to export")
    
    class Config:
        json_schema_extra = {
            "example": {
                "filters": {
                    "event_category": "SYNC",
                    "date_from": "2024-01-01",
                    "date_to": "2024-01-31"
                },
                "format": "csv",
                "include_details": True,
                "max_records": 5000
            }
        }


class AuditLogExportResponse(BaseModel):
    """Response model for audit log export"""
    export_id: str = Field(..., description="Unique identifier for the export")
    download_url: str = Field(..., description="URL to download the exported file")
    expires_at: datetime = Field(..., description="When the download URL expires")
    record_count: int = Field(..., description="Number of records in the export")
    file_size_bytes: int = Field(..., description="Size of the exported file")
    
    class Config:
        json_schema_extra = {
            "example": {
                "export_id": "export_123",
                "download_url": "https://storage.googleapis.com/exports/audit_export_123.csv",
                "expires_at": "2024-01-16T10:30:00Z",
                "record_count": 1250,
                "file_size_bytes": 524288
            }
        }


# Query parameter models for API endpoints
class AuditQueryParams(BaseModel):
    """Combined query parameters for audit log endpoints"""
    # Pagination
    page: int = Field(1, ge=1, description="Page number")
    limit: int = Field(50, ge=1, le=200, description="Items per page")
    sort_by: Optional[str] = Field("timestamp", description="Sort field")
    sort_order: Optional[str] = Field("desc", pattern="^(asc|desc)$", description="Sort order")
    
    # Filters
    client_id: Optional[str] = None
    entity_id: Optional[str] = None
    user_id: Optional[str] = None
    event_category: Optional[AuditEventCategory] = None
    event_type: Optional[str] = None
    status: Optional[AuditEventStatus] = None
    date_from: Optional[date] = None
    date_to: Optional[date] = None
    error_only: Optional[bool] = None
    search_text: Optional[str] = None


class AuditBulkActionRequest(BaseModel):
    """Request model for bulk actions on audit logs"""
    audit_ids: List[str] = Field(..., description="List of audit log IDs")
    action: str = Field(..., pattern="^(delete|archive|export)$", description="Action to perform")
    
    class Config:
        json_schema_extra = {
            "example": {
                "audit_ids": ["audit_123", "audit_456", "audit_789"],
                "action": "archive"
            }
        }


class AuditBulkActionResponse(BaseModel):
    """Response model for bulk actions on audit logs"""
    message: str = Field(..., description="Success message")
    processed_count: int = Field(..., description="Number of entries processed")
    failed_count: int = Field(0, description="Number of entries that failed processing")
    errors: Optional[List[str]] = Field(None, description="Error messages if any")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "Bulk archive completed successfully",
                "processed_count": 3,
                "failed_count": 0,
                "errors": None
            }
        }