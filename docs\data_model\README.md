# Data Model Documentation

This section provides a detailed overview of the Firestore data model used by the DRCR application. Understanding the data model is crucial for developing new features, troubleshooting issues, and effectively querying data through the API or directly (for analytics or administrative purposes).

## Core Principles

The DRCR Firestore data model is designed with the following principles in mind:

*   **Scalability & Performance**: Utilizing a top-level collection structure for most data entities to leverage Firestore's scaling capabilities and optimize query performance.
*   **Clarity & Consistency**: Employing standardized field names (e.g., `client_id`, `entity_id`, `created_at`, `updated_at`) across collections to enhance readability and predictability.
*   **Relationship Management**: Managing relationships between data entities primarily through explicit ID fields (e.g., a `TRANSACTIONS` document contains a `client_id` and `entity_id` to link it to its respective client and entity).
*   **Data Integrity**: While Firestore is schema-less, the application enforces data structure and integrity through Pydantic models in the REST API and careful data handling in backend processes.

## Navigation

*   **[Firestore Collections](./collections.md)**: Detailed descriptions of each top-level collection, including their purpose, document ID strategy, key fields, and specific relationships.
*   **[Data Relationships & Flow](./relationships.md)**: An overview of how different collections relate to each other and high-level data flow diagrams (conceptual).
*   **[Diagrams](./diagrams/)**: Visual representations, such as ERDs (Entity-Relationship Diagrams), to further illustrate the data model. 