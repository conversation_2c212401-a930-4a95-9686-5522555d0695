# CLAUDE.md - DRCR Frontend Project Context

## Project Overview
This is the frontend application for the DRCR (Debit Reconciliation & Categorization Resource) system - a React-based financial data management platform with TypeScript, Vite, and Tailwind CSS.

## 🚨 RECENT API CHANGES (2025-06-22)

### Schedule Status System Simplified
**BREAKING CHANGES:**
- ❌ **Removed:** `pending_review` status → Use `proposed` instead
- ❌ **Removed:** `pending_confirmation` status → Remove from filters
- ❌ **Removed:** `POST /schedules/{scheduleId}/post-ready` endpoint → Use `POST /schedules/{scheduleId}/confirm` instead

**New Status Flow:**
```
pending_configuration → proposed → confirmed → posted
```

**Required Frontend Updates:**
1. **Status Filters:** Replace `pending_review` with `proposed`, remove `pending_confirmation`
2. **API Calls:** Change `/post-ready` to `/confirm` 
3. **Detection Method:** Use `detection_method` field instead of status for LLM warnings
4. **Calculation Method:** Show new `calculation_method` field (`day_based` or `equal_monthly`)

**Valid Statuses:** `pending_configuration`, `proposed`, `confirmed`, `posted`, `skipped`, `cancelled`, `error`

---

## Backend API Reference

### **API Configuration**
- **Base URL:** `http://localhost:8081` (development)
- **Production URL:** `https://drcr-d660a.web.app` (backend)
- **Authentication:** Bearer token in Authorization header
- **Content-Type:** `application/json`

### **Schedule Endpoints**

#### **1. Preview & Calculation**
```typescript
// Calculate preview without saving
POST /schedules/calculate-preview
Body: {
  amount: number;
  start_date: string; // "YYYY-MM-DD"
  end_date: string;
  calculation_method?: "auto" | "day_based" | "equal_monthly";
}
Response: {
  calculation_method: "day_based" | "equal_monthly";
  total_months: number;
  monthly_entries: Array<{
    month_date: string;
    amount: number;
  }>;
}
```

#### **2. Schedule Management**
```typescript
// Get schedule details
GET /schedules/{scheduleId}
Response: {
  id: string;
  status: "pending_configuration" | "proposed" | "confirmed" | "posted";
  calculation_method: "day_based" | "equal_monthly";
  detection_method: "llm_only" | "gl_coding";
  transaction_id: string;
  amount: number;
  monthly_entries: Array<MonthlyEntry>;
  amortizationAccountCode?: string;
  expenseAccountCode?: string;
  // ... other fields
}

// Update schedule metadata ONLY (does NOT change monthly entries)
PUT /schedules/{scheduleId}
Body: {
  account_code?: string;         // Asset/prepayment account code
  expense_account_code?: string; // Expense account code  
  description?: string;          // Schedule description
  // WARNING: amount, start_date, end_date changes require recalculation
  // Use /recalculate endpoint instead for these fields
}

// Recalculate entire schedule (⚠️ OVERWRITES all monthly entries, resets to 'proposed' status)
PUT /schedules/{scheduleId}/recalculate
Body: {
  amount: number;
  start_date: string;
  end_date: string;
  calculation_method?: "auto" | "day_based" | "equal_monthly";
}
// ⚠️ WARNING: This destroys all existing monthly entries and regenerates them

// Preview changes without saving
PUT /schedules/{scheduleId}/preview-changes
Body: {
  amount?: number;
  start_date?: string;
  end_date?: string;
  calculation_method?: "auto" | "day_based" | "equal_monthly";
}
```

#### **3. Schedule Actions**
```typescript
// Confirm schedule (proposed → confirmed)
POST /schedules/{scheduleId}/confirm

// Skip schedule (any status → skipped)  
POST /schedules/{scheduleId}/skip

// Update status manually
PUT /schedules/{scheduleId}/status
Body: {
  status: "cancelled" | "error" | etc.
}
```

#### **4. Monthly Entry Updates** 
✅ **NEW ENDPOINT AVAILABLE** - Update individual monthly entry amounts!

```typescript
// Update individual monthly entry amount
PUT /schedules/{scheduleId}/entries/{entryIndex}
Body: {
  amount: number;  // New amount for this specific month
}
Response: {
  success: boolean;
  message: string;
  updated_entry: {
    entry_index: number;
    amount: number;
    month_date: string;
    status: string;
  }
}

// Example usage:
PUT /schedules/abc123/entries/0
Body: { "amount": 1250.50 }
```

**Business Rules:**
- Cannot edit entries from posted schedules
- Cannot edit individual entries that are already posted to Xero
- Entry index is 0-based (first month = 0, second month = 1, etc.)
- Changes are tracked in audit log with original and new amounts

#### **5. Manual Schedule Creation** 
✅ **NEW ENDPOINT** - Create schedules manually for invoices without schedules!

```typescript
// Create a new schedule with user-edited preview data
POST /schedules
Body: {
  transaction_id: string;           // Transaction/invoice ID
  amount: number;                   // Total amount to amortize
  start_date: string;              // "YYYY-MM-DD" format
  end_date: string;                // "YYYY-MM-DD" format  
  calculation_method: string;       // "day_based" or "equal_monthly"
  monthly_entries: Array<{         // User-edited monthly entries
    month_date: string;            // "YYYY-MM-DD"
    amount: number;                // Monthly amount
    days_in_month?: number;        // Optional
    proportion: number;            // Proportion of total
  }>;
  account_code?: string;           // Asset/prepayment account (optional)
  expense_account_code?: string;   // Expense account (optional)
  description?: string;            // Schedule description (optional)
}
Response: {
  success: boolean;
  message: string;
  schedule_id: string;
  schedule: {
    id: string;
    status: "pending_configuration" | "proposed";
    transaction_id: string;
    amount: number;
    monthly_entries: Array<MonthlyEntry>;
    // ... other schedule fields
  }
}

// Example workflow for manual schedule creation:
// 1. Generate preview using POST /schedules/calculate-preview
// 2. User edits monthly amounts in UI
// 3. Save final schedule using POST /schedules
```

**Business Rules:**
- Schedule status is automatically set to "proposed" if both account codes provided
- Schedule status is "pending_configuration" if account codes missing
- Monthly entries preserve user edits from preview
- Detection method is automatically set to "manual" 
- Audit log entry tracks manual creation

#### **6. Journal Posting**
```typescript
// Post single monthly entry to Xero
POST /schedules/{scheduleId}/entries/{entryIndex}/post

// Post multiple entries to Xero (recommended)
POST /schedules/{scheduleId}/entries/bulk-post
Body: {
  entry_indices: number[]; // Which months to post [0,1,2,...]
}
```

### **Status System**

#### **Status Flow**
```
pending_configuration → proposed → confirmed → posted
         ↓                ↓           ↓
    (configure)      (review)    (approve)    (post to Xero)
```

#### **Status Meanings**
- **`pending_configuration`**: Missing account codes (LLM-detected schedules)
- **`proposed`**: Ready for user review and approval
- **`confirmed`**: Approved, ready to post to accounting system
- **`posted`**: Successfully posted to Xero
- **`skipped`**: User chose not to amortize
- **`cancelled`**: User cancelled
- **`error`**: Error occurred

#### **Actionable Statuses** (need user attention)
```typescript
const actionableStatuses = ['pending_configuration', 'proposed'];
```

### **Data Models**

#### **Schedule Object**
```typescript
interface Schedule {
  id: string;
  transaction_id: string;
  line_item_id?: string;
  status: ScheduleStatus;
  
  // Amounts & Dates
  amount: number;
  currency: string;
  entry_date: string;
  
  // Account Codes
  account_code?: string;           // Asset/prepayment account
  expense_account_code?: string;   // Expense account
  
  // Calculation Metadata (NEW)
  calculation_method?: "day_based" | "equal_monthly";
  detection_method?: "llm_only" | "gl_coding";
  
  // Description & Journal Info
  description?: string;
  journal_id_external?: string;
  journal_link_external?: string;
  
  // Monthly Breakdown
  monthly_entries?: MonthlyEntry[];
  
  // Timestamps
  created_at: string;
  updated_at: string;
}

interface MonthlyEntry {
  monthDate: string;
  amount: number;
  status: string;
  postedJournalId?: string;
}
```

### **Authentication**
```typescript
// Add to all API requests
headers: {
  'Authorization': `Bearer ${firebaseToken}`,
  'Content-Type': 'application/json'
}
```

### **Error Handling**
```typescript
// Common error responses
{
  detail: string;        // Error message
  status_code: number;   // HTTP status
}

// Common status codes:
// 400 - Invalid request data
// 401 - Authentication required
// 403 - Permission denied  
// 404 - Schedule not found
// 500 - Server error
```

### **✅ MONTHLY ENTRY EDITING NOW AVAILABLE**

**SOLUTION:** New endpoint allows updating individual monthly entry amounts!

**USAGE:**
```typescript
// ✅ NEW WAY - Direct monthly entry updates
const updateMonthlyAmount = async (scheduleId: string, entryIndex: number, newAmount: number) => {
  try {
    const response = await api.put(`/schedules/${scheduleId}/entries/${entryIndex}`, {
      amount: newAmount
    });
    
    if (response.data.success) {
      console.log('Updated entry:', response.data.updated_entry);
      // Refresh schedule data to show changes
      await fetchScheduleData(scheduleId);
    }
  } catch (error) {
    if (error.response?.status === 400) {
      // Handle business rule violations
      alert(error.response.data.detail);
    } else {
      alert('Failed to update monthly entry');
    }
  }
};
```

**Error Handling:**
```typescript
// Common error scenarios:
// 400 - Invalid entry index
// 400 - Schedule is already posted
// 400 - Individual entry is already posted to Xero
// 404 - Schedule not found
// 403 - No access to client
```

**Frontend Implementation Example:**
```typescript
// Usage in component:
const handleAmountChange = async (entryIndex: number, newAmount: number) => {
  try {
    await updateMonthlyAmount(schedule.id, entryIndex, newAmount);
    // Success - update local state
    setSchedule(prev => ({
      ...prev,
      monthlyEntries: prev.monthlyEntries.map((entry, idx) => 
        idx === entryIndex ? { ...entry, amount: newAmount } : entry
      )
    }));
  } catch (error) {
    console.error('Failed to update monthly entry:', error);
  }
};
```

#### **✅ MANUAL SCHEDULE CREATION WORKFLOW**

**Complete implementation example for creating schedules for invoices without schedules:**

```typescript
// 1. Check if invoice has existing schedules
const checkForExistingSchedules = async (transactionId: string) => {
  try {
    const response = await api.get(`/transactions/${transactionId}/schedules`);
    return response.data.length > 0;
  } catch (error) {
    console.error('Failed to check existing schedules:', error);
    return false;
  }
};

// 2. Generate preview for manual editing
const generateSchedulePreview = async (previewData: {
  amount: number;
  start_date: string;
  end_date: string;
  entity_id: string;
  calculation_method?: "auto" | "day_based" | "equal_monthly";
}) => {
  try {
    const response = await api.post('/schedules/calculate-preview', previewData);
    return response.data;
  } catch (error) {
    console.error('Failed to generate preview:', error);
    throw error;
  }
};

// 3. Save schedule with user edits
const createFinalSchedule = async (scheduleData: {
  transaction_id: string;
  amount: number;
  start_date: string;
  end_date: string;
  calculation_method: string;
  monthly_entries: Array<{
    month_date: string;
    amount: number;
    days_in_month?: number;
    proportion: number;
  }>;
  account_code?: string;
  expense_account_code?: string;
  description?: string;
}) => {
  try {
    const response = await api.post('/schedules', scheduleData);
    if (response.data.success) {
      console.log('Schedule created:', response.data.schedule_id);
      return response.data.schedule;
    }
  } catch (error) {
    console.error('Failed to create schedule:', error);
    throw error;
  }
};

// 4. Complete workflow in React component
const ManualScheduleCreation = ({ transaction }) => {
  const [preview, setPreview] = useState(null);
  const [editedEntries, setEditedEntries] = useState([]);
  const [accountCodes, setAccountCodes] = useState({
    account_code: '',
    expense_account_code: ''
  });

  const handleGeneratePreview = async () => {
    try {
      const previewData = await generateSchedulePreview({
        amount: transaction.totalAmount,
        start_date: '2024-01-01', // User input
        end_date: '2024-12-31',   // User input
        entity_id: transaction.entity_id,
        calculation_method: 'auto'
      });
      
      setPreview(previewData);
      setEditedEntries(previewData.monthly_entries);
    } catch (error) {
      alert('Failed to generate preview');
    }
  };

  const handleSaveSchedule = async () => {
    try {
      const schedule = await createFinalSchedule({
        transaction_id: transaction.id,
        amount: preview.total_amount,
        start_date: preview.start_date,
        end_date: preview.end_date,
        calculation_method: preview.calculation_method,
        monthly_entries: editedEntries,
        ...accountCodes
      });
      
      // Redirect to schedule details or refresh transaction
      onScheduleCreated(schedule);
    } catch (error) {
      alert('Failed to create schedule');
    }
  };

  const handleEntryAmountChange = (index: number, newAmount: number) => {
    setEditedEntries(prev => prev.map((entry, idx) => 
      idx === index ? { ...entry, amount: newAmount } : entry
    ));
  };

  return (
    <div>
      <button onClick={handleGeneratePreview}>Generate Preview</button>
      
      {preview && (
        <div>
          <h3>Preview (Editable)</h3>
          {editedEntries.map((entry, index) => (
            <div key={index}>
              <span>{entry.month_date}: </span>
              <input 
                type="number" 
                value={entry.amount}
                onChange={(e) => handleEntryAmountChange(index, parseFloat(e.target.value))}
              />
            </div>
          ))}
          
          <input 
            placeholder="Asset Account Code"
            value={accountCodes.account_code}
            onChange={(e) => setAccountCodes(prev => ({...prev, account_code: e.target.value}))}
          />
          <input 
            placeholder="Expense Account Code"
            value={accountCodes.expense_account_code}
            onChange={(e) => setAccountCodes(prev => ({...prev, expense_account_code: e.target.value}))}
          />
          
          <button onClick={handleSaveSchedule}>Create Schedule</button>
        </div>
      )}
    </div>
  );
};
```

**Error Handling:**
```typescript
// Common error scenarios for manual schedule creation:
// 400 - Invalid transaction ID
// 400 - Invalid date range
// 400 - Missing required fields
// 404 - Transaction not found
// 403 - No access to client
```

### **Frontend Integration Notes**

#### **LLM Confidence Warnings**
```typescript
// Use detection_method instead of status
const needsReview = schedule.detection_method === 'llm_only';
if (needsReview) {
  showWarning("⚠️ AI Detected - Please Review Carefully");
}
```

#### **Calculation Method Display**
```typescript
const methodLabel = schedule.calculation_method === 'day_based' 
  ? 'Day-based Distribution' 
  : 'Equal Monthly Distribution';
```

#### **Status Filters**
```typescript
// For actionable items
const actionableFilters = ['pending_configuration', 'proposed'];

// For all status options
const allStatusFilters = [
  'pending_configuration',
  'proposed', 
  'confirmed',
  'posted',
  'skipped',
  'cancelled',
  'error'
];
```

## Technology Stack
- **React 18** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** with shadcn/ui components
- **Firebase** for authentication and hosting
- **Zustand** for state management
- **React Router DOM** for navigation
- **React Hook Form** with Zod validation
- **Axios** for API calls
- **Vitest** for testing

## Development Commands
```bash
# Development server
npm run dev

# Build for production
npm run build

# Lint code
npm run lint

# Run tests
npm test

# Run tests with UI
npm run test:ui

# Run tests once
npm run test:run

# Preview production build
npm run preview
```

## Project Structure
- `src/components/` - Reusable UI components
  - `ui/` - shadcn/ui components
  - `auth/` - Authentication components
  - `layout/` - Layout components
  - `entities/` - Entity management components
- `src/pages/` - Page components
- `src/features/` - Feature-based code organization
- `src/hooks/` - Custom React hooks
- `src/services/` - API service layers
- `src/store/` - Zustand state stores
- `src/types/` - TypeScript type definitions
- `src/lib/` - Utility functions

## Code Quality
- TypeScript strict mode enabled
- ESLint with React-specific rules
- Prettier formatting (via ESLint)
- Component testing with React Testing Library

## Key Features
- Firebase Authentication integration
- Entity management with Xero integration
- Dashboard with client data visualization
- Prepayments and transaction handling
- Responsive design with mobile support

## API Integration
- Backend API at configurable endpoint
- Authentication via Firebase tokens
- Error handling and loading states
- API client in `src/lib/api.ts`

## Testing Strategy
- Unit tests for utilities and hooks
- Component tests for UI components
- Integration tests for API interactions
- Test files co-located with source code

## Development Notes
- Uses absolute imports with path mapping
- Follows React best practices and hooks patterns
- Implements proper error boundaries
- Uses React Suspense for code splitting
- **NEVER hardcode data values in components - always fetch from APIs**
- If API fails, show empty state or error - do not use fallback hardcoded data