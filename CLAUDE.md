# CLAUDE.md for DRCR Backend

## Project Overview
DRCR Unified Prepayment Automation Backend - A Python FastAPI backend system for automating prepayment processing with Xero integration, AI-powered document processing, and intelligent amortization schedule generation.

## Quick Commands

### Development Server
```bash
# Start REST API server
cd /mnt/d/Projects/drcr_back/rest_api
python run_server.py

# Start with specific host/port
python run_server.py --host 0.0.0.0 --port 8080
```

### Testing
```bash
# Run all tests
cd /mnt/d/Projects/drcr_back
python -m pytest tests/ -v

# Run specific test categories
python -m pytest tests/api/ -v                    # API tests
python -m pytest tests/integration/ -v           # Integration tests
python -m pytest tests/performance/ -v           # Performance tests
python -m pytest tests/unit/ -v                  # Unit tests

# Cloud functions local testing
python -m cloud_functions.xero_sync_consumer.main

# PowerShell tests (Windows)
powershell -File tests/powershell/test_firebase_auth.ps1
powershell -File tests/powershell/test_transactions_api.ps1
```

### Deployment
```bash
# Manual GCP deployment
./gcloud_manual_deploy.sh

# Deploy cloud functions
python scripts/deploy_scheduled_sync.py

# Production deployment scripts
./scripts/deployment/deploy.ps1
```

### Development Utilities
```bash
# Performance testing
python scripts/utilities/performance_test.py --url http://localhost:8081 --token-file firebase_id_token.txt

# Generate encryption key
python scripts/utilities/generate_encryption_key.py

# Fix configuration issues
python scripts/utilities/fix_xero_config.py

# Test specific endpoints
python scripts/utilities/test_api_endpoint.py
```

### Database & Firestore
```bash
# Create Firestore indexes
python scripts/utilities/create_firestore_indexes.py

# Token management
python tests/token_management/check_all_tokens.py
python tests/token_management/cleanup_duplicate_tokens.py

# Admin user utilities
python scripts/utilities/restore_admin_user.py
python scripts/utilities/verify_admin_user.py
```

**IMPORTANT - Firestore Field Access:**
- **Schema Reference**: `firestore_schema.yml` - Complete field definitions and canonical mappings for all collections
- **Field Access Helpers**: `rest_api/utils/field_access.py` - Use canonical field access functions:
  - `get_field(data, "canonical_name")` - General field access with alias resolution
  - `get_account_code(line_data)` - Account code access (handles AccountCode/accountCode variations)
  - `get_journal_lines(journal_data)` - Journal lines access (handles JournalLines/lines variations)
- **Field Naming Convention**: Always use canonical names (snake_case) in new code, helpers handle legacy aliases
- **NEVER use direct field access patterns like**: `data.get("field") or data.get("Field")` - use helpers instead

## Technology Stack
- **Framework**: FastAPI (Python 3.9+)
- **Database**: Google Cloud Firestore
- **Storage**: Google Cloud Storage
- **Authentication**: Firebase Auth
- **AI/ML**: OpenAI GPT-4o/4.1-mini, Mistral API
- **Integration**: Xero API
- **Deployment**: Google Cloud Functions, Cloud Run
- **Infrastructure**: Terraform

## Key Architecture Components

### REST API (`rest_api/`)
- **Main Application**: `main.py` - FastAPI app with middleware and routers
- **Routes**: Authentication, Clients, Entities, Firms, Xero, Transactions, Reports, Schedules, Invoices, Attachments
- **Models**: Pydantic models for API schemas and data validation
- **Services**: Business logic layer for core functionality
- **Dependencies**: Database connections, authentication, security

### Cloud Functions (`cloud_functions/`)
- **xero_sync_consumer**: Main sync processor for Xero data
- **scheduled_sync_processor**: Automated scheduled synchronization

### Shared Logic (`drcr_shared_logic/`)
- **Clients**: Base client and Xero client implementations
- **Document Processor**: AI-powered invoice document processing
- **Config**: Secret management and configuration loading

### Core Features
1. **Two-Stage Sync Architecture**: Fast metadata loading (Cloud Functions) + heavy processing (Cloud Run)
2. **Automated Xero Data Sync**: Invoices, Chart of Accounts, Contacts
3. **AI Document Processing**: PDF/image extraction with OpenAI and Mistral OCR
4. **Prepayment Identification**: Service period analysis and GL coding
5. **Hybrid Amortization**: Smart selection between 12-month equal vs day-based distribution
6. **Journal Management**: Automated proposed journal entries
7. **Cost-Effective Token Storage**: Encrypted Firestore storage vs expensive Secret Manager
8. **Real-time Progress Tracking**: Firestore-based sync job monitoring

## Environment Configuration

### Required Environment Variables (.env)
```env
# Google Cloud
GCP_PROJECT_ID="your-gcp-project-id"
GCP_REGION="europe-west2"
GCS_BUCKET_NAME="your-gcs-bucket-name"
FIREBASE_CREDENTIALS_PATH="path-to-firebase-credentials.json"

# Secret Management
LOAD_SECRETS_FROM_SECRET_MANAGER="false"  # true for production

# Xero API
XERO_CLIENT_ID="your-xero-client-id"
XERO_CLIENT_SECRET="your-xero-client-secret"
XERO_REDIRECT_URI="http://localhost:8081/xero/callback"
XERO_SCOPES="accounting.transactions accounting.settings offline_access"
XERO_TEST_TENANT_ID_FROM_ENV="your-xero-tenant-id"

# Token Storage
TOKEN_ENCRYPTION_KEY="your-fernet-encryption-key"

# AI Services
OPENAI_API_KEY="your-openai-api-key"
OPENAI_MODEL="gpt-4.1-mini"
MISTRAL_API_KEY="your-mistral-api-key"

# API Configuration
API_SECRET_KEY="your-secret-key"
API_DEBUG=True
API_HOST="0.0.0.0"
API_PORT=8080

# PubSub
PUBSUB_TOPIC_XERO_SYNC="xero-sync-topic"
PUBSUB_SUBSCRIPTION_XERO_SYNC="xero-sync-subscription"

# Two-Stage Sync Configuration
FASTAPI_BASE_URL="https://drcr-d660a-rest-api.uc.r.appspot.com"
# SERVICE_ACCOUNT_TOKEN="your-service-account-token"  # Optional, for internal service auth
```

## Documentation

### Key Documentation Files
- `docs/DOCUMENTATION_INDEX.md` - Complete documentation index
- `docs/api_guide/` - API endpoints and authentication
- `docs/development/` - Setup and development guidelines
- `docs/PERFORMANCE_OPTIMIZATION_SUMMARY.md` - Performance metrics and optimization
- `docs/HYBRID_AMORTIZATION.md` - Amortization system guide
- `docs/FULLSTACK_QUICK_REFERENCE.md` - Quick deployment reference
- **`firestore_schema.yml`** - **CRITICAL**: Firestore field definitions, canonical mappings, and data contracts

### Critical Project Rules
1. **Always use full paths when working with multiple projects**
   - Frontend: `/mnt/d/Projects/drcr_front/`
   - Backend: `/mnt/d/Projects/drcr_back/`

2. **Check documentation before making changes**
   - Current status: `docs/DOCUMENTATION_INDEX.md`
   - Project phase: `docs/dev_plan.txt`
   - Current tasks: `docs/TASK_PLANNER_UPDATED.md`

3. **NO HARDCODED VALUES**
   - Never hardcode URLs, keys, or configuration values
   - Always use environment variables with proper fallback handling
   - Throw meaningful errors when configuration is missing

4. **API CHANGES DOCUMENTATION**
   - **CRITICAL**: When making ANY API changes (endpoints, status, fields, etc.), IMMEDIATELY document changes in D:\Projects\drcr_front\Claude.md
   - Frontend project path: `/mnt/d/Projects/drcr_front/`
   - Document: removed endpoints, new endpoints, changed request/response formats, status changes
   - Do NOT assume frontend knows about backend changes - write detailed migration notes

5. **DATA CONSISTENCY ACROSS PROCESSING STAGES** - CRITICAL FOR PRODUCTION
   - **Core Principle**: When data flows through multiple processing stages, ALL stages must have compatible data contracts
   - **Field Compatibility**: 
     * Never assume downstream services can access fields the same way as upstream services
     * Document required fields for each service and ensure upstream provides them
     * Use consistent field names, data types, and structures across all stages
   - **Data Type Consistency**:
     * Dates: Use consistent format (datetime objects vs ISO strings) across all stages
     * Numbers: Maintain precision and type (int vs float vs Decimal)
     * Strings: Handle null/None values consistently (empty string vs null)
   - **Testing Protocol**:
     * **NEVER trust "works locally" for multi-stage systems**
     * Test full end-to-end flow in cloud environment that matches production
     * If any service finds different results locally vs cloud, immediately check data flow compatibility
     * Create integration tests that verify data flows correctly between all stages
   - **Documentation Requirements**:
     * Document data contracts for each processing stage
     * List required fields, data types, and access patterns for each service
     * Update documentation when ANY stage changes its data expectations
   - **Red Flags - Immediate Investigation Required**:
     * Service works perfectly locally but fails in cloud
     * Different results between development and production environments
     * Services that depend on specific field access patterns (e.g., raw_data.field vs top_level_field)
     * Any service that shows "0 results" in cloud but positive results locally

## Project Structure Quick Reference
```
drcr_back/
├── rest_api/           # FastAPI application
├── cloud_functions/    # GCP Cloud Functions
├── drcr_shared_logic/  # Shared business logic
├── tests/              # Comprehensive test suite
├── scripts/            # Utility and deployment scripts
├── docs/               # Comprehensive documentation
├── terraform/          # Infrastructure as Code
├── deployment/         # Deployment configurations
└── local_utils/        # Development utilities
```

## Performance Metrics
- **Health endpoint**: 43.3ms average, 340+ req/sec
- **Xero OAuth flow**: ~2 seconds (93% improvement from 30s)
- **Token storage cost**: 80-90% reduction using Firestore vs Secret Manager
- **Database**: Global connection pooling and batch operations
- **Compression**: GZip middleware enabled

## Development Workflow
1. **Setup**: Install dependencies, configure environment variables
2. **Authentication**: Use `gcloud auth application-default login`
3. **Local Testing**: Start REST API server and run test suites
4. **Cloud Function Testing**: Use local testing modules
5. **Performance Testing**: Run performance validation scripts
6. **Deployment**: Use deployment scripts for production

## Common Issues & Solutions
- **Token expiration**: Use token management utilities in `tests/token_management/`
- **Firestore connection**: Check global database client initialization
- **Xero API issues**: Review connection details and OAuth flow
- **Performance issues**: Run performance tests and check optimization summary

This project follows modern Python FastAPI best practices with comprehensive testing, performance optimization, and production-ready deployment configurations.