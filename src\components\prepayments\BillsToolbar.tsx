import React from 'react';
import { But<PERSON> } from '../ui/button';
import { Loader2, Download, Edit, RotateCcw, Save, Send } from 'lucide-react';

interface BillsToolbarProps {
  // Status information
  totalEntries: number;
  postedEntries: number;
  
  // Button states
  canExport: boolean;
  canReset: boolean;
  canSaveChanges: boolean;
  canPostReady: boolean;
  
  // Loading states
  isExporting: boolean;
  isResetting: boolean;
  isSaving: boolean;
  isPosting: boolean;
  
  // Validation messages
  validationMessages?: {
    canSave: string[];
    canPost: string[];
    canReset: string[];
  };
  
  // Handlers
  onExport: () => void;
  onReset: () => void;
  onSaveChanges: () => void;
  onPostReady: () => void;
}

export function BillsToolbar({
  totalEntries,
  postedEntries,
  canExport,
  canReset,
  canSaveChanges,
  canPostReady,
  isExporting,
  isResetting,
  isSaving,
  isPosting,
  validationMessages,
  onExport,
  onReset,
  onSaveChanges,
  onPostReady,
}: BillsToolbarProps) {
  // Get current validation messages
  const hasValidationErrors = validationMessages && (
    validationMessages.canSave.length > 0 ||
    validationMessages.canPost.length > 0 ||
    validationMessages.canReset.length > 0
  );

  const getActiveValidationMessages = () => {
    if (!validationMessages) return [];
    
    const messages = [];
    if (!canSaveChanges && validationMessages.canSave.length > 0) {
      messages.push(...validationMessages.canSave.map(msg => `Save: ${msg}`));
    }
    if (!canPostReady && validationMessages.canPost.length > 0) {
      messages.push(...validationMessages.canPost.map(msg => `Post: ${msg}`));
    }
    if (!canReset && validationMessages.canReset.length > 0) {
      messages.push(...validationMessages.canReset.map(msg => `Reset: ${msg}`));
    }
    return messages;
  };

  const activeMessages = getActiveValidationMessages();

  return (
    <div className="absolute bottom-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-t border-gray-200 shadow-lg">
      {/* Validation messages bar */}
      {activeMessages.length > 0 && (
        <div className="px-6 py-2 bg-amber-50 border-b border-amber-200">
          <div className="flex items-center space-x-2 text-sm text-amber-700">
            <span className="font-medium">⚠️ Action requirements:</span>
            <span>{activeMessages.join(' • ')}</span>
          </div>
        </div>
      )}
      
      <div className="flex items-center justify-between px-6 py-3 max-w-full">
        {/* Left side - Status information */}
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          <span className="font-medium">
            {postedEntries} of {totalEntries} entries posted
          </span>
          {totalEntries > 0 && (
            <div className="w-32 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(postedEntries / totalEntries) * 100}%` }}
              />
            </div>
          )}
        </div>

        {/* Right side - Action buttons */}
        <div className="flex items-center space-x-2">
          {/* Export button */}
          <Button
            variant="outline"
            size="sm"
            onClick={onExport}
            disabled={true}
            className="hidden sm:flex"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>

          {/* Reset Configuration button */}
          <Button
            variant="outline"
            size="sm"
            onClick={onReset}
            disabled={!canReset || isResetting}
          >
            {isResetting ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RotateCcw className="h-4 w-4 mr-2" />
            )}
            Reset
          </Button>


          {/* Save Changes button - Primary action */}
          <Button
            size="sm"
            onClick={onSaveChanges}
            disabled={!canSaveChanges || isSaving}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>

          {/* Post Ready Entries button */}
          <Button
            size="sm"
            onClick={onPostReady}
            disabled={!canPostReady || isPosting}
            className="bg-green-600 hover:bg-green-700"
          >
            {isPosting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Posting...
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Post Ready Entries
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Mobile layout - Stack buttons if needed */}
      <div className="sm:hidden border-t border-gray-100 px-4 py-2">
        <div className="flex flex-wrap gap-2 justify-center">
          <Button
            variant="outline"
            size="sm"
            onClick={onExport}
            disabled={true}
          >
            <Download className="h-4 w-4" />
          </Button>
          {/* Add other mobile buttons as needed */}
        </div>
      </div>
    </div>
  );
}