import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Calendar, DollarSign, Clock, AlertCircle, CheckCircle2 } from 'lucide-react';
import { ScheduleStatus } from '../../types/schedule.types';
import { getScheduleStatusConfig, isTerminalStatus, isActionNeededStatus } from '../../types/schedule.types';

interface AmortizationSummaryProps {
  totalAmount: number;
  monthlyAmount: number;
  numberOfPeriods: number;
  startDate: string;
  endDate: string;
  status: ScheduleStatus;
  selectedBillsCount: number;
  currencyCode?: string;
}

export function AmortizationSummary({
  totalAmount,
  monthlyAmount,
  numberOfPeriods,
  startDate,
  endDate,
  status,
  selectedBillsCount,
  currencyCode = 'GBP',
}: AmortizationSummaryProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: currencyCode,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getStatusBadge = () => {
    const statusConfig = getScheduleStatusConfig(status);
    
    return (
      <Badge 
        variant={statusConfig.variant}
        className={statusConfig.className}
      >
        <span className="mr-1">{statusConfig.icon}</span>
        {statusConfig.label}
      </Badge>
    );
  };

  const getDateRange = () => {
    if (!startDate || !endDate) return 'No dates selected';
    const start = formatDate(startDate);
    const end = formatDate(endDate);
    return `${start} - ${end}`;
  };

  if (selectedBillsCount === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Schedule Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-lg font-medium mb-2">No Bills Selected</p>
            <p className="text-sm">Select bills from the left sidebar to see amortization summary</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          Schedule Summary
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Amortized Amount */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <DollarSign className="h-4 w-4" />
              Amortized Amount
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {formatCurrency(totalAmount)}
            </div>
            <div className="text-xs text-gray-500">
              {selectedBillsCount} bill{selectedBillsCount !== 1 ? 's' : ''} selected
            </div>
          </div>

          {/* Period */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Calendar className="h-4 w-4" />
              Period
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {numberOfPeriods} months
            </div>
            <div className="text-xs text-gray-500">
              {getDateRange()}
            </div>
          </div>

          {/* Monthly Amount */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Clock className="h-4 w-4" />
              Monthly Amount
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {formatCurrency(monthlyAmount)}
            </div>
            <div className="text-xs text-gray-500">
              Equal payments
            </div>
          </div>

          {/* Status */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <CheckCircle2 className="h-4 w-4" />
              Status
            </div>
            <div className="flex items-center">
              {getStatusBadge()}
            </div>
            <div className="text-xs text-gray-500">
              {status === ScheduleStatus.PENDING_CONFIGURATION
                ? 'Needs configuration'
                : status === ScheduleStatus.PROPOSED
                ? 'Ready for review'
                : status === ScheduleStatus.CONFIRMED
                ? 'Configuration complete'
                : status === ScheduleStatus.POSTED || status === ScheduleStatus.PARTIALLY_POSTED
                ? 'Schedule completed'
                : status === ScheduleStatus.CANCELLED || status === ScheduleStatus.SKIPPED || status === ScheduleStatus.ERROR
                ? 'Schedule terminated'
                : 'In progress'
              }
            </div>
          </div>
        </div>

        {/* Additional Summary Info */}
        {totalAmount > 0 && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">
                Straight line amortization over {numberOfPeriods} periods
              </span>
              <span className="font-medium">
                {formatCurrency(monthlyAmount)} per month
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}