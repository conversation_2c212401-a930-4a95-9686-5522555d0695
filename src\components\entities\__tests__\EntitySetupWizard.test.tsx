import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { EntitySetupWizard } from '../EntitySetupWizard';

// Mock the API client
const mockApiClient = {
  getEntityWizardAnalysis: vi.fn(),
  completeEntitySetup: vi.fn(),
};

vi.mock('@/lib/api', () => ({
  ApiClient: vi.fn().mockImplementation(() => mockApiClient),
}));

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({
      clientId: 'test-client-123',
      entityId: 'test-entity-456',
    }),
    useNavigate: () => mockNavigate,
  };
});

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

// Sample test data
const mockWizardAnalysis = {
  entity_id: 'test-entity-456',
  entity_name: 'Test Company Ltd',
  organization_info: {
    name: 'Test Company Ltd',
    base_currency: 'GBP',
    financial_year_start: '2024-04-01',
  },
  bills_analysis: {
    bills_with_attachments: 25,
    total_bills: 150,
    scanning_cost_estimate: 'Variable (1 credit per page - most bills are 1-3 pages)',
    expected_prepayments: '5-8 items',
    financial_year_start: '2024-04-01',
  },
  accounts_analysis: {
    prepayment_asset_accounts: [
      {
        code: '1200',
        name: 'Prepaid Insurance',
        recommended: true,
      },
      {
        code: '1201',
        name: 'Prepaid Rent',
        recommended: true,
      },
    ],
    relevant_expense_accounts: [
      {
        code: '6200',
        name: 'Insurance Expense',
      },
    ],
    suggested_exclusions: [
      {
        code: '6300',
        name: 'Bank Fees',
      },
    ],
  },
  recommendations: {
    sync_start_date: '2024-04-01',
    sync_frequency: 'daily',
    enable_ai_scanning: true,
    selected_asset_accounts: ['1200', '1201'],
    excluded_accounts: ['6300'],
    base_currency: 'GBP',
  },
};

// Wrapper component for router context
const WizardWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>{children}</BrowserRouter>
);

describe('EntitySetupWizard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockApiClient.getEntityWizardAnalysis.mockResolvedValue(mockWizardAnalysis);
    mockApiClient.completeEntitySetup.mockResolvedValue({
      message: 'Setup completed successfully',
      entity_id: 'test-entity-456',
    });
  });

  it('renders loading state initially', () => {
    render(
      <WizardWrapper>
        <EntitySetupWizard />
      </WizardWrapper>
    );

    expect(screen.getByText('Analyzing your entity data...')).toBeInTheDocument();
  });

  it('renders wizard steps after loading', async () => {
    render(
      <WizardWrapper>
        <EntitySetupWizard />
      </WizardWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Setup Test Company Ltd')).toBeInTheDocument();
    });

    // Check progress indicator
    expect(screen.getByText('Document Scanning')).toBeInTheDocument();
    expect(screen.getByText('Sync Settings')).toBeInTheDocument();
    expect(screen.getByText('Complete')).toBeInTheDocument();
  });

  it('displays document scanning information in step 1', async () => {
    render(
      <WizardWrapper>
        <EntitySetupWizard />
      </WizardWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('AI Document Scanning')).toBeInTheDocument();
    });

    // Check bills analysis display
    expect(screen.getByText('25')).toBeInTheDocument(); // bills with attachments
    expect(screen.getByText('5-8 items')).toBeInTheDocument(); // expected prepayments
    expect(screen.getByText('Variable (1 credit per page - most bills are 1-3 pages)')).toBeInTheDocument();

    // Check AI scanning checkbox
    const checkbox = screen.getByLabelText('Enable AI document scanning (Recommended)');
    expect(checkbox).toBeChecked(); // Should be enabled by default based on recommendations
  });

  it('allows navigation between steps', async () => {
    render(
      <WizardWrapper>
        <EntitySetupWizard />
      </WizardWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('AI Document Scanning')).toBeInTheDocument();
    });

    // Navigate to step 2
    const nextButton = screen.getByText('Next');
    fireEvent.click(nextButton);

    await waitFor(() => {
      expect(screen.getByText('Sync Configuration')).toBeInTheDocument();
    });

    // Navigate back to step 1
    const previousButton = screen.getByText('Previous');
    fireEvent.click(previousButton);

    await waitFor(() => {
      expect(screen.getByText('AI Document Scanning')).toBeInTheDocument();
    });
  });

  it('displays sync settings in step 2', async () => {
    render(
      <WizardWrapper>
        <EntitySetupWizard />
      </WizardWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('AI Document Scanning')).toBeInTheDocument();
    });

    // Navigate to step 2
    fireEvent.click(screen.getByText('Next'));

    await waitFor(() => {
      expect(screen.getByText('Sync Configuration')).toBeInTheDocument();
    });

    // Check sync period start date input
    const dateInput = screen.getByDisplayValue('2024-04-01');
    expect(dateInput).toBeInTheDocument();

    // Check sync frequency select
    const frequencySelect = screen.getByDisplayValue('Daily (Recommended)');
    expect(frequencySelect).toBeInTheDocument();

    // Check data types checkboxes
    expect(screen.getByLabelText('Bills & Purchases')).toBeChecked();
    expect(screen.getByLabelText('Sales Invoices')).toBeChecked();

    // Check prepayment asset accounts
    expect(screen.getByLabelText('[1200] Prepaid Insurance')).toBeChecked();
    expect(screen.getByLabelText('[1201] Prepaid Rent')).toBeChecked();
  });

  it('shows review summary in step 3', async () => {
    render(
      <WizardWrapper>
        <EntitySetupWizard />
      </WizardWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('AI Document Scanning')).toBeInTheDocument();
    });

    // Navigate to step 3
    fireEvent.click(screen.getByText('Next'));
    await waitFor(() => {
      expect(screen.getByText('Sync Configuration')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Next'));
    await waitFor(() => {
      expect(screen.getByText('Review & Complete')).toBeInTheDocument();
    });

    // Check review content
    expect(screen.getByText('✓ Enabled - Will scan 25 documents')).toBeInTheDocument();
    expect(screen.getByText('daily sync from 2024-04-01')).toBeInTheDocument();
    expect(screen.getByText('2 accounts configured')).toBeInTheDocument();

    // Check complete setup button
    expect(screen.getByText('Complete Setup')).toBeInTheDocument();
  });

  it('handles AI scanning toggle', async () => {
    render(
      <WizardWrapper>
        <EntitySetupWizard />
      </WizardWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('AI Document Scanning')).toBeInTheDocument();
    });

    const checkbox = screen.getByLabelText('Enable AI document scanning (Recommended)');
    
    // Toggle off
    fireEvent.click(checkbox);
    expect(checkbox).not.toBeChecked();

    // Navigate to review step to check the change
    fireEvent.click(screen.getByText('Next'));
    fireEvent.click(screen.getByText('Next'));

    await waitFor(() => {
      expect(screen.getByText('○ Disabled - Can be enabled later')).toBeInTheDocument();
    });
  });

  it('handles form submission', async () => {
    render(
      <WizardWrapper>
        <EntitySetupWizard />
      </WizardWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('AI Document Scanning')).toBeInTheDocument();
    });

    // Navigate to final step
    fireEvent.click(screen.getByText('Next'));
    fireEvent.click(screen.getByText('Next'));

    await waitFor(() => {
      expect(screen.getByText('Complete Setup')).toBeInTheDocument();
    });

    // Submit the form
    fireEvent.click(screen.getByText('Complete Setup'));

    await waitFor(() => {
      expect(mockApiClient.completeEntitySetup).toHaveBeenCalledWith(
        'test-entity-456',
        expect.objectContaining({
          enable_ai_scanning: true,
          sync_settings: expect.any(Object),
          account_settings: expect.any(Object),
        })
      );
    });

    expect(mockNavigate).toHaveBeenCalledWith('/clients/test-client-123/entities');
  });

  it('handles API errors gracefully', async () => {
    mockApiClient.getEntityWizardAnalysis.mockRejectedValue(new Error('API Error'));

    render(
      <WizardWrapper>
        <EntitySetupWizard />
      </WizardWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Setup Error')).toBeInTheDocument();
      expect(screen.getByText('Failed to load entity data. Please try again.')).toBeInTheDocument();
    });

    expect(screen.getByText('Back to Entities')).toBeInTheDocument();
  });

  it('disables previous button on first step', async () => {
    render(
      <WizardWrapper>
        <EntitySetupWizard />
      </WizardWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('AI Document Scanning')).toBeInTheDocument();
    });

    const previousButton = screen.getByText('Previous');
    expect(previousButton).toBeDisabled();
  });

  it('shows loading state during submission', async () => {
    // Make the API call take longer
    mockApiClient.completeEntitySetup.mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve({ message: 'Success', entity_id: 'test' }), 100))
    );

    render(
      <WizardWrapper>
        <EntitySetupWizard />
      </WizardWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('AI Document Scanning')).toBeInTheDocument();
    });

    // Navigate to final step and submit
    fireEvent.click(screen.getByText('Next'));
    fireEvent.click(screen.getByText('Next'));
    fireEvent.click(screen.getByText('Complete Setup'));

    // Should show loading state
    expect(screen.getByText('Complete Setup')).toBeDisabled();
  });

  it('updates sync settings correctly', async () => {
    render(
      <WizardWrapper>
        <EntitySetupWizard />
      </WizardWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('AI Document Scanning')).toBeInTheDocument();
    });

    // Navigate to sync settings
    fireEvent.click(screen.getByText('Next'));

    await waitFor(() => {
      expect(screen.getByText('Sync Configuration')).toBeInTheDocument();
    });

    // Change sync frequency
    const frequencySelect = screen.getByDisplayValue('Daily (Recommended)');
    fireEvent.change(frequencySelect, { target: { value: 'weekly' } });

    // Uncheck a data type
    const billsCheckbox = screen.getByLabelText('Bills & Purchases');
    fireEvent.click(billsCheckbox);

    // Navigate to review and check changes are reflected
    fireEvent.click(screen.getByText('Next'));

    await waitFor(() => {
      expect(screen.getByText('weekly sync from 2024-04-01')).toBeInTheDocument();
    });
  });
});