import React from 'react';
import { Badge } from '@/components/ui/badge';
import { ScheduleStatus, getScheduleStatusConfig } from '@/types/schedule.types';

interface StatusBadgeProps {
  status: ScheduleStatus;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
}

export function StatusBadge({ 
  status, 
  size = 'md', 
  showIcon = true, 
  className = '' 
}: StatusBadgeProps) {
  const config = getScheduleStatusConfig(status);
  
  const sizeClasses = {
    sm: 'text-xs px-1.5 py-0.5',
    md: 'text-xs px-2 py-1',
    lg: 'text-sm px-3 py-1.5'
  };

  return (
    <Badge 
      variant={config.variant} 
      className={`${config.className} ${sizeClasses[size]} ${className}`}
    >
      {showIcon && config.icon && (
        <span className="mr-1" role="img" aria-label={config.label}>
          {config.icon}
        </span>
      )}
      {config.label}
    </Badge>
  );
} 