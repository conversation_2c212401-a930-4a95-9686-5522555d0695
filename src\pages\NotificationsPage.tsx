import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFirmName } from '../hooks/useFirmName';
import { AppSidebar } from '../components/layout/AppSidebar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Switch } from '../components/ui/switch';
import { Label } from '../components/ui/label';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { Separator } from '../components/ui/separator';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '../components/ui/breadcrumb';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '../components/ui/sidebar';
import { Bell, Mail, Smartphone, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { toast } from 'sonner';

interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  prepaymentAlerts: boolean;
  syncFailures: boolean;
  weeklyReports: boolean;
  monthlyReports: boolean;
  systemUpdates: boolean;
  securityAlerts: boolean;
}

export function NotificationsPage() {
  const navigate = useNavigate();
  const { firmName, isLoading: firmNameLoading } = useFirmName();
  const [settings, setSettings] = useState<NotificationSettings>({
    emailNotifications: true,
    pushNotifications: false,
    prepaymentAlerts: true,
    syncFailures: true,
    weeklyReports: true,
    monthlyReports: false,
    systemUpdates: true,
    securityAlerts: true,
  });

  const [isLoading, setIsLoading] = useState(false);

  const handleSettingChange = (key: keyof NotificationSettings, value: boolean) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSaveSettings = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement actual API call to save notification settings
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      toast.success('Notification settings updated successfully');
    } catch (error) {
      toast.error('Failed to update notification settings');
    } finally {
      setIsLoading(false);
    }
  };

  // Sample recent notifications
  const recentNotifications = [
    {
      id: 1,
      type: 'success',
      title: 'Prepayment Schedule Created',
      message: 'New amortization schedule created for Invoice #INV-2024-001',
      timestamp: '2 hours ago',
      read: false
    },
    {
      id: 2,
      type: 'warning',
      title: 'Sync Warning',
      message: 'Some transactions could not be processed automatically',
      timestamp: '1 day ago',
      read: true
    },
    {
      id: 3,
      type: 'info',
      title: 'Weekly Report Available',
      message: 'Your weekly prepayment summary is ready for review',
      timestamp: '3 days ago',
      read: true
    },
    {
      id: 4,
      type: 'error',
      title: 'Xero Connection Issue',
      message: 'Connection to Xero entity "ABC Corp" failed',
      timestamp: '1 week ago',
      read: true
    }
  ];

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-amber-600" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Bell className="h-4 w-4 text-blue-600" />;
    }
  };

  const getNotificationBadge = (type: string) => {
    switch (type) {
      case 'success':
        return <Badge variant="default" className="bg-green-100 text-green-800">Success</Badge>;
      case 'warning':
        return <Badge variant="default" className="bg-amber-100 text-amber-800">Warning</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="secondary">Info</Badge>;
    }
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset className="flex-1 overflow-hidden">
        <header className="sticky top-0 z-10 flex h-16 shrink-0 items-center gap-2 border-b bg-background px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate('/dashboard');
                  }}
                  className="cursor-pointer"
                >
                  {firmNameLoading ? 'Loading...' : firmName}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage>Notifications</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </header>
        <div className="flex-1 overflow-auto">
          <div className="p-8 max-w-4xl">
            <div className="mb-8">
              <h1 className="text-2xl font-bold mb-2">Notifications</h1>
              <p className="text-muted-foreground">
                Manage your notification preferences and view recent alerts.
              </p>
            </div>

            <div className="space-y-6">
          {/* Notification Settings Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notification Settings
              </CardTitle>
              <CardDescription>
                Configure how and when you receive notifications.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* General Settings */}
              <div>
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  General Settings
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="email-notifications">Email Notifications</Label>
                      <p className="text-sm text-muted-foreground">Receive notifications via email</p>
                    </div>
                    <Switch
                      id="email-notifications"
                      checked={settings.emailNotifications}
                      onCheckedChange={(checked) => handleSettingChange('emailNotifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="push-notifications">Push Notifications</Label>
                      <p className="text-sm text-muted-foreground">Receive browser push notifications</p>
                    </div>
                    <Switch
                      id="push-notifications"
                      checked={settings.pushNotifications}
                      onCheckedChange={(checked) => handleSettingChange('pushNotifications', checked)}
                      disabled
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Prepayment Alerts */}
              <div>
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <AlertCircle className="h-4 w-4" />
                  Prepayment Alerts
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="prepayment-alerts">Prepayment Detection</Label>
                      <p className="text-sm text-muted-foreground">Alert when new prepayments are detected</p>
                    </div>
                    <Switch
                      id="prepayment-alerts"
                      checked={settings.prepaymentAlerts}
                      onCheckedChange={(checked) => handleSettingChange('prepaymentAlerts', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="sync-failures">Sync Failures</Label>
                      <p className="text-sm text-muted-foreground">Alert when data synchronization fails</p>
                    </div>
                    <Switch
                      id="sync-failures"
                      checked={settings.syncFailures}
                      onCheckedChange={(checked) => handleSettingChange('syncFailures', checked)}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Reports */}
              <div>
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Scheduled Reports
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="weekly-reports">Weekly Reports</Label>
                      <p className="text-sm text-muted-foreground">Receive weekly prepayment summaries</p>
                    </div>
                    <Switch
                      id="weekly-reports"
                      checked={settings.weeklyReports}
                      onCheckedChange={(checked) => handleSettingChange('weeklyReports', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="monthly-reports">Monthly Reports</Label>
                      <p className="text-sm text-muted-foreground">Receive monthly prepayment summaries</p>
                    </div>
                    <Switch
                      id="monthly-reports"
                      checked={settings.monthlyReports}
                      onCheckedChange={(checked) => handleSettingChange('monthlyReports', checked)}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* System Notifications */}
              <div>
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <Smartphone className="h-4 w-4" />
                  System Notifications
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="system-updates">System Updates</Label>
                      <p className="text-sm text-muted-foreground">Notifications about system updates and maintenance</p>
                    </div>
                    <Switch
                      id="system-updates"
                      checked={settings.systemUpdates}
                      onCheckedChange={(checked) => handleSettingChange('systemUpdates', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="security-alerts">Security Alerts</Label>
                      <p className="text-sm text-muted-foreground">Important security notifications</p>
                    </div>
                    <Switch
                      id="security-alerts"
                      checked={settings.securityAlerts}
                      onCheckedChange={(checked) => handleSettingChange('securityAlerts', checked)}
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end pt-4">
                <Button onClick={handleSaveSettings} disabled={isLoading}>
                  {isLoading ? 'Saving...' : 'Save Settings'}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Recent Notifications Card */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Notifications</CardTitle>
              <CardDescription>
                Your latest notifications and alerts.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`flex items-start gap-3 p-3 rounded-lg border ${
                      !notification.read ? 'bg-blue-50 border-blue-200' : 'bg-muted/50'
                    }`}
                  >
                    {getNotificationIcon(notification.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm">{notification.title}</h4>
                        {getNotificationBadge(notification.type)}
                        {!notification.read && (
                          <Badge variant="secondary" className="text-xs">New</Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">{notification.message}</p>
                      <p className="text-xs text-muted-foreground mt-1">{notification.timestamp}</p>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-4">
                <Button variant="outline" disabled>
                  View All Notifications (Coming Soon)
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Coming Soon Notice */}
          <Card className="border-amber-200 bg-amber-50">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-amber-800">Advanced Notification Features Coming Soon</h3>
                  <p className="text-sm text-amber-700 mt-1">
                    Push notifications, advanced filtering, and notification history features are currently
                    in development. Email notifications are fully functional.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
