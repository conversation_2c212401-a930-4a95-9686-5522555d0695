import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Search, PlusCircle } from 'lucide-react';
import type { DashboardFilters } from '../types';

interface DashboardFiltersProps {
  filters: DashboardFilters;
  onFiltersChange: (filters: Partial<DashboardFilters>) => void;
  onAddNewClient: () => void;
  isAdmin?: boolean;
}

export function DashboardFiltersComponent({ 
  filters, 
  onFiltersChange, 
  onAddNewClient, 
  isAdmin = true 
}: DashboardFiltersProps) {
  return (
    <div className="flex flex-row gap-2 items-center p-2 border-b bg-white flex-shrink-0 rounded-lg shadow-sm">
      <Select 
        value={filters.statusFilter} 
        onValueChange={(value) => onFiltersChange({ statusFilter: value })}
      >
        <SelectTrigger className="w-[150px] justify-between bg-white text-xs h-8">
          <SelectValue placeholder="Filter by status..." />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Statuses</SelectItem>
          <SelectItem value="action_needed">Action Needed</SelectItem>
          <SelectItem value="error">Error</SelectItem>
          <SelectItem value="ok">OK</SelectItem>
        </SelectContent>
      </Select>

      <div className="flex-grow"></div>

      <div className="relative">
        <Search className="absolute left-2 top-2 h-3 w-3 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search clients..."
          value={filters.clientFilter}
          onChange={(e) => onFiltersChange({ clientFilter: e.target.value })}
          className="pl-6 w-[300px] h-8 text-xs"
        />
      </div>
      <div className="flex items-center space-x-2">
        {isAdmin && (
          <Button onClick={onAddNewClient} className="h-8 text-xs">
            <PlusCircle className="mr-1 h-3 w-3" /> Add Client
          </Button>
        )}
      </div>
    </div>
  );
} 