# cloud_functions/xero_sync_consumer/main.py
import os
import sys # Ensure sys is imported

# Add the current and parent directories to sys.path
# In cloud function context, we need proper path setup for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)

# Add current directory to sys.path for local imports (utils, endpoints, etc.)
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Add parent directory to sys.path for cloud_functions package (when running locally)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

import base64 # Keep standard library imports first if preferred
import json
import asyncio
import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone, date
import re

from dotenv import load_dotenv
load_dotenv()  # Load .env file as early as possible

from google.cloud import firestore
import httpx
import functions_framework

# UPDATED IMPORTS for shared logic:
from drcr_shared_logic.clients.base_client import BaseAccountingClient
from drcr_shared_logic.clients.xero_client import XeroApiClient
from rest_api.services.amortization_service import AmortizationService
from drcr_shared_logic.document_processor import (
    extract_data_with_openai_direct,
    is_direct_openai_attempt_sufficient,
    process_pdf_with_mistral_ocr,
    process_image_with_mistral_ocr,
    extract_structured_data_with_openai,
    post_process_extracted_invoice_data,
)

# REFACTORED HELPER IMPORTS - Use relative imports for Cloud Function deployment
from utils.sync_helpers import _upload_to_gcs, get_other_secret, _normalize_to_float, _create_audit_log_entry, _update_firestore_sync_timestamp, _trigger_heavy_processing_stage
from utils.llm_utils import _fetch_and_process_attachments, _perform_combined_prepayment_analysis
from utils.bills_processor import _check_if_current_version_exists

# Settings management
from settings import get_settings

# Modular architecture
from context import SyncContext
from dispatcher import dispatch_sync_endpoints, is_endpoint_modularized

# Load secrets from Secret Manager in production (if enabled)
try:
    from drcr_shared_logic.config.secret_loader import load_secrets_at_startup
    SECRET_LOADER_AVAILABLE = True
except ImportError:
    SECRET_LOADER_AVAILABLE = False

# Initialize secrets loading
if SECRET_LOADER_AVAILABLE and os.getenv("LOAD_SECRETS_FROM_SECRET_MANAGER", "false").lower() == "true":
    try:
        success = load_secrets_at_startup()
        if success:
            print("Cloud Function: Successfully loaded secrets from Secret Manager")
        else:
            print("Cloud Function: Some secrets failed to load from Secret Manager - continuing with environment variables")
    except Exception as e:
        print(f"Cloud Function: Failed to load secrets from Secret Manager: {e}")
        print("Cloud Function: Continuing with environment variables...")
else:
    print("Cloud Function: Using environment variables for secrets")

# Initialize settings and configure logging
settings = get_settings()
logger = logging.getLogger(__name__)

# GCP Project ID from settings
GCP_PROJECT_ID = settings.GCP_PROJECT_ID


# --- Audit Log Helper Function moved to sync_helpers.py ---


# --- Entity Settings Helper Function moved to sync_helpers.py ---


# --- Firestore Sync Timestamp Helper moved to sync_helpers.py ---


async def get_accounting_client(
    platform: str, entity_id: str, client_id: Optional[str] # Changed platform_org_id to entity_id, tenant_id to client_id
) -> BaseAccountingClient:
    """
    Factory function to instantiate and return the appropriate accounting client.
    """
    # Configurations for each client can be loaded from env vars or a config file/service
    # These are passed to the client constructor.
    client_config: Dict[str, Any] = {
        "GCP_PROJECT_ID": GCP_PROJECT_ID,
        "XERO_CLIENT_ID": os.getenv("XERO_CLIENT_ID"),
        "XERO_CLIENT_SECRET": os.getenv("XERO_CLIENT_SECRET"),
        "XERO_REDIRECT_URI": os.getenv("XERO_REDIRECT_URI_REST_API"),
        "XERO_SCOPES": os.getenv(
            "XERO_SCOPES", "accounting.transactions,accounting.settings,offline_access"
        ),
        "XERO_OAUTH_SECRET_NAME": os.getenv(
            "XERO_OAUTH_SECRET_NAME_PREFIX", "xero-oauth-tokens"
        )
        + f"_{entity_id}", # Use entity_id
    }

    if platform.lower() == "xero":
        logger.info(f"Creating XeroApiClient for entity_id: {entity_id}")
        # XeroApiClient's create method expects platform_org_id and tenant_id
        # We map our standardized entity_id and client_id to these expected parameters
        client = await XeroApiClient.create( 
            platform_org_id=entity_id, 
            tenant_id=client_id, 
            config=client_config
        )
        return client
    else:
        error_msg = f"Unsupported accounting platform: {platform}"
        logger.error(error_msg)
        raise ValueError(error_msg)


# Helper function to parse date strings robustly
def _parse_date_string_to_date(date_string: Optional[str]) -> Optional[date]:
    if not date_string:
        return None
    try:
        # Xero often uses ISO 8601 format like "YYYY-MM-DDTHH:mm:ss"
        # We only need the date part.
        return datetime.fromisoformat(date_string.replace("Z", "+00:00")).date()
    except ValueError:
        try:
            # Attempt other common formats if ISO fails, e.g., "YYYY-MM-DD"
            return datetime.strptime(date_string, "%Y-%m-%d").date()
        except ValueError:
            logger.warning(f"Could not parse date string: {date_string}")
            return None


@functions_framework.cloud_event
def xero_sync_consumer(cloud_event):
    """Cloud Function entry point for Pub/Sub messages."""
    print("XERO SYNC CONSUMER STARTED - Basic print statement")
    logger.info("XERO SYNC CONSUMER STARTED - Logger statement")
    logger.info(f"Received Pub/Sub cloud event: {cloud_event}")
    
    # Extract the event data from the cloud event
    if not cloud_event.data:
        logger.error("Invalid Pub/Sub message: Missing data field.")
        return
    
    # Convert cloud event to the expected format
    event = {
        "data": cloud_event.data.get("message", {}).get("data", "")
    }
    
    # Use existing event loop instead of creating new one
    # This avoids the performance penalty of asyncio.run()
    try:
        loop = asyncio.get_running_loop()
        # If we're already in an async context, create a task
        logger.debug("Event loop already running, using asyncio.create_task")
        asyncio.create_task(_process_xero_sync_event(event))
    except RuntimeError:
        # No running loop - standard case for Cloud Functions
        # Use new_event_loop() for Python 3.11+ compatibility
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(_process_xero_sync_event(event))
        finally:
            loop.close()


async def _process_xero_sync_event(event):
    """Process the Xero sync event (extracted from cloud function for reuse)."""
    logger.info(f"Processing Xero sync event: {event}")
    if not event or "data" not in event:
        logger.error("Invalid Pub/Sub message: Missing data field.")
        return

    # Initialize db to None to handle cases where it's not created before an error
    db = None

    try:
        message_data_str = base64.b64decode(event["data"]).decode("utf-8")
        message_data = json.loads(message_data_str)
        logger.info(f"Decoded message data: {message_data}")
    except Exception as e:
        logger.error(f"Error decoding Pub/Sub message data: {e}", exc_info=True)
        return

    # Standardize IDs from message payload
    entity_id = message_data.get("platformOrgId") or message_data.get("xeroTenantId")
    client_id = message_data.get("tenantId") 
    
    if not entity_id:
        logger.error(f"Missing platformOrgId/xeroTenantId (mapped to entity_id) in message data: {message_data}")
        # db initialization for audit log
        db_for_audit = firestore.AsyncClient(project=GCP_PROJECT_ID)
        try:
            await _create_audit_log_entry(
                db_for_audit, "SYNC", "XERO_SYNC_JOB_FAILURE", client_id, "UNKNOWN_ENTITY", "FAILURE", 
                {"error": "Missing entity_id in PubSub message", "message_data": message_data}
            )
        finally:
            await db_for_audit.close()
        return

    if not client_id:
        # Attempt to fetch client_id from entity_settings later if not provided in message
        logger.warning(f"Missing tenantId (mapped to client_id) in message data for entity_id {entity_id}. Will attempt to retrieve from settings.")

    sync_job_id = message_data.get("syncJobId", str(uuid.uuid4()))
    requested_endpoints = message_data.get("endpoints", [])
    force_full_sync_endpoints = message_data.get("forceFullSyncEndpoints", [])
    target_date_str = message_data.get("targetDate")
    target_date = _parse_date_string_to_date(target_date_str) if target_date_str else date.today()

    db = firestore.AsyncClient(project=GCP_PROJECT_ID)

    try:
        await _create_audit_log_entry(
            db, "SYNC", "XERO_SYNC_JOB_STARTED", client_id, entity_id, "STARTED", 
            {"syncJobId": sync_job_id, "requestedEndpoints": requested_endpoints, "forceFullSync": force_full_sync_endpoints}
        )

        entity_settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
        entity_settings_doc = await entity_settings_ref.get()
        if not entity_settings_doc.exists:
            logger.error(f"Entity settings not found for entity_id: {entity_id}. Cannot proceed.")
            await _create_audit_log_entry(
                db, "SYNC", "XERO_SYNC_JOB_FAILURE", client_id, entity_id, "FAILURE", 
                {"error": "Entity settings not found", "syncJobId": sync_job_id}
            )
            return
        
        raw_entity_settings = entity_settings_doc.to_dict()
        # Merge with global settings using the Settings system
        entity_settings = settings.get_entity_settings(raw_entity_settings)
        
        if not client_id and raw_entity_settings.get("clientId"):
            client_id = raw_entity_settings["clientId"] # Retrieve client_id from settings
            logger.info(f"Retrieved client_id '{client_id}' from entity_settings for entity_id '{entity_id}'.")
        elif not client_id: # Still no client_id
             logger.warning(f"client_id could not be determined for entity_id {entity_id}. Some operations might be affected.")
        
        # Initialize counters for sync operations
        processed_prepayments_count = 0


        xero_client = await get_accounting_client(
            platform="xero", entity_id=entity_id, client_id=client_id # Pass standardized IDs
        )
        
        if not await xero_client.is_authenticated():
            logger.error(f"Xero client authentication failed for entity_id: {entity_id}.")
            await _create_audit_log_entry(
                db, "SYNC", "XERO_AUTHENTICATION_FAILURE", client_id, entity_id, "FAILURE", 
                {"error": "Xero client authentication failed", "syncJobId": sync_job_id}
            )
            return

        # Organisation Sync - Fetch and store critical organisation details
        logger.info(f"Starting Organisation sync for entity_id: {entity_id}, client_id: {client_id}")
        try:
            org_details = await xero_client.get_organisation_details()
            if org_details:
                # Update ENTITIES collection with organisation details
                entity_ref = db.collection("ENTITIES").document(entity_id)
                entity_update = {
                    "xero_organisation_id": org_details.get("OrganisationID"),
                    "xero_short_code": org_details.get("ShortCode"),
                    "base_currency": org_details.get("BaseCurrency"),
                    "country_code": org_details.get("CountryCode"),
                    "timezone": org_details.get("Timezone"),
                    "financial_year_end_day": org_details.get("FinancialYearEndDay"),
                    "financial_year_end_month": org_details.get("FinancialYearEndMonth"),
                    "organisation_status": org_details.get("OrganisationStatus"),
                    "updated_at": firestore.SERVER_TIMESTAMP
                }
                
                # Calculate current financial year start date
                financial_year_start_date = None
                if org_details.get("FinancialYearEndMonth") and org_details.get("FinancialYearEndDay"):
                    try:
                        today = date.today()
                        current_year = today.year
                        fy_end_month = int(org_details.get("FinancialYearEndMonth"))
                        fy_end_day = int(org_details.get("FinancialYearEndDay"))
                        
                        # Financial year end date for current calendar year
                        fy_end_current_year = date(current_year, fy_end_month, fy_end_day)
                        
                        if today <= fy_end_current_year:
                            # We're still in the financial year that started last calendar year
                            if fy_end_month == 12:
                                # If FY ends in December, next FY starts January 1st of current year
                                fy_start = date(current_year, 1, 1)
                            else:
                                # FY starts the day after end date of previous year
                                fy_start = date(current_year - 1, fy_end_month + 1, 1)
                        else:
                            # We're in the new financial year that started this calendar year
                            if fy_end_month == 12:
                                # If FY ends in December, next FY starts January 1st of next year
                                fy_start = date(current_year + 1, 1, 1)
                            else:
                                # FY starts the day after end date of current year
                                fy_start = date(current_year, fy_end_month + 1, 1)
                        
                        financial_year_start_date = fy_start.strftime('%Y-%m-%d')
                        entity_update["financial_year_start_date"] = financial_year_start_date
                        
                        logger.info(f"Calculated financial year start date for entity {entity_id}: {financial_year_start_date} (FY ends {fy_end_month}/{fy_end_day})")
                    except Exception as e:
                        logger.warning(f"Failed to calculate financial year start date for entity {entity_id}: {e}")
                # Remove None values
                entity_update = {k: v for k, v in entity_update.items() if v is not None}
                
                await entity_ref.update(entity_update)
                logger.info(f"Successfully updated ENTITIES with organisation details for entity_id: {entity_id}. ShortCode: {org_details.get('ShortCode')}")
                
                await _create_audit_log_entry(
                    db, "SYNC", "ORGANISATION_SYNC_SUCCESS", client_id, entity_id, "SUCCESS", 
                    {"shortCode": org_details.get("ShortCode"), "organisationID": org_details.get("OrganisationID"), "syncJobId": sync_job_id}
                )
            else:
                logger.warning(f"No organisation details returned for entity_id: {entity_id}")
                await _create_audit_log_entry(
                    db, "SYNC", "ORGANISATION_SYNC_WARNING", client_id, entity_id, "WARNING", 
                    {"error": "No organisation details returned", "syncJobId": sync_job_id}
                )
        except Exception as e_org:
            logger.error(f"Error syncing Organisation for entity_id {entity_id}: {e_org}", exc_info=True)
            await _create_audit_log_entry(
                db, "SYNC", "ORGANISATION_SYNC_FAILURE", client_id, entity_id, "FAILURE", 
                {"error": str(e_org), "syncJobId": sync_job_id}
            )


        # Create shared sync context for modular endpoints
        ctx = SyncContext(
            db=db,
            xero_client=xero_client,
            settings=settings,
            entity_id=entity_id,
            client_id=client_id,
            raw_entity_settings=raw_entity_settings,
            entity_settings=entity_settings,  # This will be overridden in __post_init__
            message_data=message_data,
            force_full_sync_endpoints=force_full_sync_endpoints,
            sync_job_id=sync_job_id,
            processed_prepayments_count=processed_prepayments_count
        )
        
        # Dispatch to modular endpoints
        modular_results = await dispatch_sync_endpoints(ctx, requested_endpoints)
        processed_prepayments_count = ctx.processed_prepayments_count
        
        # Extract counts for backward compatibility
        saved_bills_count = modular_results.get("Bills", {}).get("count", 0)
        saved_invoices_count = modular_results.get("Invoices", {}).get("count", 0)
        saved_contacts_count = modular_results.get("Contacts", {}).get("count", 0)
        saved_accounts_count = modular_results.get("Accounts", {}).get("count", 0)
        saved_journals_count = modular_results.get("ManualJournals", {}).get("count", 0)
        saved_spend_money_count = modular_results.get("SpendMoney", {}).get("count", 0)
        saved_payments_count = modular_results.get("Payments", {}).get("count", 0)
        saved_bank_transactions_count = modular_results.get("BankTransactions", {}).get("count", 0)

        # Legacy endpoints processing (to be extracted)



        # Proposed Journals Processing - SKIP IN TWO-STAGE MODE
        # This is heavy processing that should happen in Stage 2 (Cloud Run)
        use_two_stage_processing = entity_settings.get("_system_enableTwoStageSync", True)
        
        if ("ProposedJournals" in requested_endpoints or not requested_endpoints) and not use_two_stage_processing:
            logger.info(f"Processing proposed journals for entity {entity_id} (legacy single-stage mode)")
            await _generate_proposed_journals_for_due_entries(
                db=db, client_id=client_id, entity_id=entity_id, entity_settings=entity_settings, target_date=target_date
            )
            if entity_settings.get("_system_autoPostProposedJournalsToXero", False):
                await _post_proposed_journals_to_xero(
                    db=db, client_id=client_id, entity_id=entity_id, entity_settings=entity_settings, xero_client_for_posting=xero_client
                )
        elif use_two_stage_processing:
            logger.info(f"Skipping proposed journal processing for entity {entity_id} (two-stage mode - will be handled in Stage 2 Cloud Run)")
        
        # Trigger Stage 2 heavy processing after all endpoints have completed (including batch commits)
        # Run detector regardless of bill count - manual journals may need processing even with 0 bills
        sync_status = "SUCCESS"
        trigger_error = None
        
        if use_two_stage_processing and not ctx.heavy_stage_triggered:
            ctx.heavy_stage_triggered = True
            logger.info(f"All endpoint syncs completed - triggering Stage 2 heavy processing")
            try:
                await _trigger_heavy_processing_stage(ctx)
            except Exception as e_trigger:
                logger.error(f"Failed to trigger heavy processing for entity {entity_id}: {e_trigger}", exc_info=True)
                sync_status = "PARTIAL_SUCCESS"
                trigger_error = str(e_trigger)
        
        audit_details = {
            "syncJobId": sync_job_id, 
            "processedEndpoints": requested_endpoints
        }
        if trigger_error:
            audit_details["triggerError"] = trigger_error
        
        await _create_audit_log_entry(
            db, "SYNC", "XERO_SYNC_JOB_COMPLETED", client_id, entity_id, sync_status, audit_details
        )
        
        # Update entity status back to "active" after successful sync completion
        try:
            entity_ref = db.collection("ENTITIES").document(entity_id)
            await entity_ref.update({
                "status": "active",
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            logger.info(f"Updated entity {entity_id} status back to 'active' after successful sync completion")
        except Exception as e_status_update:
            logger.warning(f"Failed to update entity {entity_id} status back to 'active': {e_status_update}")
        
        logger.info(f"Xero sync job completed successfully for entity_id: {entity_id}")

    except Exception as e_main:
        logger.error(f"Unhandled error in Xero sync consumer for entity_id {entity_id}: {e_main}", exc_info=True)
        # Only create audit log if db is available
        if db:
            await _create_audit_log_entry(
                db, "SYNC", "XERO_SYNC_JOB_CRITICAL_FAILURE", client_id, entity_id, "FAILURE", 
                {"error": str(e_main), "syncJobId": locals().get("sync_job_id"), "message_data": locals().get("message_data", locals().get("event", {}).get("data"))}
            )
            
            # Update entity status back to "active" even on failure to prevent stuck "syncing" state
            try:
                entity_ref = db.collection("ENTITIES").document(entity_id)
                await entity_ref.update({
                    "status": "active",
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
                logger.info(f"Updated entity {entity_id} status back to 'active' after sync failure")
            except Exception as e_status_update:
                logger.warning(f"Failed to update entity {entity_id} status back to 'active' after failure: {e_status_update}")
        else:
            logger.warning("Cannot create audit log entry for critical failure: Firestore client not initialized")
    finally:
        if db: 
            try:
                # Check if close() method exists and is awaitable
                if hasattr(db, 'close') and callable(getattr(db, 'close')):
                    close_result = db.close()
                    if close_result is not None:
                        await close_result
                    logger.info("Firestore client closed successfully.")
                else:
                    logger.info("Firestore client does not have close method.")
            except Exception as close_error:
                logger.error(f"Error closing Firestore client: {close_error}")
        else:
            logger.info("Firestore client was None, no need to close.")


async def _generate_and_save_amortization_schedule(
    db: firestore.AsyncClient,
    invoice_id: str, 
    line_item_data: Dict[str, Any],
    master_period_start_date: datetime.date,
    master_period_end_date: datetime.date,
    entity_settings: Dict[str, Any],
    client_id: Optional[str],
    entity_id: str,
    parent_invoice_data: Dict[str, Any],
    llm_service_start_date: Optional[str] = None,
    llm_service_end_date: Optional[str] = None,
    is_llm_detected: bool = False
) -> Optional[str]:
    line_item_id = line_item_data.get("LineItemID")
    logger.info(
        f"Attempting to generate amortization schedule for Invoice: {invoice_id}, LineItem: {line_item_id}"
    )

    if not client_id:
        logger.warning(
            f"Cannot generate schedule for Invoice {invoice_id}, Line {line_item_id}: missing clientId in entity_settings."
        )
        return None

    prepayment_asset_codes = entity_settings.get("prepayment_asset_account_codes", [])
    
    if is_llm_detected:
        # For LLM-only detections, use the original account code as expense account
        # and use the first prepayment asset account for the asset side
        # Use configured prepayment asset account, fail if none configured
        if not prepayment_asset_codes:
            logger.warning(
                f"Cannot generate LLM schedule for Invoice {invoice_id}, Line {line_item_id}: No prepayment_asset_account_codes defined in entity settings."
            )
            return None
        amortization_account_code = prepayment_asset_codes[0]
        
        # Extract expense account from the original line item
        original_line_item_account_code = line_item_data.get("AccountCode")
        if not original_line_item_account_code:
            logger.warning(
                f"Cannot generate LLM schedule for Invoice {invoice_id}, Line {line_item_id}: Missing AccountCode on line item."
            )
            return None
        
        # Use the original line item account code as the expense account
        expense_account_code = original_line_item_account_code
        
        # Validate that expense account is not the same as asset account
        if expense_account_code in prepayment_asset_codes:
            logger.warning(
                f"LLM-detected prepayment for Invoice {invoice_id}, Line {line_item_id}: Original AccountCode {expense_account_code} is a prepayment asset account. This suggests GL-based detection should have been used instead."
            )
            # For now, still proceed but log this as unusual
        
        logger.info(f"LLM-detected prepayment: Using asset account {amortization_account_code}, expense account {expense_account_code} (from original line item)")
    else:
        # Traditional GL-based logic
        amortization_account_code = (
            prepayment_asset_codes[0] if prepayment_asset_codes else None
        )
        if not amortization_account_code:
            logger.warning(
                f"Cannot generate schedule for Invoice {invoice_id}, Line {line_item_id}: No prepayment_asset_account_codes defined in entity settings for {entity_id}."
            )
            return None

        # Determine the initial expense account code from the line item
        original_line_item_account_code = line_item_data.get("AccountCode")
        resolved_expense_account_code = original_line_item_account_code # Start with this

        if not resolved_expense_account_code:
            logger.warning(
                f"Cannot generate schedule for Invoice {invoice_id}, Line {line_item_id}: Missing AccountCode on line item."
            )
            return None

        # Check if the line item is coded to a prepayment asset account
        if original_line_item_account_code in prepayment_asset_codes:
            logger.info(
                f"Invoice {invoice_id}, Line {line_item_id}: Line item AccountCode {original_line_item_account_code} is a prepayment asset account. Attempting to use vendor default expense account."
            )
            contact_data = parent_invoice_data.get("Contact") # Contact field in Xero Invoice is a summary
            if contact_data and isinstance(contact_data, dict):
                contact_id_from_invoice = contact_data.get("ContactID") # This is the Xero ContactID
                if contact_id_from_invoice:
                    try:
                        # MODIFIED: Read from top-level COUNTERPARTIES using Xero ContactID as document ID
                        contact_doc_ref = db.collection("COUNTERPARTIES").document(contact_id_from_invoice)
                        contact_doc = await contact_doc_ref.get()
                        if contact_doc.exists:
                            contact_firestore_data = contact_doc.to_dict()
                            # Ensure the fetched counterparty belongs to the same entity_id and client_id for integrity
                            if contact_firestore_data.get("entity_id") != entity_id or contact_firestore_data.get("client_id") != client_id:
                                logger.error(f"Invoice {invoice_id}, Line {line_item_id}: Fetched Counterparty {contact_id_from_invoice} does not match expected entity_id/client_id. Data integrity issue? Skipping schedule.")
                                return None
                            vendor_default_expense_code = contact_firestore_data.get("_system_defaultAmortizationExpenseAccountCode")
                            if vendor_default_expense_code:
                                logger.info(f"Invoice {invoice_id}, Line {line_item_id}: Found vendor default expense account {vendor_default_expense_code} for Contact {contact_id_from_invoice}.")
                                # CRITICAL: Ensure the default expense account itself is not a prepayment asset account
                                if vendor_default_expense_code in prepayment_asset_codes:
                                    logger.error(f"Invoice {invoice_id}, Line {line_item_id}: Vendor default expense account {vendor_default_expense_code} for Contact {contact_id_from_invoice} is itself a prepayment asset account. Misconfiguration. Cannot generate schedule.")
                                    return None # Misconfiguration
                                resolved_expense_account_code = vendor_default_expense_code
                            else:
                                logger.warning(f"Invoice {invoice_id}, Line {line_item_id}: Contact {contact_id_from_invoice} exists but has no '_system_defaultAmortizationExpenseAccountCode' defined. Cannot generate schedule for this GL-coded prepayment line.")
                                return None
                        else:
                            logger.warning(f"Invoice {invoice_id}, Line {line_item_id}: Counterparty document for ID {contact_id_from_invoice} not found in COUNTERPARTIES. Cannot determine vendor default expense account.")
                            return None
                    except Exception as e_contact_fetch:
                        logger.error(f"Invoice {invoice_id}, Line {line_item_id}: Error fetching counterparty {contact_id_from_invoice} from COUNTERPARTIES: {e_contact_fetch}", exc_info=True)
                        return None
                else:
                    logger.warning(f"Invoice {invoice_id}, Line {line_item_id}: ContactID not found on parent invoice. Cannot determine vendor default expense account.")
                    return None
            else:
                logger.warning(f"Invoice {invoice_id}, Line {line_item_id}: Contact data not found or invalid on parent invoice. Cannot determine vendor default expense account.")
                return None
        
        # Final check on the resolved expense account code (could be original or from vendor default)
        if not resolved_expense_account_code: # Should be caught by earlier checks, but as a safeguard
            logger.error(f"Invoice {invoice_id}, Line {line_item_id}: Resolved expense_account_code is missing before proceeding. This should not happen.")
            return None
        
        # The original check still applies if, after resolving, it's still a prepayment account (e.g. if a non-GL coded line somehow got here, which is unlikely)
        # OR if the original line was NOT GL-coded to an asset, this check runs on its original AccountCode.
        if resolved_expense_account_code in prepayment_asset_codes:
            logger.warning(
                f"Invoice {invoice_id}, Line {line_item_id}: Final resolved AccountCode {resolved_expense_account_code} is a prepayment asset account. Cannot generate schedule. (This might occur if a non-GL line was passed or default was misconfigured)"
            )
            return None

        # Assign to the variable name used by the rest of the function
        expense_account_code = resolved_expense_account_code

    total_amount_to_amortize = line_item_data.get("LineAmount")
    if total_amount_to_amortize is None or total_amount_to_amortize == 0:
        logger.info(
            f"Invoice {invoice_id}, Line {line_item_id}: Amount to amortize is zero or None. No schedule generated."
        )
        return None

    # Determine service period: Use LLM-extracted dates if available, otherwise fall back to master period
    start_date = master_period_start_date
    end_date = master_period_end_date
    service_period_source = "master_period"
    
    if llm_service_start_date and llm_service_end_date:
        try:
            llm_start = _parse_date_string_to_date(llm_service_start_date)
            llm_end = _parse_date_string_to_date(llm_service_end_date)
            
            if llm_start and llm_end and llm_start <= llm_end:
                start_date = llm_start
                end_date = llm_end
                service_period_source = "llm_extracted"
                logger.info(f"Invoice {invoice_id}, Line {line_item_id}: Using LLM-extracted service period: {llm_service_start_date} to {llm_service_end_date}")
            else:
                logger.warning(f"Invoice {invoice_id}, Line {line_item_id}: Invalid LLM service dates ({llm_service_start_date} to {llm_service_end_date}), falling back to master period")
        except Exception as e_date_parse:
            logger.warning(f"Invoice {invoice_id}, Line {line_item_id}: Error parsing LLM service dates: {e_date_parse}, falling back to master period")
    
    line_item_description = line_item_data.get("Description", "N/A")

    # --- NEW PREPAYMENT DURATION CHECK using > 32 days ---
    if start_date and end_date:  # Ensure dates are valid
        num_days_in_period = (end_date - start_date).days + 1  # Inclusive day count
        if num_days_in_period <= 32:
            logger.info(
                f"Invoice {invoice_id}, Line {line_item_id}: Period length ({num_days_in_period} days) is <= 32 days. Does not qualify as prepayment. No schedule generated."
            )
            return None
        else:
            logger.info(
                f"Invoice {invoice_id}, Line {line_item_id}: Period length ({num_days_in_period} days) qualifies as prepayment. Proceeding with schedule generation using {service_period_source} dates."
            )
    else:  # Should have been caught earlier by main loop's check, but good to be safe
        logger.warning(
            f"Invoice {invoice_id}, Line {line_item_id}: Missing start or end date for duration check. Skipping schedule."
        )
        return None
    # --- END NEW PREPAYMENT DURATION CHECK ---

    # Use AmortizationService to calculate the schedule
    try:
        amortization_service = AmortizationService()
        schedule_preview = amortization_service.calculate_preview(
            amount=total_amount_to_amortize,
            start_date=start_date,
            end_date=end_date,
            entity_settings=entity_settings
        )
        
        logger.info(
            f"Invoice {invoice_id}, Line {line_item_id}: Amount €{total_amount_to_amortize}, "
            f"Method: {schedule_preview['calculation_method']}, Periods: {schedule_preview['total_months']}"
        )
        
        # Convert the preview format to the database format expected by existing code
        monthly_entries = []
        for entry in schedule_preview['monthly_entries']:
            # Parse the ISO date string back to datetime for database storage
            month_date_str = entry['month_date']
            month_date = datetime.fromisoformat(month_date_str).replace(tzinfo=timezone.utc)
            
            monthly_entries.append({
                "monthDate": month_date,
                "amount": entry['amount'],
                "status": "proposed",
                "postedJournalId": None,
                "postedJournalLineId": None,
                "matchConfidence": None,
                "lastActionByUserId": None,
                "lastActionTimestamp": None,
                "postingError": None,
            })
        
        actual_periods = schedule_preview['total_months']
        
    except Exception as e:
        logger.error(
            f"Invoice {invoice_id}, Line {line_item_id}: Failed to calculate amortization schedule: {e}"
        )
        return None
    
    if actual_periods == 0:
        logger.warning(
            f"Calculated zero periods for amortization between {start_date} and {end_date} for Invoice {invoice_id}, Line {line_item_id}. Skipping schedule."
        )
        return None

    schedule_id_val = str(uuid.uuid4()) # Renamed from schedule_id to avoid conflict with field name
    schedule_data = {
        "schedule_id": schedule_id_val, # Use the generated UUID as the primary ID for the schedule
        "transaction_id": invoice_id,
        "line_item_id": line_item_id,
        "entity_id": entity_id, # Ensure this is our standardized entity_id
        "client_id": client_id, # Ensure this is our standardized client_id
        "status": "proposed",
        "originalAmount": total_amount_to_amortize,
        "amortizationStartDate": datetime(
            start_date.year, start_date.month, start_date.day, tzinfo=timezone.utc
        ),
        "amortizationEndDate": datetime(
            end_date.year, end_date.month, end_date.day, tzinfo=timezone.utc
        ),
        "numberOfPeriods": actual_periods,
        "periodType": "monthly",
        "amortizationAccountCode": amortization_account_code,
        "expenseAccountCode": expense_account_code,
        "description": f"Amortization for: {line_item_description[:100]}",
        "monthlyEntries": monthly_entries,
        "calculation_method": schedule_preview['calculation_method'],  # Store the calculation method used
        "is_llm_detected": is_llm_detected,  # Mark LLM-detected schedules for UI handling
        "detection_method": "llm_only" if is_llm_detected else "gl_coding",
        "created_at": firestore.SERVER_TIMESTAMP,
        "updated_at": firestore.SERVER_TIMESTAMP,
    }
    schedule_data = {k: v for k, v in schedule_data.items() if v is not None} # Clean None values

    try:
        schedule_doc_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id_val)
        await schedule_doc_ref.set(schedule_data)
        logger.info(
            f"Firestore 'set' command completed for schedule {schedule_id_val} in top-level AMORTIZATION_SCHEDULES. Attempting to verify..."
        )

        # TRY TO READ IT BACK IMMEDIATELY
        verify_doc = await schedule_doc_ref.get()
        if verify_doc.exists:
            logger.info(
                f"VERIFIED: Successfully saved and read back AMORTIZATION_SCHEDULE {schedule_id_val} from top-level collection for Invoice {invoice_id}, Line {line_item_id}."
            )
        else:
            logger.error(
                f"VERIFICATION FAILED: AMORTIZATION_SCHEDULE {schedule_id_val} NOT FOUND after 'set' command to top-level collection for Invoice {invoice_id}, Line {line_item_id}."
            )
            return None  # Ensure we don't return a schedule_id if verification fails

        # Update parent transaction (this part assumes TRANSACTIONS is top-level)
        # Use set with merge=True to handle cases where the document might not exist yet
        parent_transaction_ref = db.collection("TRANSACTIONS").document(invoice_id)
        try:
            # First, check if the document exists
            parent_doc = await parent_transaction_ref.get()
            if parent_doc.exists:
                # Document exists, use update
                await parent_transaction_ref.update({
                    "_system_amortizationScheduleIDs": firestore.ArrayUnion([schedule_id_val]),
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
                logger.info(f"Successfully updated existing TRANSACTIONS document {invoice_id} with amortization schedule {schedule_id_val}")
            else:
                # Document doesn't exist, use set with merge=True to create it with minimal data
                logger.warning(f"TRANSACTIONS document {invoice_id} does not exist yet. Creating with amortization schedule reference.")
                await parent_transaction_ref.set({
                    "transaction_id": invoice_id,
                    "entity_id": entity_id,
                    "client_id": client_id,
                    "_system_amortizationScheduleIDs": [schedule_id_val],
                    "created_at": firestore.SERVER_TIMESTAMP,
                    "updated_at": firestore.SERVER_TIMESTAMP
                }, merge=True)
                logger.info(f"Successfully created TRANSACTIONS document {invoice_id} with amortization schedule {schedule_id_val}")
        except Exception as e_transaction_update:
            logger.error(f"Failed to update/create TRANSACTIONS document {invoice_id} with amortization schedule {schedule_id_val}: {e_transaction_update}", exc_info=True)
            # Don't fail the entire schedule creation if transaction update fails
            # The schedule is still valid and can be linked later
            logger.warning(f"Continuing with schedule creation despite transaction update failure for {invoice_id}")

        return schedule_id_val
    except Exception as e_fs_save_sched:
        logger.error(
            f"Failed to save or verify AMORTIZATION_SCHEDULE {schedule_id_val} at top-level collection for Invoice {invoice_id}, Line {line_item_id}: {e_fs_save_sched}",
            exc_info=True,
        )
        return None


async def _generate_proposed_journals_for_due_entries(
    db: firestore.AsyncClient,
    client_id: Optional[str],
    entity_id: str,
    entity_settings: Dict[str, Any],
    target_date: Optional[date] = None 
):
    logger.info(f"Starting proposed journal generation for entity {entity_id}, tenant {client_id} up to {target_date if target_date else 'today'}.")

    if not client_id:
        logger.error(f"Entity {entity_id}: Missing clientId in entity_settings. Cannot generate proposed journals.")
        return

    if not target_date:
        target_date = datetime.now(timezone.utc).date() # Use current UTC date if not specified

    # MODIFIED: Query top-level AMORTIZATION_SCHEDULES, filtering by entity_id and client_id
    schedules_query = db.collection("AMORTIZATION_SCHEDULES") \
                        .where(filter=firestore.FieldFilter("entity_id", "==", entity_id))
    if client_id: # Add client_id filter only if it's available
        schedules_query = schedules_query.where(filter=firestore.FieldFilter("client_id", "==", client_id))
    # Add other necessary filters, e.g., status to find schedules that are active/proposed
    schedules_query = schedules_query.where(filter=firestore.FieldFilter("status", "in", ["proposed", "active"])) # Example status filter

    proposed_journals_created_count = 0
    schedules_processed_count = 0

    try:
        async for schedule_doc in schedules_query.stream():
            schedules_processed_count += 1
            schedule_data = schedule_doc.to_dict()
            schedule_id = schedule_doc.id

            logger.debug(f"Entity {entity_id}: Processing schedule {schedule_id}.")

            if not schedule_data or not schedule_data.get("monthlyEntries"):
                logger.warning(f"Entity {entity_id}, Schedule {schedule_id}: No monthlyEntries found or schedule data is invalid. Skipping.")
                continue

            amortization_asset_account_code = schedule_data.get("amortizationAccountCode") # The Prepayment Asset Account
            expense_account_code = schedule_data.get("expenseAccountCode") # The Expense Account
            original_invoice_id = schedule_data.get("transactionId")
            original_line_item_id = schedule_data.get("lineItemId")
            schedule_description = schedule_data.get("description", "Amortization")

            if not all([amortization_asset_account_code, expense_account_code, original_invoice_id]):
                logger.warning(f"Entity {entity_id}, Schedule {schedule_id}: Missing critical account codes or original transactionId. Skipping journal generation for this schedule.")
                continue
            
            # Get currency from entity document (single source of truth)
            try:
                entity_doc = await db.collection("ENTITIES").document(entity_id).get()
                if entity_doc.exists:
                    entity_data = entity_doc.to_dict()
                    currency_code = entity_data.get("base_currency", "USD")
                else:
                    currency_code = "USD"
                    logger.warning(f"Entity {entity_id} not found, using default currency USD")
            except Exception as e_currency:
                logger.warning(f"Failed to fetch entity currency for {entity_id}: {e_currency}, using default USD")
                currency_code = "USD"

            updated_monthly_entries = []
            needs_schedule_update = False

            for entry in schedule_data.get("monthlyEntries", []):
                entry_month_date_ts = entry.get("monthDate") # This is a Firestore Timestamp
                entry_amount = entry.get("amount")
                entry_status = entry.get("status", "proposed") # Default to proposed if missing
                existing_journal_id = entry.get("_system_proposedJournalId")

                if not entry_month_date_ts or entry_amount is None:
                    logger.warning(f"Entity {entity_id}, Schedule {schedule_id}: Invalid monthly entry (missing date or amount). Skipping: {entry}")
                    updated_monthly_entries.append(entry) # Keep it as is
                    continue

                entry_month_date = entry_month_date_ts.date() # Convert Firestore Timestamp to Python date

                # Check if entry is due and not yet journalized
                if entry_status == "proposed" and entry_month_date <= target_date and not existing_journal_id:
                    logger.info(f"Entity {entity_id}, Schedule {schedule_id}: Entry for {entry_month_date.strftime('%Y-%m')} is due for journalizing.")
                    
                    proposed_journal_id_val = str(uuid.uuid4()) # Renamed
                    journal_date_for_posting = datetime(entry_month_date.year, entry_month_date.month, entry_month_date.day, tzinfo=timezone.utc)
                    # Potentially adjust to month-end for journal_date if preferred:
                    # last_day_of_month = entry_month_date + relativedelta(day=31) 
                    # journal_date_for_posting = datetime(last_day_of_month.year, last_day_of_month.month, last_day_of_month.day, tzinfo=timezone.utc)

                    narration = f"{schedule_description} - {entry_month_date.strftime('%B %Y')}"
                    
                    journal_lines = [
                        {
                            "lineId": str(uuid.uuid4()),
                            "accountCode": expense_account_code,
                            "description": f"Amortization of Inv: {original_invoice_id}, Line: {original_line_item_id} for {entry_month_date.strftime('%B %Y')}",
                            "amount": entry_amount,
                            "isDebit": True,
                            "isCredit": False # Explicitly set other flag
                        },
                        {
                            "lineId": str(uuid.uuid4()),
                            "accountCode": amortization_asset_account_code, # Credit the prepayment asset account
                            "description": f"Amortization of Inv: {original_invoice_id}, Line: {original_line_item_id} for {entry_month_date.strftime('%B %Y')}",
                            "amount": entry_amount,
                            "isDebit": False, # Explicitly set other flag
                            "isCredit": True
                        }
                    ]

                    proposed_journal_data = {
                        "journal_id": proposed_journal_id_val, # Use generated UUID
                        "client_id": client_id, # Standardized
                        "entity_id": entity_id, # Standardized
                        "amortization_schedule_id": schedule_id,
                        "amortizationMonthlyEntryMonthDate": entry_month_date_ts, # Store original Firestore timestamp
                        "journalDate": journal_date_for_posting,
                        "status": "proposed", # Initial status of the journal itself
                        "narration": narration,
                        "currencyCode": currency_code, 
                        "lines": journal_lines,
                        "sourceDocumentUrl": None, # TODO: Populate if GCS URI of original invoice is available on schedule
                        "xeroManualJournalID": None,
                        "postingErrorDetails": None,
                        "created_at": firestore.SERVER_TIMESTAMP,
                        "updated_at": firestore.SERVER_TIMESTAMP,
                        "lastActionByUserId": None
                    }

                    # MODIFIED: Save to top-level PROPOSED_JOURNALS
                    proposed_journal_doc_ref = db.collection("PROPOSED_JOURNALS").document(proposed_journal_id_val)
                    await proposed_journal_doc_ref.set(proposed_journal_data)
                    logger.info(f"Entity {entity_id}, Schedule {schedule_id}: Created PROPOSED_JOURNAL {proposed_journal_id_val} for entry {entry_month_date.strftime('%Y-%m')}.")
                    proposed_journals_created_count += 1

                    # Update the entry in the schedule
                    entry["status"] = "journal_proposed"
                    entry["_system_proposedJournalId"] = proposed_journal_id_val
                    entry["lastActionTimestamp"] = datetime.now(timezone.utc) # Use Python datetime for array updates
                    needs_schedule_update = True
                
                updated_monthly_entries.append(entry)
            
            if needs_schedule_update:
                # Fix: Use db.collection() instead of schedules_query.document()
                schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
                await schedule_ref.update({"monthlyEntries": updated_monthly_entries, "updated_at": firestore.SERVER_TIMESTAMP})
                logger.info(f"Entity {entity_id}: Updated monthly entries for schedule {schedule_id} with journal proposal status.")

        logger.info(f"Entity {entity_id}: Proposed journal generation finished. Processed {schedules_processed_count} schedules, created {proposed_journals_created_count} new proposed journals.")

    except Exception as e_prop_jnl:
        logger.error(f"Entity {entity_id}: Error during proposed journal generation: {e_prop_jnl}", exc_info=True)
        # Decide on error handling: re-raise, or just log and let next run attempt?

# --- End NEW: Proposed Journal Generation ---

async def _post_proposed_journals_to_xero(
    db: firestore.AsyncClient,
    client_id: Optional[str],
    entity_id: str,
    entity_settings: Dict[str, Any],
    xero_client_for_posting: XeroApiClient 
):
    logger.info(f"Starting Xero journal posting for entity {entity_id}, tenant {client_id}.")

    if not entity_settings or not entity_settings.get("clientId"):
        logger.error(f"Entity {entity_id}: Missing clientId in entity_settings or entity_settings is None. Cannot post journals.")
        return

    # MODIFIED: Query top-level PROPOSED_JOURNALS, filtering by entity_id, client_id, and status
    proposed_journals_query = db.collection("PROPOSED_JOURNALS") \
                                .where(filter=firestore.FieldFilter("entity_id", "==", entity_id)) \
                                .where(filter=firestore.FieldFilter("status", "==", "proposed")) # Or "pending_approval"
    if client_id:
        proposed_journals_query = proposed_journals_query.where(filter=firestore.FieldFilter("client_id", "==", client_id))

    journals_processed_count = 0
    journals_posted_successfully_count = 0
    journals_failed_count = 0

    try:
        async for journal_doc_snap in proposed_journals_query.stream(): # Iterate over snapshot
            journals_processed_count += 1
            proposed_journal_data = journal_doc_snap.to_dict()
            proposed_journal_firestore_id = journal_doc_snap.id
            amortization_schedule_id = proposed_journal_data.get("amortization_schedule_id")
            amortization_entry_month_date_ts = proposed_journal_data.get("amortizationMonthlyEntryMonthDate") # Firestore Timestamp

            logger.info(f"Entity {entity_id}: Attempting to post proposed journal {proposed_journal_firestore_id} to Xero.")
            
            # Convert Firestore Timestamp to ISO string for journalDate if needed by create_manual_journal
            # The create_manual_journal expects an ISO string like "2024-07-31T00:00:00Z"
            # Our PROPOSED_JOURNALS.journalDate is already a Firestore Timestamp in UTC.
            journal_date_for_xero_api = proposed_journal_data.get("journalDate") # This is a Firestore Timestamp
            if isinstance(journal_date_for_xero_api, datetime): # Ensure it's a datetime object
                proposed_journal_data["journalDate"] = journal_date_for_xero_api.isoformat().replace("+00:00", "Z")
            else:
                logger.error(f"Entity {entity_id}, Journal {proposed_journal_firestore_id}: journalDate is not a valid datetime object. Skipping.")
                # Update status to failed in Firestore
                await journal_doc_snap.reference.update({
                    "status": "posting_failed",
                    "postingErrorDetails": {"error": "InvalidDateInFirestore", "message": "journalDate was not a valid datetime object before posting."},
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
                journals_failed_count +=1
                continue

            # Determine the target status for Xero (e.g., DRAFT or POSTED)
            # For now, let's assume we always post as DRAFT. This could be configurable.
            post_as_status = "DRAFT" # Could be entity_settings.get("xeroJournalPostStatusDefault", "DRAFT")
            proposed_journal_data["status"] = post_as_status # This sets the status Xero will use

            xero_response = await xero_client_for_posting.create_manual_journal(journal_data=proposed_journal_data)

            if xero_response and xero_response.get("ManualJournals") and not xero_response.get("error"):
                # Successfully posted to Xero
                xero_manual_journal_details = xero_response["ManualJournals"][0]
                xero_manual_journal_id = xero_manual_journal_details.get("ManualJournalID")
                xero_journal_status = xero_manual_journal_details.get("Status") # e.g. DRAFT, POSTED

                logger.info(f"Entity {entity_id}: Successfully posted journal {proposed_journal_firestore_id} to Xero. Xero ID: {xero_manual_journal_id}, Xero Status: {xero_journal_status}")
                journals_posted_successfully_count += 1

                # Update PROPOSED_JOURNALS document in Firestore
                firestore_journal_update_payload = {
                    "status": f"xero_{xero_journal_status.lower()}_created", # e.g., xero_draft_created
                    "xeroManualJournalID": xero_manual_journal_id,
                    "postingErrorDetails": None,
                    "updated_at": firestore.SERVER_TIMESTAMP
                }
                await journal_doc_snap.reference.update(firestore_journal_update_payload)

                # Update corresponding monthlyEntry in AMORTIZATION_SCHEDULES
                if amortization_schedule_id and amortization_entry_month_date_ts:
                    schedule_ref = (
                        db.collection("AMORTIZATION_SCHEDULES")
                        .document(amortization_schedule_id)
                    )
                    schedule_doc_snapshot = await schedule_ref.get()
                    if schedule_doc_snapshot.exists:
                        schedule_data = schedule_doc_snapshot.to_dict()
                        updated_entries = []
                        entry_updated = False
                        for entry in schedule_data.get("monthlyEntries", []):
                            # Compare Firestore Timestamps directly
                            if entry.get("monthDate") == amortization_entry_month_date_ts:
                                entry["status"] = f"xero_{xero_journal_status.lower()}_created"
                                entry["postedJournalId"] = xero_manual_journal_id # Using Xero's ID
                                entry["lastActionTimestamp"] = datetime.now(timezone.utc)
                                entry_updated = True
                            updated_entries.append(entry)
                        
                        if entry_updated:
                            await schedule_ref.update({"monthlyEntries": updated_entries, "updated_at": firestore.SERVER_TIMESTAMP})
                            logger.info(f"Entity {entity_id}: Updated amortization schedule {amortization_schedule_id} for entry linked to journal {proposed_journal_firestore_id}.")
                    else:
                        logger.warning(f"Entity {entity_id}: Amortization schedule {amortization_schedule_id} not found for journal {proposed_journal_firestore_id}.")
                else:
                    logger.warning(f"Entity {entity_id}: Missing amortizationScheduleId or amortizationMonthlyEntryMonthDate for journal {proposed_journal_firestore_id}. Cannot update schedule.")

            else:
                # Posting failed
                journals_failed_count += 1
                error_details = xero_response if xero_response else {"error": "UnknownError", "message": "No response from Xero client or empty response."}
                logger.error(f"Entity {entity_id}: Failed to post journal {proposed_journal_firestore_id} to Xero. Details: {error_details}")
                await journal_doc_snap.reference.update({
                    "status": "posting_failed",
                    "postingErrorDetails": error_details,
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
        
        logger.info(f"Entity {entity_id}: Xero journal posting finished. Processed: {journals_processed_count}, Succeeded: {journals_posted_successfully_count}, Failed: {journals_failed_count}.")

    except Exception as e_post_jnl:
        logger.error(f"Entity {entity_id}: Error during Xero journal posting process: {e_post_jnl}", exc_info=True)




# --- Main execution block for local testing ---
if __name__ == "__main__":
    # Configure logging for the local runner
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
    logger.info("Starting local Pub/Sub subscriber to listen to Google Cloud subscription...")

    # Get pub/sub configuration from environment variables
    GCP_PROJECT_ID = os.getenv("GCP_PROJECT_ID")
    if not GCP_PROJECT_ID:
        raise ValueError("GCP_PROJECT_ID environment variable is required")
    
    SUBSCRIPTION_NAME = os.getenv("PUBSUB_SUBSCRIPTION_XERO_SYNC", "xero-sync-topic-sub")
    TOPIC_NAME = os.getenv("PUBSUB_TOPIC_XERO_SYNC", "xero-sync-topic")
    
    SUBSCRIPTION_FULL_PATH = f"projects/{GCP_PROJECT_ID}/subscriptions/{SUBSCRIPTION_NAME}"
    TOPIC_FULL_PATH = f"projects/{GCP_PROJECT_ID}/topics/{TOPIC_NAME}"
    
    logger.info(f"Using subscription path: {SUBSCRIPTION_FULL_PATH}")
    logger.info(f"Using topic path: {TOPIC_FULL_PATH}")

    # Ensure google.cloud.pubsub_v1 is imported at the top of your main.py file
    from google.cloud import pubsub_v1 
    # Ensure asyncio is imported at the top of your main.py file
    # import asyncio
    # Ensure datetime and timezone are imported for context
    # from datetime import datetime, timezone

    subscription_path = SUBSCRIPTION_FULL_PATH
    
    subscriber = pubsub_v1.SubscriberClient()

    # Define the callback function that will process received messages
    async def process_pubsub_message(message: pubsub_v1.subscriber.message.Message) -> None:
        logger.info(f"Received Pub/Sub message ID: {message.message_id}, Published at: {message.publish_time}")
        
        # This is a workaround for the Pub/Sub message handler
        # Instead of trying to use the Cloud Function interface directly,
        # we'll manually decode the message and call the function with proper parameters
        try:
            # The message.data contains the raw JSON string as bytes
            message_json = json.loads(message.data.decode('utf-8'))
            logger.info(f"Decoded message: {message_json}")
            
            # Create a simulated context
            class SimpleContext:
                def __init__(self):
                    self.event_id = message.message_id
                    self.timestamp = message.publish_time.isoformat() if message.publish_time else datetime.now(timezone.utc).isoformat()
                    self.resource = {
                        "service": "pubsub.googleapis.com",
                        "name": TOPIC_FULL_PATH,
                        "type": "pubsub"
                    }
            
            context = SimpleContext()
            
            # Re-encode the JSON for xero_sync_consumer which expects base64 encoded data
            # Create event object in the format that xero_sync_consumer expects
            event_for_consumer = {
                "data": base64.b64encode(json.dumps(message_json).encode('utf-8'))
            }
            logger.info(f"Calling xero_sync_consumer with base64-encoded data")
            
            # Call the processing function directly (not the cloud function wrapper)
            await _process_xero_sync_event(event_for_consumer)
            logger.info(f"Successfully processed message ID: {message.message_id}")
            message.ack()
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON from message: {e}. Message data: {message.data[:200]}")
            message.nack()
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            import traceback
            traceback.print_exc()
            message.nack()

        # Note: The message processing is now done directly in the try/except block above
        # We don't need the old processing code anymore

    # The callback `process_pubsub_message` is async.
    # The `subscriber.subscribe` method handles running this async callback.
    # We wrap the call to the async callback in asyncio.create_task or ensure the event loop handles it.
    def callback_wrapper(message: pubsub_v1.subscriber.message.Message):
        # Create a new event loop for the thread if one doesn't exist
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        # Run the coroutine in this thread's event loop
        loop.run_until_complete(process_pubsub_message(message))


    streaming_pull_future = subscriber.subscribe(subscription_path, callback=callback_wrapper)
    
    logger.info(f"Listening for messages on {subscription_path}... Press Ctrl+C to exit.")

    # REMOVE the old loop = asyncio.get_event_loop() and loop.run_forever() block
    # REPLACE with the following:
    try:
        # streaming_pull_future.result() will block here until the future is cancelled
        # or an unrecoverable error occurs in the subscriber's connection.
        streaming_pull_future.result()
    except KeyboardInterrupt:
        logger.info("KeyboardInterrupt received, shutting down subscriber...")
    except Exception as e: # Catch other exceptions that might terminate the future
        logger.error(f"Subscriber future terminated with an error: {e}", exc_info=True)
    finally:
        logger.info("Shutting down streaming_pull_future...")
        streaming_pull_future.cancel()  # Signal the subscriber to stop
        try:
            # Wait for the subscriber to clean up (optional timeout)
            streaming_pull_future.result(timeout=30) 
            logger.info("Streaming pull future successfully shut down.")
        except TimeoutError:
            logger.warning("Timeout waiting for streaming pull future to shut down.")
        except asyncio.CancelledError: # Future might be cancelled by the time we check result()
            logger.info("Streaming pull future was cancelled as expected.")
        except Exception as e_spf: # Catch other exceptions from future.result()
            logger.error(f"Error during streaming_pull_future final shutdown: {e_spf}", exc_info=True)
        
        # Close the subscriber client
        subscriber.close()
        logger.info("Pub/Sub subscriber client closed.")

    # --- Ensure old direct test invocation is commented out or removed ---

    logger.info("Local Pub/Sub listener script finished.")


    if not os.getenv("GCS_BUCKET_NAME"):
        logger.warning(
            "Local test: GCS_BUCKET_NAME is not set in .env. GCS uploads will be skipped or fail if attempted."
        )
