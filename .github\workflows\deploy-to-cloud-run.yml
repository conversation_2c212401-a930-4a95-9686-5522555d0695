name: Deploy to Cloud Run

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    # Clone repo
    - name: Checkout code
      uses: actions/checkout@v3
      
    # Google Cloud authentication
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    # Configure Google Cloud SDK
    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
      with:
        project_id: drcr-d660a
        
    # Configure Docker auth for Artifact Registry
    - name: Configure Docker authentication
      run: |
        gcloud auth configure-docker europe-west2-docker.pkg.dev

    # Create .env file dynamically
    - name: Generate .env file
      run: |
        echo "API_DEBUG=${{ vars.API_DEBUG }}" >> .env
        echo "API_HOST=${{ vars.API_HOST }}" >> .env
        echo "API_PORT=${{ vars.API_PORT }}" >> .env
        echo "DB_HOST=${{ vars.DB_HOST }}" >> .env
        echo "DB_NAME=${{ vars.DB_NAME }}" >> .env
        echo "DB_USER=${{ vars.DB_USER }}" >> .env
        echo "DB_PORT=${{ vars.DB_PORT }}" >> .env
        echo "FRONTEND_BASE_URL=${{ vars.FRONTEND_BASE_URL }}" >> .env
        echo "GCP_PROJECT_ID=${{ vars.GCP_PROJECT_ID }}" >> .env
        echo "GCP_REGION=${{ vars.GCP_REGION }}" >> .env
        echo "GCS_BUCKET_NAME=${{ vars.GCS_BUCKET_NAME }}" >> .env
        echo "OPENAI_MODEL=${{ vars.OPENAI_MODEL }}" >> .env
        echo "PUBSUB_SUBSCRIPTION_XERO_SYNC=${{ vars.PUBSUB_SUBSCRIPTION_XERO_SYNC }}" >> .env
        echo "PUBSUB_TOPIC_XERO_SYNC=${{ vars.PUBSUB_TOPIC_XERO_SYNC }}" >> .env
        echo "SENDGRID_FROM_EMAIL=${{ vars.SENDGRID_FROM_EMAIL }}" >> .env
        echo "SENDGRID_FROM_NAME=${{ secrets.SENDGRID_FROM_NAME }}" >> .env
        echo "XERO_CLIENT_ID=${{ vars.XERO_CLIENT_ID }}" >> .env
        echo "XERO_REDIRECT_URI_REST_API=${{ vars.XERO_REDIRECT_URI_REST_API }}" >> .env
        echo "XERO_SCOPES=${{ vars.XERO_SCOPES }}" >> .env
        echo "API_SECRET_KEY=${{ secrets.API_SECRET_KEY }}" >> .env
        echo "DB_PASSWORD=${{ secrets.DB_PASSWORD }}" >> .env
        echo "MISTRAL_API_KEY=${{ secrets.MISTRAL_API_KEY }}" >> .env
        echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> .env
        echo "SENDGRID_API_KEY=${{ secrets.SENDGRID_API_KEY }}" >> .env
        echo "TOKEN_ENCRYPTION_KEY=${{ secrets.TOKEN_ENCRYPTION_KEY }}" >> .env
        echo "XERO_CLIENT_SECRET=${{ secrets.XERO_CLIENT_SECRET }}" >> .env

    # Check if .env file exists
    - name: Check if .env file exists
      run: |
        if [ ! -f .env ]; then
          echo "Error: .env file not found"
          exit 1
        else
          echo "Success: .env file found"
          echo "Contents of .env file:"
          echo "::add-mask::false"
          echo "--------------------------------"
          cat .env
          echo "--------------------------------"
        fi

    # Build Docker-image and upload to Artifact Registry
    - name: Build and Push Docker image
      run: |
        docker build -t europe-west2-docker.pkg.dev/drcr-d660a/github-deploy/drcr-backend:$GITHUB_SHA .
        docker push europe-west2-docker.pkg.dev/drcr-d660a/github-deploy/drcr-backend:$GITHUB_SHA

    # Deploy to Google Cloud Run
#    - name: Deploy to Cloud Run
#      run: |
#        gcloud run deploy drcr-backend \
#          --image europe-west2-docker.pkg.dev/drcr-d660a/github-deploy/drcr-backend:$GITHUB_SHA \
#          --region europe-west2 \
#          --platform managed \
#          --allow-unauthenticated \
#          --update-secrets GOOGLE_APPLICATION_CREDENTIALS=gcp_credentials:latest \
#          --timeout=600 \
#          --update-env-vars \
#          API_SECRET_KEY=${{ secrets.API_SECRET_KEY }},DB_PASSWORD=${{ secrets.DB_PASSWORD }},TOKEN_ENCRYPTION_KEY=${{ secrets.TOKEN_ENCRYPTION_KEY }},XERO_CLIENT_SECRET=${{ secrets.XERO_CLIENT_SECRET }},SENDGRID_API_KEY=${{ secrets.SENDGRID_API_KEY }},MISTRAL_API_KEY=${{ secrets.MISTRAL_API_KEY }},OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}
