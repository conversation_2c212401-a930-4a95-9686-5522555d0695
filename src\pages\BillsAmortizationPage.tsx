import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuthStore } from '../store/auth.store';
import { useFirmName } from '../hooks/useFirmName';
import { useAccpaySelections, useBillsAmortizationSelections } from '../store/navigation.store';
import { toast } from 'sonner';
import { api } from '@/lib/api';
import { AppSidebar } from '../components/layout/AppSidebar';
import { HierarchicalBillsList } from '../components/prepayments/HierarchicalBillsList';
import { AmortizationSummary } from '../components/prepayments/AmortizationSummary';
import { AmortizationConfiguration } from '../components/prepayments/AmortizationConfiguration';
import { MonthlyScheduleTable } from '../components/prepayments/MonthlyScheduleTable';
import { SideBySideReview } from '../components/prepayments/SideBySideReview';
import { BillsToolbar } from '../components/prepayments/BillsToolbar';
import { EmptyAmortizationState } from '../components/prepayments/EmptyAmortizationState';
import { KeyboardShortcutsModal } from '../components/prepayments/KeyboardShortcutsModal';
import { ResizablePanel } from '../components/ui/resizable-panel';
import { ScrollArea } from '../components/ui/scroll-area';
import { PrepaymentsService, type PrepaymentsFilters } from '../services/prepayments.service';
import type { InvoiceData, AmortizableLineItem, ScheduleStatus } from '../types/schedule.types';
import { calculateAggregateScheduleStatus, mapBackendScheduleStatus } from '../types/schedule.types';
import type { 
  SupplierNode, 
  ExpandedState, 
  HierarchicalSelection,
  HierarchicalBillsData 
} from '../types/hierarchical-bills.types';
import { 
  transformToHierarchicalData, 
  filterHierarchyBySearch,
  getSelectedTotalAmount,
  getSelectedLineItemIds,
  calculateSupplierSelectionState,
  calculateInvoiceSelectionState
} from '../types/hierarchical-bills.types';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '../components/ui/breadcrumb';
import { Separator } from '../components/ui/separator';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '../components/ui/sidebar';

interface AmortizationConfig {
  method: string;
  startDate: string;
  endDate: string;
  prepaymentAccount: string;
  expenseAccount: string;
  numberOfPeriods: number;
}

interface MonthlyScheduleEntry {
  period: number;
  date: string;
  amount: number;
  status: 'proposed' | 'confirmed' | 'posted';
  runningBalance: number;
}

export function BillsAmortizationPage() {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { user } = useAuthStore();
  const { firmName, isLoading: firmNameLoading } = useFirmName();
  // Use accpay store for client/entity (backward compatibility with old prepayments page)
  const {
    selectedClientId,
    selectedEntityId,
    setClientId,
    setEntityId,
  } = useAccpaySelections();
  
  // Use bills amortization store only for status filters
  const {
    selectedStatusFilters,
    setStatusFilters,
  } = useBillsAmortizationSelections();

  // State
  const [hierarchicalData, setHierarchicalData] = useState<HierarchicalBillsData>({ suppliers: [] });
  const [expandedState, setExpandedState] = useState<ExpandedState>({
    suppliers: new Set(),
    invoices: new Set(),
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Client/Entity state
  const [clients, setClients] = useState<Array<{clientId: string; clientName: string}>>([]);
  const [entities, setEntities] = useState<Array<{entityId: string; entityName: string; connectionStatus: string}>>([]);
  const [isLoadingClients, setIsLoadingClients] = useState(false);
  const [isLoadingEntities, setIsLoadingEntities] = useState(false);

  // Attachment viewing state
  const [attachmentModalOpen, setAttachmentModalOpen] = useState(false);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);
  const [attachmentData, setAttachmentData] = useState<{
    transactionData: any;
    scheduleData: any;
    attachmentUrl: string;
  } | null>(null);
  const [attachmentLoading, setAttachmentLoading] = useState(false);
  const [attachmentError, setAttachmentError] = useState<string | null>(null);

  // Keyboard navigation state
  const [focusedInvoiceIndex, setFocusedInvoiceIndex] = useState<number>(-1);
  const [navigationMode, setNavigationMode] = useState<'invoice' | 'lineitem'>('invoice');
  const [focusedLineItemIndex, setFocusedLineItemIndex] = useState<number>(-1);
  const [showKeyboardHelp, setShowKeyboardHelp] = useState(false);

  // Toolbar state
  const [toolbarLoading, setToolbarLoading] = useState({
    exporting: false,
    resetting: false,
    saving: false,
    posting: false,
  });

  // Validation message state
  const [validationMessages, setValidationMessages] = useState<{
    canSave: string[];
    canPost: string[];
    canReset: string[];
  }>({
    canSave: [],
    canPost: [],
    canReset: [],
  });

  // Pagination state
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalItems: 0,
    totalPages: 0,
    pageSize: 50, // Reduced from 100 for better performance
    hasMore: false,
    isLoadingMore: false,
  });

  // Initialize client/entity/status filters from URL params (priority) or localStorage
  useEffect(() => {
    const clientIdFromUrl = searchParams.get('client_id');
    const entityIdFromUrl = searchParams.get('entity_id');
    const statusFiltersFromUrl = searchParams.get('status_filters');
    
    console.log('BillsAmortizationPage: URL params - clientId:', clientIdFromUrl, 'entityId:', entityIdFromUrl, 'statusFilters:', statusFiltersFromUrl);
    
    // URL parameters always take priority - set them immediately if they exist
    if (clientIdFromUrl) {
      setClientId(clientIdFromUrl);
    }
    if (entityIdFromUrl) {
      setEntityId(entityIdFromUrl);
    }
    if (statusFiltersFromUrl) {
      const statusFilters = statusFiltersFromUrl.split(',').filter(s => s.trim());
      setStatusFilters(statusFilters);
    }
  }, []);

  // Update URL when selections change
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    
    if (selectedClientId) {
      params.set('client_id', selectedClientId);
    } else {
      params.delete('client_id');
    }
    
    if (selectedEntityId && selectedEntityId !== 'all') {
      params.set('entity_id', selectedEntityId);
    } else {
      params.delete('entity_id');
    }
    
    if (selectedStatusFilters && selectedStatusFilters.length > 0) {
      params.set('status_filters', selectedStatusFilters.join(','));
    } else {
      params.delete('status_filters');
    }
    
    setSearchParams(params, { replace: true });
  }, [selectedClientId, selectedEntityId, selectedStatusFilters, setSearchParams]);

  // Fetch clients data
  const fetchClients = async () => {
    try {
      setIsLoadingClients(true);
      const response = await api.getClients();
      const clientsData = (response.clients || []).map(client => ({
        clientId: client.client_id,
        clientName: client.name,
      }));
      setClients(clientsData);
      
      // If no client selected but we have clients, select first one
      if (!selectedClientId && clientsData.length > 0) {
        setClientId(clientsData[0].clientId);
      }
    } catch (error) {
      console.error('Error fetching clients:', error);
      toast.error('Failed to load clients');
    } finally {
      setIsLoadingClients(false);
    }
  };

  // Fetch entities for selected client
  const fetchEntities = async (clientId: string) => {
    try {
      setIsLoadingEntities(true);
      const response = await api.getEntitiesForClient(clientId);
      const entitiesData = (response.entities || []).map(entity => ({
        entityId: entity.entity_id,
        entityName: entity.entity_name,
        connectionStatus: entity.connection_status,
      }));
      setEntities(entitiesData);
    } catch (error) {
      console.error('Error fetching entities:', error);
      toast.error('Failed to load entities');
    } finally {
      setIsLoadingEntities(false);
    }
  };

  // Load clients on mount
  useEffect(() => {
    fetchClients();
  }, []);

  // Load entities when client changes
  useEffect(() => {
    if (selectedClientId) {
      fetchEntities(selectedClientId);
      // Reset entity selection when client changes
      if (entities.length > 0 && !entities.find(e => e.entityId === selectedEntityId)) {
        setEntityId(null);
      }
    } else {
      setEntities([]);
      setEntityId(null);
    }
  }, [selectedClientId]);

  // Load bills data (initial load and refresh)
  const loadBills = async (resetData = true) => {
    try {
      if (resetData) {
        setIsLoading(true);
        setPagination(prev => ({ ...prev, currentPage: 1 }));
      }
      setError(null);

      const filters: PrepaymentsFilters = {
        client_id: selectedClientId,
        entity_id: (selectedEntityId && selectedEntityId !== 'all') ? selectedEntityId : undefined,
        page: resetData ? 1 : pagination.currentPage,
        limit: pagination.pageSize,
        status_filters: selectedStatusFilters && selectedStatusFilters.length > 0 ? selectedStatusFilters : undefined,
      };

      const response = await PrepaymentsService.getPrepaymentsData(filters);
      
      // Update pagination info
      setPagination(prev => ({
        ...prev,
        totalItems: response.pagination.totalItems || 0,
        totalPages: response.pagination.totalPages || 0,
        hasMore: (response.pagination.currentPage || 1) < (response.pagination.totalPages || 0),
      }));
      
      // Transform to hierarchical structure
      const hierarchical = transformToHierarchicalData(response.suppliers);
      
      if (resetData) {
        // Replace data for initial load or filter changes
        setHierarchicalData(hierarchical);
        // Reset navigation state for new data
        setFocusedInvoiceIndex(-1);
        setNavigationMode('invoice');
        setFocusedLineItemIndex(-1);
      } else {
        // Append data for pagination (Load More)
        setHierarchicalData(prev => ({
          suppliers: [...prev.suppliers, ...hierarchical.suppliers]
        }));
        // Note: Focus position is preserved during pagination
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load bills');
    } finally {
      setIsLoading(false);
      setPagination(prev => ({ ...prev, isLoadingMore: false }));
    }
  };

  // Load bills when filters change
  useEffect(() => {
    if (!selectedClientId) return;
    loadBills(true); // Reset data on filter change
  }, [selectedClientId, selectedEntityId, selectedStatusFilters]); // eslint-disable-line react-hooks/exhaustive-deps

  // Handle attachment viewing
  const handleAttachmentClick = async (invoiceId: string, attachmentId: string) => {
    try {
      setAttachmentLoading(true);
      setAttachmentError(null);
      setSelectedInvoiceId(invoiceId);

      // Find the invoice data in our hierarchical structure
      let foundInvoice: any = null;
      let foundSupplier: any = null;
      
      for (const supplier of hierarchicalData.suppliers) {
        for (const invoice of supplier.invoices) {
          if (invoice.invoiceId === invoiceId) {
            foundInvoice = invoice;
            foundSupplier = supplier;
            break;
          }
        }
        if (foundInvoice) break;
      }

      if (!foundInvoice) {
        throw new Error('Invoice not found');
      }

      // Get attachment URL (assuming api.getAttachmentBlob method exists)
      const attachmentUrl = await api.getAttachmentBlob(attachmentId);

      // Transform invoice data for SideBySideReview component
      const transactionData = {
        transactionId: foundInvoice.invoiceId,
        reference: foundInvoice.reference,
        counterpartyName: foundSupplier.supplierName,
        hasAttachment: foundInvoice.hasAttachment,
        attachmentId: foundInvoice.attachmentId,
        currencyCode: foundInvoice.currencyCode,
        lineItems: foundInvoice.lineItems.map((item: any) => ({
          lineItemId: item.lineItemId,
          description: item.description,
          lineAmount: item.lineAmount,
          isAmortized: item.isSelected // Using selection state as amortized indicator
        }))
      };

      // Create schedule summary from selected line items
      const scheduleData = foundInvoice.lineItems.some((item: any) => item.isSelected) ? {
        status: 'proposed',
        originalAmount: foundInvoice.lineItems
          .filter((item: any) => item.isSelected)
          .reduce((sum: number, item: any) => sum + item.lineAmount, 0),
        amortizationStartDate: new Date().toISOString().split('T')[0], // Placeholder
        amortizationEndDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Placeholder
        numberOfPeriods: 12, // Placeholder
        amortizationAccountCode: foundInvoice.lineItems.find((item: any) => item.isSelected)?.prepaymentAccountCode || '',
        expenseAccountCode: foundInvoice.lineItems.find((item: any) => item.isSelected)?.expenseAccountCode || null
      } : null;

      setAttachmentData({
        transactionData,
        scheduleData,
        attachmentUrl
      });

      setAttachmentModalOpen(true);
    } catch (error: any) {
      console.error('Error loading attachment:', error);
      setAttachmentError(error.message || 'Failed to load attachment');
    } finally {
      setAttachmentLoading(false);
    }
  };

  // Filter hierarchical data based on search term
  const filteredSuppliers = useMemo(() => {
    return filterHierarchyBySearch(hierarchicalData.suppliers, searchTerm);
  }, [hierarchicalData.suppliers, searchTerm]);

  // Calculate summary data from selected line items
  const summaryData = useMemo(() => {
    const totalAmount = getSelectedTotalAmount(hierarchicalData.suppliers);
    const selectedLineItemIds = getSelectedLineItemIds(hierarchicalData.suppliers);
    
    // Get all months from selected line items to determine date range
    const allMonthKeys: string[] = [];
    const selectedScheduleIds: string[] = [];
    const selectedLineItemStatuses: ScheduleStatus[] = [];
    
    hierarchicalData.suppliers.forEach(supplier => {
      supplier.invoices.forEach(invoice => {
        invoice.lineItems.forEach(lineItem => {
          if (lineItem.isSelected) {
            // Collect schedule IDs for selected line items
            if (lineItem.scheduleId && !selectedScheduleIds.includes(lineItem.scheduleId)) {
              selectedScheduleIds.push(lineItem.scheduleId);
            }
            
            // Collect statuses for aggregation
            const mappedStatus = mapBackendScheduleStatus(lineItem.status);
            selectedLineItemStatuses.push(mappedStatus);
            
            if (lineItem.monthlyBreakdown) {
              Object.keys(lineItem.monthlyBreakdown).forEach(monthKey => {
                if (!allMonthKeys.includes(monthKey)) {
                  allMonthKeys.push(monthKey);
                }
              });
            }
          }
        });
      });
    });
    
    const sortedMonths = allMonthKeys.sort();
    const startDate = sortedMonths.length > 0 ? `${sortedMonths[0]}-01` : new Date().toISOString().split('T')[0];
    const endDate = sortedMonths.length > 0 ? `${sortedMonths[sortedMonths.length - 1]}-01` : startDate;
    const numberOfPeriods = sortedMonths.length;
    const monthlyAmount = numberOfPeriods > 0 ? totalAmount / numberOfPeriods : 0;

    // Calculate aggregate status from selected line items
    const aggregateStatus = calculateAggregateScheduleStatus(selectedLineItemStatuses);

    return {
      totalAmount,
      monthlyAmount,
      numberOfPeriods,
      selectedScheduleIds,
      startDate,
      endDate,
      status: aggregateStatus,
      selectedCount: selectedLineItemIds.length,
    };
  }, [hierarchicalData.suppliers]);

  // Amortization configuration - editable state with defaults from schedules
  const [amortizationConfig, setAmortizationConfig] = useState<AmortizationConfig>({
    method: 'straight_line',
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    numberOfPeriods: 12,
    prepaymentAccount: '',
    expenseAccount: '',
  });

  // Update config defaults when selected items change
  useEffect(() => {
    // Get account codes from selected line items to set as defaults
    const prepaymentAccounts = new Set<string>();
    const expenseAccounts = new Set<string>();
    
    hierarchicalData.suppliers.forEach(supplier => {
      supplier.invoices.forEach(invoice => {
        invoice.lineItems.forEach(lineItem => {
          if (lineItem.isSelected) {
            if (lineItem.prepaymentAccountCode) {
              prepaymentAccounts.add(lineItem.prepaymentAccountCode);
            }
            if (lineItem.expenseAccountCode) {
              expenseAccounts.add(lineItem.expenseAccountCode);
            }
          }
        });
      });
    });

    setAmortizationConfig(prev => ({
      ...prev,
      startDate: summaryData.startDate,
      endDate: summaryData.endDate,
      numberOfPeriods: summaryData.numberOfPeriods,
      // Set account defaults from schedules if not already set
      prepaymentAccount: prev.prepaymentAccount || Array.from(prepaymentAccounts)[0] || '',
      expenseAccount: prev.expenseAccount || Array.from(expenseAccounts)[0] || '',
    }));
  }, [summaryData, hierarchicalData.suppliers]);

  // Get monthly schedule entries from selected line items
  const monthlySchedule = useMemo(() => {
    const allMonthKeys = new Set<string>();
    const monthlyData: Record<string, { amount: number; status: string }> = {};

    // Extract all monthly breakdowns from selected line items
    hierarchicalData.suppliers.forEach(supplier => {
      supplier.invoices.forEach(invoice => {
        invoice.lineItems.forEach(lineItem => {
          if (lineItem.isSelected && lineItem.monthlyBreakdown) {
            Object.entries(lineItem.monthlyBreakdown).forEach(([monthKey, monthData]) => {
              allMonthKeys.add(monthKey);
              if (!monthlyData[monthKey]) {
                monthlyData[monthKey] = { amount: 0, status: monthData.status };
              }
              monthlyData[monthKey].amount += monthData.amount;
            });
          }
        });
      });
    });

    // Convert to sorted entries
    const sortedKeys = Array.from(allMonthKeys).sort((a, b) => {
      const [yearA, monthA] = a.split('-').map(Number);
      const [yearB, monthB] = b.split('-').map(Number);
      if (yearA !== yearB) return yearA - yearB;
      return monthA - monthB;
    });

    let runningBalance = summaryData.totalAmount;
    return sortedKeys.map((monthKey, index) => {
      const monthData = monthlyData[monthKey];
      const amount = monthData.amount;
      runningBalance -= amount;

      return {
        period: index + 1,
        date: `${monthKey}-01`, // Convert YYYY-MM to YYYY-MM-01
        amount,
        status: monthData.status as 'proposed' | 'confirmed' | 'posted',
        runningBalance: Math.max(0, runningBalance),
      };
    });
  }, [hierarchicalData.suppliers, summaryData.totalAmount]);

  // Update validation messages when dependencies change
  useEffect(() => {
    const messages = generateValidationMessages();
    setValidationMessages(messages);
  }, [summaryData.selectedCount, summaryData.totalAmount, amortizationConfig.prepaymentAccount, amortizationConfig.expenseAccount, monthlySchedule]);

  // Initialize focus to first invoice and expand first supplier when data loads
  useEffect(() => {
    const allInvoices = filteredSuppliers.flatMap(supplier => supplier.invoices);
    if (allInvoices.length > 0) {
      // Always focus the first invoice when suppliers change (but don't select it)
      if (focusedInvoiceIndex === -1 || focusedInvoiceIndex >= allInvoices.length) {
        setFocusedInvoiceIndex(0);
        console.log('Focusing first invoice for navigation');
        
        // Auto-expand the first supplier so the first invoice is visible
        if (filteredSuppliers.length > 0) {
          const firstSupplier = filteredSuppliers[0];
          if (!expandedState.suppliers.has(firstSupplier.supplierId)) {
            console.log('Auto-expanding first supplier for immediate navigation');
            handleToggleExpand('supplier', firstSupplier.supplierId);
          }
        }
      }
    } else {
      // No invoices available, reset focus
      setFocusedInvoiceIndex(-1);
    }
  }, [filteredSuppliers]);

  // Auto-expand supplier and scroll to focused invoice
  useEffect(() => {
    if (focusedInvoiceIndex >= 0) {
      const allInvoices = filteredSuppliers.flatMap(supplier => supplier.invoices);
      const focusedInvoice = allInvoices[focusedInvoiceIndex];
      
      if (focusedInvoice) {
        // Find which supplier contains this invoice
        const supplierWithInvoice = filteredSuppliers.find(supplier => 
          supplier.invoices.some(invoice => invoice.invoiceId === focusedInvoice.invoiceId)
        );
        
        if (supplierWithInvoice) {
          // Auto-expand the supplier if it's not already expanded
          const wasExpanded = expandedState.suppliers.has(supplierWithInvoice.supplierId);
          if (!wasExpanded) {
            console.log('Auto-expanding supplier:', supplierWithInvoice.supplierName);
            handleToggleExpand('supplier', supplierWithInvoice.supplierId);
          }
          
          // Scroll to focused invoice (delay for expansion to complete)
          setTimeout(() => {
            const invoiceElement = document.querySelector(`[data-invoice-id="${focusedInvoice.invoiceId}"]`);
            if (invoiceElement) {
              console.log('Scrolling to focused invoice:', focusedInvoice.invoiceId);
              invoiceElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'nearest'
              });
            }
          }, wasExpanded ? 0 : 300); // Delay if we just expanded
        }
      }
    }
  }, [focusedInvoiceIndex, filteredSuppliers, expandedState.suppliers]);

  // Keyboard navigation setup
  useEffect(() => {
    document.addEventListener('keydown', handleKeyboardNavigation);
    return () => {
      document.removeEventListener('keydown', handleKeyboardNavigation);
    };
  }, [focusedInvoiceIndex, navigationMode, focusedLineItemIndex, filteredSuppliers, summaryData, amortizationConfig, monthlySchedule, expandedState]);

  // Handle expand/collapse
  const handleToggleExpand = (type: 'supplier' | 'invoice', id: string) => {
    setExpandedState(prev => {
      const newState = { ...prev };
      if (type === 'supplier') {
        const newSuppliers = new Set(prev.suppliers);
        if (newSuppliers.has(id)) {
          newSuppliers.delete(id);
        } else {
          newSuppliers.add(id);
        }
        newState.suppliers = newSuppliers;
      } else {
        const newInvoices = new Set(prev.invoices);
        if (newInvoices.has(id)) {
          newInvoices.delete(id);
        } else {
          newInvoices.add(id);
        }
        newState.invoices = newInvoices;
      }
      return newState;
    });
  };

  // Handle selection
  const handleToggleSelection = (type: 'supplier' | 'invoice' | 'lineItem', id: string) => {
    setHierarchicalData(prev => {
      const newData = { ...prev };
      
      // Find which supplier contains the target
      const targetSupplierIndex = prev.suppliers.findIndex(supplier => {
        if (type === 'supplier') return supplier.supplierId === id;
        if (type === 'invoice') return supplier.invoices.some(inv => inv.invoiceId === id);
        if (type === 'lineItem') return supplier.invoices.some(inv => inv.lineItems.some(item => item.lineItemId === id));
        return false;
      });
      
      newData.suppliers = prev.suppliers.map((supplier, index) => {
        // Only process the target supplier, unselect all others
        if (index !== targetSupplierIndex) {
          return {
            ...supplier,
            isSelected: false,
            invoices: supplier.invoices.map(invoice => ({
              ...invoice,
              isSelected: false,
              lineItems: invoice.lineItems.map(lineItem => ({
                ...lineItem,
                isSelected: false,
              }))
            }))
          };
        }
        if (type === 'supplier' && supplier.supplierId === id) {
          const newSelected = !supplier.isSelected;
          return {
            ...supplier,
            isSelected: newSelected,
            invoices: supplier.invoices.map(invoice => ({
              ...invoice,
              isSelected: newSelected,
              lineItems: invoice.lineItems.map(lineItem => ({
                ...lineItem,
                isSelected: newSelected,
              }))
            }))
          };
        }
        
        if (type === 'invoice') {
          // Only one invoice can be selected at a time
          const updatedInvoices = supplier.invoices.map(invoice => {
            if (invoice.invoiceId === id) {
              const newSelected = !invoice.isSelected;
              return {
                ...invoice,
                isSelected: newSelected,
                lineItems: invoice.lineItems.map(lineItem => ({
                  ...lineItem,
                  isSelected: newSelected,
                }))
              };
            } else {
              // Unselect all other invoices in this supplier
              return {
                ...invoice,
                isSelected: false,
                lineItems: invoice.lineItems.map(lineItem => ({
                  ...lineItem,
                  isSelected: false,
                }))
              };
            }
          });
          
          return {
            ...supplier,
            isSelected: false, // Never select supplier when individual invoice is selected
            invoices: updatedInvoices,
          };
        }
        
        if (type === 'lineItem') {
          const updatedInvoices = supplier.invoices.map(invoice => {
            const hasTargetLineItem = invoice.lineItems.some(item => item.lineItemId === id);
            
            if (hasTargetLineItem) {
              // This invoice contains the target line item
              const updatedLineItems = invoice.lineItems.map(lineItem => {
                if (lineItem.lineItemId === id) {
                  return { ...lineItem, isSelected: !lineItem.isSelected };
                }
                return lineItem;
              });
              
              // Update invoice selection based on line item selections
              const allLineItemsSelected = updatedLineItems.every(item => item.isSelected);
              
              return {
                ...invoice,
                isSelected: allLineItemsSelected,
                lineItems: updatedLineItems,
              };
            } else {
              // This invoice doesn't contain the target - unselect it
              return {
                ...invoice,
                isSelected: false,
                lineItems: invoice.lineItems.map(lineItem => ({
                  ...lineItem,
                  isSelected: false,
                }))
              };
            }
          });
          
          return {
            ...supplier,
            isSelected: false, // Never select supplier when individual line item is selected
            invoices: updatedInvoices,
          };
        }
        
        return supplier;
      });
      
      return newData;
    });
  };

  const handleSelectAll = (selected: boolean) => {
    setHierarchicalData(prev => ({
      ...prev,
      suppliers: prev.suppliers.map(supplier => ({
        ...supplier,
        isSelected: selected,
        invoices: supplier.invoices.map(invoice => ({
          ...invoice,
          isSelected: selected,
          lineItems: invoice.lineItems.map(lineItem => ({
            ...lineItem,
            isSelected: selected,
          }))
        }))
      }))
    }));
  };

  const handleConfigurationChange = (updates: Partial<AmortizationConfig>) => {
    setAmortizationConfig(prev => {
      const updated = { ...prev, ...updates };
      
      // Auto-calculate end date when start date or periods change
      if (updates.startDate || updates.numberOfPeriods) {
        if (updated.startDate && updated.numberOfPeriods > 0) {
          const startDate = new Date(updated.startDate);
          const endDate = new Date(startDate);
          endDate.setMonth(endDate.getMonth() + updated.numberOfPeriods);
          updated.endDate = endDate.toISOString().split('T')[0];
        }
      }
      
      return updated;
    });
  };

  const handleScheduleEntryEdit = (period: number, updates: Partial<MonthlyScheduleEntry>) => {
    console.log('Edit schedule entry:', period, updates);
    
    // Update the hierarchical data to reflect the changes
    setHierarchicalData(prev => {
      const newData = { ...prev };
      newData.suppliers = prev.suppliers.map(supplier => ({
        ...supplier,
        invoices: supplier.invoices.map(invoice => ({
          ...invoice,
          lineItems: invoice.lineItems.map(lineItem => {
            if (lineItem.isSelected && lineItem.monthlyBreakdown) {
              // Find the month key that corresponds to this period
              const monthKeys = Object.keys(lineItem.monthlyBreakdown).sort();
              const monthKey = monthKeys[period - 1]; // period is 1-based
              
              if (monthKey && updates.amount !== undefined) {
                return {
                  ...lineItem,
                  monthlyBreakdown: {
                    ...lineItem.monthlyBreakdown,
                    [monthKey]: {
                      ...lineItem.monthlyBreakdown[monthKey],
                      amount: updates.amount,
                    }
                  }
                };
              }
            }
            return lineItem;
          })
        }))
      }));
      return newData;
    });
  };

  const handleScheduleExport = () => {
    console.log('Export schedule');
    // Implement export functionality
  };

  // Helper function to extract current selection state
  const getCurrentSelectionState = () => {
    const selectedLineItemIds = new Set<string>();
    const selectedInvoiceIds = new Set<string>();
    const selectedSupplierIds = new Set<string>();

    hierarchicalData.suppliers.forEach(supplier => {
      if (supplier.isSelected) selectedSupplierIds.add(supplier.supplierId);
      
      supplier.invoices.forEach(invoice => {
        if (invoice.isSelected) selectedInvoiceIds.add(invoice.invoiceId);
        
        invoice.lineItems.forEach(lineItem => {
          if (lineItem.isSelected) selectedLineItemIds.add(lineItem.lineItemId);
        });
      });
    });

    return { selectedLineItemIds, selectedInvoiceIds, selectedSupplierIds };
  };

  // Helper function to restore selection state on fresh data
  const restoreSelectionState = (data: HierarchicalBillsData, selectionState: ReturnType<typeof getCurrentSelectionState>) => {
    return {
      ...data,
      suppliers: data.suppliers.map(supplier => ({
        ...supplier,
        isSelected: selectionState.selectedSupplierIds.has(supplier.supplierId),
        invoices: supplier.invoices.map(invoice => ({
          ...invoice,
          isSelected: selectionState.selectedInvoiceIds.has(invoice.invoiceId),
          lineItems: invoice.lineItems.map(lineItem => ({
            ...lineItem,
            isSelected: selectionState.selectedLineItemIds.has(lineItem.lineItemId)
          }))
        }))
      }))
    };
  };

  const handleDataRefresh = async (preserveSelection = false, disableCache = false) => {
    if (!selectedClientId) return;

    try {
      // Capture current selection state if requested
      const selectionState = preserveSelection ? getCurrentSelectionState() : null;

      const filters: PrepaymentsFilters = {
        client_id: selectedClientId,
        entity_id: selectedEntityId || undefined,
        page: 1,
        limit: 100,
        status_filters: selectedStatusFilters && selectedStatusFilters.length > 0 ? selectedStatusFilters : undefined,
      };

      const response = await PrepaymentsService.getPrepaymentsData(filters, disableCache);
      
      // Transform to hierarchical structure
      let hierarchical = transformToHierarchicalData(response.suppliers);

      // Restore selection state if needed
      if (preserveSelection && selectionState) {
        hierarchical = restoreSelectionState(hierarchical, selectionState);
      }

      setHierarchicalData(hierarchical);
    } catch (err) {
      console.error('Error refreshing data:', err);
    }
  };

  // Handle client/entity changes
  const handleClientChange = (clientId: string) => {
    setClientId(clientId);
  };

  const handleEntityChange = (entityId: string) => {
    setEntityId(entityId);
  };

  const handleStatusFiltersChange = (filters: string[]) => {
    setStatusFilters(filters);
  };

  // Keyboard navigation handlers
  const handleKeyboardNavigation = (event: KeyboardEvent) => {
    // Don't interfere with form inputs
    if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
      return;
    }

    const allInvoices = filteredSuppliers.flatMap(supplier => supplier.invoices);
    
    switch (event.key) {
      case 'ArrowUp':
        event.preventDefault();
        if (navigationMode === 'invoice') {
          // Navigate between invoices
          if (allInvoices.length > 0) {
            setFocusedInvoiceIndex(prev => {
              const newIndex = prev <= 0 ? 0 : prev - 1;
              console.log('Arrow Up: Focus moving to invoice index:', newIndex);
              return newIndex;
            });
          }
        } else if (navigationMode === 'lineitem') {
          // Navigate within line items or return to invoice level
          if (focusedInvoiceIndex >= 0 && focusedInvoiceIndex < allInvoices.length) {
            const focusedInvoice = allInvoices[focusedInvoiceIndex];
            if (focusedLineItemIndex <= 0) {
              // Return to invoice level navigation
              setNavigationMode('invoice');
              setFocusedLineItemIndex(-1);
              console.log('Arrow Up: Returning to invoice navigation mode');
            } else {
              // Move to previous line item
              setFocusedLineItemIndex(prev => prev - 1);
              console.log('Arrow Up: Focus moving to line item index:', focusedLineItemIndex - 1);
            }
          }
        }
        break;
        
      case 'ArrowDown':
        event.preventDefault();
        if (navigationMode === 'invoice') {
          // Navigate between invoices or enter line item mode
          if (allInvoices.length > 0) {
            if (focusedInvoiceIndex >= 0 && focusedInvoiceIndex < allInvoices.length) {
              const focusedInvoice = allInvoices[focusedInvoiceIndex];
              const isExpanded = expandedState.invoices.has(focusedInvoice.invoiceId);
              
              if (isExpanded && focusedInvoice.lineItems.length > 0) {
                // Enter line item navigation mode
                setNavigationMode('lineitem');
                setFocusedLineItemIndex(0);
                console.log('Arrow Down: Entering line item navigation mode');
              } else {
                // Navigate to next invoice
                setFocusedInvoiceIndex(prev => {
                  const newIndex = prev >= allInvoices.length - 1 ? allInvoices.length - 1 : prev + 1;
                  console.log('Arrow Down: Focus moving to invoice index:', newIndex);
                  
                  // Auto-load more data when approaching the end
                  if (newIndex >= allInvoices.length - 3 && pagination.hasMore && !pagination.isLoadingMore) {
                    console.log('Auto-loading more data due to keyboard navigation');
                    handleLoadMore();
                  }
                  
                  return newIndex;
                });
              }
            } else {
              // No invoice focused, focus first one
              setFocusedInvoiceIndex(0);
              console.log('Arrow Down: Focus moving to invoice index: 0');
            }
          }
        } else if (navigationMode === 'lineitem') {
          // Navigate within line items
          if (focusedInvoiceIndex >= 0 && focusedInvoiceIndex < allInvoices.length) {
            const focusedInvoice = allInvoices[focusedInvoiceIndex];
            if (focusedLineItemIndex < focusedInvoice.lineItems.length - 1) {
              setFocusedLineItemIndex(prev => prev + 1);
              console.log('Arrow Down: Focus moving to line item index:', focusedLineItemIndex + 1);
            }
          }
        }
        break;
        
      case 'Enter':
        event.preventDefault();
        if (event.ctrlKey || event.metaKey) {
          // Ctrl+Enter for Post Ready
          const canPost = summaryData.selectedCount > 0 &&
            hasUnpostedEntries(monthlySchedule) && 
            hasMandatoryFieldsPopulated(amortizationConfig) && 
            amortizationCoversTotal(monthlySchedule, summaryData.totalAmount);
          if (canPost) {
            handleToolbarPostReady();
          }
        }
        break;
        
      case 'ArrowLeft':
      case 'ArrowRight':
        event.preventDefault();
        // Left/Right arrows for invoice expand/collapse
        if (focusedInvoiceIndex >= 0 && focusedInvoiceIndex < allInvoices.length) {
          const focusedInvoice = allInvoices[focusedInvoiceIndex];
          handleToggleExpand('invoice', focusedInvoice.invoiceId);
        }
        break;
        
      case ' ': // Space
        event.preventDefault();
        if (focusedInvoiceIndex >= 0 && focusedInvoiceIndex < allInvoices.length) {
          const focusedInvoice = allInvoices[focusedInvoiceIndex];
          
          if (navigationMode === 'invoice') {
            // Toggle selection of all line items in focused invoice
            handleToggleSelection('invoice', focusedInvoice.invoiceId);
          } else if (navigationMode === 'lineitem' && focusedLineItemIndex >= 0) {
            // Toggle selection of specific line item
            if (focusedLineItemIndex < focusedInvoice.lineItems.length) {
              const lineItem = focusedInvoice.lineItems[focusedLineItemIndex];
              handleToggleSelection('lineItem', lineItem.lineItemId);
            }
          }
        }
        break;
        
      case 'Escape':
        event.preventDefault();
        if (navigationMode === 'lineitem') {
          // Return to invoice navigation mode
          setNavigationMode('invoice');
          setFocusedLineItemIndex(-1);
          console.log('Escape: Returning to invoice navigation mode');
        } else {
          // Clear focus and all selections
          setFocusedInvoiceIndex(-1);
          setNavigationMode('invoice');
          setFocusedLineItemIndex(-1);
          // Clear all selections
          setHierarchicalData(prev => ({
            ...prev,
            suppliers: prev.suppliers.map(supplier => ({
              ...supplier,
              isSelected: false,
              invoices: supplier.invoices.map(invoice => ({
                ...invoice,
                isSelected: false,
                lineItems: invoice.lineItems.map(lineItem => ({
                  ...lineItem,
                  isSelected: false
                }))
              }))
            }))
          }));
        }
        break;
        
      case '?':
      case '/':
        // Handle both ? and Shift+/ (which can register as either ? or /)
        if (event.key === '?' || (event.key === '/' && event.shiftKey)) {
          event.preventDefault();
          setShowKeyboardHelp(true);
        }
        break;
        
      case 's':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          if (summaryData.selectedCount > 0 && amortizationConfig.prepaymentAccount && amortizationConfig.expenseAccount) {
            handleToolbarSaveChanges();
          }
        }
        break;
        
      case 'a':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          if (focusedInvoiceIndex >= 0 && focusedInvoiceIndex < allInvoices.length) {
            const focusedInvoice = allInvoices[focusedInvoiceIndex];
            // Select all line items from the focused invoice only
            focusedInvoice.lineItems.forEach(lineItem => {
              if (!lineItem.isSelected) {
                handleToggleSelection('lineItem', lineItem.lineItemId);
              }
            });
          }
        }
        break;
        
      case 'd':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          // Deselect all line items
          setHierarchicalData(prev => ({
            ...prev,
            suppliers: prev.suppliers.map(supplier => ({
              ...supplier,
              invoices: supplier.invoices.map(invoice => ({
                ...invoice,
                lineItems: invoice.lineItems.map(lineItem => ({
                  ...lineItem,
                  isSelected: false
                }))
              }))
            }))
          }));
        }
        break;
    }
  };

  // Validation helper functions for post button logic
  const hasUnpostedEntries = (schedule: MonthlyScheduleEntry[]): boolean => {
    return schedule.some(entry => entry.status !== 'posted');
  };

  const hasMandatoryFieldsPopulated = (config: AmortizationConfig): boolean => {
    return !!(config.prepaymentAccount && config.expenseAccount);
  };

  const amortizationCoversTotal = (schedule: MonthlyScheduleEntry[], totalAmount: number): boolean => {
    if (schedule.length === 0 || totalAmount === 0) return false;
    
    const scheduleTotal = schedule.reduce((sum, entry) => sum + entry.amount, 0);
    // Allow small rounding differences (within 0.01)
    return Math.abs(scheduleTotal - totalAmount) <= 0.01;
  };

  // Fetch saved configuration from backend
  const fetchSavedConfiguration = async (): Promise<AmortizationConfig | null> => {
    if (summaryData.selectedScheduleIds.length === 0) return null;

    try {
      // Get configuration from the first selected schedule
      const scheduleId = summaryData.selectedScheduleIds[0];
      const scheduleDetails = await PrepaymentsService.getSchedule(scheduleId);
      
      // Transform backend response to AmortizationConfig format
      return {
        method: 'straight_line', // Backend may not store this, using default
        startDate: scheduleDetails.start_date || new Date().toISOString().split('T')[0],
        endDate: scheduleDetails.end_date || new Date().toISOString().split('T')[0],
        numberOfPeriods: scheduleDetails.number_of_periods || 12,
        prepaymentAccount: scheduleDetails.account_code || '',
        expenseAccount: scheduleDetails.expense_account_code || '',
      };
    } catch (error) {
      console.error('Error fetching saved configuration:', error);
      return null;
    }
  };

  // Validation message generators
  const generateValidationMessages = () => {
    const messages = {
      canSave: [] as string[],
      canPost: [] as string[],
      canMarkReady: [] as string[],
      canReset: [] as string[],
    };

    // Check if any selected schedules are posted
    const hasPostedSchedules = summaryData.selectedScheduleIds.some(scheduleId => {
      // Look for posted entries in the monthly schedule
      return monthlySchedule.some(entry => entry.status === 'posted');
    });

    // Save validation messages
    if (summaryData.selectedCount === 0) {
      messages.canSave.push('No bills selected');
    }
    if (hasPostedSchedules) {
      messages.canSave.push('Cannot modify posted schedules');
    }
    if (!amortizationConfig.prepaymentAccount) {
      messages.canSave.push('Prepayment account is required');
    }
    if (!amortizationConfig.expenseAccount) {
      messages.canSave.push('Expense account is required');
    }

    // Post validation messages
    if (summaryData.selectedCount === 0) {
      messages.canPost.push('No bills selected');
    }
    if (!hasUnpostedEntries(monthlySchedule)) {
      messages.canPost.push('All entries are already posted');
    }
    if (!hasMandatoryFieldsPopulated(amortizationConfig)) {
      if (!amortizationConfig.prepaymentAccount) {
        messages.canPost.push('Prepayment account is required');
      }
      if (!amortizationConfig.expenseAccount) {
        messages.canPost.push('Expense account is required');
      }
    }
    if (!amortizationCoversTotal(monthlySchedule, summaryData.totalAmount)) {
      const scheduleTotal = monthlySchedule.reduce((sum, entry) => sum + entry.amount, 0);
      messages.canPost.push(`Amortization total (${scheduleTotal.toFixed(2)}) doesn't match selected amount (${summaryData.totalAmount.toFixed(2)})`);
    }

    // Mark Ready validation messages
    if (!monthlySchedule.some(e => e.status === 'proposed')) {
      messages.canMarkReady.push('No proposed entries to mark as ready');
    }

    // Reset validation messages
    if (summaryData.selectedCount === 0) {
      messages.canReset.push('No bills selected');
    }

    return messages;
  };

  // Load more data for pagination
  const handleLoadMore = async () => {
    if (pagination.isLoadingMore || !pagination.hasMore) return;
    
    setPagination(prev => ({ 
      ...prev, 
      currentPage: prev.currentPage + 1,
      isLoadingMore: true 
    }));
    
    await loadBills(false); // Don't reset data, append instead
  };

  // Unified toolbar handlers
  const handleToolbarExport = () => {
    console.log('Export schedule');
    setToolbarLoading(prev => ({ ...prev, exporting: true }));
    
    // Simulate export operation
    setTimeout(() => {
      setToolbarLoading(prev => ({ ...prev, exporting: false }));
    }, 1000);
  };

  const handleToolbarReset = async () => {
    setToolbarLoading(prev => ({ ...prev, resetting: true }));
    
    try {
      // Fetch the actual saved configuration from backend
      const savedConfig = await fetchSavedConfiguration();
      
      if (savedConfig) {
        // Restore to saved configuration
        handleConfigurationChange(savedConfig);
        // Also refresh the data to restore original monthly values
        await handleDataRefresh(true, true); // Preserve selection state, disable cache
        toast.success('Configuration and monthly values reset to saved values');
      } else {
        // Fall back to defaults if no saved config found
        const defaultStartDate = new Date().toISOString().split('T')[0];
        const defaultEndDate = new Date();
        defaultEndDate.setMonth(defaultEndDate.getMonth() + 12);
        
        handleConfigurationChange({
          method: 'straight_line',
          startDate: defaultStartDate,
          endDate: defaultEndDate.toISOString().split('T')[0],
          numberOfPeriods: 12,
          prepaymentAccount: '',
          expenseAccount: '',
        });
        // Also refresh the data to restore original monthly values
        await handleDataRefresh(true, true); // Preserve selection state, disable cache
        toast.info('Reset to default configuration and original monthly values');
      }
    } catch (error) {
      console.error('Error resetting configuration:', error);
      toast.error('Failed to reset configuration');
    } finally {
      setToolbarLoading(prev => ({ ...prev, resetting: false }));
    }
  };


  const handleToolbarSaveChanges = async () => {
    if (summaryData.selectedScheduleIds.length === 0) return;

    setToolbarLoading(prev => ({ ...prev, saving: true }));
    
    try {
      // Save configuration changes to all selected schedules
      const configPromises = summaryData.selectedScheduleIds.map(scheduleId =>
        PrepaymentsService.updateSchedule(scheduleId, {
          account_code: amortizationConfig.prepaymentAccount,
          expense_account_code: amortizationConfig.expenseAccount,
          start_date: amortizationConfig.startDate,
          end_date: amortizationConfig.endDate,
        })
      );

      // Save monthly amount changes using new entry update endpoint
      const amountPromises = monthlySchedule.map(async (entry, entryIndex) => {
        if (summaryData.selectedScheduleIds[0]) {
          return PrepaymentsService.updateMonthlyEntry(
            summaryData.selectedScheduleIds[0], 
            entryIndex, 
            entry.amount
          );
        }
      }).filter(Boolean);

      await Promise.all([...configPromises, ...amountPromises]);
      await handleDataRefresh(true, true); // Preserve selection state, disable cache
      
      toast.success('Changes saved successfully');
    } catch (error) {
      console.error('Error saving changes:', error);
      toast.error('Failed to save changes');
    } finally {
      setToolbarLoading(prev => ({ ...prev, saving: false }));
    }
  };

  const handleToolbarPostReady = async () => {
    if (summaryData.selectedScheduleIds.length === 0) return;

    setToolbarLoading(prev => ({ ...prev, posting: true }));
    
    try {
      console.log('Post Ready: Starting with schedules:', summaryData.selectedScheduleIds);
      console.log('Post Ready: Current amortization config:', amortizationConfig);
      console.log('Post Ready: Monthly schedule entries:', monthlySchedule.length);
      
      // Step 1: Save all changes first (critical - must save before posting!)
      const configPromises = summaryData.selectedScheduleIds.map(scheduleId =>
        PrepaymentsService.updateSchedule(scheduleId, {
          account_code: amortizationConfig.prepaymentAccount,
          expense_account_code: amortizationConfig.expenseAccount,
          start_date: amortizationConfig.startDate,
          end_date: amortizationConfig.endDate,
        })
      );

      const amountPromises = monthlySchedule.map(async (entry, entryIndex) => {
        if (summaryData.selectedScheduleIds[0]) {
          return PrepaymentsService.updateMonthlyEntry(
            summaryData.selectedScheduleIds[0], 
            entryIndex, 
            entry.amount
          );
        }
      }).filter(Boolean);

      await Promise.all([...configPromises, ...amountPromises]);

      // Step 2: Post entries to Xero (try without confirm step first)
      // Post all monthly entries (indices 0 through monthlySchedule.length - 1)
      const entryIndices = monthlySchedule.map((_, index) => index);
      const postPromises = summaryData.selectedScheduleIds.map(scheduleId =>
        PrepaymentsService.postReadyEntries(scheduleId, entryIndices)
      );
      
      await Promise.all(postPromises);
      await handleDataRefresh(true, true); // Preserve selection state, disable cache
      
      toast.success('Changes saved and posted to Xero successfully');
    } catch (error) {
      console.error('Error saving and posting changes:', error);
      if (error.response?.status === 422) {
        console.error('422 Validation Error Details:', error.response.data);
        toast.error(`Validation Error: ${error.response.data?.message || 'Invalid request data'}`);
      } else {
        toast.error('Failed to save and post changes');
      }
    } finally {
      setToolbarLoading(prev => ({ ...prev, posting: false }));
    }
  };

  if (!selectedClientId) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">No client selected</p>
      </div>
    );
  }

  // Create the left panel content
  const leftPanelContent = (
    <HierarchicalBillsList
      suppliers={filteredSuppliers}
      expandedState={expandedState}
      searchTerm={searchTerm}
      onSearchChange={setSearchTerm}
      onToggleExpand={handleToggleExpand}
      onToggleSelection={handleToggleSelection}
      onSelectAll={handleSelectAll}
      onAttachmentClick={handleAttachmentClick}
      isLoading={isLoading}
      error={error}
      clients={clients}
      entities={entities}
      selectedClientId={selectedClientId}
      selectedEntityId={(selectedEntityId && selectedEntityId !== 'all') ? selectedEntityId : null}
      isLoadingClients={isLoadingClients}
      isLoadingEntities={isLoadingEntities}
      onClientChange={handleClientChange}
      onEntityChange={handleEntityChange}
      selectedStatusFilters={selectedStatusFilters}
      onStatusFiltersChange={handleStatusFiltersChange}
      focusedInvoiceIndex={focusedInvoiceIndex}
      navigationMode={navigationMode}
      focusedLineItemIndex={focusedLineItemIndex}
      pagination={{
        hasMore: pagination.hasMore,
        isLoadingMore: pagination.isLoadingMore,
        totalItems: pagination.totalItems,
        currentPage: pagination.currentPage,
      }}
      onLoadMore={handleLoadMore}
    />
  );

  // Create the right panel content - with relative positioning for toolbar
  const rightPanelContent = (
    <div className="h-full relative">
      {summaryData.selectedCount === 0 ? (
        // Show empty state when no line items are selected
        <EmptyAmortizationState 
          focusedInvoiceIndex={focusedInvoiceIndex}
          totalInvoices={filteredSuppliers.flatMap(supplier => supplier.invoices).length}
        />
      ) : (
        // Show normal content when line items are selected
        <>
          <ScrollArea className="h-full">
            <div className="p-6 space-y-6 pb-32">
              {/* Schedule Summary */}
              <div>
                <AmortizationSummary
                  totalAmount={summaryData.totalAmount}
                  monthlyAmount={summaryData.monthlyAmount}
                  numberOfPeriods={summaryData.numberOfPeriods}
                  startDate={summaryData.startDate}
                  endDate={summaryData.endDate}
                  status={summaryData.status}
                  selectedBillsCount={summaryData.selectedCount}
                />
              </div>

              {/* Configuration Form */}
              <div>
                <AmortizationConfiguration
                  config={amortizationConfig}
                  onChange={handleConfigurationChange}
                  disabled={summaryData.selectedCount === 0}
                  entityId={(selectedEntityId && selectedEntityId !== 'all') ? selectedEntityId : undefined}
                />
              </div>

              {/* Schedule Table */}
              <div>
                <MonthlyScheduleTable
                  scheduleEntries={monthlySchedule}
                  onEditEntry={handleScheduleEntryEdit}
                />
              </div>
            </div>
          </ScrollArea>
          
          {/* Bottom sticky toolbar - only show when content is active */}
          <BillsToolbar
            totalEntries={monthlySchedule.length}
            postedEntries={monthlySchedule.filter(e => e.status === 'posted').length}
            canExport={monthlySchedule.length > 0}
            canReset={summaryData.selectedCount > 0}
            canSaveChanges={
              validationMessages.canSave.length === 0
            }
            canPostReady={
              summaryData.selectedCount > 0 &&
              hasUnpostedEntries(monthlySchedule) && 
              hasMandatoryFieldsPopulated(amortizationConfig) && 
              amortizationCoversTotal(monthlySchedule, summaryData.totalAmount)
            }
            isExporting={toolbarLoading.exporting}
            isResetting={toolbarLoading.resetting}
            isSaving={toolbarLoading.saving}
            isPosting={toolbarLoading.posting}
            validationMessages={validationMessages}
            onExport={handleToolbarExport}
            onReset={handleToolbarReset}
            onSaveChanges={handleToolbarSaveChanges}
            onPostReady={handleToolbarPostReady}
          />
        </>
      )}
    </div>
  );

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset className="flex-1 overflow-hidden flex flex-col">
          {/* Header - Fixed height */}
          <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      navigate('/dashboard');
                    }}
                    className="cursor-pointer"
                  >
                    {firmNameLoading ? 'Loading...' : firmName}
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      navigate('/accpay/all');
                    }}
                    className="cursor-pointer"
                  >
                    ACCPAY
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Prepayments</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </header>

          {/* Main content - Fill remaining space with resizable panels */}
          <div className="flex-1 overflow-hidden">
            <ResizablePanel
              leftPanel={leftPanelContent}
              rightPanel={rightPanelContent}
              defaultLeftWidth={640}
              minLeftWidth={360}
              maxLeftWidth={800}
            />
          </div>
        </SidebarInset>
      </SidebarProvider>

      {/* Attachment viewing modal */}
      <SideBySideReview
        isOpen={attachmentModalOpen}
        onClose={() => {
          setAttachmentModalOpen(false);
          setAttachmentData(null);
          setSelectedInvoiceId(null);
          setAttachmentError(null);
        }}
        transactionId={selectedInvoiceId}
        transactionData={attachmentData?.transactionData || null}
        scheduleData={attachmentData?.scheduleData || null}
        attachmentUrl={attachmentData?.attachmentUrl || null}
        isLoading={attachmentLoading}
        error={attachmentError}
      />

      <KeyboardShortcutsModal
        open={showKeyboardHelp}
        onOpenChange={setShowKeyboardHelp}
      />
    </div>
  );
}