// Contact/Counterparty Types

export enum ContactType {
  VENDOR = "vendor",
  CUSTOMER = "customer", 
  SUPPLIER = "supplier",
  EMPLOYEE = "employee",
  OTHER = "other"
}

export interface ContactAddress {
  address_type?: "billing" | "shipping" | "business" | "other";
  line1?: string;
  line2?: string;
  city?: string;
  region?: string;
  postal_code?: string;
  country?: string;
}

export interface ContactPhone {
  phone_type?: string; // e.g., "MOBILE", "OFFICE", "FAX"
  phone_number?: string;
  phone_area_code?: string;
  phone_country_code?: string;
}

export interface ContactAmortizationSettings {
  default_expense_account_code?: string;
  default_amortization_months?: number;
}

export interface Contact {
  contact_id: string;
  counterparty_id: string;
  client_id: string;
  entity_id: string;
  name: string;
  contact_type?: ContactType;
  email_address?: string;
  phones?: ContactPhone[];
  addresses?: ContactAddress[];
  tax_number?: string;
  website?: string;
  source_system?: string;
  source_system_id?: string;
  source_updated_at_utc?: string;
  amortization_settings?: ContactAmortizationSettings;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
  raw_source_data?: Record<string, any>;
  system_defaultAmortizationExpenseAccountCode?: string;
}

export interface ContactSummary {
  counterparty_id: string;
  client_id: string;
  entity_id: string;
  name: string;
  contact_type?: ContactType;
  email_address?: string;
  source_system?: string;
  is_active: boolean;
  updated_at?: string;
  has_amortization_settings: boolean;
  transaction_count: number;
}

export interface ContactFilters {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  client_id?: string;
  entity_id?: string;
  name_filter?: string;
  contact_type?: ContactType;
  email_filter?: string;
  source_system?: string;
  is_active?: boolean;
  has_amortization_settings?: boolean;
}

export interface ContactListResponse {
  contacts: ContactSummary[];
  pagination: {
    current_page: number;
    page_size: number;
    total_items: number;
    total_pages: number;
    has_next: boolean;
    has_previous: boolean;
  };
  filters_applied?: ContactFilters;
}

export interface ContactDetailResponse {
  contact: Contact;
  transaction_count: number;
  recent_transactions?: any[];
  amortization_schedules_count?: number;
}