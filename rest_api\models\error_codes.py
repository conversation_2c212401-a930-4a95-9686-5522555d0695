from enum import Enum

class AppErrorCode(str, Enum):
    # General Authentication Errors
    AUTHENTICATION_REQUIRED = "AUTHENTICATION_REQUIRED" # Generic, if no token provided (usually handled by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)
    AUTHENTICATION_FAILED = "AUTHENTICATION_FAILED"   # Generic failure if not more specific
    TOKEN_INVALID = "TOKEN_INVALID"                 # Token is malformed or signature invalid
    TOKEN_EXPIRED = "TOKEN_EXPIRED"                 # Token has expired
    USER_NOT_FOUND_FIREBASE = "USER_NOT_FOUND_FIREBASE" # UID in token doesn't exist in Firebase Auth

    # Application-Level User/Role Errors (Post-Firebase Auth)
    USER_PROFILE_NOT_FOUND = "USER_PROFILE_NOT_FOUND" # User authenticated but no profile in FIRM_USERS/CLIENT_USERS
    ROLE_NOT_ASSIGNED = "ROLE_NOT_ASSIGNED"         # User has no role in the application
    
    # Authorization / Permission Errors
    FORBIDDEN_ACTION = "FORBIDDEN_ACTION"            # Generic insufficient permissions
    ADMIN_PRIVILEGES_REQUIRED = "ADMIN_PRIVILEGES_REQUIRED"
    CLIENT_ACCESS_DENIED = "CLIENT_ACCESS_DENIED"

    # Generic Server & Validation errors (might overlap with HTTP status codes but can be more specific)
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR" # Already used in generic_exception_handler
    VALIDATION_ERROR = "VALIDATION_ERROR"           # Already used in request_validation_exception_handler
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"       # For generic 404s where applicable

    # Transaction Specific Errors
    TRANSACTION_NOT_FOUND = "TRANSACTION_NOT_FOUND"
    TRANSACTION_OPERATION_FORBIDDEN = "TRANSACTION_OPERATION_FORBIDDEN"
    INVALID_TRANSACTION_PAYLOAD = "INVALID_TRANSACTION_PAYLOAD" # For issues with payload not caught by Pydantic schema (e.g. empty update)
    INVALID_ENTITY_FOR_CLIENT = "INVALID_ENTITY_FOR_CLIENT"
    TRANSACTION_TOTAL_MISMATCH = "TRANSACTION_TOTAL_MISMATCH"
    INVALID_TRANSACTION_DATES = "INVALID_TRANSACTION_DATES"

    # You can add more specific codes as needed, e.g.:
    # XERO_AUTH_ERROR = "XERO_AUTH_ERROR"
    # DATABASE_QUERY_ERROR = "DATABASE_QUERY_ERROR" 