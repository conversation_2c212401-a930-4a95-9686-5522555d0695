# Full-Stack GitLab CI/CD Setup Guide - DRCR Application

## 🚀 **Overview**

This guide sets up automated deployment for your complete DRCR application:
- **Frontend**: Optimized React app → Firebase Hosting
- **Backend**: Python FastAPI → Google Cloud Run
- **Full-Stack**: Coordinated deployment of both services

## 📁 **Project Structure Expected**

```
your-gitlab-repo/
├── frontend/                    # DRCR Frontend
│   ├── src/
│   ├── package.json
│   ├── vite.config.ts
│   ├── firebase.json
│   └── .firebaserc
├── backend/                     # DRCR Backend  
│   ├── rest_api/
│   ├── requirements.txt
│   ├── Dockerfile
│   └── tests/
├── .gitlab-ci.yml              # This pipeline
└── README.md
```

## 🔧 **Prerequisites & Setup**

### **1. Firebase Setup (Frontend)**

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login and get CI token
firebase login:ci
# Copy the token for GitLab variables
```

### **2. Google Cloud Setup (Backend)**

```bash
# Install Google Cloud SDK
# Create service account for CI/CD
gcloud iam service-accounts create gitlab-ci \
    --description="GitLab CI/CD Service Account" \
    --display-name="GitLab CI"

# Grant necessary permissions
gcloud projects add-iam-policy-binding drcr-d660a \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/run.admin"

gcloud projects add-iam-policy-binding drcr-d660a \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/storage.admin"

gcloud projects add-iam-policy-binding drcr-d660a \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/iam.serviceAccountUser"

# Create and download service account key
gcloud iam service-accounts keys create gcp-key.json \
    --iam-account=<EMAIL>

# Encode for GitLab (base64)
base64 gcp-key.json > gcp-key-base64.txt
```

### **3. GitLab CI/CD Variables**

In your GitLab project: **Settings** → **CI/CD** → **Variables**

| Variable Name | Value | Protected | Masked | Description |
|---------------|-------|-----------|---------|-------------|
| `FIREBASE_TOKEN` | Your Firebase CI token | ✅ | ✅ | Frontend deployment |
| `GCP_SERVICE_KEY` | Base64 encoded service key | ✅ | ✅ | Backend deployment |

### **4. GitLab Container Registry**

Enable Container Registry in your GitLab project:
- **Settings** → **General** → **Visibility** → **Container Registry** → **Enabled**

## 🔄 **Pipeline Architecture**

### **Stages Overview:**
1. **Install** - Dependencies for both frontend & backend
2. **Test** - Linting, unit tests, coverage reports  
3. **Build** - Optimized frontend bundle + Docker image
4. **Deploy** - Multi-environment deployment

### **Deployment Environments:**

| Environment | Frontend | Backend | Trigger |
|-------------|----------|---------|---------|
| **Preview** | Firebase Preview Channel | ❌ | Manual (MR) |
| **Staging** | Firebase Staging Channel | Cloud Run Staging | Manual (`develop`) |
| **Production** | Firebase Main | Cloud Run Production | Manual (`main`) |

## 🎯 **Workflow Examples**

### **Feature Development:**
```bash
# 1. Create feature branch
git checkout -b feature/dashboard-optimization

# 2. Work on both frontend and backend
# Edit frontend/src/features/dashboard/...
# Edit backend/rest_api/routes/...

# 3. Push and create merge request
git add .
git commit -m "Optimize dashboard with new API endpoints"
git push origin feature/dashboard-optimization

# 4. In GitLab MR: Manually trigger preview deployment
# Preview URL: https://drcr-d660a--preview-[MR-ID].web.app
```

### **Staging Deployment:**
```bash
# 1. Merge to develop
git checkout develop
git merge feature/dashboard-optimization
git push origin develop

# 2. Pipeline runs automatically (install, test, build)
# 3. Manually trigger staging deployment
# Frontend: https://drcr-d660a--staging.web.app
# Backend: https://drcr-backend-staging-[hash].run.app
```

### **Production Deployment:**
```bash
# 1. Merge to main
git checkout main  
git merge develop
git push origin main

# 2. Pipeline runs automatically (install, test, build)
# 3. Manually trigger production deployment
# Frontend: https://drcr-d660a.web.app
# Backend: https://drcr-backend-prod-[hash].run.app
```

## 📊 **Pipeline Jobs Breakdown**

### **Frontend Jobs:**
- `install_frontend` - npm dependencies with caching
- `test_frontend` - ESLint + Vitest (when ready)
- `build_frontend` - Optimized Vite build with bundle analysis
- `deploy_frontend_*` - Firebase Hosting deployment

### **Backend Jobs:**
- `install_backend` - Python venv + pip dependencies  
- `test_backend` - pytest with coverage reports
- `build_backend` - Docker image build + push to registry
- `deploy_backend_*` - Google Cloud Run deployment

### **Full-Stack Jobs:**
- `deploy_fullstack_production` - Coordinates both deployments

## 🔍 **Monitoring & Debugging**

### **View Pipeline Status:**
- GitLab Project → **CI/CD** → **Pipelines**
- Click pipeline → See all jobs (frontend/backend)
- Download artifacts (build files, coverage, Docker images)

### **Common Issues & Solutions:**

#### **Frontend Build Fails:**
```bash
# Test locally first
cd frontend
npm run build
npm run lint
```

#### **Backend Build Fails:**
```bash
# Test locally first  
cd backend
python -m pytest tests/
docker build -t test-backend .
```

#### **Firebase Deployment Fails:**
```bash
# Check token validity
firebase login:ci  # Get new token
# Update FIREBASE_TOKEN in GitLab variables
```

#### **Cloud Run Deployment Fails:**
```bash
# Check service account permissions
gcloud auth list
gcloud projects get-iam-policy drcr-d660a
```

## 🚀 **Advanced Configuration**

### **Environment Variables per Stage:**

Add to `.gitlab-ci.yml`:
```yaml
variables:
  FRONTEND_API_URL_STAGING: "https://drcr-backend-staging.run.app"
  FRONTEND_API_URL_PRODUCTION: "https://drcr-backend-prod.run.app"
```

### **Database Migrations:**
```yaml
migrate_database:
  stage: deploy
  script:
    - cd backend
    - python manage.py migrate
  only:
    - main
  when: manual
```

### **Notifications:**
```yaml
notify_deployment:
  stage: deploy
  script:
    - curl -X POST $SLACK_WEBHOOK -d '{"text":"🚀 DRCR deployed!"}'
  only:
    - main
```

## 📈 **Expected Performance**

### **Build Times:**
- Frontend: ~2-3 minutes (with caching)
- Backend: ~3-4 minutes (with caching)  
- Total pipeline: ~8-10 minutes

### **Bundle Analysis:**
Frontend build will show:
```
📊 Frontend bundle analysis:
  react-vendor-[hash].js: ~326 KB
  firebase-[hash].js: ~164 KB
  index-[hash].js: ~124 KB
  data-[hash].js: ~35 KB
```

### **Deployment URLs:**
- **Frontend Prod**: https://drcr-d660a.web.app
- **Frontend Staging**: https://drcr-d660a--staging.web.app
- **Backend Prod**: https://drcr-backend-prod-[hash].run.app
- **Backend Staging**: https://drcr-backend-staging-[hash].run.app

## 🔒 **Security Best Practices**

### **Secrets Management:**
- ✅ All tokens stored as GitLab masked variables
- ✅ Service account with minimal required permissions
- ✅ Environment-specific configurations
- ✅ No secrets in code or logs

### **Access Control:**
- ✅ Manual deployment gates for production
- ✅ Protected branches (`main`, `develop`)
- ✅ Merge request approvals required

## 🆘 **Troubleshooting Guide**

| Issue | Check | Solution |
|-------|-------|----------|
| Frontend build fails | `npm run build` locally | Fix TypeScript/linting errors |
| Backend tests fail | `pytest tests/` locally | Fix failing tests |
| Docker build fails | `docker build .` locally | Check Dockerfile and dependencies |
| Firebase deploy fails | Token validity | Regenerate `FIREBASE_TOKEN` |
| Cloud Run deploy fails | Service account permissions | Check GCP IAM roles |
| Pipeline stuck | GitLab runners | Check runner availability |

## 🎉 **Success Metrics**

After setup, you'll have:

- ✅ **Automated full-stack builds** on every commit
- ✅ **Quality gates** (linting, tests, coverage)
- ✅ **Multi-environment deployments** (preview, staging, production)
- ✅ **Performance monitoring** (bundle sizes, build times)
- ✅ **Coordinated deployments** (frontend + backend together)
- ✅ **Rollback capabilities** (manual deployment gates)

Your optimized DRCR application will be automatically built, tested, and deployed with proper separation of concerns and performance optimizations! 🚀

---

**Next Steps:**
1. Set up GitLab variables
2. Organize code into `frontend/` and `backend/` directories
3. Push `.gitlab-ci.yml` to your repository
4. Create your first merge request and test the pipeline! 