import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Settings, 
  Trash2, 
  RefreshCw, 
  Wifi, 
  WifiOff, 
  AlertCircle,
  Building2,
  Eye,
  Link,
  Unlink,
  Loader2
} from 'lucide-react';
import { EntityStatusBadge } from '@/components/ui/entity-status-badge';
import { useSyncStatusPolling } from '@/hooks/useSyncStatusPolling';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { CreateEntityModal } from '@/components/entities/CreateEntityModal';
import { OAuthCallback, useOAuthCallback } from '@/components/entities/OAuthCallback';
import { EntitySettingsManagement } from '@/components/entities/EntitySettingsManagement';
import { EntityDetailView } from '@/components/entities/EntityDetailView';
import { 
  DraggableDialog, 
  DraggableDialogContent, 
  DraggableDialogHeader, 
  DraggableDialogTitle, 
  DraggableDialogDescription,
  DraggableDialogFooter 
} from '@/components/ui/draggable-dialog';
import { EntitiesService } from '@/services/entities.service';
import { useClientStore } from '@/store/client.store';
import { AppSidebar } from '@/components/layout/AppSidebar';
import { useFirmName } from '@/hooks/useFirmName';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar';
import type { 
  EntitySummary, 
  EntityListResponse, 
  EntityType,
  EntityStatus
} from '@/types/entity.types';

export function EntityManagement() {
  const { clientId, entityId } = useParams();
  const navigate = useNavigate();
  const { isOAuthCallback } = useOAuthCallback();
  const { firmName, isLoading: firmNameLoading } = useFirmName();
  
  // State
  const [entities, setEntities] = useState<EntitySummary[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Extract entity IDs for sync polling
  const entityIds = entities.map(entity => entity.entity_id);
  
  // Use sync status polling hook - enabled but with graceful error handling
  const { syncStatuses, refreshSyncStatus } = useSyncStatusPolling(entityIds, {
    enabled: entityIds.length > 0 && !isLoading, // Re-enabled with error handling
    pollingInterval: 30000 // 30 seconds
  });

  // Console log sync statuses for debugging
  useEffect(() => {
    if (syncStatuses.size > 0) {
      console.log('=== SYNC STATUS DATA ===');
      syncStatuses.forEach((syncData, entityId) => {
        const entity = entities.find(e => e.entity_id === entityId);
        console.log(`Entity: ${entity?.entity_name || entityId} (${entity?.type || 'unknown'})`, {
          entityId,
          syncStatus: syncData.syncStatus,
          lastSync: syncData.lastSync,
          connectionStatus: syncData.connectionStatus
        });
      });
      console.log('========================');
    }
  }, [syncStatuses, entities]);

  // Helper function to check if entity is syncing
  const isEntitySyncing = useCallback((entity: EntitySummary) => {
    const entitySyncStatus = syncStatuses.get(entity.entity_id);
    return entity.connection_status === 'syncing' || 
           entity.sync_status?.is_syncing || 
           entitySyncStatus?.syncStatus?.is_syncing;
  }, [syncStatuses]);
  const [creditInfo, setCreditInfo] = useState<{
    credit_balance: number;
    credits_used_total: number;
    credits_used_openai: number;
    credits_used_mistral: number;
    last_credit_usage?: string;
  } | null>(null);
  
  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [selectedEntityForSettings, setSelectedEntityForSettings] = useState<EntitySummary | null>(null);
  
  // Entity settings save state
  const [entitySaveHandler, setEntitySaveHandler] = useState<(() => Promise<void>) | null>(null);
  const [canSaveEntity, setCanSaveEntity] = useState(false);
  const [isSavingEntity, setIsSavingEntity] = useState(false);
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Client store
  const { currentClient, fetchClient } = useClientStore();

  // Load client details when clientId changes
  useEffect(() => {
    if (clientId && (!currentClient || currentClient.client_id !== clientId)) {
      fetchClient(clientId);
    }
  }, [clientId, currentClient, fetchClient]);

  const loadEntities = useCallback(async () => {
    if (!clientId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      const response = await EntitiesService.getEntitiesForClient(clientId, {});
      setEntities(response.entities);
      
      // Extract credit info if available
      if ((response as any).credit_info) {
        console.log('Credit info found:', (response as any).credit_info);
        setCreditInfo((response as any).credit_info);
      } else {
        console.log('No credit_info in response');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load entities');
    } finally {
      setIsLoading(false);
    }
  }, [clientId]);

  // Load entities when component mounts or clientId changes
  useEffect(() => {
    loadEntities();
  }, [loadEntities]);

  // Filter entities client-side for better performance
  const filteredEntities = entities.filter(entity => {
    const matchesSearch = searchTerm === '' || 
      entity.entity_name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = typeFilter === 'all' || entity.type === typeFilter;
    
    const matchesStatus = statusFilter === 'all' || entity.connection_status === statusFilter;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  // Handle OAuth callback
  if (isOAuthCallback) {
    return (
      <OAuthCallback
        entityId={entityId}
        onSuccess={(entityId, response) => {
          navigate(`/clients/${clientId}/entities/${entityId}`);
        }}
        onError={(error) => {
          setError(error);
          navigate(`/clients/${clientId}/entities`);
        }}
        onCancel={() => {
          navigate(`/clients/${clientId}/entities`);
        }}
      />
    );
  }


  const handleCreateEntity = () => {
    setShowCreateModal(true);
  };

  const handleEntityCreated = (entityId: string) => {
    loadEntities();
    // Optionally navigate to the new entity's data page
    // navigate(`/clients/${clientId}/entities/${entityId}`);
  };

  const handleViewEntity = (entity: EntitySummary) => {
    // Navigate to entity data explorer
    navigate(`/clients/${clientId}/entities/${entity.entity_id}`);
  };

  const handleEditEntity = (entity: EntitySummary) => {
    console.log('handleEditEntity called with entity:', entity);
    
    if (isEntitySyncing(entity)) {
      setError('Cannot open entity settings while sync is in progress. Please wait for sync to complete.');
      return;
    }
    
    setSelectedEntityForSettings(entity);
    setShowSettingsModal(true);
    console.log('Modal state set to true, selectedEntity:', entity.entity_name);
  };

  const handleDeleteEntity = async (entity: EntitySummary) => {
    if (!confirm(`Are you sure you want to delete "${entity.entity_name}"?`)) {
      return;
    }

    try {
      await EntitiesService.deleteEntity(entity.entity_id);
      loadEntities();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete entity');
    }
  };

  const handleSyncEntity = async (entity: EntitySummary) => {
    try {
      if (isEntitySyncing(entity)) {
        setError('Sync is already in progress for this entity. Please wait for it to complete.');
        return;
      }
      
      // Optimistically update the entity to show syncing status
      setEntities(prev => prev.map(e => 
        e.entity_id === entity.entity_id 
          ? { ...e, connection_status: 'syncing' as any }
          : e
      ));
      
      await EntitiesService.triggerSync(entity.entity_id);
      
      // Refresh sync status immediately after triggering sync
      await refreshSyncStatus(entity.entity_id);
      
      // Refresh the entities list after a delay to allow sync to complete
      setTimeout(() => {
        loadEntities();
      }, 2000);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to trigger sync');
      // Revert optimistic update on error
      loadEntities();
    }
  };

  const handleDisconnectEntity = async (entity: EntitySummary) => {
    if (!confirm(`Are you sure you want to disconnect "${entity.entity_name}"?`)) {
      return;
    }

    try {
      await EntitiesService.disconnectEntity(entity.entity_id);
      loadEntities();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to disconnect entity');
    }
  };

  const handleEntitySaveAction = (saveHandler: () => Promise<void>, canSave: boolean, isSaving: boolean) => {
    setEntitySaveHandler(() => saveHandler);
    setCanSaveEntity(canSave);
    setIsSavingEntity(isSaving);
  };

  // Memoize the settings fetch functions to prevent repeated API calls
  const handleFetchSettings = useCallback(async (entityId: string) => {
    const entity = await EntitiesService.getEntity(entityId);
    return {
      entityId: entity.entity_id,
      entityName: entity.entity_name,
      prepaymentAssetAccountCodes: entity.settings?.prepayment_asset_account_codes || [],
      defaultExpenseAccountCode: entity.settings?.default_expense_account_code || null,
      defaultAmortizationMonths: entity.settings?.default_amortization_months || 12,
      amortizationMaterialityThreshold: entity.settings?.amortization_materiality_threshold || 1000,
      autoSyncEnabled: entity.settings?.auto_sync_enabled ?? true,
      syncFrequency: entity.settings?.sync_frequency || 'daily',
      syncSpendMoney: entity.settings?.sync_spend_money ?? true,
      excludedPnlAccountCodes: entity.settings?.excluded_pnl_account_codes || [],
      transactionSyncStartDate: entity.settings?.transaction_sync_start_date || '',
      syncInvoices: entity.settings?.sync_invoices ?? true,
      syncBills: entity.settings?.sync_bills ?? true,
      syncPayments: entity.settings?.sync_payments ?? true,
      syncBankTransactions: entity.settings?.sync_bank_transactions ?? true,
      syncJournalEntries: entity.settings?.sync_journal_entries ?? true,
      autoPostProposedJournals: entity.settings?.auto_post_proposed_journals ?? false,
      baseCurrencyCode: entity.settings?.base_currency_code || 'USD'
    };
  }, []);

  const handleFetchChartOfAccounts = useCallback(async (entityId: string) => {
    const accounts = await EntitiesService.getChartOfAccounts(entityId);
    return accounts;
  }, []);

  const handleSaveSettings = useCallback(async (entityId: string, settings: any) => {
    await EntitiesService.updateEntitySettings(entityId, {
      prepayment_asset_account_codes: settings.prepaymentAssetAccountCodes,
      default_expense_account_code: settings.defaultExpenseAccountCode,
      default_amortization_months: settings.defaultAmortizationMonths,
      amortization_materiality_threshold: settings.amortizationMaterialityThreshold,
      auto_sync_enabled: settings.autoSyncEnabled,
      sync_frequency: settings.syncFrequency,
      sync_spend_money: settings.syncSpendMoney,
      excluded_pnl_account_codes: settings.excludedPnlAccountCodes,
      transaction_sync_start_date: settings.transactionSyncStartDate,
      sync_invoices: settings.syncInvoices,
      sync_bills: settings.syncBills,
      sync_payments: settings.syncPayments,
      sync_bank_transactions: settings.syncBankTransactions,
      sync_journal_entries: settings.syncJournalEntries,
      auto_post_proposed_journals: settings.autoPostProposedJournals,
      base_currency_code: settings.baseCurrencyCode
    });
  }, []);

  // Helper function to get enhanced entity with sync status
  const getEntityWithSyncStatus = (entity: EntitySummary) => {
    const syncStatusData = syncStatuses.get(entity.entity_id);
    
    // If we have sync status data, use it. Otherwise, check if entity shows syncing connection status
    const enhancedEntity = {
      ...entity,
      sync_status: syncStatusData?.syncStatus || ('sync_status' in entity ? entity.sync_status : null)
    };
    
    // If connection_status is 'syncing', create a synthetic sync status
    if (entity.connection_status === 'syncing' && !enhancedEntity.sync_status) {
      enhancedEntity.sync_status = {
        is_syncing: true,
        current_step: 'Data synchronization in progress',
        progress_percentage: undefined,
        estimated_remaining: undefined,
        user_message: 'Sync in progress...'
      };
    }
    
    return enhancedEntity;
  };

  const renderEntityList = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-end">
        <Button onClick={handleCreateEntity}>
          <Plus className="h-4 w-4 mr-2" />
          Add Entity
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search entities..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select
              value={typeFilter}
              onValueChange={(value) => setTypeFilter(value)}
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="xero">Xero</SelectItem>
                <SelectItem value="qbo">QuickBooks</SelectItem>
                <SelectItem value="manual">Manual</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={statusFilter}
              onValueChange={(value) => setStatusFilter(value)}
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="disconnected">Disconnected</SelectItem>
                <SelectItem value="error">Error</SelectItem>
                <SelectItem value="syncing">Syncing</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Credit Information */}
      {creditInfo && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Credit Usage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{creditInfo.credit_balance}</div>
                <div className="text-sm text-gray-500">Credits Available</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{creditInfo.credits_used_total}</div>
                <div className="text-sm text-gray-500">Total Used</div>
              </div>
              <div className="text-center">
                <div className="text-lg text-gray-700">
                  {creditInfo.last_credit_usage 
                    ? new Date(creditInfo.last_credit_usage).toLocaleDateString()
                    : 'Never'
                  }
                </div>
                <div className="text-sm text-gray-500">Last Usage</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Entity List */}
      <div className="grid gap-4">
        {isLoading ? (
          [...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="flex gap-2">
                    <div className="h-6 bg-gray-200 rounded w-20"></div>
                    <div className="h-6 bg-gray-200 rounded w-24"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : filteredEntities.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No entities found</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm ? 'No entities match your search criteria.' : 'Get started by adding your first entity.'}
              </p>
              <Button onClick={handleCreateEntity}>
                <Plus className="h-4 w-4 mr-2" />
                Add Entity
              </Button>
            </CardContent>
          </Card>
        ) : (
          filteredEntities.map((entity) => (
            <Card key={entity.entity_id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="text-base font-semibold truncate">{entity.entity_name}</h3>
                      <Badge variant="outline" className="text-xs">{entity.type ? entity.type.toUpperCase() : 'UNKNOWN TYPE'}</Badge>
                      <EntityStatusBadge 
                        entity={getEntityWithSyncStatus(entity)} 
                        syncStatus={syncStatuses.get(entity.entity_id)?.syncStatus}
                      />
                    </div>
                    <div className="flex items-center gap-3 text-xs text-gray-500">
                      {entity.last_sync && (
                        <span>Last sync: {new Date(entity.last_sync).toLocaleDateString()}</span>
                      )}
                      {entity.pending_items_count !== undefined && entity.pending_items_count > 0 && (
                        <span className="text-yellow-600">{entity.pending_items_count} pending</span>
                      )}
                      {entity.error_count !== undefined && entity.error_count > 0 && (
                        <span className="text-red-600">{entity.error_count} errors</span>
                      )}
                    </div>
                    {entity.error_message && (
                      <p className="text-xs text-red-600 mt-1 truncate">{entity.error_message}</p>
                    )}
                  </div>
                  <div className="flex items-center gap-1 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewEntity(entity)}
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      View
                    </Button>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span>
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={isEntitySyncing(entity)}
                            onClick={() => handleEditEntity(entity)}
                          >
                            <Settings className="h-3 w-3 mr-1" />
                            Settings
                          </Button>
                        </span>
                      </TooltipTrigger>
                      <TooltipContent>
                        {isEntitySyncing(entity)
                          ? 'Settings unavailable during sync'
                          : 'Configure entity settings'
                        }
                      </TooltipContent>
                    </Tooltip>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="px-2">
                          <MoreHorizontal className="h-3 w-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem 
                          disabled={isEntitySyncing(entity)}
                          onClick={() => handleSyncEntity(entity)}
                        >
                          <RefreshCw className={`h-4 w-4 mr-2 ${isEntitySyncing(entity) ? 'animate-spin' : ''}`} />
                          {isEntitySyncing(entity) ? 'Syncing...' : 'Sync Now'}
                        </DropdownMenuItem>
                        {entity.connection_status === 'active' && (
                          <DropdownMenuItem onClick={() => handleDisconnectEntity(entity)}>
                            <Unlink className="h-4 w-4 mr-2" />
                            Disconnect
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem 
                          onClick={() => handleDeleteEntity(entity)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );


  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset className="flex-1 overflow-hidden">
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate('/dashboard');
                  }}
                  className="cursor-pointer"
                >
                  {firmNameLoading ? 'Loading...' : firmName}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                {entityId ? (
                  <BreadcrumbLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      navigate(`/clients/${clientId}/entities`);
                    }}
                    className="cursor-pointer"
                  >
                    {currentClient?.name ? `${currentClient.name} - Entities` : 'Entities'}
                  </BreadcrumbLink>
                ) : (
                  <BreadcrumbPage>
                    {currentClient?.name ? `${currentClient.name} - Entities` : 'Entity Management'}
                  </BreadcrumbPage>
                )}
              </BreadcrumbItem>
              {entityId && (
                <>
                  <BreadcrumbSeparator className="hidden md:block" />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Entity Details</BreadcrumbPage>
                  </BreadcrumbItem>
                </>
              )}
            </BreadcrumbList>
          </Breadcrumb>
        </header>
        <div className="flex-1 overflow-auto">
          <div className="p-6">
            {entityId ? <EntityDetailView /> : renderEntityList()}
          </div>
        </div>
      </SidebarInset>

      {/* Create Entity Modal */}
      <CreateEntityModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        clientId={clientId || ''}
        clientName={currentClient?.name}
        onSuccess={handleEntityCreated}
      />

      {/* Entity Settings Modal */}
      {selectedEntityForSettings && (
        <DraggableDialog open={showSettingsModal} onOpenChange={setShowSettingsModal}>
          <DraggableDialogContent className="flex flex-col p-0 gap-0">
            <DraggableDialogHeader className="p-6 border-b flex-shrink-0">
              <DraggableDialogTitle>Entity Settings - {selectedEntityForSettings.entity_name}</DraggableDialogTitle>
              <DraggableDialogDescription>
                Configure prepayment and expense accounts for this entity.
              </DraggableDialogDescription>
            </DraggableDialogHeader>
            <div className="flex-1 overflow-y-auto p-6">
              <EntitySettingsManagement
                entityId={selectedEntityForSettings.entity_id}
                fetchSettings={handleFetchSettings}
                fetchChartOfAccounts={handleFetchChartOfAccounts}
                saveSettings={handleSaveSettings}
                onClose={() => setShowSettingsModal(false)}
                hideFooter={true}
                onSaveAction={handleEntitySaveAction}
              />
            </div>
            <DraggableDialogFooter className="p-6 border-t flex-shrink-0">
              <Button variant="outline" onClick={() => setShowSettingsModal(false)} disabled={isSavingEntity}>
                Cancel
              </Button>
              <Button
                onClick={entitySaveHandler || (() => {})}
                disabled={isSavingEntity || !canSaveEntity || !entitySaveHandler}
              >
                {isSavingEntity && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save Settings
              </Button>
            </DraggableDialogFooter>
          </DraggableDialogContent>
        </DraggableDialog>
      )}
    </SidebarProvider>
  );
}