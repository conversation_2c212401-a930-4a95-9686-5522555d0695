# Bills for Amortization Feature

## Overview
The Bills for Amortization page provides an alternative interface to the traditional grid-based prepayments page. It features a sidebar-based layout optimized for managing bill amortization schedules.

## Features

### 🔍 Hierarchical Bills Selection Sidebar
- **Three-Level Hierarchy**: Suppliers → Invoices → Line Items with proper indentation
- **Expand/Collapse**: Tree structure navigation with chevron icons
- **Searchable Across All Levels**: Filter by supplier names, invoice numbers, or line item descriptions
- **Smart Multi-Select**: 
  - Parent selection automatically selects all children
  - Child selections update parent states (full/partial/none)
  - Indeterminate checkboxes for partial selections
- **Status Indicators**: Color-coded status dots and badges at each hierarchy level
- **Smart Tags**: Automatically detects Multi COA, Single COA, tracking categories, and attachments at line item level
- **Visual Hierarchy**: Proper indentation (0px → 16px → 32px) with connecting lines

### 📊 Amortization Summary
- **Real-time Calculations**: Total amount, monthly amount, and period duration
- **Status Tracking**: Visual indicators for configuration completion
- **Key Metrics**: Displays selected bills count and calculation summary

### ⚙️ Configuration Panel
- **Amortization Method**: Currently supports Straight Line (equal monthly amounts)
- **Period Settings**: Configurable number of months (1-60)
- **Date Selection**: Start date picker with validation
- **Account Mapping**: 
  - Prepayment Account selector (filtered by asset accounts)
  - Expense Account selector (filtered by expense accounts)
- **Validation**: Real-time feedback on configuration completeness

### 📅 Monthly Schedule Table
- **Period Breakdown**: Month-by-month amortization schedule
- **Running Balance**: Tracks remaining balance after each period
- **Status Management**: Pending, Ready, and Posted status for each entry
- **Actions**: Edit individual entries and export functionality
- **Visual Indicators**: Color-coded status and progress tracking

## Navigation
- **URL**: `/accpay/bills-amortization`
- **Menu**: ACCPAY → Bills Amortization
- **Query Parameters**: Supports `client_id` for client-specific filtering

## Components

### Core Components
- `BillsAmortizationPage.tsx` - Main page container with hierarchical state management
- `HierarchicalBillsList.tsx` - Advanced tree view sidebar with multi-level selection
- `BillsList.tsx` - Legacy flat list component (deprecated)
- `AmortizationSummary.tsx` - Summary metrics display
- `AmortizationConfiguration.tsx` - Configuration form
- `MonthlyScheduleTable.tsx` - Schedule breakdown table

### Integration
- Uses existing `PrepaymentsService` for data fetching
- Reuses `schedule.types.ts` for type definitions
- Integrates with current authentication and routing system
- Maintains consistency with existing shadcn/ui design system

## Technical Details

### State Management
```typescript
interface HierarchicalBillsState {
  hierarchicalData: HierarchicalBillsData
  expandedState: {
    suppliers: Set<string>
    invoices: Set<string>
  }
  searchTerm: string
  amortizationConfig: {
    method: string
    startDate: string
    prepaymentAccount: string
    expenseAccount: string
    numberOfPeriods: number
  }
  scheduleData: MonthlyEntry[]
}

interface SupplierNode {
  supplierId: string
  supplierName: string
  totalAmount: number
  invoiceCount: number
  isExpanded: boolean
  isSelected: boolean
  invoices: InvoiceNode[]
}
```

### Data Flow
1. Load supplier data from prepayments API
2. Transform flat data into hierarchical structure (Suppliers → Invoices → Line Items)
3. Apply multi-level search filtering with auto-expansion
4. Calculate amortization based on selected line items
5. Generate monthly schedule with proper totals
6. Enable configuration and posting

### Responsive Design
- **Desktop**: Two-panel layout (sidebar + main content)
- **Mobile**: Collapsible sidebar with stacked content
- **Tablet**: Responsive grid adjustments

## Future Enhancements
- Additional amortization methods (Declining Balance, Sum of Years)
- Bulk operations for schedule management
- Advanced filtering and sorting options
- Schedule template saving and reuse
- Integration with accounting system posting
- Export to multiple formats (CSV, PDF, Excel)

## Related Files
- `/src/pages/BillsAmortizationPage.tsx` - Main page with hierarchical state management
- `/src/components/prepayments/HierarchicalBillsList.tsx` - Advanced tree view component
- `/src/components/prepayments/AmortizationSummary.tsx` - Summary metrics display
- `/src/components/prepayments/AmortizationConfiguration.tsx` - Configuration form
- `/src/components/prepayments/MonthlyScheduleTable.tsx` - Schedule breakdown table
- `/src/types/hierarchical-bills.types.ts` - New hierarchical data type definitions
- `/src/types/schedule.types.ts` - Base schedule type definitions
- `/src/services/prepayments.service.ts` - API service layer