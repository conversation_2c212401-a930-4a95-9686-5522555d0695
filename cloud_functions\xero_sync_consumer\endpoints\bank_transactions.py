"""
BankTransactions Endpoint Handler

Handles general BankTransactions synchronization from Xero.
Covers all bank transaction types (SPEND, RECEIVE, etc.).
"""

import logging
from typing import List
from datetime import datetime, timezone

from google.cloud import firestore

from context import SyncContext
from utils.sync_helpers import _create_audit_log_entry, _update_firestore_sync_timestamp
from utils.bills_processor import _check_if_current_version_exists

logger = logging.getLogger(__name__)

__all__ = ["sync_bank_transactions"]


async def sync_bank_transactions(ctx: SyncContext, requested_endpoints: List[str]) -> int:
    """
    Handle BankTransactions synchronization from Xero.
    
    Args:
        ctx: Sync context containing all dependencies
        requested_endpoints: List of requested endpoints for sync
        
    Returns:
        Number of bank transactions processed
        
    Raises:
        Exception: If bank transactions sync fails
    """
    if "BankTransactions" not in requested_endpoints and requested_endpoints:
        return 0
    
    # Check if bank transactions sync is enabled for this entity
    if not ctx.should_sync_endpoint("BankTransactions", True):
        logger.info(f"BankTransactions sync disabled for entity_id: {ctx.entity_id}")
        return 0
    
    try:
        logger.info(f"Starting BankTransactions sync for entity_id: {ctx.entity_id}")
        
        full_sync = "BankTransactions" in ctx.force_full_sync_endpoints
        last_sync_timestamp_utc_str = None
        transaction_sync_start_date = ctx.message_data.get("transactionSyncStartDate")
        
        # Get last sync timestamp for incremental sync
        sync_ts_field = "_system_lastSyncTimestampUtc_BankTransactions"
        last_sync_timestamp_utc_str = ctx.get_entity_setting(sync_ts_field) if not full_sync else None
        
        # Use transaction_sync_start_date only for FIRST sync (when no previous sync timestamp exists)
        use_date_filter = transaction_sync_start_date and not last_sync_timestamp_utc_str
        where_filter = None
        
        if use_date_filter:
            # Parse the date and format for Xero DateTime constructor (FIRST SYNC ONLY)
            try:
                date_obj = datetime.strptime(transaction_sync_start_date, '%Y-%m-%d').date()
                where_filter = f'Date>=DateTime({date_obj.year},{date_obj.month:02d},{date_obj.day:02d},00,00,00)'
                logger.info(f"First BankTransactions sync - using date filter from {transaction_sync_start_date}")
            except ValueError:
                logger.warning(f"Invalid transactionSyncStartDate format: {transaction_sync_start_date}, skipping date filter")
                use_date_filter = False
        
        logger.info(f"Fetching BankTransactions with filter: {where_filter if where_filter else 'None'}")
        bank_transactions_data = await ctx.xero_client.get_records(
            "BankTransactions", 
            where_filter=where_filter,
            if_modified_since=last_sync_timestamp_utc_str if not use_date_filter else None
        )
        
        saved_bank_transactions_count = 0
        skipped_bank_transactions_count = 0
        
        # Process bank transactions in batches
        batch = ctx.db.batch()
        batch_count = 0
        batch_size = ctx.get_entity_setting("batch_size", 50)
        
        for bank_transaction in bank_transactions_data:
            bank_transaction_id = bank_transaction.get("BankTransactionID")
            if not bank_transaction_id:
                logger.warning(f"BankTransaction data missing BankTransactionID for entity {ctx.entity_id}. Skipping: {bank_transaction}")
                continue
            
            # CHECK IF WE ALREADY HAVE THE CURRENT VERSION - SKIP PROCESSING IF SO
            new_updated_at = bank_transaction.get("UpdatedDateUTC")
            if await _check_if_current_version_exists(ctx.db, bank_transaction_id, new_updated_at, ctx.entity_id, "TRANSACTIONS", "bank_transaction"):
                skipped_bank_transactions_count += 1
                continue  # Skip all processing for this bank transaction - we already have current version
            
            # Create transaction document for bank transaction
            transaction_doc = {
                "transaction_id": bank_transaction_id,
                "entity_id": ctx.entity_id,
                "client_id": ctx.client_id,
                "source_system": "XERO",
                "source_system_id": bank_transaction_id,
                "document_type": "BANK_TRANSACTION",
                "transaction_type": bank_transaction.get("Type", "BANK_TRANSACTION"),
                "bank_transaction_id": bank_transaction_id,
                "date": bank_transaction.get("Date"),
                "reference": bank_transaction.get("Reference"),
                "bank_account": bank_transaction.get("BankAccount", {}),
                "contact": bank_transaction.get("Contact", {}),
                "status": bank_transaction.get("Status"),
                "total_amount": bank_transaction.get("Total", 0),
                "line_items": bank_transaction.get("LineItems", []),
                "is_reconciled": bank_transaction.get("IsReconciled", False),
                "url": bank_transaction.get("Url"),
                "raw_xero_data": bank_transaction,
                "created_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP,
                "source_updated_at_utc": bank_transaction.get("UpdatedDateUTC"),
            }
            
            # Remove None values
            transaction_doc = {k: v for k, v in transaction_doc.items() if v is not None}
            
            # Add to batch
            transaction_ref = ctx.db.collection("TRANSACTIONS").document(bank_transaction_id)
            batch.set(transaction_ref, transaction_doc, merge=True)
            batch_count += 1
            saved_bank_transactions_count += 1
            
            # Commit batch when it reaches size limit
            if batch_count >= batch_size:
                await batch.commit()
                batch = ctx.db.batch()
                batch_count = 0
        
        # Commit remaining documents
        if batch_count > 0:
            await batch.commit()
        
        logger.info(f"BankTransactions sync completed for entity_id: {ctx.entity_id} - Processed: {saved_bank_transactions_count}, Skipped: {skipped_bank_transactions_count}")
        await _update_firestore_sync_timestamp(
            ctx.db, ctx.entity_id, "BankTransactions", datetime.now(timezone.utc).isoformat(), "BankTransactions sync successful"
        )
        await _create_audit_log_entry(
            ctx.db, "SYNC", "BANK_TRANSACTIONS_SYNC_SUCCESS", ctx.client_id, ctx.entity_id, "SUCCESS", 
            {"bank_transactions_processed": saved_bank_transactions_count, "bank_transactions_skipped": skipped_bank_transactions_count, "syncJobId": ctx.sync_job_id}
        )
        
        return saved_bank_transactions_count
        
    except Exception as e:
        logger.error(f"BankTransactions sync failed for entity_id: {ctx.entity_id}: {e}", exc_info=True)
        await _create_audit_log_entry(
            ctx.db, "SYNC", "BANK_TRANSACTIONS_SYNC_FAILURE", ctx.client_id, ctx.entity_id, "FAILURE",
            {"error": str(e), "syncJobId": ctx.sync_job_id}
        )
        raise