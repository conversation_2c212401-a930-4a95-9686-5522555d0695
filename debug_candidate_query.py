#!/usr/bin/env python3
"""
Debug the exact candidate journal query that's failing in Google Cloud
"""
import asyncio
import os
import sys

# Add project root to path
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Set environment
if not os.getenv("GOOGLE_CLOUD_PROJECT"):
    os.environ["GOOGLE_CLOUD_PROJECT"] = "drcr-d660a"

from google.cloud import firestore
from rest_api.config.detector_config import MANUAL_JOURNALS_COLLECTION, JOURNAL_QUERY_LIMIT

async def debug_candidate_query():
    """Run the exact same query that's failing in Google Cloud"""
    
    db = firestore.AsyncClient()
    entity_id = "8ead108d-f6a2-41e4-b2a8-962024248a66"
    
    print(f"=== Debugging Candidate Journal Query ===")
    print(f"Entity ID: {entity_id}")
    print(f"Collection: {MANUAL_JOURNALS_COLLECTION}")
    
    # Step 1: Load asset codes (same as detector)
    print(f"\n=== Loading Asset Codes ===")
    asset_codes = set()
    
    # Check ENTITY_SETTINGS first
    entity_settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
    entity_settings_doc = await entity_settings_ref.get()
    
    if entity_settings_doc.exists:
        settings = entity_settings_doc.to_dict()
        prepayment_asset_codes = settings.get("prepayment_asset_account_codes", [])
        if prepayment_asset_codes:
            asset_codes.update(prepayment_asset_codes)
            print(f"Found asset codes in ENTITY_SETTINGS: {prepayment_asset_codes}")
    
    # Fallback to ENTITIES.settings if needed
    if not asset_codes:
        entity_ref = db.collection("ENTITIES").document(entity_id)
        entity_doc = await entity_ref.get()
        if entity_doc.exists:
            entity_data = entity_doc.to_dict()
            settings = entity_data.get("settings", {})
            prepayment_asset_codes = settings.get("prepayment_asset_account_codes", [])
            if prepayment_asset_codes:
                asset_codes.update(prepayment_asset_codes)
                print(f"Found asset codes in ENTITIES.settings: {prepayment_asset_codes}")
    
    print(f"Final asset codes: {list(asset_codes)}")
    
    # Step 2: Run the exact query from the detector
    print(f"\n=== Running Candidate Query ===")
    journals_query = db.collection(MANUAL_JOURNALS_COLLECTION).where(
        filter=firestore.FieldFilter("entity_id", "==", entity_id)
    ).where(
        filter=firestore.FieldFilter("status", "==", "POSTED")
    ).limit(JOURNAL_QUERY_LIMIT)
    
    processed_count = 0
    candidates_found = 0
    
    async for journal_doc in journals_query.stream():
        journal_data = journal_doc.to_dict()
        processed_count += 1
        
        print(f"\nJournal {journal_doc.id}:")
        print(f"  Status: {journal_data.get('status')}")
        
        # Check for asset credits (same logic as detector)
        lines = journal_data.get("lines", []) or journal_data.get("journal_lines", []) or journal_data.get("JournalLines", [])
        print(f"  Lines found: {len(lines)}")
        
        has_asset_credit = False
        for line in lines:
            account_code = line.get("accountCode") or line.get("AccountCode")
            
            # Handle credit detection - check both formats
            credit_amount = line.get("credit", 0)
            if credit_amount == 0:
                # Xero format: negative LineAmount = credit
                line_amount = float(line.get("LineAmount", 0))
                credit_amount = abs(line_amount) if line_amount < 0 else 0
            
            print(f"    Line: account={account_code}, credit={credit_amount}, in_asset_codes={account_code in asset_codes}")
            
            if credit_amount > 0 and account_code in asset_codes:
                has_asset_credit = True
                print(f"    ✅ ASSET CREDIT FOUND!")
                break
        
        if has_asset_credit:
            candidates_found += 1
            print(f"  ✅ CANDIDATE #{candidates_found}")
        else:
            print(f"  ❌ Not a candidate")
    
    print(f"\n=== Results ===")
    print(f"Processed journals: {processed_count}")
    print(f"Candidates found: {candidates_found}")
    print(f"Asset codes: {list(asset_codes)}")

if __name__ == "__main__":
    asyncio.run(debug_candidate_query())