# MVP Firestore Schema - Core Collections in Active Use
# Version: 1.0
# Last Updated: 2025-01-07

collections:
  TRANSACTIONS:
    description: "Bill and invoice transaction records from Xero"
    fields:
      entity_id: {type: string, required: true, description: "Entity identifier"}
      client_id: {type: string, required: true, description: "Client identifier"}
      document_number: {type: string, description: "Bill/invoice number"}
      total_amount: {type: number, description: "Total amount including tax"}
      date_issued: {type: datetime, description: "Date when document was issued"}
      line_items: {type: array, description: "Array of line item objects"}
      processing_status: {type: string, description: "completed | failed | skipped | metadata_loaded"}
      document_type: {type: string, description: "BILL | INVOICE"}
      transaction_type: {type: string, description: "ACCPAY | ACCREC"}
      
  MANUAL_JOURNALS:
    description: "Manual journal entries from Xero"
    fields:
      entity_id: {type: string, required: true, description: "Entity identifier"}
      client_id: {type: string, required: true, description: "Client identifier"}
      journal_date: {type: datetime, description: "Date of journal entry"}
      lines: {type: array, description: "Array of journal line objects"}
      narration: {type: string, description: "Journal description/narrative"}
      status: {type: string, description: "POSTED | DRAFT"}
      # lines[].account_code: {type: string, description: "Chart of accounts code"}
      # lines[].line_amount: {type: number, description: "Line amount (positive=debit, negative=credit)"}
      
  ENTITIES:
    description: "Entity configuration and settings"
    fields:
      entity_id: {type: string, required: true, description: "Unique entity identifier"}
      client_id: {type: string, required: true, description: "Parent client identifier"}
      prepayment_asset_account_codes: {type: array, description: "List of prepayment asset account codes"}
      excluded_pnl_account_codes: {type: array, description: "Account codes to exclude from processing"}
      enable_llm_prepayment_detection: {type: boolean, description: "Enable AI-powered prepayment detection"}
      enable_prepayment_release_detector: {type: boolean, description: "Enable manual journal detection"}
      scanning_amount_threshold: {type: number, description: "Minimum amount for processing"}
      
  PREPAYMENT_RELEASES_DETECTED:
    description: "Detected manual journal releases of prepayments"
    fields:
      entity_id: {type: string, required: true, description: "Entity identifier"}
      client_id: {type: string, required: true, description: "Client identifier"}
      journal_id: {type: string, required: true, description: "Manual journal ID"}
      detection_confidence: {type: string, description: "high | medium | low"}
      linked_transaction_id: {type: string, description: "ID of linked originating bill"}
      synthetic_schedule_id: {type: string, description: "ID of created synthetic schedule"}
      asset_account_code: {type: string, description: "Prepayment asset account"}
      expense_account_code: {type: string, description: "Expense account"}
      amount: {type: number, description: "Release amount"}
      journal_date: {type: datetime, description: "Date of journal entry"}
      
  AMORTIZATION_SCHEDULES:
    description: "Prepayment amortization schedules"
    fields:
      schedule_id: {type: string, required: true, description: "Unique schedule identifier"}
      transaction_id: {type: string, required: true, description: "Source bill/transaction ID"}
      entity_id: {type: string, required: true, description: "Entity identifier"}
      client_id: {type: string, required: true, description: "Client identifier"}
      status: {type: string, description: "draft | posted | cancelled"}
      detection_method: {type: string, description: "gl_coding | llm_detected | external_mj"}
      original_amount: {type: number, description: "Original prepayment amount"}
      amortization_start_date: {type: datetime, description: "Start date for amortization"}
      amortization_end_date: {type: datetime, description: "End date for amortization"}
      number_of_periods: {type: number, description: "Total number of periods"}
      amortization_account_code: {type: string, description: "Asset account code"}
      expense_account_code: {type: string, description: "Expense account code"}

# Field Aliases (mirrors FIELD_ALIASES in rest_api/utils/field_access.py)
aliases:
  # Account codes - most common source of confusion
  AccountCode: account_code
  accountCode: account_code
  
  # Date fields - multiple variations from Xero
  journalDate: journal_date
  Date: date_issued        # Xero's main date field maps to date_issued for transactions
  DateString: date_issued  # Alternative Xero date format
  DueDate: date_due        # Xero due date field
  DueDateString: date_due  # Alternative Xero due date format
  UpdatedDateUTC: updated_date_utc
  
  # Line item arrays - different naming conventions
  JournalLines: lines
  journal_lines: lines
  LineItems: line_items
  
  # Amount fields - various formats
  LineAmount: line_amount
  Total: total_amount
  
  # Other common variations
  Narration: narration

# Data Type Notes:
# - Datetime fields: Store as Firestore Timestamp objects, not ISO strings
# - Account codes: Always strings, may be numeric or alphanumeric
# - Amounts: Store as numbers (float), not strings
# - Arrays: Empty arrays [] when no data, never null
# - Booleans: true/false, not strings or numbers

# Field Access Guidelines:
# 1. Always use canonical field names in new code
# 2. Use get_field() helper for reading potentially aliased data
# 3. Use get_account_code() and get_journal_lines() convenience helpers
# 4. Handle null/undefined values gracefully with defaults
# 5. Validate data types when reading from external sources (Xero API)

# Migration Strategy:
# - No migration needed for MVP (clean start)
# - Dual-write canonical + legacy fields when adding new collections
# - Use telemetry to track alias usage before deprecation
# - Batch update historical data when ready to remove aliases