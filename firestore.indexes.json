{"indexes": [{"collectionGroup": "MANUAL_JOURNALS", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entity_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "journalDate", "order": "DESCENDING"}]}, {"collectionGroup": "MANUAL_JOURNALS", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entity_id", "order": "ASCENDING"}, {"fieldPath": "journalDate", "order": "ASCENDING"}]}, {"collectionGroup": "AMORTIZATION_SCHEDULES", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entity_id", "order": "ASCENDING"}, {"fieldPath": "_system_linkedManualJournalID", "order": "ASCENDING"}]}, {"collectionGroup": "ACCOUNTS", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entity_id", "order": "ASCENDING"}, {"fieldPath": "accountType", "order": "ASCENDING"}]}, {"collectionGroup": "TRANSACTIONS", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entityId", "order": "ASCENDING"}, {"fieldPath": "dateUtc", "order": "DESCENDING"}]}, {"collectionGroup": "AMORTIZATION_SCHEDULES", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entityId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "PROPOSED_JOURNALS", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entityId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "AUDIT_LOG", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entityId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}], "fieldOverrides": []}