# Changelog

## [2025-06-10] - Incremental Sync Optimization

### Fixed
- **Scheduled Sync Efficiency**: Fixed Xero sync to use incremental sync after first sync instead of always using date filter
- **Transaction Sync Logic**: Corrected behavior where `transaction_sync_start_date` was applied to every sync, causing unnecessary full data pulls

### Performance Improvements
- **First Sync**: Uses date filter from `transaction_sync_start_date` to get historical data (e.g., all bills from 2025-02-01)
- **Subsequent Syncs**: Uses `If-Modified-Since` header for efficient incremental sync (only changed records)
- **API Efficiency**: Reduces Xero API calls and data transfer for scheduled syncs

### Technical Changes
- **Bills Sync**: Modified `cloud_functions/xero_sync_consumer/main.py` lines 600-631
- **Invoices Sync**: Modified `cloud_functions/xero_sync_consumer/main.py` lines 463-495
- **Logic Update**: `use_date_filter = transaction_sync_start_date and not last_sync_timestamp_utc_str`

### Documentation
- **Updated**: `docs/SCHEDULED_SYNC_SYSTEM.md` with new "Incremental Sync and Date Filtering" section
- **Added**: Decision matrix and implementation details for sync behavior

### Impact
- **Before**: Every sync pulled ALL bills from Feb 1st, 2025 (inefficient)
- **After**: First sync gets historical data, subsequent syncs only get changes (efficient)

## [2025-01-XX] - KSP Prepayment Processing & Schedule Display Fixes

### Fixed
- **Amortization Schedule Display**: Fixed frontend showing uniform amounts instead of actual Firebase schedule data
- **Action Buttons**: Fixed missing action buttons for `pending_configuration` schedules in prepayments dashboard
- **Entity Settings**: Fixed "Amortization Materiality Threshold" not loading actual values from Firebase
- **Monthly Breakdown**: Fixed frontend ignoring actual monthly entries from Firebase and generating incorrect preview data
- **Date Parsing**: Enhanced date parsing to handle Firebase Timestamp objects properly

### Backend Changes
- **Schedule Model**: Added `monthly_entries` field to include monthly breakdown data in API responses
- **Entity Settings API**: Added missing `amortization_materiality_threshold` and `default_expense_account_code` fields to entity settings response
- **Schedule Transformer**: Enhanced `firestore_to_schedule_model` to include `monthlyEntries` from Firebase documents
- **Attachment Support**: Added attachment fields to Invoice model and transformer for proper attachment handling

### Frontend Changes
- **Action Button Logic**: Added `PENDING_CONFIGURATION` status to action button conditions at invoice and line-item levels
- **Entity Settings Loading**: Enhanced `fetchSettings` to properly map all entity settings fields from backend
- **Monthly Entries Processing**: Improved `transformMonthlyEntries` to handle Firebase Timestamps and multiple date formats
- **Schedule Data Priority**: Modified frontend to prioritize actual backend schedule data over generated previews
- **Date Format Handling**: Added robust date parsing for ISO strings, Date objects, and Firebase Timestamp objects

### Technical Details
- **Issue**: KSP Rechtsanwalt bill (€79.5, 365-day period) was showing "0 prepayments processed" despite correct LLM detection
- **Root Cause**: Multiple issues including low LLM confidence, missing decision logic, account code mismatches, and frontend data transformation problems
- **Solution**: Comprehensive fixes across LLM processing, backend API responses, and frontend data handling

### Files Modified
#### Backend
- `rest_api/models/schedule.py` - Added monthly_entries field
- `rest_api/utils/transformers.py` - Enhanced schedule transformer
- `rest_api/routes/entities.py` - Added missing entity settings fields
- `rest_api/models/invoice.py` - Added attachment fields

#### Frontend
- `DRCR Frontend/src/pages/PrepaymentsPage.tsx` - Fixed action button conditions
- `DRCR Frontend/src/pages/EntityManagement.tsx` - Enhanced entity settings mapping
- `DRCR Frontend/src/services/prepayments.service.ts` - Improved schedule data processing
- `DRCR Frontend/src/types/schedule.types.ts` - Updated status mappings

### Testing
- Verified KSP bill now shows correct action buttons
- Confirmed actual Firebase schedule amounts are displayed (€5.23, €6.53, €6.75, etc. instead of uniform €6.63)
- Tested entity settings properly load and save amortization materiality threshold
- Validated attachment viewing functionality works correctly 