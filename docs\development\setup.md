# Prerequisites & Setup

This guide details the necessary software, tools, and steps to set up your local development environment for the DRCR backend.

## 1. Software Prerequisites

Ensure you have the following software installed on your system. It's generally recommended to use the latest stable versions unless a specific version is noted.

*   **Python**:
    *   Version: 3.9+ (Please confirm the exact version used in `runtime.txt` or project settings if available. If not, 3.9+ is a safe bet for modern FastAPI applications).
    *   Verify with: `python --version` or `python3 --version`
*   **Pip (Python Package Installer)**:
    *   Usually comes with Python.
    *   Verify with: `pip --version` or `pip3 --version`
*   **Git**:
    *   For version control.
    *   Verify with: `git --version`
*   **Docker**:
    *   Required if any part of the development workflow or dependencies (like Context7 as mentioned in `AGENT.md`) relies on Docker containers. Also useful for running emulators or databases consistently.
    *   Verify with: `docker --version`
*   **Google Cloud SDK (gcloud CLI)**:
    *   Essential for interacting with Google Cloud Platform services, including Firebase (especially if not using Firebase CLI for all Firebase interactions) and deploying to Cloud Run.
    *   Installation: [Google Cloud SDK Documentation](https://cloud.google.com/sdk/docs/install)
    *   Verify with: `gcloud --version`
    *   After installation, authenticate: `gcloud auth login`
    *   Configure your project: `gcloud config set project <YOUR_GCP_PROJECT_ID>`
*   **Firebase CLI**:
    *   Useful for managing Firebase projects, deploying Cloud Functions, and running the Firebase Emulator Suite.
    *   Installation: `npm install -g firebase-tools` (requires Node.js and npm) or other methods from [Firebase CLI documentation](https://firebase.google.com/docs/cli#install_the_firebase_cli).
    *   Verify with: `firebase --version`
    *   Login: `firebase login`

## 2. Clone the Repository

Clone the DRCR backend repository to your local machine:

```bash
git clone <repository_url>
cd <repository_directory_name>
```
Replace `<repository_url>` with the actual URL of your Git repository and `<repository_directory_name>` with the name of the directory created by Git.

## 3. Set Up a Python Virtual Environment

It's highly recommended to use a virtual environment to manage project dependencies and avoid conflicts with other Python projects.

*   **Navigate to the project root directory.**
*   **Create a virtual environment (e.g., named `.venv`)**:
    ```bash
    python3 -m venv .venv
    ```
*   **Activate the virtual environment**:
    *   On macOS and Linux:
        ```bash
        source .venv/bin/activate
        ```
    *   On Windows (Git Bash or similar):
        ```bash
        source .venv/Scripts/activate
        ```
    *   On Windows (Command Prompt/PowerShell):
        ```bash
        .venv\Scripts\activate.bat
        ```
    Your command prompt should now indicate that the virtual environment is active.

## 4. Install Dependencies

Once your virtual environment is active, install the required Python packages listed in `requirements.txt`:

```bash
pip install -r requirements.txt
```
(If your project uses a different dependency file, like `poetry.lock` and `pyproject.toml` for Poetry, adjust this command accordingly.)

## 5. Configure Environment Variables

The application requires certain environment variables to be set for configuration, API keys, and service account paths.

*   **Create a `.env` file**:
    *   In the root of the project (or as specified by your application's configuration loading mechanism, often alongside `main.py` or in a `config` directory), create a `.env` file.
    *   **This file should be added to your `.gitignore` file to prevent committing sensitive credentials.**
*   **Required Variables (Example - actual variables may differ)**:
    A `.env.example` file might be present in the repository. If so, copy it to `.env` and fill in the values. Common variables include:
    ```env
    # Firebase/Google Cloud
    GOOGLE_APPLICATION_CREDENTIALS="path/to/your/serviceAccountKey.json"
    FIREBASE_PROJECT_ID="your-firebase-project-id"

    # Xero API Keys (for development/testing if applicable)
    XERO_CLIENT_ID="your_xero_client_id"
    XERO_CLIENT_SECRET="your_xero_client_secret"
    XERO_REDIRECT_URI="your_xero_dev_redirect_uri"  # e.g., http://localhost:8081/xero/callback

    # OAuth Token Storage (Firestore) - Cost-effective alternative to Secret Manager
    TOKEN_ENCRYPTION_KEY="your_fernet_encryption_key_here"  # Generate with: from cryptography.fernet import Fernet; Fernet.generate_key().decode()

    # Database (if not using emulators exclusively for local dev)
    # DB_HOST="localhost"
    # DB_PORT="5432"
    # DB_USER="your_db_user"
    # DB_PASSWORD="your_db_password"
    # DB_NAME="drcr_dev"

    # API Settings
    # ALLOWED_ORIGINS="http://localhost:3000,http://localhost:8080" # Example for CORS
    ```
    Consult the application's configuration files (e.g., in `drcr_shared_logic/config.py` or similar) to identify all required environment variables.

## 6. Obtain Necessary Credentials/Files

*   **Firebase Service Account Key (`serviceAccountKey.json`)**:
    *   This JSON file allows server-side applications to authenticate with Firebase and Google Cloud services.
    *   Generate it from your Firebase project settings:
        1.  Go to Firebase Console -> Project Settings -> Service accounts.
        2.  Select "Python" under "Admin SDK configuration snippet".
        3.  Click "Generate new private key" and save the JSON file.
    *   Place this file in a secure location accessible to your application and set the `GOOGLE_APPLICATION_CREDENTIALS` environment variable to its path (as shown in the `.env` example). **Do NOT commit this file to Git.**
*   **Xero API Credentials**:
    *   If you need to interact with the Xero API directly for development or testing, you'll need to create a Xero app in the [Xero Developer Portal](https://developer.xero.com/myapps).
    *   Obtain the Client ID, Client Secret, and set up a Redirect URI. These will be used in your `.env` file.

---

Once these steps are completed, you should have a functional local development environment. Refer to the [Running Locally](./running_locally.md) guide for instructions on starting the application and its components. 