"""
Endpoint Dispatcher

Handles routing of sync requests to appropriate endpoint handlers.
Replaces the monolithic approach in main.py with clean modular dispatch.
"""

import logging
from typing import Dict, Any, List, Callable, Awaitable

from context import SyncContext
from endpoints.bills import sync_bills
from endpoints.invoices import sync_invoices
from endpoints.contacts import sync_contacts
from endpoints.accounts import sync_accounts
from endpoints.manual_journals import sync_manual_journals
from endpoints.spend_money import sync_spend_money
from endpoints.payments import sync_payments
from endpoints.bank_transactions import sync_bank_transactions

logger = logging.getLogger(__name__)

# Registry of available endpoint handlers
ENDPOINT_HANDLERS: Dict[str, Callable[[SyncContext, List[str]], Awaitable[int]]] = {
    "Bills": sync_bills,
    "Invoices": sync_invoices,
    "Contacts": sync_contacts,
    "Accounts": sync_accounts,
    "ManualJournals": sync_manual_journals,
    "SpendMoney": sync_spend_money,
    "Payments": sync_payments,
    "BankTransactions": sync_bank_transactions,
}


async def dispatch_sync_endpoints(
    ctx: SyncContext,
    requested_endpoints: List[str]
) -> Dict[str, Any]:
    """
    Dispatch sync requests to appropriate endpoint handlers.
    
    Args:
        ctx: Sync context containing all dependencies
        requested_endpoints: List of endpoints to sync (empty = all)
        
    Returns:
        Dictionary with sync results for each endpoint
    """
    results = {}
    
    # If no specific endpoints requested, sync all available endpoints
    if not requested_endpoints:
        endpoints_to_sync = list(ENDPOINT_HANDLERS.keys())
    else:
        endpoints_to_sync = [ep for ep in requested_endpoints if ep in ENDPOINT_HANDLERS]
    
    logger.info(f"Dispatching sync for endpoints: {endpoints_to_sync}")
    
    for endpoint_name in endpoints_to_sync:
        try:
            logger.info(f"Starting {endpoint_name} sync")
            handler = ENDPOINT_HANDLERS[endpoint_name]
            count = await handler(ctx, requested_endpoints)
            results[endpoint_name] = {
                "status": "success",
                "count": count,
                "message": f"Processed {count} records"
            }
            logger.info(f"{endpoint_name} sync completed: {count} records")
            
        except Exception as e:
            logger.error(f"{endpoint_name} sync failed: {e}", exc_info=True)
            results[endpoint_name] = {
                "status": "error",
                "count": 0,
                "message": f"Failed: {str(e)}"
            }
    
    # Handle legacy endpoints not yet extracted (temporary fallback)
    legacy_endpoints = [ep for ep in requested_endpoints if ep not in ENDPOINT_HANDLERS and requested_endpoints]
    if legacy_endpoints:
        logger.warning(f"Legacy endpoints not yet modularized: {legacy_endpoints}")
        results["_legacy_endpoints"] = {
            "status": "pending_extraction",
            "endpoints": legacy_endpoints,
            "message": "These endpoints are still in main.py and will be processed there"
        }
    
    return results


def get_available_endpoints() -> List[str]:
    """Get list of available modular endpoints."""
    return list(ENDPOINT_HANDLERS.keys())


def is_endpoint_modularized(endpoint_name: str) -> bool:
    """Check if an endpoint has been extracted to the modular system."""
    return endpoint_name in ENDPOINT_HANDLERS