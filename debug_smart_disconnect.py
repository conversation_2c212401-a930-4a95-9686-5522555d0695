#!/usr/bin/env python3
"""
Debug script to check entity xero_user_id population and test smart disconnect logic
"""

import asyncio
import logging
import os
import sys
from typing import Dict, List, Optional

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def check_entities():
    """Check entities and their xero_user_id status"""
    try:
        from google.cloud import firestore
        from drcr_shared_logic.clients.xero_client import XeroApiClient
        
        db = firestore.Client()
        
        # Get all Xero entities
        entities_ref = db.collection("ENTITIES")
        entities = []
        
        async for doc in entities_ref.stream():
            entity_data = doc.to_dict()
            entity_data["entity_id"] = doc.id
            
            # Check if it's a Xero entity
            if entity_data.get("type") == "xero":
                entities.append(entity_data)
        
        logger.info(f"Found {len(entities)} Xero entities")
        
        for entity in entities:
            entity_id = entity["entity_id"]
            entity_name = entity.get("entity_name", "Unknown")
            status = entity.get("status", "unknown")
            connection_details = entity.get("connection_details", {})
            xero_user_id = connection_details.get("xero_user_id")
            xero_tenant_id = connection_details.get("xero_tenant_id")
            client_id = entity.get("client_id")
            
            logger.info(f"Entity: {entity_name}")
            logger.info(f"  ID: {entity_id}")
            logger.info(f"  Status: {status}")
            logger.info(f"  Client ID: {client_id}")
            logger.info(f"  Xero Tenant ID: {xero_tenant_id}")
            logger.info(f"  Has xero_user_id: {bool(xero_user_id)}")
            if xero_user_id:
                logger.info(f"  Xero User ID: {xero_user_id}")
            
            # If missing xero_user_id, try to get it
            if not xero_user_id and xero_tenant_id and client_id:
                logger.info(f"  Attempting to retrieve missing xero_user_id...")
                try:
                    xero_client = await XeroApiClient.create(
                        platform_org_id=xero_tenant_id,
                        tenant_id=client_id
                    )
                    
                    retrieved_user_id = await xero_client.get_xero_user_id()
                    if retrieved_user_id:
                        logger.info(f"  Retrieved xero_user_id: {retrieved_user_id}")
                        
                        # Update the entity
                        entity_ref = db.collection("ENTITIES").document(entity_id)
                        await entity_ref.update({
                            "connection_details.xero_user_id": retrieved_user_id
                        })
                        logger.info(f"  Updated entity with xero_user_id")
                    else:
                        logger.warning(f"  Could not retrieve xero_user_id")
                        
                except Exception as e:
                    logger.error(f"  Error retrieving xero_user_id: {e}")
            
            logger.info("")  # Empty line for readability
            
    except Exception as e:
        logger.error(f"Error: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(check_entities())