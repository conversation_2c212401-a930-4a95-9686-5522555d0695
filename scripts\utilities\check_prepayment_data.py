#!/usr/bin/env python3
"""
Script to check prepayment-related data in Firestore for testing.
Focuses on transactions, amortization schedules, and prepayment identification.
"""

import os
import sys
from datetime import datetime
from dotenv import load_dotenv
load_dotenv()

# Add project root to path
project_root = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from google.cloud import firestore

# Configuration - using existing entity from check_sync_data.py
ENTITY_ID = '9101f43d-170a-4564-a043-d029f010e55c'  # Xero tenant ID
CLIENT_ID = '49687fc2-556d-4116-b441-505771881a01'  # Our client ID

def main():
    # Initialize Firestore client
    project_id = os.getenv('GCP_PROJECT_ID')
    print(f'🔧 Using project ID: {project_id}')
    
    db = firestore.Client(project=project_id)
    
    print(f'\n🎯 DRCR Prepayment Testing - Data Check')
    print(f'Entity ID: {ENTITY_ID}')
    print(f'Client ID: {CLIENT_ID}')
    print('=' * 60)
    
    # Check ENTITY_SETTINGS first
    print('\n📋 ENTITY SETTINGS')
    print('-' * 30)
    try:
        settings_ref = db.collection('ENTITY_SETTINGS').document(ENTITY_ID)
        settings_doc = settings_ref.get()
        
        if settings_doc.exists:
            settings_data = settings_doc.to_dict()
            print('✅ Entity settings found:')
            print(f'  Client ID: {settings_data.get("clientId", "N/A")}')
            print(f'  Prepayment Asset Accounts: {settings_data.get("prepaymentAssetAccountCodes", [])}')
            print(f'  Excluded P&L Accounts: {settings_data.get("excludedPnlAccountCodes", [])}')
            print(f'  Default Amortization Months: {settings_data.get("defaultAmortizationMonths", "N/A")}')
            print(f'  Last Invoice Sync: {settings_data.get("_system_lastSyncTimestampUtc_Invoices", "Never")}')
            
            # Check if prepayment asset accounts are configured
            prepayment_accounts = settings_data.get("prepaymentAssetAccountCodes", [])
            if not prepayment_accounts:
                print('  ⚠️  WARNING: No prepayment asset accounts configured!')
                print('     This means GL-based prepayment detection won\'t work.')
        else:
            print('  ❌ No entity settings found!')
            
    except Exception as e:
        print(f'  ❌ Error checking entity settings: {e}')
    
    # Check TRANSACTIONS collection for prepayments
    print('\n💰 TRANSACTIONS (Prepayment Focus)')
    print('-' * 40)
    try:
        transactions_query = (
            db.collection('TRANSACTIONS')
            .where(filter=firestore.FieldFilter("entityId", "==", ENTITY_ID))
            .order_by('dateUtc', direction=firestore.Query.DESCENDING)
            .limit(20)
        )
        
        transactions = list(transactions_query.stream())
        print(f'Found {len(transactions)} recent transactions for this entity')
        
        prepayment_by_llm = 0
        prepayment_by_gl = 0
        with_schedules = 0
        
        if transactions:
            print('\nTransaction Details:')
            for i, doc in enumerate(transactions):
                data = doc.to_dict()
                
                # Check prepayment flags
                is_prepayment_llm = data.get('_system_isPrepaymentByLLM', False)
                is_prepayment_gl = data.get('_system_isPrepaymentByGLCoding', False)
                schedule_ids = data.get('_system_amortizationScheduleIDs', [])
                
                if is_prepayment_llm:
                    prepayment_by_llm += 1
                if is_prepayment_gl:
                    prepayment_by_gl += 1
                if schedule_ids:
                    with_schedules += 1
                
                # Display transaction info
                status_icon = "💰" if (is_prepayment_llm or is_prepayment_gl) else "📄"
                print(f'  {status_icon} {i+1}. {data.get("reference", "N/A")} - ${data.get("total", 0)}')
                print(f'     Contact: {data.get("contact", {}).get("name", "N/A")}')
                print(f'     Date: {data.get("dateUtc", "N/A")}')
                
                if is_prepayment_llm or is_prepayment_gl:
                    print(f'     🎯 PREPAYMENT DETECTED:')
                    if is_prepayment_llm:
                        print(f'        • LLM Detection: {data.get("_system_prepaymentReasonByLLM", "N/A")}')
                        print(f'        • Service Period: {data.get("ExpectedServiceStartDate", "N/A")} to {data.get("ExpectedServiceEndDate", "N/A")}')
                    if is_prepayment_gl:
                        print(f'        • GL Coding Detection: {data.get("_system_prepaymentReasonByGLCoding", "N/A")}')
                        gl_lines = data.get("_system_linesCodedToPrepaymentAsset", [])
                        if gl_lines:
                            print(f'        • Prepayment Lines: {len(gl_lines)} line(s)')
                    
                    if schedule_ids:
                        print(f'        • Amortization Schedules: {len(schedule_ids)} schedule(s)')
                        for schedule_id in schedule_ids:
                            print(f'          - {schedule_id}')
                
                # Show LLM extraction data if available
                if data.get('_system_llm_extracted_raw_json_initial'):
                    print(f'     🤖 LLM Processing: Initial extraction completed')
                    if data.get('_system_llm_fallback_used'):
                        print(f'        • Fallback processing used')
                
                print()  # Empty line for readability
            
            # Summary
            print(f'\n📊 PREPAYMENT SUMMARY:')
            print(f'   Total Transactions: {len(transactions)}')
            print(f'   Prepayments by LLM: {prepayment_by_llm}')
            print(f'   Prepayments by GL Coding: {prepayment_by_gl}')
            print(f'   With Amortization Schedules: {with_schedules}')
            
        else:
            print('  ❌ No transactions found!')
            
    except Exception as e:
        print(f'  ❌ Error checking transactions: {e}')
    
    # Check AMORTIZATION_SCHEDULES collection
    print('\n📅 AMORTIZATION SCHEDULES')
    print('-' * 30)
    try:
        schedules_query = (
            db.collection('AMORTIZATION_SCHEDULES')
            .where(filter=firestore.FieldFilter("entityId", "==", ENTITY_ID))
            .order_by('createdAt', direction=firestore.Query.DESCENDING)
            .limit(10)
        )
        
        schedules = list(schedules_query.stream())
        print(f'Found {len(schedules)} amortization schedules for this entity')
        
        if schedules:
            total_original_amount = 0
            total_monthly_entries = 0
            
            for i, doc in enumerate(schedules):
                data = doc.to_dict()
                monthly_entries = data.get('monthlyEntries', [])
                original_amount = data.get('originalAmount', 0)
                
                total_original_amount += original_amount
                total_monthly_entries += len(monthly_entries)
                
                print(f'  📅 {i+1}. Schedule ID: {doc.id}')
                print(f'     Transaction: {data.get("transactionId", "N/A")}')
                print(f'     Original Amount: ${original_amount}')
                print(f'     Period: {data.get("amortizationStartDate")} to {data.get("amortizationEndDate")}')
                print(f'     Monthly Entries: {len(monthly_entries)}')
                print(f'     Status: {data.get("status", "N/A")}')
                
                # Show first few monthly entries
                if monthly_entries:
                    print(f'     First few entries:')
                    for j, entry in enumerate(monthly_entries[:3]):
                        month_date = entry.get('monthDate')
                        if hasattr(month_date, 'strftime'):
                            month_str = month_date.strftime('%Y-%m')
                        else:
                            month_str = str(month_date)
                        print(f'       {j+1}. {month_str} - ${entry.get("amount", 0)} - {entry.get("status", "N/A")}')
                    if len(monthly_entries) > 3:
                        print(f'       ... and {len(monthly_entries) - 3} more entries')
                print()
            
            print(f'📊 SCHEDULE SUMMARY:')
            print(f'   Total Schedules: {len(schedules)}')
            print(f'   Total Original Amount: ${total_original_amount:,.2f}')
            print(f'   Total Monthly Entries: {total_monthly_entries}')
            
        else:
            print('  ❌ No amortization schedules found!')
            
    except Exception as e:
        print(f'  ❌ Error checking amortization schedules: {e}')
    
    # Check PROPOSED_JOURNALS collection
    print('\n📝 PROPOSED JOURNALS')
    print('-' * 25)
    try:
        journals_query = (
            db.collection('PROPOSED_JOURNALS')
            .where(filter=firestore.FieldFilter("entityId", "==", ENTITY_ID))
            .order_by('createdAt', direction=firestore.Query.DESCENDING)
            .limit(5)
        )
        
        journals = list(journals_query.stream())
        print(f'Found {len(journals)} proposed journals for this entity')
        
        if journals:
            for i, doc in enumerate(journals):
                data = doc.to_dict()
                print(f'  📝 {i+1}. Journal ID: {doc.id}')
                print(f'     Schedule: {data.get("amortizationScheduleId", "N/A")}')
                print(f'     Amount: ${data.get("amount", 0)}')
                print(f'     Month: {data.get("monthDate", "N/A")}')
                print(f'     Status: {data.get("status", "N/A")}')
                if data.get('xeroManualJournalID'):
                    print(f'     Xero Journal ID: {data.get("xeroManualJournalID")}')
                print()
        else:
            print('  ℹ️  No proposed journals found (this is normal if schedules haven\'t been processed for journal generation)')
            
    except Exception as e:
        print(f'  ❌ Error checking proposed journals: {e}')
    
    # Check recent AUDIT_LOG entries
    print('\n📋 RECENT AUDIT LOGS')
    print('-' * 25)
    try:
        audit_query = (
            db.collection('AUDIT_LOG')
            .where(filter=firestore.FieldFilter("entityId", "==", ENTITY_ID))
            .order_by('timestamp', direction=firestore.Query.DESCENDING)
            .limit(10)
        )
        
        audit_logs = list(audit_query.stream())
        print(f'Found {len(audit_logs)} recent audit logs for this entity')
        
        for doc in audit_logs:
            data = doc.to_dict()
            timestamp = data.get('timestamp')
            if hasattr(timestamp, 'strftime'):
                time_str = timestamp.strftime('%Y-%m-%d %H:%M:%S')
            else:
                time_str = str(timestamp)
            
            print(f'  📋 {time_str} - {data.get("eventType", "N/A")}: {data.get("status", "N/A")}')
            if 'details' in data:
                details = data['details']
                if 'count' in details:
                    print(f'     Count: {details["count"]}')
                if 'endpoint' in details:
                    print(f'     Endpoint: {details["endpoint"]}')
                    
    except Exception as e:
        print(f'  ❌ Error checking audit logs: {e}')
    
    print('\n🏁 Prepayment data check complete!')
    print('\n💡 Next steps for testing:')
    print('   1. If no prepayments found, try syncing more invoices')
    print('   2. Check entity settings for prepayment asset account codes')
    print('   3. Look for invoices with attachments for LLM processing')
    print('   4. Test journal generation if schedules exist')

if __name__ == "__main__":
    main() 