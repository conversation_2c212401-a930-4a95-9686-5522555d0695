from pydantic import BaseModel, Field, validator
from typing import List, Optional
from datetime import date
from decimal import Decimal
from enum import Enum

class CalculationMethod(str, Enum):
    """Enumeration for amortization calculation methods."""
    AUTO = "auto"  # Use materiality threshold to decide
    DAY_BASED = "day_based"  # Force day-based proportional calculation
    EQUAL_MONTHLY = "equal_monthly"  # Force equal monthly distribution

class SchedulePreviewRequest(BaseModel):
    """Request model for schedule preview calculation."""
    amount: float = Field(..., gt=0, description="Total amount to amortize (must be positive)")
    start_date: date = Field(..., description="Service period start date")
    end_date: date = Field(..., description="Service period end date")
    entity_id: str = Field(..., description="Entity ID for getting materiality threshold")
    calculation_method: CalculationMethod = Field(CalculationMethod.AUTO, description="Calculation method to use")
    
    @validator('end_date')
    def end_date_must_be_after_start_date(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('end_date must be after start_date')
        return v
    
    @validator('amount')
    def amount_must_be_reasonable(cls, v):
        if v > 10_000_000:  # 10 million limit
            raise ValueError('amount exceeds maximum allowed value')
        return v

class MonthlyEntry(BaseModel):
    """Model for a single monthly amortization entry."""
    month_date: str = Field(..., description="Month date in ISO format (YYYY-MM-DD)")
    amount: float = Field(..., description="Amount for this month")
    days_in_month: Optional[int] = Field(None, description="Days used in this month (for day-based calculation)")
    proportion: float = Field(..., description="Proportion of total amount for this month")

class SchedulePreviewResponse(BaseModel):
    """Response model for schedule preview calculation."""
    calculation_method: str = Field(..., description="Method used: 'day_based' or 'equal_monthly'")
    calculation_method_requested: str = Field(..., description="Method that was requested ('auto', 'day_based', or 'equal_monthly')")
    materiality_threshold: float = Field(..., description="Materiality threshold used for method selection")
    method_override_applied: bool = Field(..., description="Whether user override was applied instead of auto threshold")
    total_amount: float = Field(..., description="Original total amount")
    total_calculated: float = Field(..., description="Sum of all monthly amounts (should match total_amount)")
    total_months: int = Field(..., description="Number of months in the schedule")
    start_date: str = Field(..., description="Service period start date")
    end_date: str = Field(..., description="Service period end date")
    monthly_entries: List[MonthlyEntry] = Field(..., description="List of monthly amortization entries")
    
    class Config:
        json_schema_extra = {
            "example": {
                "calculation_method": "day_based",
                "materiality_threshold": 1000.0,
                "total_amount": 2500.0,
                "total_calculated": 2500.0,
                "total_months": 11,
                "start_date": "2024-06-15",
                "end_date": "2025-05-31",
                "monthly_entries": [
                    {
                        "month_date": "2024-06-30",
                        "amount": 114.29,
                        "days_in_month": 16,
                        "proportion": 0.0457
                    },
                    {
                        "month_date": "2024-07-31",
                        "amount": 221.43,
                        "days_in_month": 31,
                        "proportion": 0.0886
                    }
                ]
            }
        }

class ScheduleModificationPreviewRequest(BaseModel):
    """Request model for previewing changes to an existing schedule."""
    amount: float = Field(..., gt=0, description="New total amount to amortize")
    start_date: date = Field(..., description="New service period start date")
    end_date: date = Field(..., description="New service period end date")
    calculation_method: CalculationMethod = Field(CalculationMethod.AUTO, description="Calculation method to use")
    
    @validator('end_date')
    def end_date_must_be_after_start_date(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('end_date must be after start_date')
        return v
    
    @validator('amount')
    def amount_must_be_reasonable(cls, v):
        if v > 10_000_000:  # 10 million limit
            raise ValueError('amount exceeds maximum allowed value')
        return v

class NewSchedulePreviewRequest(BaseModel):
    """Request model for previewing a new schedule creation."""
    amount: float = Field(..., gt=0, description="Total amount to amortize")
    start_date: date = Field(..., description="Service period start date")
    end_date: date = Field(..., description="Service period end date")
    calculation_method: CalculationMethod = Field(CalculationMethod.AUTO, description="Calculation method to use")
    
    @validator('end_date')
    def end_date_must_be_after_start_date(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('end_date must be after start_date')
        return v
    
    @validator('amount')
    def amount_must_be_reasonable(cls, v):
        if v > 10_000_000:  # 10 million limit
            raise ValueError('amount exceeds maximum allowed value')
        return v

class ScheduleUpdateRequest(BaseModel):
    """Request model for updating an existing schedule."""
    amount: float = Field(..., gt=0, description="New total amount to amortize")
    start_date: date = Field(..., description="New service period start date")
    end_date: date = Field(..., description="New service period end date")
    calculation_method: CalculationMethod = Field(CalculationMethod.AUTO, description="Calculation method to use")
    
    @validator('end_date')
    def end_date_must_be_after_start_date(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('end_date must be after start_date')
        return v
    
    @validator('amount')
    def amount_must_be_reasonable(cls, v):
        if v > 10_000_000:  # 10 million limit
            raise ValueError('amount exceeds maximum allowed value')
        return v

class ScheduleUpdateResponse(BaseModel):
    """Response model for schedule update."""
    success: bool = Field(..., description="Whether the update was successful")
    message: str = Field(..., description="Success or error message")
    schedule_id: str = Field(..., description="ID of the updated schedule")
    updated_entries_count: int = Field(..., description="Number of entries updated")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Schedule updated successfully",
                "schedule_id": "f65e75a1-3d34-45e1-b60b-12e69d56d735",
                "updated_entries_count": 11
            }
        }

class MonthlyEntryUpdateRequest(BaseModel):
    """Request model for updating a single monthly entry amount."""
    amount: float = Field(..., gt=0, description="New amount for this monthly entry")
    
    @validator('amount')
    def amount_must_be_reasonable(cls, v):
        if v > 10_000_000:  # 10 million limit
            raise ValueError('amount exceeds maximum allowed value')
        return v

class MonthlyEntryUpdateResponse(BaseModel):
    """Response model for monthly entry update."""
    success: bool = Field(..., description="Whether the update was successful")
    message: str = Field(..., description="Success or error message")
    updated_entry: dict = Field(..., description="Updated monthly entry details")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Monthly entry updated successfully",
                "updated_entry": {
                    "entry_index": 0,
                    "amount": 1250.50,
                    "month_date": "2024-01-31",
                    "status": "pending"
                }
            }
        }

class ScheduleCreateRequest(BaseModel):
    """Request model for creating a new schedule with edited preview data."""
    transaction_id: str = Field(..., description="Transaction ID to create schedule for")
    amount: float = Field(..., gt=0, description="Total amount to amortize")
    start_date: date = Field(..., description="Service period start date")
    end_date: date = Field(..., description="Service period end date")
    calculation_method: str = Field(..., description="Calculation method used")
    monthly_entries: List[MonthlyEntry] = Field(..., description="Monthly entries (may be user-edited)")
    account_code: Optional[str] = Field(None, description="Asset/prepayment account code")
    expense_account_code: Optional[str] = Field(None, description="Expense account code")
    description: Optional[str] = Field(None, description="Schedule description")
    
    @validator('end_date')
    def end_date_must_be_after_start_date(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('end_date must be after start_date')
        return v
    
    @validator('amount')
    def amount_must_be_reasonable(cls, v):
        if v > 10_000_000:  # 10 million limit
            raise ValueError('amount exceeds maximum allowed value')
        return v

class ScheduleCreateResponse(BaseModel):
    """Response model for schedule creation."""
    success: bool = Field(..., description="Whether the creation was successful")
    message: str = Field(..., description="Success or error message")
    schedule_id: str = Field(..., description="ID of the created schedule")
    schedule: dict = Field(..., description="Created schedule details")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Schedule created successfully",
                "schedule_id": "f65e75a1-3d34-45e1-b60b-12e69d56d735",
                "schedule": {
                    "id": "f65e75a1-3d34-45e1-b60b-12e69d56d735",
                    "status": "proposed",
                    "transaction_id": "abc123",
                    "amount": 12000.00,
                    "monthly_entries": []
                }
            }
        }