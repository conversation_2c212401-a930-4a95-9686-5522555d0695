"""
Accounts Endpoint Handler

Handles Chart of Accounts synchronization from Xero to CHART_OF_ACCOUNTS collection.
Pure metadata sync - no heavy processing required.
"""

import logging
from typing import List
from datetime import datetime, timezone

from google.cloud import firestore

from context import SyncContext
from utils.sync_helpers import _create_audit_log_entry, _update_firestore_sync_timestamp
from utils.bills_processor import _check_if_current_version_exists

logger = logging.getLogger(__name__)

__all__ = ["sync_accounts"]


async def sync_accounts(ctx: SyncContext, requested_endpoints: List[str]) -> int:
    """
    Handle Accounts (Chart of Accounts) synchronization from Xero.
    
    Args:
        ctx: Sync context containing all dependencies
        requested_endpoints: List of requested endpoints for sync
        
    Returns:
        Number of accounts processed
        
    Raises:
        Exception: If accounts sync fails
    """
    if "Accounts" not in requested_endpoints and requested_endpoints:
        return 0
    
    try:
        logger.info(f"Starting Accounts sync for entity_id: {ctx.entity_id}")
        
        full_sync = "Accounts" in ctx.force_full_sync_endpoints
        last_sync_timestamp_utc_str = None
        if not full_sync:
            sync_ts_field = "_system_lastSyncTimestampUtc_Accounts"
            last_sync_timestamp_utc_str = ctx.get_entity_setting(sync_ts_field)
        
        accounts_data = await ctx.xero_client.get_records("Accounts", if_modified_since=last_sync_timestamp_utc_str)
        saved_accounts_count = 0
        skipped_accounts_count = 0
        
        # Use batch write for better performance
        batch = ctx.db.batch()
        batch_count = 0
        batch_size = ctx.get_entity_setting("batch_size", 50)
        
        for account in accounts_data:
            account_id_from_xero = account.get("AccountID")
            if not account_id_from_xero:
                logger.warning(f"Account data missing AccountID for entity {ctx.entity_id}. Skipping: {account}")
                continue
            
            # CHECK IF WE ALREADY HAVE THE CURRENT VERSION - SKIP PROCESSING IF SO
            new_updated_at = account.get("UpdatedDateUTC")
            if await _check_if_current_version_exists(ctx.db, account_id_from_xero, new_updated_at, ctx.entity_id, "CHART_OF_ACCOUNTS", "account"):
                skipped_accounts_count += 1
                continue  # Skip all processing for this account - we already have current version
            
            # Extract account metadata
            account_doc = {
                "entity_id": ctx.entity_id,
                "client_id": ctx.client_id,
                "source_system": "XERO",
                "account_id": account_id_from_xero,
                "code": account.get("Code"),
                "name": account.get("Name"),
                "type": account.get("Type"),
                "tax_type": account.get("TaxType"),
                "description": account.get("Description"),
                "class": account.get("Class"),
                "system_account": account.get("SystemAccount"),
                "enable_payments_to_account": account.get("EnablePaymentsToAccount", False),
                "show_in_expense_claims": account.get("ShowInExpenseClaims", False),
                "bank_account_number": account.get("BankAccountNumber"),
                "bank_account_type": account.get("BankAccountType"),
                "currency_code": account.get("CurrencyCode"),
                "reporting_code": account.get("ReportingCode"),
                "reporting_code_name": account.get("ReportingCodeName"),
                "has_attachments": account.get("HasAttachments", False),
                "status": account.get("Status", "ACTIVE"),
                "raw_xero_data": account,
                "last_updated_utc": new_updated_at,
                "sync_timestamp": firestore.SERVER_TIMESTAMP
            }

            account_ref = ctx.db.collection("CHART_OF_ACCOUNTS").document(account_id_from_xero)
            batch.set(account_ref, account_doc, merge=True)
            saved_accounts_count += 1
            batch_count += 1
            
            # Commit batch when it reaches size limit
            if batch_count >= batch_size:
                await batch.commit()
                batch = ctx.db.batch()  # Reset batch
                batch_count = 0
        
        # Commit any remaining documents in the batch
        if batch_count > 0:
            await batch.commit()
        
        logger.info(f"Accounts sync completed for entity_id: {ctx.entity_id} - Processed: {saved_accounts_count}, Skipped: {skipped_accounts_count}")
        await _update_firestore_sync_timestamp(
            ctx.db, ctx.entity_id, "Accounts", datetime.now(timezone.utc).isoformat(), "Chart of Accounts sync successful"
        )
        await _create_audit_log_entry(
            ctx.db, "SYNC", "ACCOUNTS_SYNC_SUCCESS", ctx.client_id, ctx.entity_id, "SUCCESS",
            {"accounts_processed": saved_accounts_count, "accounts_skipped": skipped_accounts_count, "syncJobId": ctx.sync_job_id}
        )
        
        return saved_accounts_count
        
    except Exception as e:
        logger.error(f"Accounts sync failed for entity_id: {ctx.entity_id}: {e}", exc_info=True)
        await _create_audit_log_entry(
            ctx.db, "SYNC", "ACCOUNTS_SYNC_FAILURE", ctx.client_id, ctx.entity_id, "FAILURE",
            {"error": str(e), "syncJobId": ctx.sync_job_id}
        )
        raise