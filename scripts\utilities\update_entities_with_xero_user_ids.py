#!/usr/bin/env python3
"""
Script to update existing entities with Xero user IDs.
This script will:
1. Find all connected Xero entities in Firebase
2. Load their tokens and call Xero userinfo endpoint
3. Update entity connection_details with xero_user_id
4. Report results

Usage:
    python scripts/utilities/update_entities_with_xero_user_ids.py [--dry-run]
"""

import asyncio
import logging
import sys
import os
import argparse
from datetime import datetime
from typing import Dict, List, Optional

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from google.cloud import firestore
from drcr_shared_logic.clients.xero_client import XeroApiClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class XeroUserIdUpdater:
    def __init__(self, dry_run: bool = False):
        self.dry_run = dry_run
        self.db = firestore.Client()
        self.results = {
            "total_entities": 0,
            "connected_entities": 0,
            "successful_updates": 0,
            "failed_updates": 0,
            "skipped_entities": 0,
            "errors": []
        }

    async def find_connected_entities(self) -> List[Dict]:
        """Find all entities that are connected to Xero"""
        logger.info("Finding connected Xero entities...")
        
        entities_ref = self.db.collection("ENTITIES")
        
        # Get all entities and filter for those with Xero connections
        docs = entities_ref.stream()
        
        xero_entities = []
        total_entities = 0
        
        for doc in docs:
            entity_data = doc.to_dict()
            entity_data["entity_id"] = doc.id
            total_entities += 1
            
            # Check if this entity has Xero connection details
            connection_details = entity_data.get("connection_details", {})
            entity_status = entity_data.get("status", "")
            xero_tenant_id = connection_details.get("xero_tenant_id")
            
            # Log all entities for debugging
            logger.info(f"Entity: {entity_data.get('entity_name', 'Unknown')} "
                       f"(status: {entity_status}, type: {entity_data.get('type', 'N/A')}, "
                       f"has_xero_tenant: {bool(xero_tenant_id)})")
            
            # Include entities that have xero_tenant_id and are not disconnected
            if (xero_tenant_id and entity_status not in ["disconnected", "inactive"]):
                xero_entities.append(entity_data)
                logger.info(f"✅ Including Xero entity: {entity_data.get('entity_name', 'Unknown')} "
                           f"(tenant: {xero_tenant_id[:8]}...)")
            elif xero_tenant_id:
                logger.info(f"⏭️  Skipping entity due to status: {entity_status}")
        
        self.results["total_entities"] = total_entities
        self.results["connected_entities"] = len(xero_entities)
        
        logger.info(f"Found {len(xero_entities)} connected Xero entities out of {total_entities} total entities")
        return xero_entities

    async def get_xero_user_id_for_entity(self, entity: Dict) -> Optional[str]:
        """Get Xero user ID for a specific entity"""
        entity_id = entity["entity_id"]
        client_id = entity.get("client_id")
        connection_details = entity.get("connection_details", {})
        xero_tenant_id = connection_details.get("xero_tenant_id")
        
        if not client_id or not xero_tenant_id:
            logger.warning(f"Entity {entity_id} missing client_id or xero_tenant_id")
            return None
            
        try:
            # Create Xero client and load tokens
            xero_client = await XeroApiClient.create(
                platform_org_id=xero_tenant_id,
                tenant_id=client_id
            )
            
            # Get Xero user ID
            xero_user_id = await xero_client.get_xero_user_id()
            
            if xero_user_id:
                logger.info(f"Retrieved Xero user ID for entity {entity_id}: {xero_user_id}")
                return xero_user_id
            else:
                logger.warning(f"Could not retrieve Xero user ID for entity {entity_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting Xero user ID for entity {entity_id}: {e}")
            self.results["errors"].append(f"Entity {entity_id}: {str(e)}")
            return None

    async def update_entity_with_user_id(self, entity: Dict, xero_user_id: str) -> bool:
        """Update entity with Xero user ID"""
        entity_id = entity["entity_id"]
        
        if self.dry_run:
            logger.info(f"[DRY RUN] Would update entity {entity_id} with xero_user_id: {xero_user_id}")
            return True
            
        try:
            entity_ref = self.db.collection("ENTITIES").document(entity_id)
            
            update_data = {
                "connection_details.xero_user_id": xero_user_id,
                "updated_at": firestore.SERVER_TIMESTAMP
            }
            
            await entity_ref.update(update_data)
            logger.info(f"Successfully updated entity {entity_id} with xero_user_id: {xero_user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating entity {entity_id}: {e}")
            self.results["errors"].append(f"Update failed for {entity_id}: {str(e)}")
            return False

    async def process_entities(self):
        """Main processing function"""
        logger.info(f"Starting Xero user ID update process (dry_run: {self.dry_run})")
        
        # Find connected entities
        entities = await self.find_connected_entities()
        
        if not entities:
            logger.info("No connected entities found")
            return
            
        # Process each entity
        for i, entity in enumerate(entities, 1):
            entity_id = entity["entity_id"]
            logger.info(f"Processing entity {i}/{len(entities)}: {entity_id}")
            
            # Check if already has xero_user_id
            connection_details = entity.get("connection_details", {})
            existing_user_id = connection_details.get("xero_user_id")
            
            if existing_user_id:
                logger.info(f"Entity {entity_id} already has xero_user_id: {existing_user_id}")
                self.results["skipped_entities"] += 1
                continue
                
            # Get Xero user ID
            xero_user_id = await self.get_xero_user_id_for_entity(entity)
            
            if xero_user_id:
                # Update entity
                success = await self.update_entity_with_user_id(entity, xero_user_id)
                if success:
                    self.results["successful_updates"] += 1
                else:
                    self.results["failed_updates"] += 1
            else:
                self.results["failed_updates"] += 1
                
            # Small delay between requests
            await asyncio.sleep(0.5)

    def print_results(self):
        """Print summary results"""
        print("\n" + "="*60)
        print("XERO USER ID UPDATE RESULTS")
        print("="*60)
        print(f"Total entities found:      {self.results['total_entities']}")
        print(f"Connected entities:        {self.results['connected_entities']}")
        print(f"Successful updates:        {self.results['successful_updates']}")
        print(f"Failed updates:            {self.results['failed_updates']}")
        print(f"Skipped (already has ID):  {self.results['skipped_entities']}")
        
        if self.results["errors"]:
            print(f"\nErrors ({len(self.results['errors'])}):")
            for error in self.results["errors"]:
                print(f"  - {error}")
                
        if self.dry_run:
            print(f"\n[DRY RUN MODE] No actual updates were made")
            
        print("="*60)

async def main():
    parser = argparse.ArgumentParser(description="Update entities with Xero user IDs")
    parser.add_argument("--dry-run", action="store_true", 
                       help="Show what would be updated without making changes")
    args = parser.parse_args()
    
    updater = XeroUserIdUpdater(dry_run=args.dry_run)
    
    try:
        await updater.process_entities()
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
    finally:
        updater.print_results()

if __name__ == "__main__":
    asyncio.run(main())