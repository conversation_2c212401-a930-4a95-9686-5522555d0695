import React, { useState, useEffect } from 'react';
import { Plus, Search, MoreHorizontal, Shield, Users, UserCheck, UserX, Mail } from 'lucide-react';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { useFirmName } from '../hooks/useFirmName';

import { UsersService, type User, type UsersListResponse } from '../services/users.service';
import { AppSidebar } from '../components/layout/AppSidebar';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { Badge } from '../components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '../components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Label } from '../components/ui/label';
import { Skeleton } from '../components/ui/skeleton';
import { Alert, AlertDescription } from '../components/ui/alert';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '../components/ui/breadcrumb';
import { Separator } from '../components/ui/separator';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '../components/ui/sidebar';

interface InviteUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUserInvited: () => void;
}

const InviteUserDialog: React.FC<InviteUserDialogProps> = ({ open, onOpenChange, onUserInvited }) => {
  const [email, setEmail] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [role, setRole] = useState<'firm_admin' | 'firm_staff'>('firm_staff');
  const [assignedClientIds, setAssignedClientIds] = useState<string[]>([]);
  const [availableClients, setAvailableClients] = useState<Array<{client_id: string, name: string}>>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingClients, setIsLoadingClients] = useState(false);

  // Fetch available clients when dialog opens
  useEffect(() => {
    if (open) {
      fetchClients();
    }
  }, [open]);

  const fetchClients = async () => {
    try {
      setIsLoadingClients(true);
      // Use the same API pattern as other pages
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081'}/clients/summary`, {
        headers: {
          'Authorization': `Bearer ${await import('../services/auth.service').then(m => m.AuthService.getCurrentUserToken())}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setAvailableClients(data.clients.map((c: any) => ({
          client_id: c.client_id,
          name: c.name
        })));
      }
    } catch (error) {
      console.error('Error fetching clients:', error);
    } finally {
      setIsLoadingClients(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) {
      toast.error('Email is required');
      return;
    }

    setIsLoading(true);
    try {
      await UsersService.inviteUser({
        email: email.trim(),
        role,
        display_name: displayName.trim() || undefined,
        assigned_client_ids: role === 'firm_staff' ? assignedClientIds : [], // Only assign clients to staff
      });

      toast.success('User invited successfully');
      setEmail('');
      setDisplayName('');
      setRole('firm_staff');
      setAssignedClientIds([]);
      onOpenChange(false);
      onUserInvited();
    } catch (error: any) {
      toast.error(error.message || 'Failed to invite user');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClientToggle = (clientId: string) => {
    setAssignedClientIds(prev => 
      prev.includes(clientId) 
        ? prev.filter(id => id !== clientId)
        : [...prev, clientId]
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Invite New User</DialogTitle>
          <DialogDescription>
            Send an invitation to a new user to join your firm.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="displayName">Display Name (Optional)</Label>
            <Input
              id="displayName"
              placeholder="John Doe"
              value={displayName}
              onChange={(e) => setDisplayName(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="role">Role</Label>
            <Select value={role} onValueChange={(value: 'firm_admin' | 'firm_staff') => setRole(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="firm_staff">Firm Staff</SelectItem>
                <SelectItem value="firm_admin">Firm Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* Client Assignment - only show for firm_staff */}
          {role === 'firm_staff' && (
            <div className="space-y-2">
              <Label>Assign Clients</Label>
              {isLoadingClients ? (
                <div className="text-sm text-muted-foreground">Loading clients...</div>
              ) : availableClients.length > 0 ? (
                <div className="space-y-2 max-h-32 overflow-y-auto border rounded p-2">
                  {availableClients.map((client) => (
                    <div key={client.client_id} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`client-${client.client_id}`}
                        checked={assignedClientIds.includes(client.client_id)}
                        onChange={() => handleClientToggle(client.client_id)}
                        className="rounded"
                      />
                      <Label 
                        htmlFor={`client-${client.client_id}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {client.name}
                      </Label>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">No clients available</div>
              )}
              <div className="text-xs text-muted-foreground">
                Firm admins have access to all clients automatically
              </div>
            </div>
          )}
          
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Inviting...' : 'Send Invitation'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

const UserManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const { firmName, isLoading: firmNameLoading } = useFirmName();
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [firmId, setFirmId] = useState<string>('');

  const loadUsers = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response: UsersListResponse = await UsersService.listUsers();
      setUsers(response.users);
      setFilteredUsers(response.users);
      setFirmId(response.firm_id);
    } catch (error: any) {
      setError(error.message || 'Failed to load users');
      toast.error('Failed to load users');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  useEffect(() => {
    const filtered = users.filter(user =>
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.display_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredUsers(filtered);
  }, [users, searchTerm]);

  const handleStatusChange = async (userId: string, newStatus: User['status']) => {
    try {
      await UsersService.updateUserStatus(userId, { status: newStatus });
      toast.success('User status updated successfully');
      loadUsers();
    } catch (error: any) {
      toast.error(error.message || 'Failed to update user status');
    }
  };

  const handleRoleChange = async (userId: string, newRole: User['role']) => {
    try {
      const user = users.find(u => u.user_id === userId);
      if (!user) return;

      await UsersService.updateUserRole(userId, {
        role: newRole,
        assigned_client_ids: user.assigned_client_ids,
      });
      toast.success('User role updated successfully');
      loadUsers();
    } catch (error: any) {
      toast.error(error.message || 'Failed to update user role');
    }
  };

  const handleRemoveUser = async (userId: string, userEmail: string) => {
    if (!confirm(`Are you sure you want to remove ${userEmail} from your firm?`)) {
      return;
    }

    try {
      await UsersService.removeUser(userId);
      toast.success('User removed successfully');
      loadUsers();
    } catch (error: any) {
      toast.error(error.message || 'Failed to remove user');
    }
  };

  const handleResendInvite = async (userId: string, userEmail: string) => {
    try {
      await UsersService.resendInvite(userId);
      toast.success(`Invitation resent to ${userEmail}`);
      loadUsers();
    } catch (error: any) {
      toast.error(error.message || 'Failed to resend invitation');
    }
  };

  const getStatusBadge = (status: User['status']) => {
    const variants = {
      active: 'default',
      inactive: 'secondary',
      invited: 'outline',
      suspended: 'destructive',
    } as const;

    return (
      <Badge variant={variants[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getRoleBadge = (role: User['role']) => {
    return (
      <Badge variant={role === 'firm_admin' ? 'default' : 'secondary'}>
        {role === 'firm_admin' ? (
          <>
            <Shield className="w-3 h-3 mr-1" />
            Admin
          </>
        ) : (
          <>
            <Users className="w-3 h-3 mr-1" />
            Staff
          </>
        )}
      </Badge>
    );
  };

  const getStatsCards = () => {
    const activeUsers = users.filter(u => u.status === 'active').length;
    const adminUsers = users.filter(u => u.role === 'firm_admin').length;
    const invitedUsers = users.filter(u => u.status === 'invited').length;

    return [
      {
        title: 'Total Users',
        value: users.length,
        icon: Users,
        description: 'All users in your firm',
      },
      {
        title: 'Active Users',
        value: activeUsers,
        icon: UserCheck,
        description: 'Currently active users',
      },
      {
        title: 'Administrators',
        value: adminUsers,
        icon: Shield,
        description: 'Users with admin privileges',
      },
      {
        title: 'Pending Invites',
        value: invitedUsers,
        icon: UserX,
        description: 'Users with pending invitations',
      },
    ];
  };

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Alert>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      navigate('/dashboard');
                    }}
                    className="cursor-pointer"
                  >
                    {firmNameLoading ? 'Loading...' : firmName}
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Users</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
              <p className="text-muted-foreground">
                Manage users, roles, and permissions for your firm
              </p>
            </div>
            <Button onClick={() => setInviteDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Invite User
            </Button>
          </div>

          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {getStatsCards().map((stat, index) => (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                  <stat.icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <p className="text-xs text-muted-foreground">{stat.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Search and Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Users</CardTitle>
              <CardDescription>
                A list of all users in your firm with their roles and status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2 mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search users by email, name, or role..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>

              {/* Users Table */}
              {isLoading ? (
                <div className="space-y-2">
                  {[...Array(5)].map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                  ))}
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Last Sign In</TableHead>
                      <TableHead>Clients</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.map((user) => (
                      <TableRow key={user.user_id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {user.display_name || user.email.split('@')[0]}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {user.email}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{getRoleBadge(user.role)}</TableCell>
                        <TableCell>{getStatusBadge(user.status)}</TableCell>
                        <TableCell>
                          {user.last_sign_in ? (
                            <div className="text-sm">
                              {new Date(user.last_sign_in).toLocaleDateString()}
                            </div>
                          ) : (
                            <span className="text-muted-foreground">Never</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {user.assigned_client_ids.length > 0 ? (
                              `${user.assigned_client_ids.length} client${user.assigned_client_ids.length !== 1 ? 's' : ''}`
                            ) : (
                              <span className="text-muted-foreground">No clients</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              
                              {/* Resend Invite - only for invited users */}
                              {user.status === 'invited' && (
                                <>
                                  <DropdownMenuItem
                                    onClick={() => handleResendInvite(user.user_id, user.email)}
                                  >
                                    <Mail className="w-4 h-4 mr-2" />
                                    Resend Invite
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                </>
                              )}
                              
                              {/* Role Management */}
                              <DropdownMenuLabel className="text-xs text-muted-foreground">
                                Change Role
                              </DropdownMenuLabel>
                              {user.role !== 'firm_admin' && (
                                <DropdownMenuItem
                                  onClick={() => handleRoleChange(user.user_id, 'firm_admin')}
                                >
                                  <Shield className="w-4 h-4 mr-2" />
                                  Make Admin
                                </DropdownMenuItem>
                              )}
                              {user.role !== 'firm_staff' && (
                                <DropdownMenuItem
                                  onClick={() => handleRoleChange(user.user_id, 'firm_staff')}
                                >
                                  <Users className="w-4 h-4 mr-2" />
                                  Make Staff
                                </DropdownMenuItem>
                              )}
                              
                              <DropdownMenuSeparator />
                              
                              {/* Status Management */}
                              <DropdownMenuLabel className="text-xs text-muted-foreground">
                                Change Status
                              </DropdownMenuLabel>
                              {user.status !== 'active' && (
                                <DropdownMenuItem
                                  onClick={() => handleStatusChange(user.user_id, 'active')}
                                >
                                  <UserCheck className="w-4 h-4 mr-2" />
                                  Activate
                                </DropdownMenuItem>
                              )}
                              {user.status !== 'inactive' && (
                                <DropdownMenuItem
                                  onClick={() => handleStatusChange(user.user_id, 'inactive')}
                                >
                                  <UserX className="w-4 h-4 mr-2" />
                                  Deactivate
                                </DropdownMenuItem>
                              )}
                              
                              <DropdownMenuSeparator />
                              
                              {/* Remove User */}
                              <DropdownMenuItem
                                onClick={() => handleRemoveUser(user.user_id, user.email)}
                                className="text-destructive"
                              >
                                Remove from Firm
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}

              {!isLoading && filteredUsers.length === 0 && (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">
                    {searchTerm ? 'No users found matching your search.' : 'No users found.'}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Invite User Dialog */}
          <InviteUserDialog
            open={inviteDialogOpen}
            onOpenChange={setInviteDialogOpen}
            onUserInvited={loadUsers}
          />
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
};

export default UserManagementPage; 