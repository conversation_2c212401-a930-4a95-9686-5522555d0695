# DRCR Backend Testing Guide

Automated testing is a cornerstone of the DRCR backend development process. It is integral to maintaining code quality, preventing regressions, enabling confident refactoring, and ensuring the reliability of our financial data processing. This guide outlines our testing philosophy, the types of tests we employ, how they are structured, instructions for running them, and best practices for writing new tests.

## 1. Testing Philosophy

Our testing philosophy is built on the principle of a "testing pyramid," emphasizing a large base of fast and reliable unit tests, a moderate number of integration tests to verify interactions between components, and a smaller set of end-to-end tests (if applicable) to validate full system workflows.

*   **Confidence**: Tests should provide high confidence that the code behaves as expected under various conditions.
*   **Speed**: Tests, especially unit tests, should run quickly to provide rapid feedback during development.
*   **Isolation**: Unit tests should test components in isolation, mocking external dependencies.
*   **Maintainability**: Tests should be clear, concise, and easy to maintain alongside the application code.
*   **Coverage**: We strive for comprehensive test coverage, particularly for critical business logic and data transformation pathways.

## 2. Test Types

The DRCR backend primarily utilizes the following types of tests:

*   **Unit Tests**:
    *   **Purpose**: To test individual functions, methods, or classes (e.g., data transformers, utility functions, individual service methods) in isolation. They are designed to be fast and verify the smallest pieces of testable code.
    *   **Dependencies**: External dependencies (like database calls, external API requests, other services) are mocked out.
    *   **Location**: `tests/unit/`, mirroring the structure of the module being tested (e.g., tests for `rest_api/utils/transformers.py` are in `tests/unit/utils/test_transformers.py`).

*   **Integration Tests**:
    *   **Purpose**: To test the interaction between different components of the application or with emulated external services. For example, testing API endpoints' interaction with service layers and an emulated Firestore database, or testing how a Cloud Function responds to a Firestore trigger.
    *   **Dependencies**: May interact with emulated services (e.g., Firebase Emulator Suite) but not live external systems.
    *   **Location**: `tests/integration/`.
    *   **FastAPI-Specific Integration Tests**:
        *   Located in `tests/integration/routes/` directory
        *   Use FastAPI's `TestClient` to simulate HTTP requests
        *   Mock dependencies using `app.dependency_overrides` instead of `unittest.mock.patch`
        *   Test authentication, authorization, request validation, and response formatting

*(Future: End-to-End Tests - If implemented, this section would describe tests that validate entire user flows through the deployed system.)*

## 3. Test Structure

Our tests are organized within the `tests/` directory at the root of the project:

```
drcr_back/
├── rest_api/
│   ├── models/
│   ├── routers/
│   └── utils/
│       └── transformers.py
├── tests/
│   ├── unit/
│   │   └── utils/
│   │       └── test_transformers.py
│   ├── integration/
│   │   └── routers/
│   │       └── test_clients_api.py
│   └── conftest.py  # (If using pytest for shared fixtures)
│   └── __init__.py
├── ... (other project files)
└── requirements.txt
```

*   Test files are named with a `test_` prefix (e.g., `test_transformers.py`).
*   Test classes within files are named with a `Test` prefix (e.g., `TestTransformers`).
*   Test methods within classes are named with a `test_` prefix (e.g., `test_transform_valid_invoice`).

## 4. Running Tests

Ensure your Python virtual environment is activated before running tests.

*   **Run all tests** (using `unittest` discovery from the project root):
    ```bash
    python -m unittest discover -s tests -p "test_*.py"
    ```
*   **Run tests in a specific file**:
    ```bash
    python -m unittest tests/unit/utils/test_transformers.py
    ```
*   **Run a specific test class**:
    ```bash
    python -m unittest tests.unit.utils.test_transformers.TestTransformers
    ```
*   **Run a single test method**:
    ```bash
    python -m unittest tests.unit.utils.test_transformers.TestTransformers.test_transform_valid_accrec_invoice
    ```

*(If `pytest` becomes the standard, update commands accordingly, e.g., `pytest`, `pytest path/to/test_file.py`)*

**Important Considerations**:
*   For integration tests requiring Firebase emulators, ensure the emulators are running (see [Running Locally](./running_locally.md)).
*   Ensure any necessary environment variables are set or that tests explicitly configure clients for emulated environments.

## 5. Writing New Tests

Follow these guidelines when creating new tests:

*   **Adhere to Naming Conventions**: As described in "Test Structure."
*   **Arrange, Act, Assert (AAA)**: Structure tests clearly:
    1.  **Arrange**: Set up preconditions, data, and mocks.
    2.  **Act**: Execute the code under test.
    3.  **Assert**: Verify the outcome against expectations using `self.assertEqual()`, `self.assertTrue()`, etc.
*   **Independence**: Tests should be independent and not rely on the state or outcome of other tests.
*   **Focus**: Each test method should ideally focus on verifying a single aspect or behavior of the unit under test.
*   **Readability**: Write clear, descriptive test names and keep tests concise.
*   **Test Happy Paths and Edge Cases**: Include tests for normal operation, expected error conditions, boundary values, and empty/missing data.

## 6. Test Data

Consistent and well-structured test data is crucial for reliable tests.

*   **Helper Functions**: For generating sample input data for tests (especially for complex objects like Firestore documents), use helper functions. These improve readability and maintainability.
    *   **Example**: In `tests/unit/utils/test_transformers.py`, we use `create_sample_firestore_transaction_data()` and `create_sample_firestore_schedule_data()` to generate consistent dictionary inputs for our transformer tests.
    ```python
    # In tests/unit/utils/test_transformers.py
    def create_sample_firestore_schedule_data(...):
        # ... returns a dictionary
        pass

    class TestTransformers(unittest.TestCase):
        def test_some_schedule_scenario(self):
            # ARRANGE
            schedule_id = "sched-001"
            # Note: 'rest_api.models.schedule.ScheduleStatus' would need to be imported
            # from ..models.schedule import ScheduleStatus
            # For this example, assuming ScheduleStatus is available
            sample_data = create_sample_firestore_schedule_data(status="CONFIRMED", ...) # Using string value for test data

            # ACT
            result = firestore_to_schedule_model(schedule_id, sample_data) # Assuming this function is imported

            # ASSERT
            # Assuming ScheduleStatus enum is imported in the test file for comparison
            from rest_api.models.schedule import ScheduleStatus
            self.assertEqual(result.status, ScheduleStatus.CONFIRMED)
    ```
*   **Small, Focused Data**: For unit tests, use the minimal amount of data necessary to test the specific behavior.
*   **Avoid Real Sensitive Data**: Never use real production or sensitive user data in your tests.
*   **External Data Files (for larger datasets)**: If very large, static datasets are needed (more common for integration or performance tests), consider storing them in a separate test data directory (e.g., `tests/data/`) and loading them as needed. However, prefer inline or helper-generated data for unit tests for clarity.

## 7. Mocking

Mocking is essential for isolating the unit under test from its external dependencies (e.g., database interactions, calls to other services or external APIs, file system operations). We primarily use Python's built-in `unittest.mock` module.

*   **When to Mock**:
    *   External systems (databases, third-party APIs).
    *   Services or components outside the unit being tested.
    *   Functions or objects that are slow, non-deterministic (like `datetime.now()`), or have undesirable side effects during tests.

*   **How to Mock (using `unittest.mock.patch`)**:
    *   `@patch('module.path.to.TargetClassOrFunction')`: Decorator to replace an object with a mock for the duration of a test method. The path is where the target is *looked up*, not where it's defined.
    *   `patch.object(target_object, 'attribute_name')`: To mock an attribute (e.g., a method) on an already existing object instance.
    *   `MagicMock`: A flexible mock object that can mimic methods and attributes, and track calls.

*   **Example: Mocking a Firestore Client Call in a Service Method**

    Consider a service function `get_user_details` that instantiates and uses a Firestore client internally:
    ```python
    # In your_project/services/user_service.py
    from google.cloud import firestore # Firestore client imported at module level

    def get_user_details(user_id: str):
        db = firestore.Client() # Firestore client instantiated here
        doc_ref = db.collection("users").document(user_id)
        doc = doc_ref.get()
        if doc.exists:
            return doc.to_dict()
        return None
    ```

    A unit test for `get_user_details` would mock `firestore.Client` where it is used in `user_service.py`:
    ```python
    # In tests/unit/services/test_user_service.py
    import unittest
    from unittest.mock import patch, MagicMock
    from your_project.services.user_service import get_user_details # Import your function

    class TestUserService(unittest.TestCase):
        @patch('your_project.services.user_service.firestore.Client') # Target the Client constructor
        def test_get_user_details_found(self, MockFirestoreClientConstructor):
            # ARRANGE
            # Configure the mock instance that 'firestore.Client()' will return
            mock_db_instance = MockFirestoreClientConstructor.return_value

            mock_doc_snapshot = MagicMock()
            mock_doc_snapshot.exists = True
            mock_doc_snapshot.to_dict.return_value = {"name": "Test User", "email": "<EMAIL>"}

            # Configure the chain of calls on the mock_db_instance
            mock_db_instance.collection.return_value.document.return_value.get.return_value = mock_doc_snapshot

            user_id = "user123"

            # ACT: Call the function that uses the (now mocked) firestore.Client()
            user_details = get_user_details(user_id)

            # ASSERT
            MockFirestoreClientConstructor.assert_called_once() # Verify Client() was called
            mock_db_instance.collection.assert_called_once_with("users")
            mock_db_instance.collection.return_value.document.assert_called_once_with(user_id)
            mock_doc_snapshot.to_dict.assert_called_once()
            self.assertIsNotNone(user_details)
            self.assertEqual(user_details["name"], "Test User")

        @patch('your_project.services.user_service.firestore.Client')
        def test_get_user_details_not_found(self, MockFirestoreClientConstructor):
            # ARRANGE
            mock_db_instance = MockFirestoreClientConstructor.return_value
            mock_doc_snapshot = MagicMock()
            mock_doc_snapshot.exists = False
            mock_db_instance.collection.return_value.document.return_value.get.return_value = mock_doc_snapshot
            user_id = "user_not_exists"

            # ACT
            user_details = get_user_details(user_id)

            # ASSERT
            self.assertIsNone(user_details)
    ```

*   **Verify Calls**: Use `mock_object.assert_called_once_with(...)`, `mock_object.assert_any_call(...)`, `mock_object.call_count`, etc., to verify that mocked dependencies were interacted with as expected.
*   **Return Values and Side Effects**: Configure mocks to return specific values (`return_value`) or simulate side effects (`side_effect`) to control the behavior of dependencies during a test.

## 8. FastAPI Integration Testing

Integration tests for FastAPI endpoints require special consideration due to the framework's dependency injection system and async nature.

### Setting Up FastAPI Integration Tests

1. **Test Structure**:
   ```python
   # In tests/integration/routes/test_transactions_api.py
   import unittest
   import asyncio
   from fastapi.testclient import TestClient
   from fastapi import status
   from unittest.mock import MagicMock, AsyncMock

   from rest_api.app import app
   from rest_api.core import firebase_auth
   from rest_api import dependencies

   class TestTransactionsAPI(unittest.TestCase):
       def setUp(self):
           self.client = TestClient(app)
           self.mock_auth_user = firebase_auth.AuthUser(
               uid="test_user_uid",
               email="<EMAIL>",
               display_name="Test User",
               firm_id="test_firm_id",
               role="firm_admin",
               assigned_client_ids=["test_client_id"]
           )
           # Store original dependencies to restore them later
           self.original_dependencies = app.dependency_overrides.copy()
           app.dependency_overrides = {}  # Clear at start of each test

       def tearDown(self):
           # Restore original dependencies
           app.dependency_overrides = self.original_dependencies.copy()

       def test_get_transaction(self):
           """Test GET /transactions/{transaction_id} endpoint"""
           async def async_test_logic():
               # --- Mock Setup ---
               # 1. Mock the authentication dependency
               async def mock_get_current_user():
                   return self.mock_auth_user
               app.dependency_overrides[firebase_auth.get_current_user] = mock_get_current_user

               # 2. Mock the database dependency
               mock_db = MagicMock()
               # Configure mock_db behavior...
               async def mock_get_db():
                   yield mock_db
               app.dependency_overrides[dependencies.get_db] = mock_get_db

               # --- API Call ---
               response = self.client.get("/transactions/transaction_123")

               # --- Assertions ---
               self.assertEqual(response.status_code, status.HTTP_200_OK)
               # Additional assertions...

           # Run the async test logic
           asyncio.run(async_test_logic())
   ```

### Best Practices for FastAPI Integration Tests

1. **Use `app.dependency_overrides` for Mocking**:
   * Instead of patching with `unittest.mock.patch`, use FastAPI's `app.dependency_overrides` to replace dependencies.
   * This approach is more reliable for FastAPI's dependency injection system, especially for async dependencies.

2. **Handle Async Code Properly**:
   * Use `asyncio.run()` to execute async test logic.
   * Create inner async functions to organize the test logic.
   * Use `AsyncMock` for mocking async functions.

3. **Mock Database Dependencies**:
   * For database dependencies that use async generators (like `get_db`), create a mock function that yields a mock object.
   ```python
   async def mock_get_db():
       mock_db = MagicMock()
       # Configure mock_db...
       yield mock_db
   app.dependency_overrides[dependencies.get_db] = mock_get_db
   ```

4. **Test Authentication and Authorization**:
   * Mock the `get_current_user` dependency to return a user with appropriate permissions.
   * Test different user roles and access scenarios.

5. **Router Prefix Convention**:
   * Define router prefixes only in `app.py` when including routers, not in the router files themselves.
   * This prevents duplicate prefixes in URL paths (e.g., `/transactions/transactions/`).
   * Example:
     ```python
     # In app.py
     app.include_router(transactions.router, prefix="/transactions", tags=["Transactions"])

     # In routes/transactions.py
     router = APIRouter(tags=["Transactions"])  # No prefix here
     ```

6. **Clean Up After Tests**:
   * Always restore the original dependencies in `tearDown()` to prevent test interference.
   * Reset any global state that might affect other tests.

7. **Test Data Management**:
   * Create helper functions for generating test data.
   * Use consistent test data across unit and integration tests.

## 9. Firebase Token Management for API Testing

When testing API endpoints that require authentication, you'll need valid Firebase ID tokens. These tokens expire after **1 hour** and must be refreshed regularly during development and testing.

### Token Expiration Information

- **Expiration Time**: Firebase ID tokens expire after 1 hour
- **Impact**: Expired tokens will cause API calls to fail with authentication errors
- **Automatic Refresh**: The frontend automatically handles token refresh, but backend testing requires manual token generation

### When to Refresh Tokens

You need to refresh your Firebase token when you encounter:

- **401 Unauthorized** responses from API endpoints
- **"Token expired"** error messages
- **"Invalid token"** authentication failures
- **403 Forbidden** errors due to authentication issues
- When starting a new testing session after more than 1 hour

### Token Refresh Command

To generate a fresh Firebase ID token for testing:

```bash
cd D:\Projects\drcr_back && python tests/python/get_token.py
```

### What This Command Does

1. **Authenticates** with Firebase using your test credentials
2. **Generates** a fresh ID token valid for 1 hour
3. **Saves** the token to `tests/firebase_id_token_ascii.txt`
4. **Displays** token information for verification

### Verification

After running the token refresh command, you should see output similar to:

```
Getting Firebase ID token...
ID token received. Length: 905
ID token saved to tests/firebase_id_token_ascii.txt

User details:
{
  "uid": "bm43PZwmVAYFa1ifcSSlSS0nkIO2",
  "email": "<EMAIL>",
  "display_name": "tellart",
  "firm_id": "83a0939b-dcbb-4725-b1db-5e0acc5e8607",
  "client_id": null,
  "role": "firm_admin",
  "assigned_client_ids": []
}
```

### Token File Location

The generated token is automatically saved to:
```
tests/firebase_id_token_ascii.txt
```

This file is used by:
- API testing scripts (e.g., `test_auth_clients.py`)
- Manual API testing tools
- Backend integration tests requiring authentication

### Testing Workflow

1. **Start testing session**: Run the token refresh command
2. **Run your tests**: Use the generated token for API calls
3. **Token expires**: After 1 hour, refresh the token again
4. **Continue testing**: Resume with the fresh token

### Example Usage in Tests

```python
def get_token():
    """Get the Firebase ID token from the file."""
    try:
        with open("tests/firebase_id_token_ascii.txt", "r") as f:
            token = f.read().strip()
        return token
    except FileNotFoundError:
        print("Token file not found. Please run get_token.py first.")
        return None

# Use in API calls
token = get_token()
headers = {"Authorization": f"Bearer {token}"}
response = requests.get("http://localhost:8081/clients/summary", headers=headers)
```

### Troubleshooting

**Problem**: `FileNotFoundError` when trying to read token file
**Solution**: Run the token refresh command to generate a new token file

**Problem**: Still getting 401 errors after token refresh
**Solution**:
1. Verify the token file was updated (check timestamp)
2. Ensure you're using the correct token file path
3. Check that the backend is running and accessible

**Problem**: Token generation script fails
**Solution**:
1. Verify Firebase credentials are properly configured
2. Check that `FIREBASE_CREDENTIALS_PATH` environment variable is set
3. Ensure the Firebase service account file exists and is readable

---

By adhering to these testing practices, we can build and maintain a robust, reliable, and evolvable DRCR backend. Remember to write tests for new features and bug fixes, and run them frequently during your development workflow.