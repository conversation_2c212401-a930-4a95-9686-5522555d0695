"""
ManualJournals Endpoint Handler

Handles ManualJournals synchronization from Xero with date filtering support.
Critical for schedule logic tracking and financial reporting.
"""

import logging
from typing import List
from datetime import datetime, timezone
import re

from google.cloud import firestore

from context import SyncContext
from utils.sync_helpers import _create_audit_log_entry, _update_firestore_sync_timestamp
from utils.bills_processor import _check_if_current_version_exists

logger = logging.getLogger(__name__)

__all__ = ["sync_manual_journals"]


def _parse_xero_date(xero_date_str):
    """Parse Xero date format /Date(timestamp)/ to datetime object."""
    if not xero_date_str:
        return None
    
    if isinstance(xero_date_str, str) and xero_date_str.startswith("/Date(") and xero_date_str.endswith(")/"):
        ts_match = re.search(r'(\d+)', xero_date_str)
        if ts_match:
            timestamp_ms = int(ts_match.group(1))
            return datetime.fromtimestamp(timestamp_ms / 1000, tz=timezone.utc)
    
    return None


async def sync_manual_journals(ctx: SyncContext, requested_endpoints: List[str]) -> int:
    """
    Handle ManualJournals synchronization from Xero.
    
    Args:
        ctx: Sync context containing all dependencies
        requested_endpoints: List of requested endpoints for sync
        
    Returns:
        Number of manual journals processed
        
    Raises:
        Exception: If manual journals sync fails
    """
    if "ManualJournals" not in requested_endpoints and requested_endpoints:
        return 0
    
    # Check if manual journals sync is enabled for this entity
    if not ctx.should_sync_endpoint("ManualJournals", True):
        logger.info(f"ManualJournals sync disabled for entity_id: {ctx.entity_id}")
        return 0
    
    try:
        logger.info(f"Starting ManualJournals sync for entity_id: {ctx.entity_id}")
        
        full_sync = "ManualJournals" in ctx.force_full_sync_endpoints
        last_sync_timestamp_utc_str = None
        transaction_sync_start_date = ctx.message_data.get("transactionSyncStartDate")
        
        # Get last sync timestamp for incremental sync
        sync_ts_field = "_system_lastSyncTimestampUtc_ManualJournals"
        last_sync_timestamp_utc_str = ctx.get_entity_setting(sync_ts_field) if not full_sync else None
        
        # Build where filter with date handling and exclude voided entries
        where_filters = []
        
        # Always exclude voided manual journals (deleted records aren't returned by Xero API)
        where_filters.append('Status!="VOIDED"')
        
        use_date_filter = bool(transaction_sync_start_date) and bool(full_sync)
        
        if use_date_filter:
            # Parse the date and format for Xero DateTime constructor (FIRST SYNC ONLY)
            try:
                date_obj = datetime.strptime(transaction_sync_start_date, '%Y-%m-%d').date()
                where_filters.append(f'Date>=DateTime({date_obj.year},{date_obj.month:02d},{date_obj.day:02d},00,00,00)')
                logger.info(f"First ManualJournals sync - using date filter from {transaction_sync_start_date}")
            except ValueError:
                logger.warning(f"Invalid transactionSyncStartDate format: {transaction_sync_start_date}, skipping date filter")
                use_date_filter = False
        
        # Combine filters with AND
        where_filter = ' AND '.join(where_filters) if where_filters else None
        
        logger.info(f"Fetching ManualJournals with filter: {where_filter if where_filter else 'None'}")
        
        manual_journals_data = await ctx.xero_client.get_records(
            "ManualJournals",
            where_filter=where_filter,
            if_modified_since=last_sync_timestamp_utc_str if not use_date_filter else None
        )
        
        saved_journals_count = 0
        skipped_journals_count = 0
        
        # Use batch write for better performance
        batch = ctx.db.batch()
        batch_count = 0
        batch_size = ctx.get_entity_setting("batch_size", 50)
        
        for journal in manual_journals_data:
            journal_id = journal.get("ManualJournalID")
            if not journal_id:
                logger.warning(f"ManualJournal data missing ManualJournalID for entity {ctx.entity_id}. Skipping: {journal}")
                continue
            
            # CHECK IF WE ALREADY HAVE THE CURRENT VERSION - SKIP PROCESSING IF SO
            new_updated_at = journal.get("UpdatedDateUTC")
            if await _check_if_current_version_exists(ctx.db, journal_id, new_updated_at, ctx.entity_id, "MANUAL_JOURNALS", "manual_journal"):
                skipped_journals_count += 1
                continue
            
            # Parse Xero dates to proper datetime objects
            journal_date = _parse_xero_date(journal.get("Date"))
            last_updated_utc = _parse_xero_date(journal.get("UpdatedDateUTC"))
            
            # Create manual journal document
            journal_doc = {
                "entity_id": ctx.entity_id,
                "client_id": ctx.client_id,
                "source_system": "XERO",
                "journal_id": journal_id,
                "narration": journal.get("Narration"),
                "date": journal_date,  # Parsed datetime object
                "journalDate": journal_date,  # Add both fields for compatibility
                "status": journal.get("Status"),
                "journal_number": journal.get("JournalNumber"),
                "reference": journal.get("Reference"),
                "url": journal.get("Url"),
                "journal_lines": journal.get("JournalLines", []),
                "attachments": journal.get("Attachments", []),
                "has_attachments": journal.get("HasAttachments", False),
                "show_on_cash_basis_reports": journal.get("ShowOnCashBasisReports", False),
                "raw_xero_data": journal,
                "last_updated_utc": last_updated_utc,  # Parsed datetime object
                "sync_timestamp": firestore.SERVER_TIMESTAMP
            }
            
            # Store in MANUAL_JOURNALS collection
            journal_ref = ctx.db.collection("MANUAL_JOURNALS").document(journal_id)
            batch.set(journal_ref, journal_doc, merge=True)
            saved_journals_count += 1
            batch_count += 1
            
            # Commit batch when it reaches size limit
            if batch_count >= batch_size:
                await batch.commit()
                batch = ctx.db.batch()  # Reset batch
                batch_count = 0
        
        # Commit remaining documents
        if batch_count > 0:
            await batch.commit()
        
        logger.info(f"ManualJournals sync completed for entity_id: {ctx.entity_id} - Processed: {saved_journals_count}, Skipped: {skipped_journals_count}")
        await _update_firestore_sync_timestamp(
            ctx.db, ctx.entity_id, "ManualJournals", datetime.now(timezone.utc).isoformat(), "ManualJournals sync successful"
        )
        await _create_audit_log_entry(
            ctx.db, "SYNC", "MANUAL_JOURNALS_SYNC_SUCCESS", ctx.client_id, ctx.entity_id, "SUCCESS",
            {"manual_journals_processed": saved_journals_count, "manual_journals_skipped": skipped_journals_count, "syncJobId": ctx.sync_job_id}
        )
        
        return saved_journals_count
        
    except Exception as e:
        logger.error(f"ManualJournals sync failed for entity_id: {ctx.entity_id}: {e}", exc_info=True)
        await _create_audit_log_entry(
            ctx.db, "SYNC", "MANUAL_JOURNALS_SYNC_FAILURE", ctx.client_id, ctx.entity_id, "FAILURE",
            {"error": str(e), "syncJobId": ctx.sync_job_id}
        )
        raise