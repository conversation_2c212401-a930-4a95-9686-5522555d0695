﻿Production-Ready Prepayment Detector: Critical Blockers Fix
===========================================================

Based on the latest review, these are the blocking items that **must** be closed before we can ship to production.

🛑  Blocker 1 – Add Firestore Composite Indexes
----------------------------------------------
Issue
    • No `firestore.indexes.json` exists; queries will throttle at 1 000+ docs.
Action
    • Create the index definition file and deploy the indexes.
Files
    • `firestore.indexes.json` (new)
    • `scripts/utilities/create_firestore_indexes.py` (update)
Required Indexes
    • `MANUAL_JOURNALS(entity_id, status, journalDate desc)` – main detector query
    • `MANUAL_JOURNALS(entity_id, journalDate)` – chain detection
    • `AMORTIZATION_SCHEDULES(entity_id, _system_linkedManualJournalID)` – idempotency
    • `ACCOUNTS(entity_id, accountType)` – expense-code look-ups

🛑  Blocker 2 – Fix API Timestamp Serialisation
----------------------------------------------
Issue
    • Routes return raw Firestore `Timestamp` objects (`{"_seconds": …}`) which breaks clients.
Action
    • Convert all timestamps to ISO-8601 strings before returning.
Files
    • `rest_api/routes/manual_journals.py:215-219` (detection-status route)
    • Apply the same `.isoformat()` pattern everywhere timestamps are exposed.

🛑  Blocker 3 – Configure Future-Journal Window
---------------------------------------------
Issue
    • Product requirement: "release as many months in the future as you wish". The detector still caps future window at 90 days.
Action
    • Make the window configurable per entity **or** raise default significantly.
Options
    A. Raise `FUTURE_JOURNAL_DAYS` from 90 → 365+  
    B. Add per-entity `future_journal_days` config (similar to `journal_lookback_months`).
Files
    • `rest_api/config/detector_config.py`
    • `rest_api/services/prepayment_release_detector_service.py`

⚠️  Medium-Priority Fixes (after blockers)
-----------------------------------------
• Update confirm-match route to use a Pydantic model (`MatchConfirmationRequest`).
  – `rest_api/routes/manual_journals.py:246`
• Consolidate duplicated constants in Cloud Function.
  – `cloud_functions/prepayment_release_detector/main.py:44-52`
• Add negative test for sentinel-in-array prevention.
  – `tests/test_prepayment_release_detector.py`

Implementation Priority
-----------------------
1. **Critical Path (blocks production)**
   • Create and deploy Firestore indexes (build time ≈ 5-10 min).
   • Fix timestamp serialisation.
   • Decide and implement future-window logic.
2. **Medium Path (quality / polish)**
   • Pydantic model adoption, constant consolidation, additional tests.

Validation Checklist
--------------------
✅ Full test suite passes under Firestore emulator
✅ Cloud Run execution < 60 min at typical volume
🛑 Firestore indexes deployed & building
🛑 API responses return ISO timestamp strings
🛑 Future-dated journals included in detection scope
