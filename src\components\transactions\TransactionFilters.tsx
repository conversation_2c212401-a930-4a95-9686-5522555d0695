import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown, Loader2, Search } from 'lucide-react';

interface Client {
  client_id: string;
  name: string;
}

interface Entity {
  entity_id: string;
  entity_name: string;
  type: string;
  connection_status: string;
  last_sync?: string;
}

interface TransactionFiltersProps {
  clients: Client[];
  entities: Entity[];
  selectedClientId: string;
  selectedEntityId: string;
  searchTerm: string;
  isLoadingClients: boolean;
  isLoadingEntities: boolean;
  onClientChange: (clientId: string) => void;
  onEntityChange: (entityId: string) => void;
  onSearchChange: (searchTerm: string) => void;
}

export function TransactionFilters({
  clients,
  entities,
  selectedClientId,
  selectedEntityId,
  searchTerm,
  isLoadingClients,
  isLoadingEntities,
  onClientChange,
  onEntityChange,
  onSearchChange,
}: TransactionFiltersProps) {
  return (
    <div className="flex flex-row gap-2 items-center p-2 border-b bg-white flex-shrink-0 rounded-lg shadow-sm">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="min-w-[150px] justify-between bg-white text-xs h-8" disabled={isLoadingClients}>
            {isLoadingClients ? <Loader2 className="mr-1 h-3 w-3 animate-spin" /> : null}
            {selectedClientId ? clients?.find(c => c.client_id === selectedClientId)?.name : 'Select Client'}
            {!isLoadingClients && <ChevronDown className="ml-1 h-3 w-3" />}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[300px] z-50">
          <DropdownMenuLabel>Clients</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {isLoadingClients && <DropdownMenuItem disabled>Loading...</DropdownMenuItem>}
          {!isLoadingClients && clients && clients.map((client) => (
            <DropdownMenuItem key={client.client_id} onSelect={() => onClientChange(client.client_id)}>
              {client.name}
            </DropdownMenuItem>
          ))}
          {!isLoadingClients && (!clients || clients.length === 0) && <DropdownMenuItem disabled>No clients found</DropdownMenuItem>}
        </DropdownMenuContent>
      </DropdownMenu>

      <DropdownMenu>
        <DropdownMenuTrigger asChild disabled={!selectedClientId || isLoadingEntities}>
          <Button variant="outline" className="min-w-[150px] justify-between bg-white text-xs h-8" disabled={!selectedClientId || isLoadingEntities}>
            {isLoadingEntities ? <Loader2 className="mr-1 h-3 w-3 animate-spin" /> : null}
            {selectedEntityId === 'all' ? 'All Entities' : entities?.find(e => e.entity_id === selectedEntityId)?.entity_name ?? 'Select Entity'}
            {!isLoadingEntities && <ChevronDown className="ml-1 h-3 w-3" />}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[300px] z-50">
          <DropdownMenuLabel>Entities for {clients?.find(c => c.client_id === selectedClientId)?.name}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem key="all-entities" onSelect={() => onEntityChange('all')}>All Entities</DropdownMenuItem>
          {isLoadingEntities && <DropdownMenuItem disabled>Loading...</DropdownMenuItem>}
          {!isLoadingEntities && entities && entities.map((entity) => (
            <DropdownMenuItem key={entity.entity_id} onSelect={() => onEntityChange(entity.entity_id)}>
              {entity.entity_name} ({entity.connection_status})
            </DropdownMenuItem>
          ))}
          {!isLoadingEntities && (!entities || entities.length === 0) && selectedClientId && <DropdownMenuItem disabled>No entities found</DropdownMenuItem>}
          {!selectedClientId && <DropdownMenuItem disabled>Select a client first</DropdownMenuItem>}
        </DropdownMenuContent>
      </DropdownMenu>

      <div className="flex-grow"></div>

      <div className="relative">
        <Search className="absolute left-2 top-2 h-3 w-3 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search transactions..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-6 w-[200px] h-8 text-xs"
        />
      </div>
    </div>
  );
}