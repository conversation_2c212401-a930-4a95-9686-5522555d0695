from fastapi import APIRouter, Depends, HTTPException, Path, Query, status, Body
from typing import List, Optional
from google.cloud import firestore

from ..core.firebase_auth import get_current_user, get_firm_user_with_client_access, AuthUser
from ..dependencies import get_db
from ..services.amortization_service import AmortizationService
from ..schemas.schedule_schemas import NewSchedulePreviewRequest, SchedulePreviewResponse

router = APIRouter(tags=["Invoices"])

@router.get("/")
async def list_invoices(
    client_id: str = Query(..., description="Client ID"),
    entity_id: Optional[str] = Query(None, description="Entity ID (optional)"),
    status: Optional[str] = Query(None, description="Filter by status"),
    limit: int = Query(50, description="Number of records to return"),
    offset: int = Query(0, description="Number of records to skip"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """List invoices for a client/entity with pagination"""
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Base query - always filter by client_id
    query = db.collection("TRANSACTIONS").where(filter=firestore.FieldFilter("clientId", "==", client_id))

    # Additional filters
    if entity_id:
        query = query.where(filter=firestore.FieldFilter("entityId", "==", entity_id))

    # TODO: Implement more filters and pagination

    # Example placeholder response
    return {"invoices": [], "total": 0, "limit": limit, "offset": offset}

@router.get("/{transaction_id}")
async def get_invoice(
    transaction_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Get invoice details"""
    # Get transaction record
    transaction_ref = db.collection("TRANSACTIONS").document(transaction_id)
    transaction_doc = await transaction_ref.get()

    if not transaction_doc.exists:
        raise HTTPException(status_code=404, detail="Invoice not found")

    transaction_data = transaction_doc.to_dict()
    client_id = transaction_data.get("clientId")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Get associated schedules
    schedules = []
    schedule_ids = transaction_data.get("_system_amortizationScheduleIDs", [])

    for schedule_id in schedule_ids:
        schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
        schedule_doc = await schedule_ref.get()
        if schedule_doc.exists:
            schedules.append(schedule_doc.to_dict())

    # Get associated attachments
    attachments = []
    attachment_ids = transaction_data.get("_system_attachment_ids", [])

    for attachment_id in attachment_ids:
        attachment_ref = db.collection("ATTACHMENTS").document(attachment_id)
        attachment_doc = await attachment_ref.get()
        if attachment_doc.exists:
            attachments.append(attachment_doc.to_dict())

    # Return transaction data with associated records
    return {
        **transaction_data,
        "schedules": schedules,
        "attachments": attachments
    }

@router.post("/{transaction_id}/preview-schedule", response_model=SchedulePreviewResponse)
async def preview_new_schedule_for_invoice(
    transaction_id: str = Path(..., description="Transaction/Invoice ID"),
    request: NewSchedulePreviewRequest = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Preview a new amortization schedule for an invoice that has no existing schedule.
    
    This endpoint allows users to experiment with different amortization parameters
    for an invoice before actually creating the schedule.
    """
    try:
        # Get invoice/transaction record
        transaction_ref = db.collection("TRANSACTIONS").document(transaction_id)
        transaction_doc = await transaction_ref.get()
        
        if not transaction_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invoice not found"
            )
        
        transaction_data = transaction_doc.to_dict()
        client_id = transaction_data.get("clientId")
        entity_id = transaction_data.get("entityId")
        
        # Check if user has access to this client
        await get_firm_user_with_client_access(client_id, current_user)
        
        # Check if invoice already has schedules (optional warning)
        existing_schedule_ids = transaction_data.get("_system_amortizationScheduleIDs", [])
        if existing_schedule_ids:
            # Could add a warning in the response, but still allow preview
            # This lets users explore alternative scenarios
            pass
        
        # Get entity settings for materiality threshold
        if not entity_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invoice must have an entity_id to calculate amortization schedule"
            )
        
        entity_ref = db.collection("ENTITIES").document(entity_id)
        entity_doc = await entity_ref.get()
        
        if not entity_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Entity {entity_id} not found"
            )
        
        entity_data = entity_doc.to_dict()
        entity_settings = entity_data.get("settings", {})
        
        # Calculate preview using AmortizationService
        amortization_service = AmortizationService()
        preview_result = amortization_service.calculate_preview(
            amount=request.amount,
            start_date=request.start_date,
            end_date=request.end_date,
            entity_settings=entity_settings,
            force_calculation_method=request.calculation_method.value if request.calculation_method != "auto" else None
        )
        
        return SchedulePreviewResponse(**preview_result)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to preview schedule for invoice: {str(e)}"
        )