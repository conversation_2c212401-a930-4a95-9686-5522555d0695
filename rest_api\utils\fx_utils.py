"""
Currency conversion utilities for DRCR Backend.

Provides shared currency conversion logic for bill matching, schedule creation,
and other financial operations that require currency conversion.
"""
import logging
from typing import Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)


def convert_to_base_currency(
    amount: float,
    from_currency: str,
    base_currency: str,
    exchange_rate: Optional[float] = None,
    rate_source: str = "unknown"
) -> float:
    """
    Convert amount from one currency to base currency using Xero exchange rate format.
    
    Args:
        amount: Amount to convert
        from_currency: Source currency code (e.g., 'USD', 'EUR')
        base_currency: Target base currency code (e.g., 'GBP', 'USD')
        exchange_rate: Exchange rate (from_currency to base_currency)
        rate_source: Source of the exchange rate for logging
        
    Returns:
        Converted amount in base currency
        
    Note: 
        Xero CurrencyRate format: 1 unit of from_currency = rate units of base_currency
        For example: 1 USD = 0.79 GBP means rate = 0.79
    """
    # Short-circuit if same currency
    if from_currency == base_currency:
        return amount
    
    # Validate input amount
    if not amount or amount == 0:
        return amount
    
    # Check if we have a valid exchange rate
    if not exchange_rate or exchange_rate <= 0:
        logger.warning(
            f"No valid exchange rate available for {from_currency} -> {base_currency} conversion. "
            f"Rate: {exchange_rate}, Source: {rate_source}. Returning original amount."
        )
        return amount
    
    try:
        # Sanity check: If currencies are the same, rate should be ~1.0
        if from_currency == base_currency and abs(exchange_rate - 1.0) > 0.01:
            logger.warning(
                f"Same currency conversion {from_currency}->{base_currency} but rate={exchange_rate} (expected ~1.0)"
            )
        
        # Xero rate is base→invoice, so to go invoice→base we divide
        converted_amount = amount / exchange_rate  # foreign to base
        
        logger.debug(
            f"Converting {amount:.2f} {from_currency} → {converted_amount:.2f} {base_currency} "
            f"by dividing with rate {exchange_rate} (source: {rate_source})"
        )
        
        return converted_amount
        
    except (ValueError, ZeroDivisionError) as e:
        logger.warning(
            f"Currency conversion failed: {amount} {from_currency} -> {base_currency} "
            f"with rate {exchange_rate}: {e}. Returning original amount."
        )
        return amount


def convert_to_base_currency_from_bill(
    amount: float,
    from_currency: str,
    base_currency: str,
    bill: Dict[str, Any]
) -> float:
    """
    Convert amount to base currency using exchange rate stored in bill data.
    
    Args:
        amount: Amount to convert
        from_currency: Source currency code
        base_currency: Target base currency code
        bill: Bill data containing stored exchange rate
        
    Returns:
        Converted amount in base currency
    """
    # Get stored exchange rate from bill
    stored_rate = bill.get('currency_rate')
    rate_source = bill.get('currency_rate_source', 'unknown')
    bill_currency = bill.get('currency_code', from_currency)
    
    # Validate that the bill currency matches the from_currency
    if bill_currency != from_currency:
        logger.warning(
            f"Bill currency {bill_currency} doesn't match from_currency {from_currency}. "
            f"Using bill currency for conversion."
        )
        from_currency = bill_currency
    
    return convert_to_base_currency(
        amount=amount,
        from_currency=from_currency,
        base_currency=base_currency,
        exchange_rate=stored_rate,
        rate_source=rate_source
    )


def get_effective_exchange_rate(bill: Dict[str, Any]) -> Tuple[Optional[float], str]:
    """
    Extract effective exchange rate from bill data, prioritizing payment-level rates.
    
    Args:
        bill: Bill data from Xero
        
    Returns:
        Tuple of (rate, source) where source indicates where the rate came from
    """
    try:
        # Check payments first (higher priority)
        payments = bill.get("Payments", []) or []
        for payment in payments:
            rate = payment.get("CurrencyRate")
            if rate is not None and rate > 0:
                return rate, "payment"
    except Exception:
        pass
    
    # Fall back to invoice-level rate
    invoice_rate = bill.get("CurrencyRate")
    if invoice_rate is not None and invoice_rate > 0:
        return invoice_rate, "invoice"
    
    return None, "missing"