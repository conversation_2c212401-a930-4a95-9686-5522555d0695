# DRCR Frontend Development Guidelines

## Table of Contents
1. [Project Overview](#project-overview)
2. [Critical Issues Identified](#critical-issues-identified)
3. [Code Structure & Organization](#code-structure--organization)
4. [Development Standards](#development-standards)
5. [Component Guidelines](#component-guidelines)
6. [State Management](#state-management)
7. [API Integration](#api-integration)
8. [Testing Strategy](#testing-strategy)
9. [Build & Deployment](#build--deployment)
10. [Maintenance Tasks](#maintenance-tasks)

## Project Overview

The DRCR Frontend is a React 19.1 + TypeScript application built with:
- **Build Tool**: Vite 6.3.5
- **UI Framework**: Shadcn/UI with Tailwind CSS v3
- **State Management**: Zustand
- **Authentication**: Firebase Auth
- **Routing**: React Router v7
- **HTTP Client**: Axios
- **Form Handling**: React Hook Form + Zod

## Critical Issues Identified

### 🚨 High Priority Issues

#### 1. Duplicate Files
- **`src/hooks/use-mobile.ts`** and **`src/hooks/use-mobile.tsx`** - Identical content
- **`src/components/app-sidebar.tsx`** and **`src/components/layout/AppSidebar.tsx`** - Different implementations
- **`postcss.config.js`** and **`postcss.config.cjs`** - Conflicting configurations

#### 2. Configuration Conflicts
- **PostCSS**: Two different config files with different plugin setups
  - `postcss.config.cjs` uses `@tailwindcss/postcss`
  - `postcss.config.js` uses `tailwindcss`
  - Vite config references `.cjs` version

#### 3. Inconsistent Component Structure
- Mixed component organization (root vs layout folder)
- Inconsistent import paths and naming conventions
- Sample/placeholder components mixed with production code

#### 4. Missing Environment Configuration
- No `.env.example` file for development setup
- Firebase configuration not documented

### ⚠️ Medium Priority Issues

#### 1. Tailwind CSS Version Mismatch
- Using Tailwind CSS v3 instead of preferred v4
- Missing `@tailwindcss/postcss` package for v4 integration

#### 2. Unused Dependencies
- `next-themes` package installed but not used
- `tw-animate-css` in devDependencies but not utilized

#### 3. Component Inconsistencies
- Sample data hardcoded in components
- Inconsistent prop interfaces
- Missing TypeScript strict typing in some areas

## Code Structure & Organization

### Recommended Directory Structure
```
src/
├── components/
│   ├── ui/                 # Shadcn/UI components only
│   ├── auth/              # Authentication components
│   ├── layout/            # Layout components (keep only these)
│   ├── forms/             # Form components
│   ├── tables/            # Data table components
│   └── common/            # Shared utility components
├── pages/                 # Page components
├── hooks/                 # Custom React hooks
├── lib/                   # Utility functions
├── services/              # API service classes
├── store/                 # Zustand stores
├── types/                 # TypeScript type definitions
├── providers/             # React context providers
└── assets/                # Static assets
```

### File Naming Conventions
- **Components**: PascalCase (e.g., `UserProfile.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useAuthStore.ts`)
- **Utilities**: camelCase (e.g., `formatDate.ts`)
- **Types**: camelCase with `.types.ts` suffix (e.g., `auth.types.ts`)
- **Services**: camelCase with `.service.ts` suffix (e.g., `api.service.ts`)

## Development Standards

### TypeScript Guidelines
```typescript
// ✅ Good: Explicit interfaces
interface UserProfile {
  uid: string;
  email: string;
  displayName?: string;
  role: 'admin' | 'user' | 'client';
}

// ✅ Good: Proper component typing
interface ComponentProps {
  user: UserProfile;
  onUpdate: (user: UserProfile) => void;
}

export function UserComponent({ user, onUpdate }: ComponentProps) {
  // Component implementation
}

// ❌ Bad: Any types
const user: any = getUserData();

// ❌ Bad: Missing prop types
export function UserComponent({ user, onUpdate }) {
  // Component implementation
}
```

### Import Organization
```typescript
// 1. React and React-related imports
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// 2. Third-party libraries
import { toast } from 'sonner';
import { z } from 'zod';

// 3. Internal components (UI first, then others)
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { UserProfile } from '@/components/common/UserProfile';

// 4. Hooks and utilities
import { useAuthStore } from '@/store/auth.store';
import { formatDate } from '@/lib/utils';

// 5. Types
import type { UserProfile } from '@/types/auth.types';
```

### Component Structure
```typescript
// Component props interface
interface ComponentProps {
  // Props definition
}

// Component implementation
export function ComponentName({ prop1, prop2 }: ComponentProps) {
  // 1. Hooks (state, effects, custom hooks)
  const [state, setState] = useState();
  const { user } = useAuthStore();

  // 2. Event handlers
  const handleClick = () => {
    // Handler implementation
  };

  // 3. Effects
  useEffect(() => {
    // Effect implementation
  }, []);

  // 4. Render
  return (
    <div>
      {/* JSX */}
    </div>
  );
}
```

## Component Guidelines

### Shadcn/UI Components
- Use `npx shadcn@latest add [component-name]` for new components
- Never modify components in `src/components/ui/` directly
- Create wrapper components for customizations

### Layout Components
- Keep layout components in `src/components/layout/`
- Use consistent naming: `AppSidebar`, `AppHeader`, `AppLayout`
- Implement responsive design patterns

### Form Components
```typescript
// ✅ Good: Use React Hook Form + Zod
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const schema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
});

type FormData = z.infer<typeof schema>;

export function LoginForm() {
  const form = useForm<FormData>({
    resolver: zodResolver(schema),
  });

  // Form implementation
}
```

## State Management

### Zustand Store Structure
```typescript
// ✅ Good: Typed store with clear actions
interface AuthState {
  user: UserProfile | null;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  clearError: () => void;
}

export const useAuthStore = create<AuthState & AuthActions>((set, get) => ({
  // State
  user: null,
  isLoading: false,
  error: null,

  // Actions
  signIn: async (email, password) => {
    set({ isLoading: true, error: null });
    try {
      // Implementation
    } catch (error) {
      set({ error: error.message });
    } finally {
      set({ isLoading: false });
    }
  },

  // Other actions...
}));
```

### Store Organization
- One store per domain (auth, ui, data)
- Keep stores focused and cohesive
- Use TypeScript for all store definitions
- Implement proper error handling

### Persistent State with localStorage
```typescript
// ✅ Good: User preferences with localStorage persistence
const [selectedStatusFilters, setSelectedStatusFilters] = useState<string[]>(() => {
  // Load from localStorage or default to pending statuses
  const saved = localStorage.getItem('prepayments-status-filters');
  return saved ? JSON.parse(saved) : ['pending_configuration', 'pending_review', 'pending_confirmation'];
});

// Persist status filters to localStorage
useEffect(() => {
  localStorage.setItem('prepayments-status-filters', JSON.stringify(selectedStatusFilters));
}, [selectedStatusFilters]);
```

### Filter State Management Patterns
```typescript
// ✅ Good: Complex filter state with proper typing
interface PrepaymentsFilters {
  client_id?: string;
  entity_id?: string;
  supplier_filter?: string;
  status_filters?: string[];
  page?: number;
  limit?: number;
}

// Reset pagination when filters change
const prevFiltersRef = useRef({ selectedEntityId, supplierFilter, selectedStatusFilters: selectedStatusFilters.join(',') });
useEffect(() => {
  const prev = prevFiltersRef.current;
  const current = { selectedEntityId, supplierFilter, selectedStatusFilters: selectedStatusFilters.join(',') };

  // Only reset if filters actually changed (not on initial render)
  if (prev.selectedEntityId !== current.selectedEntityId ||
      prev.supplierFilter !== current.supplierFilter ||
      prev.selectedStatusFilters !== current.selectedStatusFilters) {
    setPagination(p => ({ ...p, currentPage: 1 }));
  }

  prevFiltersRef.current = current;
}, [selectedEntityId, supplierFilter, selectedStatusFilters]);
```

## API Integration

### Service Layer Pattern
```typescript
// ✅ Good: Service class with proper typing
export class UserService {
  static async getProfile(userId: string): Promise<UserProfile> {
    return api.get<UserProfile>(`/users/${userId}`);
  }

  static async updateProfile(userId: string, data: Partial<UserProfile>): Promise<UserProfile> {
    return api.put<UserProfile>(`/users/${userId}`, data);
  }
}
```

### Error Handling
```typescript
// ✅ Good: Consistent error handling
try {
  const user = await UserService.getProfile(userId);
  // Handle success
} catch (error) {
  if (error.response?.status === 404) {
    toast.error('User not found');
  } else {
    toast.error('Failed to load user profile');
  }
  console.error('Error loading user:', error);
}
```

## Testing Strategy

### Unit Testing
- Test utility functions thoroughly
- Test custom hooks with React Testing Library
- Mock external dependencies

### Component Testing
- Test user interactions
- Test error states
- Test loading states
- Use MSW for API mocking

### Integration Testing
- Test authentication flows
- Test routing behavior
- Test form submissions

## Build & Deployment

### Environment Variables
Create `.env.example`:
```env
# Firebase Configuration
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id

# API Configuration
VITE_API_BASE_URL=http://localhost:8080
```

### Build Commands
```bash
# Development
npm run dev

# Type checking
npm run build

# Linting
npm run lint

# Preview production build
npm run preview
```

### Performance Optimization
- Use React.lazy for code splitting
- Implement proper memoization
- Optimize bundle size
- Use proper image formats and sizes

## Maintenance Tasks

### Immediate Actions Required

1. **Remove Duplicate Files**
   - Delete `src/hooks/use-mobile.tsx` (keep `.ts`)
   - Choose one sidebar implementation
   - Remove conflicting PostCSS config

2. **Fix Configuration**
   - Standardize PostCSS configuration
   - Update to Tailwind CSS v4 if desired
   - Create proper environment configuration

3. **Clean Up Components**
   - Remove sample/placeholder data
   - Implement proper TypeScript typing
   - Standardize component structure

4. **Update Dependencies**
   - Remove unused packages
   - Update to latest compatible versions
   - Add missing type definitions

### Regular Maintenance

1. **Weekly**
   - Review and update dependencies
   - Run linting and fix issues
   - Review and merge feature branches

2. **Monthly**
   - Update major dependencies
   - Review and refactor components
   - Update documentation

3. **Quarterly**
   - Performance audit
   - Security audit
   - Architecture review

---

## Quick Reference

### Common Commands
```bash
# Add new Shadcn component
npx shadcn@latest add button

# Run development server
npm run dev

# Build for production
npm run build

# Run linter
npm run lint
```

### Useful VS Code Extensions
- ES7+ React/Redux/React-Native snippets
- Tailwind CSS IntelliSense
- TypeScript Importer
- Auto Rename Tag
- Prettier - Code formatter

### Resources
- [Shadcn/UI Documentation](https://ui.shadcn.com/)
- [Tailwind CSS Documentation](https://tailwindcss.com/)
- [React Router Documentation](https://reactrouter.com/)
- [Zustand Documentation](https://github.com/pmndrs/zustand)
