import React, { useState } from 'react';
import { toast } from 'sonner';

// --- Local Asset Imports ---
// REMOVED: import drcrLogo from '@/assets/logo.png';

// --- Shadcn/UI & Lucide Imports ---
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Card,
    CardContent,
    CardFooter,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
    Loader2, // Loading spinner
    Mail, // Email icon
    Lock, // Password icon
    AlertCircle, // Error icon
    ArrowRight, // Added for the button
} from 'lucide-react';

// --- Mock Authentication Function ---
// In a real app, this would call your Firebase authentication service
const mockAuthService = {
    login: async (email: string, password: string) => {
        console.log("Attempting login with:", email);
        await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API delay

        // Basic validation for mock
        if (!email || !password) {
            throw new Error("Email and password are required.");
        }
        if (email === "<EMAIL>" && password === "password123") {
            return { success: true, user: { uid: "mock-uid-123", email: email } };
        } else if (email === "<EMAIL>" && password === "password123") {
            throw new Error("Invalid credentials. Please try again.");
        } else {
            throw new Error("Invalid email or password.");
        }
    }
};

// --- Login Page Component ---
export default function LoginPage() {
    // REMOVED: console.log("NEW LOGIN PAGE WITH 30x30 LOGO RENDERING");
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [emailError, setEmailError] = useState<string | null>(null);
    const [passwordError, setPasswordError] = useState<string | null>(null);

    const validateEmail = (emailToValidate: string) => {
        if (!emailToValidate) {
            setEmailError("Email is required.");
            return false;
        }
        // Basic email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailToValidate)) {
            setEmailError("Invalid email format.");
            return false;
        }
        setEmailError(null);
        return true;
    };

    const validatePassword = (passwordToValidate: string) => {
        if (!passwordToValidate) {
            setPasswordError("Password is required.");
            return false;
        }
        if (passwordToValidate.length < 8) {
            setPasswordError("Password must be at least 8 characters long.");
            return false;
        }
        setPasswordError(null);
        return true;
    };

    const handleLogin = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        setError(null);

        try {
            await mockAuthService.login(email, password);
            toast.success('Logged in successfully!');
            // Navigation will be handled by the useEffect above when user state changes
        } catch (err: any) {
            const errorMessage = err.message || "Failed to sign in. Please try again.";
            setError(errorMessage);
            toast.error(errorMessage);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-background p-4 font-sans">
            <Card className="w-full max-w-md shadow-sm bg-card rounded-xl border">
                {/* --- UPDATED: Header with text logo and adjusted padding --- */}
                <CardHeader className="text-center pt-12 pb-2"> {/* UPDATED padding */}
                    {/* REMOVED: img tag */}
                    <CardTitle className="text-2xl font-semibold text-center text-primary mb-8">DRCR</CardTitle>
                </CardHeader>
                <CardContent className="px-8 pt-4 pb-8"> {/* UPDATED padding */}
                    <form onSubmit={handleLogin} className="space-y-8"> {/* UPDATED spacing */}
                        {error && (
                            <Alert variant="destructive">
                                <AlertCircle className="h-4 w-4" />
                                <AlertTitle>Login Failed</AlertTitle>
                                <AlertDescription>{error}</AlertDescription>
                            </Alert>
                        )}
                        <div className="space-y-2">
                            <Label htmlFor="email" className="text-base font-medium text-foreground">Email Address</Label>
                            <div className="relative">
                                <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="email"
                                    type="email"
                                    placeholder="<EMAIL>"
                                    value={email}
                                    onChange={(e) => { setEmail(e.target.value); if (emailError) validateEmail(e.target.value); }}
                                    onBlur={() => validateEmail(email)}
                                    className={`pl-10 h-10 text-base ${emailError ? 'border-destructive focus:ring-destructive' : 'border-border focus:ring-primary'}`}
                                    aria-invalid={!!emailError}
                                    aria-describedby="email-error"
                                    autoComplete="email"
                                />
                            </div>
                            <div className="h-5 mt-1">
                                {emailError && <p id="email-error" className="text-xs text-destructive m-0">{emailError}</p>}
                            </div>
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="password" className="text-base font-medium text-foreground">Password</Label>
                            <div className="relative">
                                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="password"
                                    type="password"
                                    placeholder="••••••••"
                                    value={password}
                                    onChange={(e) => { setPassword(e.target.value); if (passwordError) validatePassword(e.target.value);}}
                                    onBlur={() => validatePassword(password)}
                                    className={`pl-10 h-10 text-base ${passwordError ? 'border-destructive focus:ring-destructive' : 'border-border focus:ring-primary'}`}
                                    aria-invalid={!!passwordError}
                                    aria-describedby="password-error"
                                    autoComplete="current-password"
                                />
                            </div>
                            <div className="h-5 mt-1">
                                {passwordError && <p id="password-error" className="text-xs text-destructive m-0">{passwordError}</p>}
                            </div>
                        </div>
                        <Button
                            type="submit"
                            className="w-full h-10 text-base font-medium flex items-center justify-center gap-2 rounded-md disabled:opacity-70 disabled:cursor-not-allowed transition-colors"
                            disabled={isLoading}
                        >
                            {isLoading ? (
                                <Loader2 className="h-5 w-5 animate-spin" />
                            ) : (
                                <ArrowRight className="h-5 w-5" />
                            )}
                            {isLoading ? 'Signing In...' : 'Sign In'}
                        </Button>
                    </form>
                </CardContent>
                <CardFooter className="flex flex-col items-center text-xs text-muted-foreground pt-4 pb-6">
                    <p>&copy; {new Date().getFullYear()} DRCR Labs. All rights reserved.</p>
                </CardFooter>
            </Card>
        </div>
    );
}

// --- Example App Wrapper (for potential previewing, if environment supports it) ---
// In a real project, this LoginPage component would be part of your routing setup.
// export default function App() {
//     return <LoginPage />;
// }
