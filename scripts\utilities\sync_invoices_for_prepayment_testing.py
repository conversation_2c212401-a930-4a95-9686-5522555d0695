#!/usr/bin/env python3
"""
Simple script to sync Xero invoices for prepayment testing.
Uses the existing pub/sub message approach from process_pubsub_message.py
but specifically configured for invoice syncing.

Usage:
    python sync_invoices_for_prepayment_testing.py
"""

import os
import sys
import json
import base64
import asyncio
from datetime import datetime
from typing import Dict, Any

# Add project root to path
project_root = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from dotenv import load_dotenv
load_dotenv()

# Configuration - using existing entity from check_sync_data.py
ENTITY_ID = '8ead108d-f6a2-41e4-b2a8-962024248a66'  # Xero tenant ID
CLIENT_ID = 'c8cc6cf1-920c-497e-b2cd-cef4cccf4005'  # Our client ID

# Invoice sync message - specifically for prepayment testing
INVOICE_SYNC_MESSAGE = {
    "platformOrgId": ENTITY_ID,
    "tenantId": CLIENT_ID, 
    "syncJobId": f"prepayment-test-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
    "endpoints": ["Invoices"],
    "forceFullSyncEndpoints": ["Invoices"],
    "targetDate": datetime.now().strftime('%Y-%m-%d'),
    "reason": "prepayment_testing"
}

def create_pubsub_event(message_data: Dict[str, Any]) -> Dict[str, Any]:
    """Create a pub/sub event structure from message data."""
    message_json = json.dumps(message_data)
    message_bytes = message_json.encode('utf-8')
    message_b64 = base64.b64encode(message_bytes).decode('utf-8')
    
    return {
        "data": message_b64,
        "messageId": f"test-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
        "publishTime": datetime.utcnow().isoformat() + "Z"
    }

async def sync_invoices_locally():
    """Sync invoices locally using the cloud function logic."""
    try:
        print("🚀 DRCR Prepayment Testing - Invoice Sync")
        print("=" * 50)
        print(f"Entity ID: {ENTITY_ID}")
        print(f"Client ID: {CLIENT_ID}")
        print("=" * 50)
        
        # Import the cloud function
        sys.path.insert(0, os.path.join(project_root, "cloud_functions", "xero_sync_consumer"))
        from main import xero_sync_consumer
        
        # Create the event structure
        event = create_pubsub_event(INVOICE_SYNC_MESSAGE)
        
        # Create a simple context object
        class SimpleContext:
            def __init__(self):
                self.timestamp = datetime.utcnow().isoformat() + "Z"
                self.event_id = f"prepayment-test-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
                self.event_type = "google.cloud.pubsub.topic.v1.messagePublished"
        
        context = SimpleContext()
        
        print(f"📤 Sync message: {json.dumps(INVOICE_SYNC_MESSAGE, indent=2)}")
        print("\n🔄 Starting invoice sync...")
        
        # Process the message
        await xero_sync_consumer(event, context)
        
        print("\n✅ Invoice sync completed successfully!")
        print("\n📊 Next steps:")
        print("   1. Check the logs above for any prepayments identified")
        print("   2. Run: python check_sync_data.py")
        print("   3. Look for transactions with '_system_isPrepaymentByLLM' or '_system_isPrepaymentByGLCoding'")
        print("   4. Check for generated amortization schedules")
        
        return True
        
    except Exception as e:
        print(f"❌ Error syncing invoices: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main function."""
    success = await sync_invoices_locally()
    
    if success:
        print("\n🎯 Invoice sync for prepayment testing completed!")
        print("\nTo check the results, you can run:")
        print("   python check_sync_data.py")
        print("   python pull_xero_invoices_for_testing.py")
    else:
        print("\n❌ Invoice sync failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 