// Audit Log Types

export enum AuditEventCategory {
  AUTHENTICATION = "authentication",
  DATA_SYNC = "data_sync",
  USER_ACTION = "user_action",
  API_CALL = "api_call",
  SYSTEM_EVENT = "system_event",
  ERROR_EVENT = "error_event",
  SECURITY_EVENT = "security_event",
  CONFIGURATION = "configuration"
}

export enum AuditEventStatus {
  SUCCESS = "success",
  FAILED = "failed",
  IN_PROGRESS = "in_progress",
  WARNING = "warning",
  COMPLETED = "completed",
  CANCELLED = "cancelled"
}

export interface AuditLogEntry {
  audit_id: string;
  event_type: string;
  event_category: string; // Use string instead of enum since backend uses "SYNC" not "data_sync"
  status: string; // Use string instead of enum since backend uses "SUCCESS" not "success"
  timestamp: string;
  user_id?: string;
  user_email?: string;
  client_id?: string;
  entity_id?: string;
  firm_id?: string;
  source_ip?: string;
  user_agent?: string;
  request_id?: string;
  session_id?: string;
  duration_ms?: number;
  details?: Record<string, any>;
  error_details?: {
    error_code?: string;
    error_message?: string;
    stack_trace?: string;
  };
  metadata?: Record<string, any>;
}

export interface AuditLogSummary {
  audit_id: string;
  event_type: string;
  event_category: string;
  status: string;
  timestamp: string;
  user_email?: string;
  duration_ms?: number;
  entity_name?: string;
  client_name?: string;
}

export interface AuditLogFilters {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  client_id?: string;
  entity_id?: string;
  user_id?: string;
  firm_id?: string;
  event_category?: string;
  event_type?: string;
  status?: string;
  date_from?: string; // ISO date string
  date_to?: string; // ISO date string
  min_duration_ms?: number;
  max_duration_ms?: number;
  source_ip?: string;
  error_only?: boolean;
  has_details?: boolean;
  search_text?: string;
}

export interface AuditLogListResponse {
  audit_logs: AuditLogSummary[];
  pagination: {
    current_page: number;
    page_size: number;
    total_items: number;
    total_pages: number;
    has_next: boolean;
    has_previous: boolean;
    oldest_entry?: string;
    newest_entry?: string;
  };
  filters_applied?: AuditLogFilters;
}

export interface AuditLogDetailResponse {
  audit_log: AuditLogEntry;
  related_entries?: AuditLogEntry[];
}

export interface AuditStatistics {
  total_events: number;
  events_by_category: Record<string, number>;
  events_by_status: Record<string, number>;
  events_by_day: Array<{
    date: string;
    count: number;
  }>;
  most_common_events: Array<{
    event_type: string;
    count: number;
  }>;
  error_rate_percentage: number;
  average_duration_ms: number;
  unique_users: number;
  unique_entities: number;
}

export interface AuditLogStatsResponse {
  statistics: AuditStatistics;
  generated_at: string;
  time_range?: {
    from?: string;
    to?: string;
  };
}