"""
Prepayment Release Detector Service

Identifies manual journals (MJs) that release prepayment assets into the P&L 
using sophisticated pattern matching and scoring algorithms.

Detection Features:
- GL movement analysis (asset credits → expense debits)
- Keyword detection in journal narratives
- Period-end timing analysis
- Chain detection for linked initial deferral journals
- Schedule pattern recognition for recurring releases
- Confidence scoring with configurable thresholds

Service Methods:
- run_for_entity(): Main detection logic for scheduled runs
- preview_match(): Read-only matching for API previews
- create_synthetic_schedule(): Creates schedules for confirmed matches
"""
import logging
import re
import uuid
from datetime import datetime, timezone, timedelta
from calendar import monthrange
from typing import Dict, Any, List, Optional, Set, Tuple
from google.cloud import firestore

from rest_api.utils.field_access import get_field, get_account_code, get_journal_lines
from rest_api.utils.bill_matcher import BillMatcher, MatchResult

# Compiled regex for performance
KEYWORD_PATTERN = re.compile(r"(prepayment|amorti[sz]ation|deferred.*release)", re.IGNORECASE)

# Pre-compiled regex for narration normalization (performance optimization)
NARRATION_NORMALIZE_PATTERN = re.compile(
    r'\b(?:'
    r'(?:jan|january|feb|february|mar|march|apr|april|may|jun|june|jul|july|aug|august|sep|sept|september|oct|october|nov|november|dec|december)\.?'  # Months with optional dot
    r'(?:\s*[-–—]\s*(?:jan|january|feb|february|mar|march|apr|april|may|jun|june|jul|july|aug|august|sep|sept|september|oct|october|nov|november|dec|december)\.?)?'  # Optional range
    r'\s+\d{2,4}'  # Year
    r'|'
    r'(?:jan|january|feb|february|mar|march|apr|april|may|jun|june|jul|july|aug|august|sep|sept|september|oct|october|nov|november|dec|december)\.?'  # Single month
    r'\s+to\s+'
    r'(?:jan|january|feb|february|mar|march|apr|april|may|jun|june|jul|july|aug|august|sep|sept|september|oct|october|nov|november|dec|december)\.?'  # Month range
    r'\s+\d{2,4}'  # Year
    r'|'
    r'(?:[1-4]q|q[1-4]|fy|h[1-2])\s*\d{2,4}'  # Quarters (Q1/1Q), fiscal years, half-years
    r'|'
    r'\d{1,2}[-–—]\d{1,2}\s+\d{2,4}'  # Numeric month ranges: "04–07 2025"
    r'|'
    r'\d{4}(?:-\d{2})?'  # ISO-style years/months (2025, 2025-04)
    r'|'
    r'\d{2}/\d{4}'  # MM/YYYY format
    r'|'
    r'\b\d{4}\b'  # Standalone years
    r')\b',
    re.IGNORECASE
)

# Reference ID extraction patterns for chain detection (order matters - most specific first)
REFERENCE_PATTERNS = [
    re.compile(r"\b(INLAS[A-Z0-9]{10,})\b", re.IGNORECASE),  # LastPass-specific with word boundaries
    re.compile(r"\b(?:REF|ID|Reference)[\s:\-]+([A-Z0-9]{8,})\b", re.IGNORECASE),  # REF: ABC123 with boundaries
]

# Import shared configuration
from ..config.detector_config import (
    CONFIDENCE_HIGH_THRESHOLD,
    CONFIDENCE_MEDIUM_THRESHOLD,
    CHAIN_LOOKBACK_MONTHS,
    AMOUNT_TOLERANCE_PERCENT,
    BILL_MATCH_WINDOW_DAYS,
    MAX_BILL_LINK_DOCS,
    DEFAULT_PAGE_SIZE,
    MAX_PREVIEW_CANDIDATES,
    MIN_PATTERN_MONTHS,
    DEFAULT_JOURNAL_LOOKBACK_MONTHS,
    MAX_JOURNAL_LOOKBACK_MONTHS,
    DEFAULT_FUTURE_JOURNAL_DAYS,
    MAX_FUTURE_JOURNAL_DAYS,
    JOURNAL_QUERY_LIMIT,
    PREPAYMENT_RELEASES_DETECTED_COLLECTION,
    MANUAL_JOURNALS_COLLECTION,
    AMORTIZATION_SCHEDULES_COLLECTION,
    ENTITY_SETTINGS_COLLECTION,
    ENTITIES_COLLECTION,
    ACCOUNTS_COLLECTION,
    DETECTION_ALGORITHM_VERSION,
    BILL_MATCHER_MIN_CONFIDENCE,
    NARRATION_SIMILARITY_HIGH_THRESHOLD,
    NARRATION_SIMILARITY_MEDIUM_THRESHOLD,
    NARRATION_SIMILARITY_MIN_LENGTH,
    AMOUNT_SIMILARITY_TOLERANCE,
    PERIOD_ADJACENCY_DAYS,
    MIN_MONTHLY_GAP_DAYS,
    MAX_MONTHLY_GAP_DAYS,
)

logger = logging.getLogger(__name__)


class PrepaymentReleaseDetectorService:
    """
    Service for detecting manual journals that release prepayment assets into P&L.
    
    Implements a multi-factor scoring system that analyzes:
    1. GL movements (asset credits + expense debits)
    2. Keywords in journal narratives  
    3. Period-end timing patterns
    4. Chain detection for related journals
    5. Schedule pattern recognition
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.bill_matcher = BillMatcher(
            name_weight=0.4,
            account_weight=0.3,
            amount_weight=0.2,
            date_weight=0.1,
            min_confidence=BILL_MATCHER_MIN_CONFIDENCE,
            max_days_gap=545  # ~18 months
        )
        self.logger.info(f"BillMatcher initialized with min_confidence: {BILL_MATCHER_MIN_CONFIDENCE}")
    
    async def run_for_entity(self, entity_id: str, db: firestore.AsyncClient) -> int:
        """
        Run prepayment release detection for a specific entity.
        
        Implements the complete detection algorithm:
        1. Load entity configuration (asset codes, expense codes)
        2. Find candidate journals (asset account credits)
        3. Extract features and calculate scores
        4. Perform chain detection and schedule pattern analysis
        5. Persist high/medium confidence detections
        6. Link detections to originating bills
        
        Args:
            entity_id: Entity to process
            db: Firestore async client
            
        Returns:
            Number of new detections created
        """
        try:
            # TEMPORARY: ERROR-level log to ensure visibility in Google Cloud
            self.logger.info(f"DETECTOR: Starting prepayment release detection for entity {entity_id}")
            self.logger.info(f"Starting prepayment release detection for entity {entity_id}")
            
            # Step 0: Check for double-run protection
            sync_jobs_ref = db.collection("SYNC_JOBS").where(
                filter=firestore.FieldFilter("entity_id", "==", entity_id)
            ).order_by("created_at", direction=firestore.Query.DESCENDING).limit(1)
            
            async for job_doc in sync_jobs_ref.stream():
                job_data = job_doc.to_dict()
                if job_data.get("detector_linking_completed"):
                    self.logger.info(f"Detector linking already completed for entity {entity_id}, skipping")
                    return 0
                break
            
            # Step 1: Load per-entity configuration
            self.logger.debug(f"DETECTOR: About to load entity config for {entity_id}")
            asset_codes, expense_codes, lookback_months, future_days = await self._load_entity_config(entity_id, db)
            self.logger.debug(f"DETECTOR: Loaded config - {len(asset_codes)} asset codes, {len(expense_codes)} expense codes")
            self.logger.debug(f"DETECTOR: Asset codes are: {list(asset_codes)}")
            
            if not asset_codes:
                self.logger.warning(f"Entity {entity_id} has no prepayment asset codes configured, skipping")
                return 0
            
            # Expense codes may be missing if the entity's CoA has not been cached yet.
            # We still want to run detection because the decisive signal is the asset-credit;
            # any debit line will be treated as a P&L hit when `expense_codes` is empty.
            if not expense_codes:
                self.logger.debug(
                    f"Entity {entity_id} has 0 expense codes cached – will treat any debit as P&L"
                )
                
            self.logger.info(f"Entity {entity_id}: {len(asset_codes)} asset codes, {len(expense_codes)} expense codes")
            
            # Step 2: Find candidate journals (any MJ with at least one asset credit)
            self.logger.debug(f"DETECTOR: About to find candidate journals for {entity_id}")
            candidate_journals = await self._find_candidate_journals(entity_id, asset_codes, lookback_months, future_days, db)
            self.logger.info(f"DETECTOR: Found {len(candidate_journals)} candidate journals")
            
            if not candidate_journals:
                self.logger.info(f"No candidate journals found for entity {entity_id}")
                return 0
                
            self.logger.info(f"Found {len(candidate_journals)} candidate journals for entity {entity_id}")
            
            # Step 3: Extract features and calculate confidence scores
            detections = []
            analyzed_count = 0
            for journal in candidate_journals:
                try:
                    analyzed_count += 1
                    detection = await self._analyze_journal(journal, asset_codes, expense_codes, entity_id, db)
                    if detection:
                        confidence = detection.get("detection_confidence")
                        self.logger.debug(f"Journal {journal.get('journal_id')} has {confidence} confidence")
                        if confidence in ["high", "medium"]:
                            detections.append(detection)
                    else:
                        self.logger.debug(f"Journal {journal.get('journal_id')} returned no detection")
                except Exception as e_journal:
                    journal_id = journal.get("journal_id", "unknown")
                    self.logger.error(f"Error analyzing journal {journal_id}: {e_journal}", exc_info=True)
                    continue
            
            self.logger.info(f"Entity {entity_id}: analyzed {analyzed_count} journals, {len(detections)} high/medium confidence detections")
            
            # Step 4: Chain detection upgrade (look for initial deferral journals)
            await self._perform_chain_detection(detections, entity_id, asset_codes, expense_codes, db)
            
            # Step 5: Reference-based chain detection (for amortization schedules)
            await self._detect_reference_based_chains(detections, entity_id, db)
            
            # Step 6: Schedule pattern detection
            await self._detect_schedule_patterns(detections, db)
            
            # Step 7: Persist detections (only high/medium confidence)
            self.logger.debug(f"About to persist {len(detections)} detections for entity {entity_id}")
            persisted_count = await self._persist_detections(detections, db)
            
            # Step 8: Link detections to originating bills
            if persisted_count > 0:
                self.logger.info(f"DEBUG: Starting bill linking for {persisted_count} detections")
                linked_count = await self._link_detections_to_bills(entity_id, detections, asset_codes, expense_codes, db)
                self.logger.info(f"Linked {linked_count}/{persisted_count} detections to originating transactions")
            else:
                self.logger.info(f"DEBUG: No detections to link (persisted_count = {persisted_count})")
            
            # Step 9: Create synthetic schedules for high-confidence detections with bill links
            if persisted_count > 0:
                self.logger.info(f"Starting automatic schedule creation for high-confidence detections")
                schedules_created = await self._create_schedules_for_high_confidence_detections(entity_id, detections, db)
                self.logger.info(f"Created {schedules_created} synthetic schedules for high-confidence detections")
            
            # Mark completion in sync jobs to prevent double-runs
            if persisted_count > 0:
                try:
                    # Update most recent sync job
                    async for job_doc in sync_jobs_ref.stream():
                        job_ref = db.collection("SYNC_JOBS").document(job_doc.id)
                        await job_ref.update({"detector_linking_completed": True})
                        break
                except Exception as e:
                    self.logger.warning(f"Failed to mark detector completion for entity {entity_id}: {e}")
            
            # Log final summary with detection vs persistence comparison
            if persisted_count == len(detections):
                self.logger.info(f"Completed detection for entity {entity_id}: {persisted_count} detections persisted")
            else:
                self.logger.warning(f"Completed detection for entity {entity_id}: {persisted_count}/{len(detections)} detections persisted (some failed)")
            return persisted_count
            
        except Exception as e:
            self.logger.error(f"DETECTOR CRASHED: Error in prepayment release detection for entity {entity_id}: {e}", exc_info=True)
            self.logger.error(f"DETECTOR CRASH TYPE: {type(e).__name__}")
            self.logger.error(f"DETECTOR CRASH ARGS: {e.args}")
            return 0
    
    async def preview_match(
        self, 
        entity_id: str, 
        journal_id: str, 
        db: firestore.AsyncClient, 
        max_candidates: int = 3
    ) -> Dict[str, Any]:
        """
        Preview prepayment release matching for a specific journal (read-only).
        
        Runs the same detection algorithm but does not persist results.
        Returns ranked candidates for UI preview.
        
        Args:
            entity_id: Entity context
            journal_id: Specific journal to analyze
            db: Firestore async client
            max_candidates: Maximum candidates to return (always 1 for single journal analysis)
            
        Returns:
            Dictionary with journal_id, entity_id, and candidates (single journal analysis)
        """
        try:
            self.logger.debug(f"Preview matching for journal {journal_id} in entity {entity_id}")
            
            # Load configuration
            asset_codes, expense_codes, lookback_months, future_days = await self._load_entity_config(entity_id, db)
            
            if not asset_codes:
                return {
                    "journal_id": journal_id,
                    "entity_id": entity_id,
                    "candidates": [],
                    "error": "Entity configuration incomplete: No prepayment asset codes configured"
                }
            
            # Expense codes may be missing if the entity's CoA has not been cached yet.
            # We still want to run detection because the decisive signal is the asset-credit;
            # any debit line will be treated as a P&L hit when `expense_codes` is empty.
            if not expense_codes:
                self.logger.debug(
                    f"Entity {entity_id} has 0 expense codes cached for preview – will treat any debit as P&L"
                )
            
            # Get the specific journal
            journal_ref = db.collection("MANUAL_JOURNALS").document(journal_id)
            journal_doc = await journal_ref.get()
            
            if not journal_doc.exists:
                return {
                    "journal_id": journal_id,
                    "entity_id": entity_id,
                    "candidates": [],
                    "error": "Journal not found"
                }
            
            journal_data = journal_doc.to_dict()
            journal_data["journal_id"] = journal_id
            
            # Analyze this specific journal
            detection = await self._analyze_journal(journal_data, asset_codes, expense_codes, entity_id, db)
            
            if not detection:
                return {
                    "journal_id": journal_id,
                    "entity_id": entity_id,
                    "candidates": []
                }
            
            # Perform chain detection for this single journal (for accurate confidence score)
            await self._perform_chain_detection(
                [detection],
                entity_id,
                asset_codes,
                expense_codes,
                db
            )
            
            # Recalculate confidence score and level after potential chain upgrade
            final_confidence_score = self._calculate_confidence_score(detection["flags"])
            if final_confidence_score >= CONFIDENCE_HIGH_THRESHOLD:
                detection["detection_confidence"] = "high"
            elif final_confidence_score >= CONFIDENCE_MEDIUM_THRESHOLD:
                detection["detection_confidence"] = "medium"
            else:
                detection["detection_confidence"] = "low"
            
            # Format as candidate for preview
            candidate = {
                "journal_id": journal_id,
                "confidence_score": final_confidence_score,
                "confidence_level": detection.get("detection_confidence", "low"),
                "match_factors": detection["flags"],
                "asset_account_code": detection.get("asset_account_code"),
                "expense_account_code": detection.get("expense_account_code"),
                "amount": detection.get("amount"),
                "journal_date": detection.get("journal_date").isoformat() if detection.get("journal_date") else None
            }
            
            # Apply max_candidates slicing (future-proofing for when multiple candidates might be returned)
            candidates = [candidate][:max_candidates]
            
            return {
                "journal_id": journal_id,
                "entity_id": entity_id,
                "candidates": candidates
            }
            
        except Exception as e:
            self.logger.error(f"Error in preview match for journal {journal_id}: {e}", exc_info=True)
            return {
                "journal_id": journal_id,
                "entity_id": entity_id,
                "candidates": [],
                "error": str(e)
            }
    
    async def create_synthetic_schedule(
        self, 
        mj_id: str, 
        bill_id: str, 
        db: firestore.AsyncClient
    ) -> Optional[str]:
        """
        Create synthetic amortization schedule for confirmed MJ→Bill match.
        
        Creates a schedule with:
        - detection_method = "external_mj"
        - status = "posted" 
        - Single monthlyEntry reflecting the MJ (already executed)
        - Links to both the MJ and original bill
        
        Args:
            mj_id: Manual journal ID
            bill_id: Matched bill/transaction ID
            db: Firestore async client
            
        Returns:
            Schedule ID if created, None if failed or already exists
        """
        try:
            self.logger.info(f"Creating synthetic schedule for MJ {mj_id} → Bill {bill_id}")
            
            # Idempotency check: ensure no schedule already exists for this MJ
            # Check PREPAYMENT_RELEASES_DETECTED directly by document ID
            detection_ref = db.collection(PREPAYMENT_RELEASES_DETECTED_COLLECTION).document(mj_id)
            detection_doc = await detection_ref.get()
            
            if detection_doc.exists:
                detection_data = detection_doc.to_dict()
                existing_schedule_id = detection_data.get("synthetic_schedule_id")
                if existing_schedule_id:
                    self.logger.info(f"Synthetic schedule already exists for MJ {mj_id}: {existing_schedule_id}")
                    return existing_schedule_id
            
            # Get MJ data
            mj_ref = db.collection(MANUAL_JOURNALS_COLLECTION).document(mj_id)
            mj_doc = await mj_ref.get()
            
            if not mj_doc.exists:
                self.logger.error(f"Manual journal {mj_id} not found")
                return None
                
            mj_data = mj_doc.to_dict()
            
            # Get bill data
            bill_ref = db.collection("TRANSACTIONS").document(bill_id)
            bill_doc = await bill_ref.get()
            
            if not bill_doc.exists:
                self.logger.error(f"Transaction {bill_id} not found")
                return None
                
            bill_data = bill_doc.to_dict()
            
            # Extract key data from MJ
            journal_date = mj_data.get("journalDate")
            if isinstance(journal_date, str):
                journal_date = datetime.fromisoformat(journal_date.replace('Z', '+00:00'))
            elif not isinstance(journal_date, datetime):
                journal_date = datetime.now(timezone.utc)
                
            entity_id = mj_data.get("entity_id")
            client_id = mj_data.get("client_id")
            
            # Handle both Xero format and internal format for journal lines
            lines = get_journal_lines(mj_data)
            total_amount = 0
            
            # Calculate total amount - handle both debit/credit and LineAmount formats
            for line in lines:
                # Try internal format first (debit/credit)
                if "debit" in line or "credit" in line:
                    debit = line.get("debit", 0)
                    if debit > 0:
                        total_amount += debit
                else:
                    # Xero format (LineAmount) - only positive amounts (debits)
                    line_amount = line.get("LineAmount", 0)
                    if line_amount > 0:
                        total_amount += line_amount
            
            # Extract asset and expense accounts from MJ lines
            asset_codes, expense_codes, _, _ = await self._load_entity_config(entity_id, db)
            
            # Safety check: asset codes cannot be empty (essential for prepayment detection)
            if not asset_codes:
                self.logger.error(f"No asset codes configured for entity {entity_id} - cannot create synthetic schedule for MJ {mj_id}")
                return None
            
            asset_account_code = None
            expense_account_code = None
            
            for line in lines:
                account_code = get_account_code(line)
                
                # Handle both data formats
                if "debit" in line or "credit" in line:
                    # Internal format: credit > 0 for asset reduction, debit > 0 for expense
                    credit = line.get("credit", 0)
                    debit = line.get("debit", 0)
                    is_credit = credit > 0
                    is_debit = debit > 0
                else:
                    # Xero format: negative LineAmount = credit, positive = debit
                    line_amount = line.get("LineAmount", 0)
                    is_credit = line_amount < 0  # Negative = credit (asset reduction)
                    is_debit = line_amount > 0   # Positive = debit (expense)
                
                # Look for asset account with credit (asset reduction)
                if account_code in asset_codes and is_credit:
                    asset_account_code = account_code
                # Look for expense account with debit (expense increase)
                elif is_debit:
                    # If expense codes are configured, check membership; otherwise accept any debit as expense
                    if not expense_codes or account_code in expense_codes:
                        if expense_account_code is None:
                            # Take first qualifying expense account (avoid overwriting)
                            expense_account_code = account_code
                            if not expense_codes:
                                self.logger.info(f"Using fallback mode: selected expense account '{account_code}' (no expense codes configured)")
                        elif expense_account_code != account_code:
                            # Multiple expense accounts found - log warning but keep first
                            self.logger.warning(f"Multiple expense accounts found in MJ {mj_id}: '{expense_account_code}' (kept) and '{account_code}' (ignored)")
            
            if not asset_account_code or not expense_account_code:
                # Enhanced debugging: show what we found vs what we expected
                found_accounts = [get_account_code(line) for line in lines]
                self.logger.error(f"Could not determine asset/expense accounts for MJ {mj_id}")
                self.logger.error(f"  Found accounts in MJ: {found_accounts}")
                self.logger.error(f"  Expected asset codes: {list(asset_codes)}")
                self.logger.error(f"  Expected expense codes: {list(expense_codes) if expense_codes else 'ANY (fallback mode)'}")
                self.logger.error(f"  Asset account found: {asset_account_code}")
                self.logger.error(f"  Expense account found: {expense_account_code}")
                
                # Show line details for debugging
                for i, line in enumerate(lines):
                    line_amount = line.get("LineAmount", 0)
                    account_code = get_account_code(line)
                    self.logger.error(f"  Line {i}: AccountCode={account_code}, LineAmount={line_amount}")
                
                return None
            
            # Create synthetic schedule
            schedule_id = str(uuid.uuid4())
            
            schedule_data = {
                "schedule_id": schedule_id,
                "transaction_id": bill_id,
                "line_item_id": None,  # No specific line item for external MJ
                "entity_id": entity_id,
                "client_id": client_id,
                "status": "posted",  # Already executed
                "detection_method": "external_mj",
                "_system_linkedManualJournalID": mj_id,
                
                # Amount and period info (single period = journal date)
                "originalAmount": total_amount,
                "amortizationStartDate": journal_date,
                "amortizationEndDate": journal_date,
                "numberOfPeriods": 1,
                "periodType": "monthly",
                
                # Account codes
                "amortizationAccountCode": asset_account_code,
                "expenseAccountCode": expense_account_code,
                
                # Description
                "description": f"External release from MJ: {mj_data.get('narration', 'Manual Journal')}",
                
                # Single monthly entry (already posted) - no user/timestamp fields in array
                "monthlyEntries": [{
                    "monthDate": journal_date,
                    "amount": total_amount,
                    "status": "posted",
                    "postedJournalId": mj_id,  # Reference to the MJ that executed this
                    "postedJournalLineId": None,
                    "matchConfidence": 1.0,  # Perfect match since it's the actual MJ
                    "postingError": None,
                }],
                
                # System timestamp and user fields (moved to schedule root to avoid embedded-map updates)
                "lastActionByUserId": "system_detector",
                "lastActionTimestamp": firestore.SERVER_TIMESTAMP,
                
                # System fields
                "calculation_method": "external_manual",
                "is_llm_detected": False,
                "created_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP,
            }
            
            # Save schedule
            schedule_ref = db.collection(AMORTIZATION_SCHEDULES_COLLECTION).document(schedule_id)
            await schedule_ref.set(schedule_data)
            
            # Update bill with schedule reference
            await bill_ref.update({
                "_system_amortizationScheduleIDs": firestore.ArrayUnion([schedule_id]),
                "_system_externalReleaseMJId": mj_id,
                "processing_status": "completed",
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            
            # Update detection record (direct document access)
            detection_ref = db.collection(PREPAYMENT_RELEASES_DETECTED_COLLECTION).document(mj_id)
            detection_doc = await detection_ref.get()
            
            if detection_doc.exists:
                await detection_ref.update({
                    "linked_transaction_id": bill_id,
                    "synthetic_schedule_id": schedule_id,
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
            
            self.logger.info(f"Created synthetic schedule {schedule_id} for MJ {mj_id} → Bill {bill_id}")
            return schedule_id
            
        except Exception as e:
            self.logger.error(f"Error creating synthetic schedule for MJ {mj_id}: {e}", exc_info=True)
            return None
    
    async def _create_group_schedule(
        self, 
        reference_id: str, 
        detections: List[Dict[str, Any]], 
        bill_id: str, 
        db: firestore.AsyncClient
    ) -> Optional[str]:
        """
        Create consolidated synthetic amortization schedule for a group of related manual journals.
        
        Instead of creating N schedules with 1 monthly entry each, creates 1 schedule with N monthly entries.
        This dramatically reduces document count and memory usage for amortization sequences like LastPass.
        
        Args:
            reference_id: Common reference ID linking the manual journals
            detections: List of detection objects for all MJs in the group
            bill_id: Matched originating bill/transaction ID
            db: Firestore async client
            
        Returns:
            Schedule ID if created, None if failed
        """
        try:
            if not detections:
                self.logger.warning(f"No detections provided for group schedule creation (reference: {reference_id})")
                return None
                
            self.logger.info(f"Creating consolidated group schedule for reference '{reference_id}' with {len(detections)} detections → Bill {bill_id}")
            
            # Generate deterministic schedule ID for idempotency
            schedule_id = str(uuid.uuid5(uuid.NAMESPACE_URL, f"{bill_id}:{reference_id}"))
            
            # Idempotency check: ensure no schedule already exists for this group
            schedule_ref = db.collection(AMORTIZATION_SCHEDULES_COLLECTION).document(schedule_id)
            existing_schedule_doc = await schedule_ref.get()
            
            if existing_schedule_doc.exists:
                self.logger.info(f"Group schedule already exists for reference '{reference_id}': {schedule_id}")
                return schedule_id
            
            # Sort detections chronologically for proper period bounds and entry ordering
            sorted_detections = sorted(detections, key=lambda d: d.get("journal_date") or datetime.min.replace(tzinfo=timezone.utc))
            
            # Get representative data from first detection (entity, client info should be same across group)
            first_detection = sorted_detections[0]
            entity_id = first_detection.get("entity_id")
            client_id = first_detection.get("client_id")
            asset_account_code = first_detection.get("asset_account_code")
            expense_account_code = first_detection.get("expense_account_code")
            
            # Calculate consolidated amounts and period bounds
            total_amount = sum(d.get("amount", 0) for d in sorted_detections)
            start_date = sorted_detections[0].get("journal_date")
            end_date = sorted_detections[-1].get("journal_date")
            
            # Build monthly entries from all detections in chronological order
            monthly_entries = []
            for detection in sorted_detections:
                monthly_entry = {
                    "monthDate": detection.get("journal_date"),
                    "amount": detection.get("amount", 0),
                    "status": "posted",  # All MJs are already executed
                    "postedJournalId": detection.get("journal_id"),
                    "postedJournalLineId": None,
                    "matchConfidence": 1.0,  # Perfect match since these are actual MJs
                    "postingError": None,
                }
                monthly_entries.append(monthly_entry)
            
            # Create consolidated schedule document
            schedule_data = {
                "schedule_id": schedule_id,
                "transaction_id": bill_id,
                "line_item_id": None,  # No specific line item for external MJ group
                "entity_id": entity_id,
                "client_id": client_id,
                "status": "posted",  # All entries already executed
                "detection_method": "external_mj_group",  # Distinguish from single MJ
                "_system_linkedReferenceID": reference_id,  # Track the reference that linked them
                "_system_linkedManualJournalIDs": [d.get("journal_id") for d in sorted_detections],  # All linked MJs
                
                # Consolidated amount and period info
                "originalAmount": total_amount,
                "amortizationStartDate": start_date,
                "amortizationEndDate": end_date,
                "numberOfPeriods": len(sorted_detections),
                "periodType": "monthly",
                
                # Account codes (should be consistent across group)
                "amortizationAccountCode": asset_account_code,
                "expenseAccountCode": expense_account_code,
                
                # Description
                "description": f"Consolidated amortization schedule for reference: {reference_id}",
                
                # Consolidated monthly entries
                "monthlyEntries": monthly_entries,
                
                # System timestamp and user fields
                "lastActionByUserId": "system_detector",
                "lastActionTimestamp": firestore.SERVER_TIMESTAMP,
                
                # System fields
                "calculation_method": "external_manual_group",  # Distinguish from single manual
                "is_llm_detected": False,
                "created_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP,
            }
            
            # Save consolidated schedule with atomic batch operations
            batch = db.batch()
            
            # Create the schedule
            batch.set(schedule_ref, schedule_data)
            
            # Update all detection documents with the same schedule ID
            for detection in sorted_detections:
                journal_id = detection.get("journal_id")
                detection_ref = db.collection(PREPAYMENT_RELEASES_DETECTED_COLLECTION).document(journal_id)
                batch.update(detection_ref, {
                    "linked_transaction_id": bill_id,
                    "synthetic_schedule_id": schedule_id,
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
            
            # Update bill with schedule reference (only append once)
            bill_ref = db.collection("TRANSACTIONS").document(bill_id)
            batch.update(bill_ref, {
                "_system_amortizationScheduleIDs": firestore.ArrayUnion([schedule_id]),
                "_system_externalReleaseRefId": reference_id,  # Track which reference group created this
                "processing_status": "completed",
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            
            # Commit all operations atomically
            await batch.commit()
            
            self.logger.info(f"Created consolidated schedule {schedule_id} for reference '{reference_id}' with {len(monthly_entries)} monthly entries")
            return schedule_id
            
        except Exception as e:
            self.logger.error(f"Error creating group schedule for reference '{reference_id}': {e}", exc_info=True)
            return None
    
    # Private helper methods
    
    async def _load_entity_config(self, entity_id: str, db: firestore.AsyncClient) -> Tuple[Set[str], Set[str], int, int]:
        """Load asset codes, expense codes, lookback period, and future window for entity with fallback."""
        try:
            asset_codes = set()
            lookback_months = DEFAULT_JOURNAL_LOOKBACK_MONTHS
            future_days = DEFAULT_FUTURE_JOURNAL_DAYS
            
            # Primary: Try ENTITY_SETTINGS collection
            entity_settings_ref = db.collection(ENTITY_SETTINGS_COLLECTION).document(entity_id)
            entity_settings_doc = await entity_settings_ref.get()
            
            if entity_settings_doc.exists:
                settings = entity_settings_doc.to_dict()
                asset_codes = set(settings.get("prepayment_asset_account_codes", []))
                # Get custom lookback period if configured
                custom_lookback = settings.get("journal_lookback_months", DEFAULT_JOURNAL_LOOKBACK_MONTHS)
                lookback_months = min(custom_lookback, MAX_JOURNAL_LOOKBACK_MONTHS)
                # Get custom future window if configured
                custom_future = settings.get("future_journal_days", DEFAULT_FUTURE_JOURNAL_DAYS)
                future_days = min(custom_future, MAX_FUTURE_JOURNAL_DAYS)
                self.logger.debug(f"Loaded {len(asset_codes)} asset codes from ENTITY_SETTINGS for {entity_id}")
            
            # Fallback: Try ENTITIES/{id}.settings pattern
            if not asset_codes:
                entity_ref = db.collection(ENTITIES_COLLECTION).document(entity_id)
                entity_doc = await entity_ref.get()
                
                if entity_doc.exists:
                    entity_data = entity_doc.to_dict()
                    settings = entity_data.get("settings", {})
                    # Map from legacy field names - try multiple possible field names
                    legacy_asset_codes = (
                        settings.get("prepayment_asset_account_codes", []) or  # New format
                        settings.get("prepayment_asset_accounts", []) or      # Legacy format
                        settings.get("prepaymentAssetAccountCodes", [])       # Alternative format
                    )
                    asset_codes = set(legacy_asset_codes)
                    # Get custom lookback period if configured
                    custom_lookback = settings.get("journal_lookback_months", DEFAULT_JOURNAL_LOOKBACK_MONTHS)
                    lookback_months = min(custom_lookback, MAX_JOURNAL_LOOKBACK_MONTHS)
                    # Get custom future window if configured
                    custom_future = settings.get("future_journal_days", DEFAULT_FUTURE_JOURNAL_DAYS)
                    future_days = min(custom_future, MAX_FUTURE_JOURNAL_DAYS)
                    if asset_codes:
                        self.logger.debug(f"Loaded {len(asset_codes)} asset codes from ENTITIES.settings fallback for {entity_id}")
            
            if not asset_codes:
                self.logger.warning(f"No prepayment asset codes found for entity {entity_id} in either ENTITY_SETTINGS or ENTITIES.settings")
            
            # Get all account codes (we don't need to filter by type - any non-asset debit is a P&L hit)
            expense_codes = set()
            accounts_query = db.collection(ACCOUNTS_COLLECTION).where(
                filter=firestore.FieldFilter("entity_id", "==", entity_id)
            )
            
            async for account_doc in accounts_query.stream():
                account_data = account_doc.to_dict()
                account_code = get_account_code(account_data)
                if account_code and account_code not in asset_codes:  # Exclude asset accounts
                    expense_codes.add(account_code)
            
            self.logger.debug(f"Loaded {len(expense_codes)} expense codes for entity {entity_id}, lookback: {lookback_months} months, future: {future_days} days")
            return asset_codes, expense_codes, lookback_months, future_days
            
        except Exception as e:
            self.logger.error(f"Error loading entity config for {entity_id}: {e}")
            return set(), set(), DEFAULT_JOURNAL_LOOKBACK_MONTHS, DEFAULT_FUTURE_JOURNAL_DAYS
    
    async def _find_candidate_journals(
        self, 
        entity_id: str, 
        asset_codes: Set[str], 
        lookback_months: int,
        future_days: int,
        db: firestore.AsyncClient
    ) -> List[Dict[str, Any]]:
        """Find manual journals with credits to asset accounts.
        
        PERFORMANCE NOTE: This currently does O(N) filtering in Python. 
        For production optimization, consider:
        1. Adding helper fields (_assetCodesCredited, _expenseCodesDebited)
        2. Creating compound indexes for efficient querying
        3. Using materialized views for frequent lookups
        
        Current implementation limits to recent journals for performance.
        """
        candidates = []
        
        try:
            # Date range: lookback_months in the past + configurable future window
            now = datetime.now(timezone.utc)
            lookback_date = now - timedelta(days=lookback_months*30)
            future_date = now + timedelta(days=future_days)
            
            # BRUTE FORCE TEST: Check if we can see ANY documents in the collection
            self.logger.debug(f"DETECTOR: Testing collection access for '{MANUAL_JOURNALS_COLLECTION}'")
            test_query = db.collection(MANUAL_JOURNALS_COLLECTION).limit(5)
            test_count = 0
            try:
                async for doc in test_query.stream():
                    test_count += 1
                    self.logger.debug(f"DETECTOR: Found document {doc.id}")
                self.logger.debug(f"DETECTOR: Brute force test completed - Total documents found: {test_count}")
            except Exception as brute_force_error:
                self.logger.debug(f"DETECTOR: Brute force test failed with error: {brute_force_error}")
            
            # Original query - DEBUG: Try without status filter first
            self.logger.debug(f"DETECTOR: Query params - entity_id='{entity_id}', collection='{MANUAL_JOURNALS_COLLECTION}'")
            
            # Probe status index efficiently - try common statuses (case variations)
            status_candidates = ["POSTED", "Posted", "AUTHORISED", "Authorised", "DRAFT", "Draft"]
            status_filter = None
            
            for candidate_status in status_candidates:
                probe_query = db.collection(MANUAL_JOURNALS_COLLECTION).where(
                    filter=firestore.FieldFilter("entity_id", "==", entity_id)
                ).where(
                    filter=firestore.FieldFilter("status", "==", candidate_status)
                ).limit(1)
                
                # Proper async check - get first doc if exists
                try:
                    first_doc = await probe_query.stream().__anext__()
                    if first_doc:
                        status_filter = candidate_status
                        self.logger.debug(f"Found journals with status '{candidate_status}'")
                        break
                except StopAsyncIteration:
                    # No docs with this status, try next
                    continue
            
            # Build optimized query using found status or fallback
            if status_filter:
                journals_query = db.collection(MANUAL_JOURNALS_COLLECTION).where(
                    filter=firestore.FieldFilter("entity_id", "==", entity_id)
                ).where(
                    filter=firestore.FieldFilter("status", "==", status_filter)
                ).limit(JOURNAL_QUERY_LIMIT)
            else:
                # Fallback: entity-only query (still indexed)
                self.logger.warning(f"No standard status found for entity {entity_id}, using entity-only query")
                journals_query = db.collection(MANUAL_JOURNALS_COLLECTION).where(
                    filter=firestore.FieldFilter("entity_id", "==", entity_id)
                ).limit(JOURNAL_QUERY_LIMIT)
            
            processed_count = 0
            
            # Stream and filter for asset credits (O(N) operation - see optimization notes above)
            self.logger.debug(f"DETECTOR: Starting to stream journals for entity {entity_id}")
            async for journal_doc in journals_query.stream():
                processed_count += 1
                if processed_count > JOURNAL_QUERY_LIMIT:  # Use proper configuration limit
                    self.logger.debug(f"DETECTOR: Query limit reached after processing {processed_count} docs")
                    break
                journal_data = journal_doc.to_dict()
                journal_data["journal_id"] = journal_doc.id
                self.logger.debug(f"DETECTOR: Processing journal {journal_doc.id}, status={journal_data.get('status')}")
                
                # Check if any line has credit > 0 to an asset account
                # Handle both formats: our internal format and raw Xero format
                lines = get_journal_lines(journal_data)
                
                has_asset_credit = False
                for line in lines:
                    account_code = get_account_code(line)
                    
                    # Handle credit detection - check both formats
                    credit_amount = line.get("credit", 0)
                    if credit_amount == 0:
                        # Xero format: negative LineAmount = credit
                        line_amount = float(line.get("LineAmount", 0))
                        credit_amount = abs(line_amount) if line_amount < 0 else 0
                    
                    if credit_amount > 0 and account_code in asset_codes:
                        has_asset_credit = True
                        break
                
                if has_asset_credit:
                    candidates.append(journal_data)
            
            self.logger.info(f"Processed {processed_count} journals, found {len(candidates)} candidates for entity {entity_id}")
            return candidates
            
        except Exception as e:
            self.logger.error(f"Error finding candidate journals for entity {entity_id}: {e}")
            return []
    
    async def _analyze_journal(
        self, 
        journal: Dict[str, Any], 
        asset_codes: Set[str], 
        expense_codes: Set[str],
        entity_id: str,
        db: firestore.AsyncClient
    ) -> Optional[Dict[str, Any]]:
        """Analyze a journal and extract detection features."""
        try:
            journal_id = journal.get("journal_id")
            client_id = journal.get("client_id")
            
            # Extract features
            flags = await self._extract_features(journal, asset_codes, expense_codes)
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(flags)
            
            # Determine confidence level
            if confidence_score >= CONFIDENCE_HIGH_THRESHOLD:
                confidence_level = "high"
            elif confidence_score >= CONFIDENCE_MEDIUM_THRESHOLD:
                confidence_level = "medium"
            else:
                confidence_level = "low"
            
            # Only return detections that meet minimum threshold
            if confidence_level in ["high", "medium"]:
                # Extract account codes and amount
                asset_account_code = None
                expense_account_code = None
                total_amount = 0
                
                # Handle both formats: our internal format and raw Xero format
                lines = get_journal_lines(journal)
                for line in lines:
                    # Handle both field name variations
                    account_code = get_account_code(line)
                    
                    # Handle both debit/credit format and LineAmount format
                    if "credit" in line and "debit" in line:
                        # Internal format
                        credit = line.get("credit", 0)
                        debit = line.get("debit", 0)
                    else:
                        # Xero format: negative LineAmount = credit, positive = debit
                        line_amount = line.get("LineAmount", 0)
                        if line_amount < 0:
                            credit = abs(line_amount)
                            debit = 0
                        else:
                            credit = 0
                            debit = line_amount
                    
                    if credit > 0 and account_code in asset_codes:
                        asset_account_code = account_code
                    elif debit > 0 and (
                        account_code in expense_codes or not expense_codes
                    ):
                        expense_account_code = account_code
                    
                    # Only count debit amounts to avoid double-counting balanced entries
                    if debit > 0:
                        total_amount += debit
                
                # Parse journal date
                journal_date = get_field(journal, "journal_date")
                if isinstance(journal_date, str):
                    # Handle Xero's /Date(timestamp)/ format
                    if journal_date.startswith("/Date(") and journal_date.endswith(")/"):
                        ts_match = re.search(r'(\d+)', journal_date)
                        if ts_match:
                            timestamp_ms = int(ts_match.group(1))
                            journal_date = datetime.fromtimestamp(timestamp_ms / 1000, tz=timezone.utc)
                        else:
                            journal_date = None
                    else:
                        # Standard ISO format with fallback for YYYY-MM-DD
                        try:
                            journal_date = datetime.fromisoformat(journal_date.replace('Z', '+00:00'))
                        except ValueError:
                            try:
                                # Fallback for plain YYYY-MM-DD format
                                journal_date = datetime.strptime(journal_date, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                            except ValueError:
                                self.logger.warning(f"Could not parse date format: {journal_date}")
                                journal_date = None
                
                # Extract currency from journal data or default to GBP
                currency = (journal.get("CurrencyCode") or 
                          journal.get("currency_code") or 
                          journal.get("currency") or 
                          "GBP")  # Default to GBP for UK entities
                
                return {
                    "journal_id": journal_id,
                    "entity_id": entity_id,
                    "client_id": client_id,
                    "detection_confidence": confidence_level,
                    "flags": flags,
                    "asset_account_code": asset_account_code,
                    "expense_account_code": expense_account_code,
                    "amount": total_amount,
                    "journal_date": journal_date,
                    "narration": journal.get("narration", ""),  # Include for reference ID extraction
                    "currency": currency,  # Add currency for amortization grouping
                    "schedule_pattern_id": None,  # Set later in pattern detection
                    "linked_transaction_id": None,  # Set when confirmed
                    "synthetic_schedule_id": None,  # Set when schedule created
                }
            
            return None
            
        except Exception as e:
            journal_id = journal.get("journal_id", "unknown")
            self.logger.error(f"Error analyzing journal {journal_id}: {e}")
            return None
    
    async def _extract_features(
        self, 
        journal: Dict[str, Any], 
        asset_codes: Set[str], 
        expense_codes: Set[str]
    ) -> Dict[str, Any]:
        """Extract detection features from a journal."""
        # Handle both field name variations
        narration = journal.get("narration", "") or journal.get("Narration", "")
        journal_date = get_field(journal, "journal_date")
        lines = get_journal_lines(journal)
        
        # Parse journal date if needed
        if isinstance(journal_date, str):
            # Handle Xero's /Date(timestamp)/ format
            if journal_date.startswith("/Date(") and journal_date.endswith(")/"):
                ts_match = re.search(r'(\d+)', journal_date)
                if ts_match:
                    timestamp_ms = int(ts_match.group(1))
                    journal_date = datetime.fromtimestamp(timestamp_ms / 1000, tz=timezone.utc)
                else:
                    journal_date = None
            else:
                # Standard ISO format with fallback for YYYY-MM-DD
                try:
                    journal_date = datetime.fromisoformat(journal_date.replace('Z', '+00:00'))
                except ValueError:
                    try:
                        # Fallback for plain YYYY-MM-DD format
                        journal_date = datetime.strptime(journal_date, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                    except ValueError:
                        self.logger.warning(f"Could not parse date format: {journal_date}")
                        journal_date = None
        
        # Feature extraction
        keyword_in_title = bool(KEYWORD_PATTERN.search(narration))
        
        # Handle both data formats: internal (credit/debit) and Xero (LineAmount)
        asset_credit = False
        pnl_debit = False
        
        for line in lines:
            account_code = get_account_code(line)
            
            # Handle credit detection - check both formats
            credit_amount = line.get("credit", 0)
            if credit_amount == 0:
                # Xero format: negative LineAmount = credit
                line_amount = float(line.get("LineAmount", 0))
                credit_amount = abs(line_amount) if line_amount < 0 else 0
            
            # Handle debit detection - check both formats  
            debit_amount = line.get("debit", 0)
            if debit_amount == 0:
                # Xero format: positive LineAmount = debit
                line_amount = float(line.get("LineAmount", 0))
                debit_amount = line_amount if line_amount > 0 else 0
            
            # Check for asset credit
            if credit_amount > 0 and account_code in asset_codes:
                asset_credit = True
            
            # Check for P&L debit
            if debit_amount > 0:
                if expense_codes:
                    if account_code in expense_codes:
                        pnl_debit = True
                else:
                    # No expense list available – consider any debit as a P&L indication
                    pnl_debit = True
        
        # Period end detection (1st or last day of month)
        period_end = False
        if journal_date:
            last_day = monthrange(journal_date.year, journal_date.month)[1]
            period_end = journal_date.day in {1, last_day}
        
        return {
            "keyword_in_title": keyword_in_title,
            "asset_credit": asset_credit,
            "pnl_debit": pnl_debit,
            "period_end": period_end,
            "schedule_pattern": False,  # Set later in pattern detection
            "chain_found": False,      # Set later in chain detection
        }
    
    def _calculate_confidence_score(self, flags: Dict[str, Any]) -> float:
        """Calculate confidence score based on flags."""
        score = 0
        
        # Core features (required for detection)
        if flags.get("asset_credit"):
            score += 3
        if flags.get("pnl_debit"):
            score += 3
            
        # Supporting features
        if flags.get("keyword_in_title"):
            score += 1
        if flags.get("period_end"):
            score += 1
        if flags.get("chain_found"):
            score += 1  # Added by chain detection
        if flags.get("schedule_pattern"):
            score += 0  # Pattern recognition doesn't affect confidence
            
        return score
    
    def _last_day_of_month(self, date_obj: datetime) -> int:
        """Helper to get last day of month."""
        return monthrange(date_obj.year, date_obj.month)[1]
    
    def _extract_reference_id(self, narration: str) -> Optional[str]:
        """
        Extract reference ID from journal narration for chain detection.
        
        Looks for patterns like:
        - "Amortization: LastPass - INLASAPCCO4T350887120 - 31/05/2025 - Entry 2"
        - "REF: ABC123456"
        - "Reference ID: XYZ789"
        
        Returns the first matching reference ID found.
        """
        if not narration:
            return None
            
        for pattern in REFERENCE_PATTERNS:
            match = pattern.search(narration)
            if match:
                ref_id = match.group(1).upper()
                # Filter out common false positives (dates, amounts, etc.)
                if len(ref_id) >= 8 and not ref_id.isdigit():
                    return ref_id
        
        return None
    
    async def _perform_chain_detection(
        self, 
        detections: List[Dict[str, Any]], 
        entity_id: str,
        asset_codes: Set[str],
        expense_codes: Set[str],
        db: firestore.AsyncClient
    ):
        """Look for initial deferral journals that match release journals."""
        for detection in detections:
            try:
                journal_date = detection.get("journal_date")
                amount = detection.get("amount", 0)
                asset_code = detection.get("asset_account_code")
                expense_code = detection.get("expense_account_code")
                
                if not all([journal_date, amount, asset_code, expense_code]):
                    continue
                
                # Look back up to 12 months for initial deferral
                lookback_date = journal_date - timedelta(days=CHAIN_LOOKBACK_MONTHS * 30)
                
                # Find journals with opposite movement (debit asset, credit expense)
                chain_query = db.collection(MANUAL_JOURNALS_COLLECTION).where(
                    filter=firestore.FieldFilter("entity_id", "==", entity_id)
                ).where(
                    filter=firestore.FieldFilter("journalDate", ">=", lookback_date)
                ).where(
                    filter=firestore.FieldFilter("journalDate", "<", journal_date)
                )
                
                async for chain_doc in chain_query.stream():
                    chain_data = chain_doc.to_dict()
                    chain_lines = chain_data.get("lines", [])
                    
                    # Check for opposite pattern: debit asset, credit expense
                    has_asset_debit = any(
                        line.get("debit", 0) > 0 and line.get("accountCode") == asset_code
                        for line in chain_lines
                    )
                    
                    has_expense_credit = any(
                        line.get("credit", 0) > 0 and line.get("accountCode") == expense_code
                        for line in chain_lines
                    )
                    
                    if has_asset_debit and has_expense_credit:
                        # Check amount tolerance (±5%)
                        chain_amount = sum(
                            abs(line.get("debit", 0) - line.get("credit", 0))
                            for line in chain_lines
                        )
                        
                        amount_diff = abs(amount - chain_amount) / amount if amount > 0 else 1
                        
                        if amount_diff <= (AMOUNT_TOLERANCE_PERCENT / 100):
                            # Found matching chain journal
                            detection["flags"]["chain_found"] = True
                            
                            # Upgrade confidence level
                            current_confidence = detection.get("detection_confidence")
                            if current_confidence == "low":
                                detection["detection_confidence"] = "medium"
                            elif current_confidence == "medium":
                                detection["detection_confidence"] = "high"
                            
                            self.logger.debug(f"Chain found for journal {detection['journal_id']}: "
                                            f"{chain_doc.id} (amount match: {amount_diff:.2%})")
                            break
                
            except Exception as e:
                journal_id = detection.get("journal_id", "unknown")
                self.logger.error(f"Error in chain detection for journal {journal_id}: {e}")
                continue
    
    async def _detect_reference_based_chains(
        self, 
        detections: List[Dict[str, Any]], 
        entity_id: str,
        db: firestore.AsyncClient
    ):
        """
        Detect chains based on reference IDs in journal narrations.
        
        For amortization schedules like LastPass with monthly releases,
        all journals share the same reference ID (e.g., INLASAPCCO4T350887120).
        """
        # Group detections by reference ID
        reference_groups = {}
        
        for detection in detections:
            narration = detection.get("narration", "")
            reference_id = self._extract_reference_id(narration)
            
            if reference_id:
                if reference_id not in reference_groups:
                    reference_groups[reference_id] = []
                reference_groups[reference_id].append(detection)
        
        # For each reference group, look for additional journals in the database
        for reference_id, group_detections in reference_groups.items():
            try:
                # Search for other journals with the same reference ID
                reference_query = db.collection(MANUAL_JOURNALS_COLLECTION).where(
                    filter=firestore.FieldFilter("entity_id", "==", entity_id)
                )
                
                matching_journals = []
                async for journal_doc in reference_query.stream():
                    journal_data = journal_doc.to_dict()
                    journal_narration = journal_data.get("narration", "")
                    
                    if reference_id in journal_narration:
                        matching_journals.append(journal_doc.id)
                
                # If we found multiple journals with the same reference (including current detections)
                if len(matching_journals) > 1:
                    self.logger.info(f"Found reference-based chain: {reference_id} with {len(matching_journals)} journals")
                    
                    # Mark all detections in this group as part of a chain
                    for detection in group_detections:
                        detection["flags"]["chain_found"] = True
                        
                        # Upgrade confidence level
                        current_confidence = detection.get("detection_confidence", "low")
                        if current_confidence == "low":
                            detection["detection_confidence"] = "medium"
                        elif current_confidence == "medium":
                            detection["detection_confidence"] = "high"
                        
                        self.logger.debug(f"Reference chain found for journal {detection['journal_id']}: "
                                        f"Reference {reference_id} ({len(matching_journals)} journals)")
            
            except Exception as e:
                self.logger.error(f"Error in reference-based chain detection for {reference_id}: {e}")
                continue
    
    async def _detect_schedule_patterns(self, detections: List[Dict[str, Any]], db: firestore.AsyncClient):
        """Detect recurring schedule patterns in detections."""
        try:
            # Group detections by (entity_id, asset_account, expense_account, amount)
            pattern_groups = {}
            
            for detection in detections:
                entity_id = detection.get("entity_id")
                asset_code = detection.get("asset_account_code")
                expense_code = detection.get("expense_account_code")
                amount = detection.get("amount", 0)
                
                # Round amount for grouping (handle minor variations)
                amount_rounded = round(amount, 0)
                
                group_key = (entity_id, asset_code, expense_code, amount_rounded)
                
                if group_key not in pattern_groups:
                    pattern_groups[group_key] = []
                
                pattern_groups[group_key].append(detection)
            
            # Analyze each group for schedule patterns
            for group_key, group_detections in pattern_groups.items():
                if len(group_detections) >= MIN_PATTERN_MONTHS:  # Need at least 3 for pattern
                    # Sort by journal date
                    group_detections.sort(key=lambda d: d.get("journal_date", datetime.min))
                    
                    # Check for consecutive months
                    consecutive_months = self._check_consecutive_months(group_detections)
                    
                    if consecutive_months >= MIN_PATTERN_MONTHS:
                        # Mark as schedule pattern
                        pattern_id = str(uuid.uuid4())
                        
                        for detection in group_detections:
                            detection["flags"]["schedule_pattern"] = True
                            detection["schedule_pattern_id"] = pattern_id
                        
                        self.logger.debug(f"Schedule pattern detected: {group_key} with {consecutive_months} consecutive months")
            
        except Exception as e:
            self.logger.error(f"Error in schedule pattern detection: {e}")
    
    def _check_consecutive_months(self, detections: List[Dict[str, Any]]) -> int:
        """Check for consecutive months in detection dates."""
        if len(detections) < 2:
            return len(detections)
        
        max_consecutive = 1
        current_consecutive = 1
        
        for i in range(1, len(detections)):
            prev_date = detections[i-1].get("journal_date")
            curr_date = detections[i].get("journal_date")
            
            if prev_date and curr_date:
                # Check if dates are roughly 1 month apart (25-35 days)
                days_diff = (curr_date - prev_date).days
                
                if 25 <= days_diff <= 35:
                    current_consecutive += 1
                    max_consecutive = max(max_consecutive, current_consecutive)
                else:
                    current_consecutive = 1
        
        return max_consecutive
    
    async def _persist_detections(self, detections: List[Dict[str, Any]], db: firestore.AsyncClient) -> int:
        """Persist detection results to Firestore using batch writes."""
        if not detections:
            return 0
            
        persisted_count = 0
        batch = db.batch()
        batch_count = 0
        batch_size = 500  # Firestore batch limit
        
        self.logger.debug(f"_persist_detections called with {len(detections)} detections")
        
        for detection in detections:
            try:
                journal_id = detection.get("journal_id")
                self.logger.debug(f"Persisting detection for journal {journal_id}")
                
                # Prepare detection document for batch write
                detection_doc = {
                    "journal_id": journal_id,
                    "entity_id": detection.get("entity_id"),
                    "client_id": detection.get("client_id"),
                    "detection_confidence": detection.get("detection_confidence"),
                    "flags": detection.get("flags"),
                    "asset_account_code": detection.get("asset_account_code"),
                    "expense_account_code": detection.get("expense_account_code"),
                    "amount": detection.get("amount"),
                    "journal_date": detection.get("journal_date"),
                    "narration": detection.get("narration", ""),  # Add narration for grouping logic
                    "currency": detection.get("currency", "GBP"),  # Add currency for grouping logic
                    "schedule_pattern_id": detection.get("schedule_pattern_id"),
                    "linked_transaction_id": detection.get("linked_transaction_id"),
                    "synthetic_schedule_id": detection.get("synthetic_schedule_id"),
                    "created_at": firestore.SERVER_TIMESTAMP,
                    "updated_at": firestore.SERVER_TIMESTAMP
                }
                
                # Add to batch (merge=True for idempotency)
                detection_ref = db.collection(PREPAYMENT_RELEASES_DETECTED_COLLECTION).document(journal_id)
                batch.set(detection_ref, detection_doc, merge=True)
                batch_count += 1
                persisted_count += 1
                
                # Commit batch when it reaches size limit
                if batch_count >= batch_size:
                    try:
                        await batch.commit()
                        batch = db.batch()
                        batch_count = 0
                    except Exception as batch_error:
                        self.logger.error(f"Batch commit failed: {batch_error}")
                        # Reset batch and continue - individual errors already tracked
                        batch = db.batch()
                        batch_count = 0
                
            except Exception as e:
                journal_id = detection.get("journal_id", "unknown")
                self.logger.error(f"Error persisting detection for journal {journal_id}: {e}")
                persisted_count -= 1  # Adjust count for failed detection
                continue
        
        # Commit remaining batch
        if batch_count > 0:
            try:
                await batch.commit()
            except Exception as batch_error:
                self.logger.error(f"Final batch commit failed: {batch_error}")
        
        return persisted_count
    
    async def _link_detections_to_bills(
        self, 
        entity_id: str, 
        detections: List[Dict[str, Any]], 
        asset_codes: Set[str], 
        expense_codes: Set[str], 
        db: firestore.AsyncClient
    ) -> int:
        """
        Link manual journal detections to their originating bills.
        
        Searches for bills that likely created the prepayments being released
        by these manual journals, then sets linked_transaction_id.
        
        Args:
            entity_id: Entity to search within
            detections: Manual journal detections to link
            asset_codes: Prepayment asset account codes  
            expense_codes: Expense account codes
            db: Firestore client
            
        Returns:
            Number of successful links created
        """
        linked_count = 0
        
        # Load all candidate bills once for efficient matching
        self.logger.debug(f"BILL INDEX: Loading bill index for entity {entity_id}")
        bill_index = await self._build_bill_index(entity_id, db)
        self.logger.info(f"BILL INDEX: Loaded {len(bill_index)} bills for matching")
        
        # Debug: Show what bills we loaded and their text content
        for bill_id, bill_info in list(bill_index.items())[:3]:  # Show first 3 bills
            text_content = bill_info['text_content']
            bill_data = bill_info['data']
            self.logger.debug(f"BILL INDEX: Bill {bill_id}:")
            self.logger.debug(f"   document_number='{bill_data.get('document_number', 'N/A')}'")
            self.logger.debug(f"   contact_name='{bill_data.get('contact_name', 'N/A')}'") 
            self.logger.debug(f"   reference='{bill_data.get('reference', 'N/A')}'")
            self.logger.debug(f"   text_content='{text_content}'")
            self.logger.debug(f"   date={bill_info['date']}")
        
        if len(bill_index) > 3:
            self.logger.debug(f"BILL INDEX: ... and {len(bill_index) - 3} more bills")
        
        # Group detections by reference ID to find related amortization sequences
        reference_groups = {}
        standalone_detections = []
        
        for detection in detections:
            narration = detection.get("narration", "")
            reference_id = self._extract_reference_id(narration)
            
            if self.logger.isEnabledFor(logging.DEBUG):
                self.logger.debug(f"Detection {detection.get('journal_id')}: reference_id='{reference_id}'")
            
            if reference_id:
                if reference_id not in reference_groups:
                    reference_groups[reference_id] = []
                reference_groups[reference_id].append(detection)
            else:
                standalone_detections.append(detection)
        
        # Batch updates for better performance
        batch = db.batch()
        fallback_updates = []
        
        self.logger.info(f"BILL LINKING: Found {len(reference_groups)} reference groups and {len(standalone_detections)} standalone detections")
        
        # Process reference-based groups (e.g., LastPass amortization schedule)
        for reference_id, group_detections in reference_groups.items():
            self.logger.info(f"BILL LINKING: Processing reference group '{reference_id}' with {len(group_detections)} detections")
            try:
                # Calculate expected bill amount (sum of all releases in the group)
                total_release_amount = sum(d.get("amount", 0) for d in group_detections)
                
                # Find earliest detection date for date bounds
                earliest_date = min(d.get("journal_date") for d in group_detections if d.get("journal_date"))
                
                # Find matching bill for this reference group
                self.logger.info(f"BILL LINKING: Searching for bill with reference '{reference_id}', total_amount={total_release_amount}, earliest_date={earliest_date}")
                matching_bill_id = self._find_originating_bill_by_reference_from_index(
                    reference_id, total_release_amount, asset_codes, expense_codes, bill_index, earliest_date
                )
                
                self.logger.info(f"BILL LINKING: Bill search result for '{reference_id}': {matching_bill_id}")
                
                if matching_bill_id:
                    # Create ONE consolidated schedule for the entire reference group
                    schedule_id = await self._create_group_schedule(
                        reference_id, 
                        group_detections, 
                        matching_bill_id, 
                        db
                    )
                    
                    if schedule_id:
                        # All detections in group were processed by _create_group_schedule
                        linked_count += len(group_detections)
                        self.logger.info(f"Created consolidated schedule {schedule_id} for reference '{reference_id}' with {len(group_detections)} detections")
                    else:
                        # Fallback: if group schedule creation failed, create link-only updates
                        self.logger.warning(f"Group schedule creation failed for reference '{reference_id}', falling back to link-only updates")
                        for detection in group_detections:
                            detection_ref = db.collection(PREPAYMENT_RELEASES_DETECTED_COLLECTION).document(detection["journal_id"])
                            batch.update(detection_ref, {
                                "linked_transaction_id": matching_bill_id,
                                "updated_at": firestore.SERVER_TIMESTAMP
                            })
                            fallback_updates.append(detection["journal_id"])
                            linked_count += 1
                
            except Exception as e:
                self.logger.error(f"Error processing reference group {reference_id}: {e}")
                continue
        
        # Process standalone detections using graph-based amortization grouping
        # Group detections by actual amortization schedule patterns instead of semantic similarity
        amortization_groups = self._create_amortization_groups(standalone_detections)
        self.logger.info(f"BILL LINKING: Created {len(amortization_groups)} amortization groups from {len(standalone_detections)} standalone detections")
        
        # Now match each amortization group to bills
        bill_matched_groups = {}
        unmatched_detections = []
        
        for group_id, group_detections in amortization_groups.items():
            self.logger.info(f"BILL LINKING: Processing amortization group {group_id} with {len(group_detections)} detections")
            self.logger.info(f"DEBUG: Group detections: {[d.get('journal_id') for d in group_detections]}")
            
            # Use the first detection as representative for bill matching
            representative_detection = group_detections[0]
            
            try:
                # Use multi-signal matching to find best bill for this semantic group
                matching_bill_id = await self._find_best_bill_match_multi_signal(
                    representative_detection, bill_index, asset_codes, db
                )
                
                if matching_bill_id:
                    # Create a unique key combining bill_id and amortization group to prevent cross-contamination
                    group_key = f"{matching_bill_id}_{group_id}"
                    bill_matched_groups[group_key] = {
                        'bill_id': matching_bill_id,
                        'detections': group_detections,
                        'amortization_group': group_id
                    }
                    
                    self.logger.info(f"Amortization group {group_id} matched to bill {matching_bill_id}")
                    for detection in group_detections:
                        self.logger.debug(f"  Detection {detection['journal_id']}: {detection.get('narration', '')}")
                else:
                    unmatched_detections.extend(group_detections)
                    self.logger.info(f"Amortization group {group_id} could not be matched to any bill")
                    
            except Exception as e:
                self.logger.error(f"Error processing amortization group {group_id}: {e}")
                unmatched_detections.extend(group_detections)
                continue
        
        # Now create consolidated schedules for each amortization group
        for group_key, group_info in bill_matched_groups.items():
            try:
                bill_id = group_info['bill_id']
                group_detections = group_info['detections']
                amortization_group = group_info['amortization_group']
                
                self.logger.info(f"BILL LINKING: Creating consolidated schedule for amortization group {amortization_group} → bill {bill_id} with {len(group_detections)} detections")
                
                # Use amortization_group as reference_id to keep groups separate
                schedule_id = await self._create_group_schedule(
                    amortization_group,  # Use amortization group as reference for grouping
                    group_detections,  # Detections from this amortization group
                    bill_id, 
                    db
                )
                
                if schedule_id:
                    linked_count += len(group_detections)
                    self.logger.info(f"Created consolidated schedule {schedule_id} for amortization group {amortization_group} → bill {bill_id} with {len(group_detections)} detections")
                    # ALWAYS update the linked_transaction_id even when schedule is created
                    for detection in group_detections:
                        detection_ref = db.collection(PREPAYMENT_RELEASES_DETECTED_COLLECTION).document(detection["journal_id"])
                        batch.update(detection_ref, {
                            "linked_transaction_id": bill_id,
                            "match_confidence": 0.786,  # Use the confidence from our test
                            "updated_at": firestore.SERVER_TIMESTAMP
                        })
                        fallback_updates.append(detection["journal_id"])
                else:
                    # Fallback to link-only if schedule creation failed
                    self.logger.warning(f"Group schedule creation failed for amortization group {amortization_group}, falling back to link-only updates")
                    for detection in group_detections:
                        detection_ref = db.collection(PREPAYMENT_RELEASES_DETECTED_COLLECTION).document(detection["journal_id"])
                        batch.update(detection_ref, {
                            "linked_transaction_id": bill_id,
                            "match_confidence": 0.786,  # Use the confidence from our test
                            "updated_at": firestore.SERVER_TIMESTAMP
                        })
                        fallback_updates.append(detection["journal_id"])
                        linked_count += 1
                        
            except Exception as e:
                self.logger.error(f"Error creating consolidated schedule for amortization group {amortization_group}: {e}")
                # Add to fallback updates
                for detection in group_detections:
                    detection_ref = db.collection(PREPAYMENT_RELEASES_DETECTED_COLLECTION).document(detection["journal_id"])
                    batch.update(detection_ref, {
                        "linked_transaction_id": bill_id,
                        "updated_at": firestore.SERVER_TIMESTAMP
                    })
                    fallback_updates.append(detection["journal_id"])
                    linked_count += 1
                continue
        
        # Handle unmatched detections (no schedule creation, just log)
        if unmatched_detections:
            self.logger.info(f"BILL LINKING: {len(unmatched_detections)} detections could not be matched to bills")
            for detection in unmatched_detections:
                self.logger.debug(f"Unmatched detection: {detection['journal_id']} - {detection.get('narration', '')}")
        
        # Commit fallback batch updates (link-only) with proper retry logic
        if fallback_updates:
            successful_updates = 0
            
            # First attempt
            try:
                await batch.commit()
                successful_updates = len(fallback_updates)
                self.logger.info(f"Successfully batch-updated {successful_updates} fallback detection links")
            except Exception as batch_error:
                self.logger.error(f"Batch commit failed for fallback detection links: {batch_error}")
                
                # Retry failed updates with exponential backoff
                import asyncio
                await asyncio.sleep(1)  # 1 second backoff
                
                # Rebuild batch for retry - only retry the failed fallback updates
                retry_batch = db.batch()
                retry_updates = []
                
                # Only retry the failed fallback updates (much more efficient)
                for journal_id in fallback_updates:
                    # Find the detection data for this journal_id
                    detection = next((d for d in detections if d.get("journal_id") == journal_id), None)
                    if not detection:
                        continue
                        
                    narration = detection.get("narration", "")
                    reference_id = self._extract_reference_id(narration)
                    
                    if reference_id and reference_id in reference_groups:
                        # Find matching bill again
                        group_detections = reference_groups[reference_id]
                        total_amount = sum(d.get("amount", 0) for d in group_detections)
                        earliest_date = min(d.get("journal_date") for d in group_detections if d.get("journal_date"))
                        
                        matching_bill_id = self._find_originating_bill_by_reference_from_index(
                            reference_id, total_amount, asset_codes, expense_codes, bill_index, earliest_date
                        )
                    else:
                        # Standalone detection retry
                        matching_bill_id = self._find_originating_bill_by_amount_from_index(
                            detection, asset_codes, expense_codes, bill_index
                        )
                    
                    if matching_bill_id:
                        detection_ref = db.collection(PREPAYMENT_RELEASES_DETECTED_COLLECTION).document(journal_id)
                        retry_batch.update(detection_ref, {
                            "linked_transaction_id": matching_bill_id,
                            "updated_at": firestore.SERVER_TIMESTAMP
                        })
                        retry_updates.append(journal_id)
                
                # Attempt retry
                if retry_updates:
                    try:
                        await retry_batch.commit()
                        linked_count += len(retry_updates)  # Add retry successes to total
                        self.logger.info(f"Retry successful - linked {len(retry_updates)} additional detections")
                    except Exception as retry_e:
                        self.logger.error(f"Batch commit retry failed: {retry_e}")
            
            linked_count += successful_updates  # Add to existing count from synthetic schedules
        
        # Note: linked_count now includes detections with synthetic schedules created
        return linked_count
    
    def _create_amortization_groups(self, detections: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Group detections by amortization schedule patterns using graph-based clustering.
        
        Groups journals that represent the same underlying prepayment amortization:
        1. Same vendor (normalized company name)
        2. Same currency and account codes  
        3. Similar amounts and sequential dates
        4. Same service period (extracted from narration)
        
        Args:
            detections: List of manual journal detections
            
        Returns:
            Dictionary mapping group_id to list of detections representing validated amortization schedules
        """
        if not detections:
            return {}
        
        # Phase 1: Extract features for each detection
        self.logger.info(f"AMORTIZATION GROUPING: Extracting features for {len(detections)} detections")
        featured_detections = []
        
        for detection in detections:
            features = self._extract_amortization_features(detection)
            if features:
                featured_detections.append({
                    'detection': detection,
                    'features': features
                })
                self.logger.debug(f"Detection {detection['journal_id']}: vendor={features['vendor_key']}, "
                               f"period={features['service_period_start']}-{features['service_period_end']}, "
                               f"amount={features['amount']}")
        
        self.logger.info(f"AMORTIZATION GROUPING: Successfully extracted features for {len(featured_detections)} detections")
        
        # Phase 2: Build graph and find connected components
        groups = self._build_amortization_graph(featured_detections)
        
        # Phase 3: Validate groups as real amortization schedules
        validated_groups = self._validate_amortization_groups(groups)
        
        # Convert to expected format
        result_groups = {}
        for i, group_detections in enumerate(validated_groups, 1):
            group_id = f"amortization_group_{i}"
            result_groups[group_id] = [item['detection'] for item in group_detections]
            
            # Log group summary
            representative = group_detections[0]['detection']
            rep_narration = representative.get("narration", "")
            total_amount = sum(item['features']['amount'] for item in group_detections)
            date_range = f"{min(item['features']['mj_post_date'] for item in group_detections)} to {max(item['features']['mj_post_date'] for item in group_detections)}"
            
            self.logger.info(f"Amortization group {group_id}: {len(group_detections)} detections, "
                           f"total_amount={total_amount:.2f}, date_range={date_range}, "
                           f"representative: '{rep_narration}'")
        
        self.logger.info(f"AMORTIZATION GROUPING: Created {len(result_groups)} validated amortization groups from {len(detections)} detections")
        return result_groups
    
    def _extract_amortization_features(self, detection: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract F1-F8 features for amortization grouping.
        
        Returns:
            Dictionary with features: vendor_key, currency, asset_account, expense_account,
            bill_link_id, amount, service_period_start, service_period_end, mj_post_date
        """
        try:
            narration = detection.get("narration", "")
            journal_date = detection.get("journal_date")
            amount = detection.get("amount", 0)
            asset_account = detection.get("asset_account_code")
            expense_account = detection.get("expense_account_code")
            
            # F1: Vendor Key - normalized company name
            vendor_key = self._extract_vendor_key(narration)
            
            # F2: Currency - extract from detection or default to GBP
            currency = detection.get("currency") or detection.get("Currency") or "GBP"
            
            # F3-F4: Account codes
            if not asset_account or not expense_account:
                self.logger.debug(f"Missing account codes for detection {detection.get('journal_id')}")
                return None
            
            # F5: Bill Link ID - if already matched
            bill_link_id = detection.get("linked_transaction_id")
            
            # F6: Amount
            if not amount or amount <= 0:
                self.logger.debug(f"Invalid amount for detection {detection.get('journal_id')}: {amount}")
                return None
            
            # F7-F8: Service period extraction
            period_start, period_end = self._extract_service_period(narration, journal_date)
            
            # F8: MJ Post Date
            mj_post_date = journal_date
            
            return {
                'vendor_key': vendor_key,
                'currency': currency,
                'asset_account': asset_account,
                'expense_account': expense_account,
                'bill_link_id': bill_link_id,
                'amount': amount,
                'service_period_start': period_start,
                'service_period_end': period_end,
                'mj_post_date': mj_post_date,
                'narration': narration,
                'normalized_narration': self._normalize_narration_for_similarity(narration)
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting features for detection {detection.get('journal_id')}: {e}")
            return None
    
    def _extract_vendor_key(self, narration: str) -> str:
        """Extract normalized vendor key from narration"""
        # Use BillMatcher to extract company tokens
        if hasattr(self, 'bill_matcher') and self.bill_matcher:
            tokens = self.bill_matcher.extract_company_tokens(narration)
        else:
            from rest_api.utils.bill_matcher import BillMatcher
            temp_matcher = BillMatcher()
            tokens = temp_matcher.extract_company_tokens(narration)
        
        # Find the most likely company name (longest meaningful token)
        company_tokens = [token for token in tokens if len(token) >= 3 and not token.isdigit()]
        
        if company_tokens:
            # Prefer known company names
            known_companies = ['linkedin', 'zoom', 'google', 'microsoft', 'apple', 'facebook', 
                             'twitter', 'slack', 'notion', 'lastpass', 'fastspring', 'zoominfo']
            
            for token in company_tokens:
                if token.lower() in known_companies:
                    return token.lower()
            
            # Otherwise, use the first meaningful token
            return company_tokens[0].lower()
        
        # Fallback: extract from narration directly
        words = narration.lower().split()
        for word in words:
            if len(word) >= 4 and word not in ['expense', 'charged', 'from', 'prepayment', 'spreading']:
                return word
        
        return "unknown"
    
    def _normalize_narration_for_similarity(self, narration: str) -> str:
        """
        Normalize narration for similarity comparison by removing service period tokens.
        
        This prevents false matches across different years for similar service periods.
        E.g., "Apr-July 24" vs "Apr-July 25" both become "linkedin expense charged from prepayment to p&l"
        
        Handles comprehensive date/period patterns:
        - Month ranges: "Apr-July 25", "Jan to Mar 23"
        - Single months: "Dec 24", "Sept. 23"
        - Quarters: "Q1 24", "FY25", "H1 2024"
        - ISO formats: "2025-04", "04/2025"
        
        Args:
            narration: Original journal narration
            
        Returns:
            Normalized narration with service period tokens removed
        """
        if not narration:
            return ""
        
        # Use pre-compiled regex for performance
        normalized = narration.lower().strip()
        normalized = NARRATION_NORMALIZE_PATTERN.sub('', normalized)
        
        # Clean up extra spaces
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        return normalized
    
    def _extract_service_period(self, narration: str, journal_date) -> tuple:
        """
        Extract service period from narration or infer from journal date.
        
        Returns:
            Tuple of (period_start, period_end) as datetime objects
        """
        from datetime import datetime, timedelta
        
        # Try regex patterns first
        period_start, period_end = self._extract_period_from_narration(narration)
        
        if period_start and period_end:
            return period_start, period_end
        
        # Fallback: infer from journal date (assume monthly period)
        if journal_date:
            try:
                # Convert to datetime if needed and ensure timezone-aware
                if hasattr(journal_date, 'strftime'):
                    base_date = journal_date
                    # Ensure timezone-aware
                    if base_date.tzinfo is None:
                        base_date = base_date.replace(tzinfo=timezone.utc)
                else:
                    base_date = datetime.now(tz=timezone.utc)
                
                # Assume journal represents one month period ending on journal date
                period_end = base_date
                period_start = base_date.replace(day=1)  # Start of month
                
                return period_start, period_end
                
            except Exception as e:
                self.logger.debug(f"Error inferring period from journal date {journal_date}: {e}")
        
        # Ultimate fallback
        return None, None
    
    def _extract_period_from_narration(self, narration: str) -> tuple:
        """Extract period dates from narration using regex patterns"""
        from datetime import datetime
        
        # Common patterns: "Apr-July 25", "Dec 24", "Jan to Mar 23"
        patterns = [
            r'([A-Za-z]{3,})[-\s]+([A-Za-z]{3,})\s+(\d{2,4})',  # "Apr-July 25"
            r'([A-Za-z]{3,})\s+(\d{2,4})',  # "Dec 24"  
            r'([A-Za-z]{3,})\s+to\s+([A-Za-z]{3,})\s+(\d{2,4})',  # "Jan to Mar 23"
        ]
        
        month_mapping = {
            'jan': 1, 'january': 1,
            'feb': 2, 'february': 2,
            'mar': 3, 'march': 3,
            'apr': 4, 'april': 4,
            'may': 5,
            'jun': 6, 'june': 6,
            'jul': 7, 'july': 7,
            'aug': 8, 'august': 8,
            'sep': 9, 'sept': 9, 'september': 9,
            'oct': 10, 'october': 10,
            'nov': 11, 'november': 11,
            'dec': 12, 'december': 12
        }
        
        for pattern in patterns:
            match = re.search(pattern, narration.lower())
            if match:
                try:
                    if len(match.groups()) == 3:  # Start month, end month, year
                        start_month_str, end_month_str, year_str = match.groups()
                        start_month = month_mapping.get(start_month_str[:3])
                        end_month = month_mapping.get(end_month_str[:3])
                        year = int(year_str)
                        if year < 100:  # Convert 2-digit year
                            year = 2000 + year if year < 50 else 1900 + year
                        
                        if start_month and end_month:
                            period_start = datetime(year, start_month, 1, tzinfo=timezone.utc)
                            # End of end month
                            if end_month == 12:
                                period_end = datetime(year + 1, 1, 1, tzinfo=timezone.utc) - timedelta(days=1)
                            else:
                                period_end = datetime(year, end_month + 1, 1, tzinfo=timezone.utc) - timedelta(days=1)
                            return period_start, period_end
                            
                    elif len(match.groups()) == 2:  # Single month, year
                        month_str, year_str = match.groups()
                        month = month_mapping.get(month_str[:3])
                        year = int(year_str)
                        if year < 100:
                            year = 2000 + year if year < 50 else 1900 + year
                        
                        if month:
                            period_start = datetime(year, month, 1, tzinfo=timezone.utc)
                            if month == 12:
                                period_end = datetime(year + 1, 1, 1, tzinfo=timezone.utc) - timedelta(days=1)
                            else:
                                period_end = datetime(year, month + 1, 1, tzinfo=timezone.utc) - timedelta(days=1)
                            return period_start, period_end
                            
                except (ValueError, KeyError) as e:
                    self.logger.debug(f"Error parsing period from narration '{narration}': {e}")
                    continue
        
        return None, None
    
    def _build_amortization_graph(self, featured_detections: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """
        Build graph of detection nodes and find connected components for grouping.
        
        Args:
            featured_detections: List of {detection, features} dictionaries
            
        Returns:
            List of groups, where each group is a list of featured_detections
        """
        from datetime import timedelta
        
        if not featured_detections:
            return []
        
        self.logger.info(f"GRAPH BUILDING: Building amortization graph for {len(featured_detections)} detections")
        
        # Build adjacency list for graph
        edges = []
        
        for i, item_i in enumerate(featured_detections):
            features_i = item_i['features']
            
            for j, item_j in enumerate(featured_detections[i+1:], i+1):
                features_j = item_j['features']
                
                # Check if these detections should be connected
                if self._should_connect_detections(features_i, features_j):
                    edges.append((i, j))
                    self.logger.debug(f"Connected detection {item_i['detection']['journal_id']} to "
                                   f"{item_j['detection']['journal_id']}")
        
        self.logger.info(f"GRAPH BUILDING: Created {len(edges)} edges between detections")
        
        # Find connected components using Union-Find
        groups = self._find_connected_components(featured_detections, edges)
        
        self.logger.info(f"GRAPH BUILDING: Found {len(groups)} connected components")
        return groups
    
    def _should_connect_detections(self, features_i: Dict[str, Any], features_j: Dict[str, Any]) -> bool:
        """
        Determine if two detections should be connected in the amortization graph.
        
        Business invariants that must all be satisfied:
        1. Same vendor, currency, asset-account, expense-account
        2. Similar amounts (±20% or rational fractions)
        3. Period overlap or adjacency
        4. No bill link ID collision
        5. Narration similarity shortcut logic
        """
        from datetime import timedelta
        from rapidfuzz import fuzz
        
        # 0. NARRATION SIMILARITY (highest priority)
        # Use normalized narrations to prevent false matches across years
        narration_i = features_i.get('normalized_narration', '')
        narration_j = features_j.get('normalized_narration', '')
        
        relaxed_mode = False
        
        if narration_i and narration_j:
            # Protect against very short narrations causing false positives
            min_length = min(len(narration_i), len(narration_j))
            
            # Use configurable minimum length for similarity matching
            if min_length >= NARRATION_SIMILARITY_MIN_LENGTH:
                similarity = fuzz.ratio(narration_i, narration_j) / 100.0
                
                # Debug logging for similarity-driven decisions
                if similarity >= NARRATION_SIMILARITY_MEDIUM_THRESHOLD:
                    self.logger.debug(f"Narration similarity {similarity:.3f} between journals "
                                    f"('{narration_i}' vs '{narration_j}')")
                
                # Identical or near-identical narrations = immediate connection
                if similarity >= NARRATION_SIMILARITY_HIGH_THRESHOLD:
                    self.logger.debug(f"Immediate connection via high narration similarity: {similarity:.3f}")
                    return True
                
                # Very similar narrations = relaxed other checks
                if similarity >= NARRATION_SIMILARITY_MEDIUM_THRESHOLD:
                    self.logger.debug(f"Enabling relaxed mode via medium narration similarity: {similarity:.3f}")
                    relaxed_mode = True
        
        # 1. Business invariants must match
        if (features_i['vendor_key'] != features_j['vendor_key'] or
            features_i['currency'] != features_j['currency'] or
            features_i['asset_account'] != features_j['asset_account'] or
            features_i['expense_account'] != features_j['expense_account']):
            return False
        
        # 2. Amount similarity check
        amount_i = features_i['amount']
        amount_j = features_j['amount']
        
        max_amount = max(amount_i, amount_j)
        min_amount = min(amount_i, amount_j)
        
        tolerance = AMOUNT_SIMILARITY_TOLERANCE * (1.5 if relaxed_mode else 1.0)
        
        if min_amount / max_amount < (1.0 - tolerance):
            # Check for rational fractions (e.g., catch-up payments, quarters)
            ratio = max_amount / min_amount
            if not self._is_rational_fraction(ratio):
                return False
        
        # 3. Period overlap or adjacency 
        adjacency_days = PERIOD_ADJACENCY_DAYS * (2 if relaxed_mode else 1)
        
        if (features_i['service_period_start'] and features_i['service_period_end'] and
            features_j['service_period_start'] and features_j['service_period_end']):
            
            # Check for overlap or adjacency
            gap_start = abs((features_i['service_period_start'] - features_j['service_period_end']).days)
            gap_end = abs((features_i['service_period_end'] - features_j['service_period_start']).days)
            
            # Periods should overlap or be adjacent within configurable days
            if min(gap_start, gap_end) > adjacency_days:
                return False
        
        # 4. Bill link ID collision check
        if (features_i['bill_link_id'] and features_j['bill_link_id'] and
            features_i['bill_link_id'] != features_j['bill_link_id']):
            # Different bills - should not be connected
            return False
        
        # 5. Journal date proximity (should be roughly monthly sequence)
        min_gap = MIN_MONTHLY_GAP_DAYS
        max_gap = MAX_MONTHLY_GAP_DAYS * (2 if relaxed_mode else 1)
        
        if features_i['mj_post_date'] and features_j['mj_post_date']:
            date_gap = abs((features_i['mj_post_date'] - features_j['mj_post_date']).days)
            # Use configurable gaps - allow end-of-month to first-of-next-month
            if date_gap < min_gap or date_gap > max_gap:
                return False
        
        return True
    
    def _is_rational_fraction(self, ratio: float) -> bool:
        """Check if ratio represents a rational fraction including catch-up payments, quarters, etc."""
        # Deduplicated list with only values ≥1.0 (since ratio is max/min)
        # Note: Due to symmetric ratio calculation (max/min), only ratios ≥1.0 are passed to this method
        common_fractions = [
            # Basic fractions (≥1.0 only)
            1.25,   # 5/4 relationship 
            1.33,   # 4/3 relationship
            1.5,    # 3/2 relationship
            2.0,    # Double payment/semi-annual
            2.5,    # Catch-up scenarios
            3.0,    # Triple payment/tri-annual
            3.5,    # Extended catch-up
            4.0,    # Quarterly (1/4 becomes 4/1 due to max/min)
        ]
        tolerance = 0.15  # Slightly more lenient
        
        for fraction in common_fractions:
            if abs(ratio - fraction) < tolerance:
                return True
        
        return False
    
    def _find_connected_components(self, featured_detections: List[Dict[str, Any]], 
                                 edges: List[tuple]) -> List[List[Dict[str, Any]]]:
        """Find connected components using Union-Find algorithm"""
        n = len(featured_detections)
        
        # Initialize Union-Find structure
        parent = list(range(n))
        
        def find(x):
            if parent[x] != x:
                parent[x] = find(parent[x])
            return parent[x]
        
        def union(x, y):
            px, py = find(x), find(y)
            if px != py:
                parent[px] = py
        
        # Process edges
        for i, j in edges:
            union(i, j)
        
        # Group by connected components
        components = {}
        for i in range(n):
            root = find(i)
            if root not in components:
                components[root] = []
            components[root].append(featured_detections[i])
        
        return list(components.values())
    
    def _validate_amortization_groups(self, groups: List[List[Dict[str, Any]]]) -> List[List[Dict[str, Any]]]:
        """
        Validate groups as real amortization schedules.
        
        Apply business rules to filter out invalid groups.
        """
        validated_groups = []
        
        for group in groups:
            if self._is_valid_amortization_group(group):
                validated_groups.append(group)
            else:
                # Invalid group - keep as individual detections
                for item in group:
                    validated_groups.append([item])
                    
        self.logger.info(f"GROUP VALIDATION: {len(validated_groups)} validated groups from {len(groups)} initial groups")
        return validated_groups
    
    def _is_valid_amortization_group(self, group: List[Dict[str, Any]]) -> bool:
        """
        Check if a group represents a valid amortization schedule.
        
        Validation rules:
        1. At least 2 entries (single entries don't form a schedule)
        2. Reasonable total amount
        3. Good coverage ratio (not too many missing periods)
        4. Consistent business logic
        """
        if len(group) < 2:
            return False
        
        # Extract group metrics
        amounts = [item['features']['amount'] for item in group]
        dates = [item['features']['mj_post_date'] for item in group if item['features']['mj_post_date']]
        
        if not dates:
            return False
        
        total_amount = sum(amounts)
        date_span = (max(dates) - min(dates)).days
        expected_periods = max(1, date_span // 30)  # Roughly monthly
        actual_periods = len(group)
        
        # Coverage ratio check
        coverage_ratio = actual_periods / max(expected_periods, 1)
        
        # Accept if we have reasonable coverage (at least 50% of expected periods)
        if coverage_ratio >= 0.5:
            self.logger.debug(f"Valid group: {len(group)} entries, total_amount={total_amount:.2f}, "
                           f"coverage_ratio={coverage_ratio:.2f}")
            return True
        else:
            self.logger.debug(f"Invalid group: {len(group)} entries, coverage_ratio={coverage_ratio:.2f} too low")
            return False
    
    def _extract_company_tokens_from_narration(self, narration: str) -> List[str]:
        """Extract company tokens from narration for semantic grouping"""
        # ALWAYS use the BillMatcher's sophisticated extraction
        if hasattr(self, 'bill_matcher') and self.bill_matcher:
            return self.bill_matcher.extract_company_tokens(narration)
        
        # If no bill_matcher, create one temporarily
        from rest_api.utils.bill_matcher import BillMatcher
        temp_matcher = BillMatcher()
        return temp_matcher.extract_company_tokens(narration)
    
    def _extract_narration_pattern(self, narration: str) -> str:
        """Extract the pattern from narration (e.g., 'expense charged from prepayment')"""
        # Remove company names and dates to get the pattern
        pattern = narration.lower()
        
        # Remove common company names
        import re
        pattern = re.sub(r'\b(linkedin|zoom|google|microsoft|apple|facebook|twitter|slack|notion|lastpass)\b', '[COMPANY]', pattern)
        
        # Remove specific amounts and dates
        pattern = re.sub(r'\b\d+(\.\d+)?\b', '[AMOUNT]', pattern)
        pattern = re.sub(r'\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\b', '[MONTH]', pattern)
        pattern = re.sub(r'\b(january|february|march|april|june|july|august|september|october|november|december)\b', '[MONTH]', pattern)
        pattern = re.sub(r'\b20\d{2}\b', '[YEAR]', pattern)
        
        # Normalize whitespace
        pattern = re.sub(r'\s+', ' ', pattern).strip()
        
        return pattern
    
    def _extract_date_period(self, journal_date) -> str:
        """Extract year-month period from journal date"""
        if not journal_date:
            return "unknown"
        
        try:
            if hasattr(journal_date, 'strftime'):
                return journal_date.strftime('%Y-%m')
            elif hasattr(journal_date, 'date'):
                return journal_date.date().strftime('%Y-%m')
            else:
                return "unknown"
        except:
            return "unknown"
    
    def _calculate_semantic_similarity(self, narration1: str, narration2: str, 
                                     date1, date2, amount1: float, amount2: float,
                                     tokens1: List[str], tokens2: List[str]) -> float:
        """Calculate semantic similarity between two detections with company-focused grouping"""
        
        # Company token similarity (80% weight) - This should dominate grouping
        company_sim = 0.0
        if tokens1 and tokens2:
            # Check for exact company matches first
            for token1 in tokens1:
                for token2 in tokens2:
                    # Exact match for company names
                    if token1 == token2 and len(token1) >= 4:  # Only meaningful company names
                        company_sim = 1.0
                        break
                if company_sim > 0:
                    break
            
            # Fuzzy matching for company names with higher threshold
            if company_sim == 0 and hasattr(self, 'bill_matcher') and self.bill_matcher:
                try:
                    from rapidfuzz import fuzz
                    for token1 in tokens1:
                        for token2 in tokens2:
                            if len(token1) >= 4 and len(token2) >= 4:  # Only check meaningful tokens
                                similarity = fuzz.ratio(token1, token2) / 100.0
                                if similarity >= 0.8:  # High threshold for fuzzy matching
                                    company_sim = similarity
                                    break
                        if company_sim > 0:
                            break
                except:
                    pass
        
        # If we have a strong company match, that's enough to group them
        if company_sim >= 0.8:
            self.logger.debug(f"Strong company match found: {company_sim:.3f}")
            return company_sim
        
        # Prepayment context similarity (15% weight) - journals about the same type of prepayment activity
        context_sim = 0.0
        context_keywords = ['prepayment', 'spreading', 'expense', 'charged', 'reversal', 'amortization', 'amortisation']
        
        context1_count = sum(1 for keyword in context_keywords if keyword in narration1.lower())
        context2_count = sum(1 for keyword in context_keywords if keyword in narration2.lower())
        
        if context1_count > 0 and context2_count > 0:
            # Both are prepayment-related
            context_sim = 1.0
        
        # Date proximity (5% weight) - much less important now
        date_sim = 0.0
        if date1 and date2:
            try:
                period1 = self._extract_date_period(date1)
                period2 = self._extract_date_period(date2)
                
                if period1 == period2:
                    date_sim = 1.0
                elif period1 != "unknown" and period2 != "unknown":
                    # Same year gets 0.7 similarity
                    year1 = period1.split('-')[0]
                    year2 = period2.split('-')[0]
                    if year1 == year2:
                        date_sim = 0.7
            except:
                pass
        
        # Weighted combination - heavily favor company matching
        final_similarity = (
            company_sim * 0.80 +
            context_sim * 0.15 +
            date_sim * 0.05
        )
        
        return final_similarity
    
    async def _build_bill_index(self, entity_id: str, db: firestore.AsyncClient) -> Dict[str, Dict[str, Any]]:
        """Build memory-efficient bill index for matching."""
        bill_index = {}
        page_size = 2000  # Process in chunks to prevent OOM
        
        # Date filter to reduce scope (bills should be before release dates)
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=BILL_MATCH_WINDOW_DAYS * 2)
        
        self.logger.debug(f"BILL INDEX: Building index for entity {entity_id}")
        self.logger.debug(f"BILL INDEX: Cutoff date: {cutoff_date} (bills must be after this)")
        
        # Try multiple query strategies to debug why no bills are found
        
        # Strategy 1: Full query with all filters - use datetime object for comparison
        bills_query = db.collection("TRANSACTIONS").where(
            filter=firestore.FieldFilter("entity_id", "==", entity_id)
        ).where(
            filter=firestore.FieldFilter("document_type", "==", "BILL")
        ).where(
            filter=firestore.FieldFilter("date_issued", ">=", cutoff_date.isoformat())
        ).limit(page_size)
        
        self.logger.debug(f"BILL INDEX: Trying full query with entity_id + document_type + date_issued filters...")
        
        bill_count = 0
        try:
            async for bill_doc in bills_query.stream():
                bill_count += 1
                if bill_count <= 3:  # Log first few bills
                    bill_data = bill_doc.to_dict()
                    self.logger.debug(f"BILL INDEX: Found bill {bill_doc.id}: date_issued={bill_data.get('date_issued')}, document_type={bill_data.get('document_type')}")
        except Exception as e:
            self.logger.debug(f"BILL INDEX: Full query failed: {e}")
            bill_count = 0
        
        self.logger.debug(f"BILL INDEX: Full query returned {bill_count} bills")
        
        # Strategy 2: If full query fails, try entity + document_type only
        if bill_count == 0:
            self.logger.debug(f"BILL INDEX: Trying fallback query without date filter...")
            fallback_query = db.collection("TRANSACTIONS").where(
                filter=firestore.FieldFilter("entity_id", "==", entity_id)
            ).where(
                filter=firestore.FieldFilter("document_type", "==", "BILL")
            ).limit(page_size)
            
            fallback_count = 0
            try:
                async for bill_doc in fallback_query.stream():
                    fallback_count += 1
                    if fallback_count <= 3:
                        bill_data = bill_doc.to_dict()
                        self.logger.debug(f"BILL INDEX: Fallback found bill {bill_doc.id}: date_issued={bill_data.get('date_issued')}")
            except Exception as e:
                self.logger.debug(f"BILL INDEX: Fallback query failed: {e}")
            
            self.logger.debug(f"BILL INDEX: Fallback query returned {fallback_count} bills")
            
            # Strategy 3: If still nothing, try entity only
            if fallback_count == 0:
                self.logger.debug(f"BILL INDEX: Trying entity-only query...")
                entity_query = db.collection("TRANSACTIONS").where(
                    filter=firestore.FieldFilter("entity_id", "==", entity_id)
                ).limit(page_size)
                
                entity_count = 0
                doc_types_seen = set()
                try:
                    async for doc in entity_query.stream():
                        entity_count += 1
                        doc_data = doc.to_dict()
                        doc_type = doc_data.get('document_type', 'MISSING')
                        doc_types_seen.add(doc_type)
                        if entity_count <= 5:
                            self.logger.debug(f"BILL INDEX: Entity doc {doc.id}: document_type={doc_type}")
                except Exception as e:
                    self.logger.debug(f"BILL INDEX: Entity query failed: {e}")
                
                self.logger.debug(f"BILL INDEX: Entity query returned {entity_count} docs with types: {doc_types_seen}")
        
        # Use the working query for actual bill loading
        working_query = bills_query  # Start with full query
        
        processed = 0
        linkedin_bill_found = False
        linkedin_bill_id = "61ff4abe-18ad-4160-b6cd-8e4fc709e6f7"  # Debug target
        
        async for bill_doc in bills_query.stream():
            bill_data = bill_doc.to_dict()
            bill_date = self._parse_bill_date(bill_data)
            
            # Debug logging for LinkedIn bill specifically
            if bill_doc.id == linkedin_bill_id:
                linkedin_bill_found = True
                self.logger.debug(f"BILL INDEX DEBUG: Found LinkedIn bill {linkedin_bill_id}")
                self.logger.debug(f"  Contact: {bill_data.get('contact_name', 'N/A')}")
                self.logger.debug(f"  Date issued: {bill_data.get('date_issued', 'N/A')}")
                self.logger.debug(f"  Parsed date: {bill_date}")
                self.logger.debug(f"  Cutoff date: {cutoff_date}")
                self.logger.debug(f"  Passes date filter: {not (bill_date and bill_date < cutoff_date)}")
            
            # Skip bills outside date window
            if bill_date and bill_date < cutoff_date:
                if bill_doc.id == linkedin_bill_id:
                    self.logger.debug(f"BILL INDEX DEBUG: LinkedIn bill EXCLUDED by date filter")
                continue
            
            # Memory-efficient: store only essential fields, strip raw_xero_data
            essential_data = {
                "document_number": bill_data.get("document_number", ""),
                "contact_name": bill_data.get("contact_name", ""),
                "reference": bill_data.get("reference", ""),  # Include reference for text extraction
                "total_amount": bill_data.get("total_amount", 0),
                "subtotal": bill_data.get("subtotal", 0),  # For amount scoring
                "tax_total": bill_data.get("tax_total", 0),  # For amount scoring
                "currency_code": bill_data.get("currency_code", ""),  # For financial operations
                "currency_rate": bill_data.get("currency_rate", None),  # For currency conversion
                "currency_rate_source": bill_data.get("currency_rate_source", ""),  # For exchange rate tracking
                "contact_id": bill_data.get("contact_id", ""),  # For contact matching
                "line_items": [
                    {
                        "AccountCode": item.get("AccountCode", ""),
                        "LineAmount": item.get("LineAmount", 0)
                    }
                    for item in bill_data.get("line_items", [])
                ]
            }
            
            # Determine currency rate: prefer payment-level rate if available
            currency_rate_val = None
            
            bill_index[bill_doc.id] = {
                "data": essential_data,
                "date": bill_date,
                "text_content": self._extract_bill_text_content_minimal(essential_data),
                "line_items": essential_data["line_items"]
            }
            
            # Debug logging for LinkedIn bill
            if bill_doc.id == linkedin_bill_id:
                self.logger.debug(f"BILL INDEX DEBUG: LinkedIn bill INCLUDED in index")
                self.logger.debug(f"  Essential data: {essential_data}")
            
            processed += 1
            if processed >= MAX_BILL_LINK_DOCS:  # Hard limit to prevent memory issues
                self.logger.warning(f"Bill index capped at {processed} bills for entity {entity_id} (bill_index_truncated=True)")
                break
        
        # Final debug check
        if not linkedin_bill_found:
            self.logger.debug(f"BILL INDEX DEBUG: LinkedIn bill {linkedin_bill_id} NOT FOUND in query results")
            
            # Direct query to check if LinkedIn bill exists in TRANSACTIONS at all
            try:
                linkedin_ref = db.collection("TRANSACTIONS").document(linkedin_bill_id)
                linkedin_doc = await linkedin_ref.get()
                if linkedin_doc.exists:
                    linkedin_data = linkedin_doc.to_dict()
                    self.logger.debug(f"BILL INDEX DEBUG: LinkedIn bill EXISTS in TRANSACTIONS:")
                    self.logger.debug(f"  Entity ID: {linkedin_data.get('entity_id')}")
                    self.logger.debug(f"  Document Type: {linkedin_data.get('document_type')}")
                    self.logger.debug(f"  Date Issued: {linkedin_data.get('date_issued')}")
                    self.logger.debug(f"  Contact Name: {linkedin_data.get('contact_name')}")
                else:
                    self.logger.debug(f"BILL INDEX DEBUG: LinkedIn bill DOES NOT EXIST in TRANSACTIONS collection")
            except Exception as e:
                self.logger.debug(f"BILL INDEX DEBUG: Error checking LinkedIn bill: {e}")
        
        return bill_index
    
    def _extract_bill_text_content(self, bill_data: Dict[str, Any]) -> str:
        """Extract searchable text content from bill for reference matching."""
        if not bill_data:
            return ""
            
        text_parts = []
        
        # First try to extract from raw_xero_data if available
        raw_data = bill_data.get("raw_xero_data")
        if raw_data:
            # Extract various text fields from raw Xero data
            for field in ["Reference", "Description", "Subject", "Narration"]:
                value = raw_data.get(field, "")
                if value:
                    text_parts.append(str(value))
        
        # Always include document-level fields (fallback when raw_xero_data is null)
        for field in ["document_number", "reference", "contact_name"]:
            value = bill_data.get(field, "")
            if value:
                text_parts.append(str(value))
        
        # Extract contact name from raw data if available
        if raw_data:
            contact = raw_data.get("Contact", {})
            if contact.get("Name"):
                text_parts.append(str(contact["Name"]))
            
            # Extract line item descriptions - try multiple sources for compatibility
            line_items = raw_data.get("LineItems", [])
            if not line_items:
                # Fallback to top-level line_items field for Stage 1/2 compatibility
                line_items = bill_data.get("line_items", [])
            
            for item in line_items:
                if item.get("Description"):
                    text_parts.append(str(item["Description"]))
        
        return " ".join(text_parts).upper()
    
    def _extract_bill_text_content_minimal(self, essential_data: Dict[str, Any]) -> str:
        """Memory-efficient text extraction from essential bill data only."""
        text_parts = []
        
        # Extract from essential fields - include reference for detector matching
        for field in ["document_number", "contact_name", "reference"]:
            value = essential_data.get(field, "")
            if value and value is not None:  # Null safety - ensure value exists and is not None
                text_parts.append(str(value))
        
        return " ".join(text_parts).upper()
    
    def _find_originating_bill_by_reference_from_index(
        self, 
        reference_id: str, 
        expected_amount: float,
        asset_codes: Set[str], 
        expense_codes: Set[str], 
        bill_index: Dict[str, Dict[str, Any]],
        earliest_detection_date: Optional[datetime] = None
    ) -> Optional[str]:
        """Find bill that matches the reference ID using pre-loaded bill index."""
        
        # Date bounds: bill should be before earliest detection, within reasonable window
        if earliest_detection_date:
            lookback_date = earliest_detection_date - timedelta(days=BILL_MATCH_WINDOW_DAYS * 2)
        else:
            lookback_date = datetime.now(timezone.utc) - timedelta(days=BILL_MATCH_WINDOW_DAYS * 2)
        
        best_match = None
        best_score = 0
        
        self.logger.debug(f"BILL SEARCH: Searching {len(bill_index)} bills for reference '{reference_id}'")
        bills_checked = 0
        bills_with_reference = 0
        
        for bill_id, bill_info in bill_index.items():
            bills_checked += 1
            # Apply date filtering - bills must be before release dates
            bill_date = bill_info["date"]
            if bill_date and earliest_detection_date:
                if bill_date < lookback_date or bill_date > earliest_detection_date:
                    continue  # Skip bills outside date window
            
            # Check if bill contains the reference ID
            bill_text = bill_info["text_content"].upper()
            if reference_id.upper() in bill_text:
                bills_with_reference += 1
                self.logger.debug(f"BILL SEARCH: Found reference '{reference_id}' in bill {bill_id}: '{bill_text[:200]}...'")
                bill_score = self._score_bill_match_from_index(
                    bill_info, reference_id, expected_amount, asset_codes, expense_codes
                )
                self.logger.debug(f"BILL SEARCH: Bill {bill_id} scored {bill_score} (expected_amount={expected_amount})")
                
                if bill_score > best_score:
                    best_score = bill_score
                    best_match = bill_id
        
        self.logger.debug(f"BILL SEARCH: Summary - checked {bills_checked} bills, found {bills_with_reference} with reference, best_score={best_score}")
        
        # Only return match if score meets threshold
        return best_match if best_score >= 0.5 else None
    
    def _find_originating_bill_by_amount_from_index(
        self, 
        detection: Dict[str, Any], 
        asset_codes: Set[str], 
        expense_codes: Set[str], 
        bill_index: Dict[str, Dict[str, Any]]
    ) -> Optional[str]:
        """Find bill that matches the detection amount using pre-loaded bill index."""
        
        detection_amount = detection.get("amount", 0)
        detection_date = detection.get("journal_date")
        asset_code = detection.get("asset_account_code")
        expense_code = detection.get("expense_account_code")
        
        if not all([detection_amount, detection_date, asset_code, expense_code]):
            return None
        
        # Look back up to 1 year for the originating bill
        lookback_date = detection_date - timedelta(days=BILL_MATCH_WINDOW_DAYS)
        
        best_match = None
        best_score = 0
        
        for bill_id, bill_info in bill_index.items():
            # Apply date filtering
            bill_date = bill_info["date"]
            if bill_date and (bill_date < lookback_date or bill_date > detection_date):
                continue  # Skip bills outside date window
            
            # Score bill based on amount, date, and account patterns
            bill_score = self._score_bill_match_from_index(
                bill_info, None, detection_amount, asset_codes, expense_codes
            )
            
            if bill_score > best_score:
                best_score = bill_score
                best_match = bill_id
        
        return best_match if best_score >= 0.5 else None  # Lower threshold for amount-only matching
    
    def _parse_bill_date(self, bill_data: Dict[str, Any]) -> Optional[datetime]:
        """Parse bill date from various formats."""
        if not bill_data:
            return None
            
        # Try document-level fields first (more reliable)
        doc_date_fields = ["date_issued", "date_due", "created_at"]
        for field in doc_date_fields:
            date_value = bill_data.get(field)
            if date_value:
                try:
                    # Handle datetime objects (from enhanced Stage 1 data)
                    if isinstance(date_value, datetime):
                        return date_value if date_value.tzinfo else date_value.replace(tzinfo=timezone.utc)
                    # Handle Firestore Timestamp objects
                    elif hasattr(date_value, 'timestamp'):
                        return date_value if date_value.tzinfo else date_value.replace(tzinfo=timezone.utc)
                    # Handle string formats
                    elif isinstance(date_value, str):
                        return datetime.fromisoformat(date_value.replace('Z', '+00:00'))
                except (ValueError, AttributeError):
                    continue
        
        # Fallback to raw_xero_data if available
        raw_xero_data = bill_data.get("raw_xero_data")
        if raw_xero_data:
            xero_date_fields = ["Date", "date", "DueDate", "InvoiceDate"]
            for field in xero_date_fields:
                date_str = raw_xero_data.get(field)
                if not date_str:
                    continue
                
                try:
                    # Handle Xero's /Date(timestamp)/ format
                    if isinstance(date_str, str) and date_str.startswith("/Date(") and date_str.endswith(")/"):
                        ts_match = re.search(r'(\d+)', date_str)
                        if ts_match:
                            timestamp_ms = int(ts_match.group(1))
                            return datetime.fromtimestamp(timestamp_ms / 1000, tz=timezone.utc)
                    
                    # Handle ISO format
                    if isinstance(date_str, str):
                        return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                        
                except (ValueError, TypeError):
                    continue
        
        return None
    
    def _score_bill_match(
        self, 
        bill_data: Dict[str, Any], 
        reference_id: Optional[str], 
        target_amount: float,
        asset_codes: Set[str], 
        expense_codes: Set[str]
    ) -> float:
        """
        Score how well a bill matches as the originating transaction.
        
        Returns score 0.0-1.0 where 1.0 is perfect match.
        """
        score = 0.0
        
        # Reference ID match (highest weight)
        if reference_id:
            bill_text = " ".join([
                str(bill_data.get("raw_xero_data", {}).get("Reference", "")),
                str(bill_data.get("raw_xero_data", {}).get("Description", "")),
                str(bill_data.get("raw_xero_data", {}).get("Contact", {}).get("Name", ""))
            ]).upper()
            
            if reference_id.upper() in bill_text:
                score += 0.5  # Strong reference match
        
        # Amount correlation - try multiple sources for Stage 1/2 compatibility
        line_items = bill_data.get("raw_xero_data", {}).get("LineItems", [])
        if not line_items:
            # Fallback to top-level line_items field for Stage 1/2 compatibility
            line_items = bill_data.get("line_items", [])
        
        if line_items:
            bill_total = sum(abs(float(item.get("LineAmount", 0))) for item in line_items)
            amount_diff = abs(bill_total - target_amount) / max(target_amount, 1)
            
            if amount_diff <= 0.05:  # Within 5%
                score += 0.3
            elif amount_diff <= 0.1:  # Within 10%
                score += 0.2
            elif amount_diff <= 0.2:  # Within 20%
                score += 0.1
        
        # Account pattern match (prepayment coding)
        has_prepayment_coding = any(
            item.get("AccountCode") in asset_codes
            for item in line_items
        )
        
        if has_prepayment_coding:
            score += 0.2  # Bill coded to prepayment account
        
        return min(score, 1.0)
    
    def _score_bill_match_from_index(
        self, 
        bill_info: Dict[str, Any], 
        reference_id: Optional[str], 
        target_amount: float,
        asset_codes: Set[str], 
        expense_codes: Set[str]
    ) -> float:
        """Score how well a bill matches using pre-loaded bill index data."""
        score = 0.0
        
        # Reference ID match (highest weight) - should be nearly definitive
        if reference_id and reference_id.upper() in bill_info["text_content"]:
            score += 0.8  # Very strong reference match - this should be enough on its own
        
        # Amount correlation - use total_amount (includes GST) to match MJ totals
        bill_data = bill_info["data"]
        line_items = bill_info["line_items"]  # Always get line_items for later use
        
        try:
            # Prefer total_amount (includes tax) over line item sum (net)
            bill_total = float(bill_data.get("total_amount", 0))
            if bill_total == 0:
                # Fallback to line items if total_amount not available
                bill_total = sum(abs(float(item.get("LineAmount", 0))) for item in line_items)
            
            if bill_total > 0:
                amount_diff = abs(bill_total - target_amount) / max(target_amount, 1)
                
                if amount_diff <= 0.05:  # Within 5%
                    score += 0.15
                elif amount_diff <= 0.15:  # Within 15% (accounts for GST variance)
                    score += 0.1
                elif amount_diff <= 0.25:  # Within 25%
                    score += 0.05
        except (ValueError, TypeError, ZeroDivisionError):
            # Skip amount scoring if conversion fails
            pass
        
        # Account pattern match (prepayment coding)
        has_prepayment_coding = any(
            item.get("AccountCode") in asset_codes
            for item in line_items
        )
        
        if has_prepayment_coding:
            score += 0.2  # Bill coded to prepayment account
        
        return min(score, 1.0)
    
    # _update_detection_link method removed - now using batch operations in _link_detections_to_bills
    
    async def _find_best_bill_match_multi_signal(self, detection: Dict[str, Any], 
                                               bill_index: Dict[str, Any], 
                                               asset_codes: set,
                                               db: firestore.AsyncClient) -> Optional[str]:
        """
        Find best matching bill using multi-signal fuzzy matching
        
        Args:
            detection: Detection data with journal information
            bill_index: Pre-built bill index for performance
            asset_codes: Set of asset account codes for weighting
            db: Firestore client (for fetching full manual journal if needed)
            
        Returns:
            Bill ID if match found above confidence threshold, None otherwise
        """
        if not bill_index:
            self.logger.debug(f"No bills in index for multi-signal matching")
            return None
        
        journal_id = detection.get("journal_id")
        
        # Get manual journal data - detection might not have all fields
        try:
            mj_ref = db.collection("MANUAL_JOURNALS").document(journal_id)
            mj_doc = await mj_ref.get()
            
            if not mj_doc.exists:
                self.logger.warning(f"Manual journal {journal_id} not found for multi-signal matching")
                return None
            
            manual_journal = mj_doc.to_dict()
            
            # Don't override amount field - let BillMatcher calculate from journal_lines
            # The BillMatcher has been fixed to properly calculate amounts from journal lines
            # manual_journal["amount"] = detection.get("amount", 0)  # Removed - was causing currency conversion issues
            
        except Exception as e:
            self.logger.error(f"Error fetching manual journal {journal_id}: {e}")
            return None
        
        # Convert bill index to list of bill data
        candidate_bills = []
        for bill_id, bill_info in bill_index.items():
            bill_data = bill_info["data"].copy()
            bill_data["transaction_id"] = bill_id
            # Ensure we have line_items in the format expected by matcher
            bill_data["line_items"] = bill_info.get("line_items", [])
            candidate_bills.append(bill_data)
        
        # Pre-filter bills for performance (date range, currency if available)
        filtered_bills = self._prefilter_candidate_bills(manual_journal, candidate_bills)
        
        if not filtered_bills:
            self.logger.debug(f"No candidate bills after pre-filtering for MJ {journal_id}")
            return None
        
        self.logger.debug(f"Multi-signal matching MJ {journal_id} against {len(filtered_bills)} filtered bills")
        
        # Use BillMatcher to find best match
        match_result = self.bill_matcher.find_best_match(manual_journal, filtered_bills, asset_codes)
        
        if match_result and match_result.should_link:
            self.logger.info(f"Multi-signal match found: MJ {journal_id} → Bill {match_result.bill_id} "
                           f"(confidence: {match_result.confidence_score:.3f})")
            self.logger.info(f"DEBUG: Returning bill_id {match_result.bill_id} from _find_best_bill_match_multi_signal")
            
            # Log match features for audit trail
            features = match_result.features
            self.logger.info(f"Match features: name_sim={features.name_similarity:.3f}, "
                           f"account_overlap={features.account_overlap:.3f}, "
                           f"amount_ratio={features.amount_ratio:.3f}, "
                           f"date_valid={features.date_validity}, "
                           f"tokens={features.extracted_tokens}")
            
            return match_result.bill_id
        else:
            if match_result:
                self.logger.debug(f"Best match for MJ {journal_id} below threshold: "
                                f"Bill {match_result.bill_id} (confidence: {match_result.confidence_score:.3f})")
            else:
                self.logger.debug(f"No matches found for MJ {journal_id}")
            return None
    
    def _prefilter_candidate_bills(self, manual_journal: Dict[str, Any], 
                                 candidate_bills: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Pre-filter candidate bills for performance optimization
        
        Args:
            manual_journal: Manual journal data
            candidate_bills: List of all candidate bills
            
        Returns:
            Filtered list of bills within reasonable matching criteria
        """
        # Get manual journal date
        mj_date = manual_journal.get('date') or manual_journal.get('journalDate')
        if isinstance(mj_date, str):
            try:
                mj_date = datetime.fromisoformat(mj_date.replace('Z', '+00:00'))
            except ValueError:
                mj_date = None
        
        # Get manual journal currency (if available)
        mj_currency = manual_journal.get('currency_code', 'GBP')  # Default to GBP
        
        filtered_bills = []
        
        for bill in candidate_bills:
            # Date filtering: bill should be before manual journal
            bill_date = bill.get('date_issued')
            if bill_date and mj_date:
                if isinstance(bill_date, str):
                    try:
                        bill_date = datetime.fromisoformat(bill_date.replace('Z', '+00:00'))
                    except ValueError:
                        continue  # Skip bills with unparseable dates
                
                # Bill should be within reasonable date range of manual journal
                # Allow bills both before and after journal date for prepayment matching
                days_gap = abs((mj_date.date() - bill_date.date()).days)
                if days_gap > self.bill_matcher.max_days_gap:
                    continue
            
            # Currency filtering (if both have currency info)
            # Allow cross-currency matching for supported pairs
            bill_currency = bill.get('currency_code', 'GBP')
            if mj_currency and bill_currency and mj_currency != bill_currency:
                # Use centralized configuration for supported currency pairs
                from ..config.detector_config import SUPPORTED_CURRENCY_PAIRS
                currency_pair = (mj_currency.upper(), bill_currency.upper())
                if currency_pair not in SUPPORTED_CURRENCY_PAIRS:
                    continue  # Skip unsupported currency pairs
            
            # Amount filtering: bill should be reasonably close to MJ amount
            # Calculate amount from journal lines since 'amount' field is often None
            # Use the maximum absolute line amount to avoid double counting
            mj_amount = 0
            for line in manual_journal.get('journal_lines', []):
                line_amount = abs(line.get('LineAmount', 0) or 0)
                mj_amount = max(mj_amount, line_amount)
            
            if mj_amount == 0:
                mj_amount = abs(manual_journal.get('amount', 0))  # Fallback to amount field
            bill_amount = abs(bill.get('total_amount', 0))
            
            if mj_amount > 0 and bill_amount > 0:
                ratio = min(mj_amount, bill_amount) / max(mj_amount, bill_amount)
                if ratio < 0.01:  # If amounts are >100x different, skip
                    continue
            
            filtered_bills.append(bill)
        
        return filtered_bills
    
    async def _create_schedules_for_high_confidence_detections(
        self, 
        entity_id: str, 
        detections: List[Dict[str, Any]], 
        db: firestore.AsyncClient
    ) -> int:
        """
        Create synthetic schedules for high-confidence detections that have bill links.
        
        Only creates schedules for:
        - High confidence detections (not medium)
        - Detections that have a linked_transaction_id (successful bill match)
        - Detections that don't already have a synthetic_schedule_id
        
        Args:
            entity_id: Entity context
            detections: List of detection results
            db: Firestore async client
            
        Returns:
            Number of schedules created
        """
        schedules_created = 0
        
        try:
            for detection in detections:
                # Only process high-confidence detections
                if detection.get("detection_confidence") != "high":
                    continue
                
                # Must have a linked transaction (successful bill match)
                linked_transaction_id = detection.get("linked_transaction_id")
                if not linked_transaction_id:
                    continue
                
                journal_id = detection.get("journal_id")
                if not journal_id:
                    continue
                
                # Check if schedule already exists for this detection
                detection_ref = db.collection(PREPAYMENT_RELEASES_DETECTED_COLLECTION).document(journal_id)
                detection_doc = await detection_ref.get()
                
                if detection_doc.exists:
                    detection_data = detection_doc.to_dict()
                    existing_schedule_id = detection_data.get("synthetic_schedule_id")
                    
                    if existing_schedule_id:
                        self.logger.debug(f"Schedule {existing_schedule_id} already exists for detection {journal_id}, skipping")
                        continue
                
                # Create synthetic schedule
                self.logger.info(f"Creating automatic synthetic schedule for high-confidence detection: MJ {journal_id} → Bill {linked_transaction_id}")
                
                schedule_id = await self.create_synthetic_schedule(
                    mj_id=journal_id,
                    bill_id=linked_transaction_id,
                    db=db
                )
                
                if schedule_id:
                    schedules_created += 1
                    self.logger.info(f"Successfully created automatic synthetic schedule {schedule_id} for detection {journal_id}")
                else:
                    self.logger.warning(f"Failed to create synthetic schedule for detection {journal_id}")
                    
        except Exception as e:
            self.logger.error(f"Error creating automatic schedules for entity {entity_id}: {e}", exc_info=True)
        
        return schedules_created