# Amortization Scripts

This folder contains utility scripts for testing and managing the hybrid amortization system.

## Scripts

### `test_hybrid_amortization.py`
Tests the hybrid amortization logic with various scenarios to verify correct behavior.

**Usage:**
```bash
python scripts/amortization/test_hybrid_amortization.py
```

**Features:**
- Tests both 12-month equal and day-based distribution methods
- Validates 50% rule application
- Checks amount accuracy and period counts
- Provides detailed breakdown analysis

### `check_entity_settings.py`
Checks and adds the materiality threshold to entity settings.

**Usage:**
```bash
python scripts/amortization/check_entity_settings.py
```

**Features:**
- Verifies current entity settings
- Adds missing `amortization_materiality_threshold` field
- Shows current amortization-related configuration

### `regenerate_ksp_schedule.py`
Regenerates the KSP Rechtsanwalt schedule with the new hybrid logic.

**Usage:**
```bash
python scripts/amortization/regenerate_ksp_schedule.py
```

**Features:**
- Deletes old schedule created with legacy logic
- Generates new schedule using hybrid amortization
- Provides detailed comparison and verification

### `update_threshold.py`
Updates the materiality threshold and regenerates schedules to test different methods.

**Usage:**
```bash
python scripts/amortization/update_threshold.py
```

**Features:**
- Updates entity settings with new threshold
- Regenerates KSP schedule with new method
- Shows detailed day-based distribution analysis

## Configuration

All scripts use the following entity configuration:
- **Entity ID**: `a8e46b01-de7d-42bb-ab01-d1a395573d51`
- **Client ID**: `49687fc2-556d-4116-b441-505771881a01`
- **Transaction ID**: `60d8b891-aabb-4dc4-9cc9-5563eb68e8ca` (KSP Rechtsanwalt)

## Requirements

- Python 3.7+
- Google Cloud Firestore access
- Project dependencies installed
- Proper authentication configured

## Testing Workflow

1. **Test Logic**: Run `test_hybrid_amortization.py` to verify calculations
2. **Check Settings**: Run `check_entity_settings.py` to ensure threshold is set
3. **Test Small Amount**: Set threshold high (e.g., €1000) and regenerate
4. **Test Large Amount**: Set threshold low (e.g., €50) and regenerate
5. **Compare Results**: Analyze different distribution methods 