import React, { useState, useCallback } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { AlertTriangle, Info, CheckCircle } from 'lucide-react';

interface ConfirmDialogOptions {
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'warning' | 'destructive' | 'success';
  onConfirm: () => void | Promise<void>;
  onCancel?: () => void;
}

interface DialogState extends ConfirmDialogOptions {
  isOpen: boolean;
  isLoading: boolean;
}

export function useConfirmDialog() {
  const [dialogState, setDialogState] = useState<DialogState | null>(null);

  const openDialog = useCallback((options: ConfirmDialogOptions) => {
    setDialogState({
      ...options,
      isOpen: true,
      isLoading: false,
      confirmText: options.confirmText || 'Confirm',
      cancelText: options.cancelText || 'Cancel',
      variant: options.variant || 'default',
    });
  }, []);

  const closeDialog = useCallback(() => {
    if (dialogState && !dialogState.isLoading) {
      setDialogState(null);
    }
  }, [dialogState]);

  const handleConfirm = useCallback(async () => {
    if (!dialogState || dialogState.isLoading) return;

    setDialogState(prev => prev ? { ...prev, isLoading: true } : null);

    try {
      await dialogState.onConfirm();
      closeDialog();
    } catch (error) {
      // Keep dialog open on error
      setDialogState(prev => prev ? { ...prev, isLoading: false } : null);
    }
  }, [dialogState, closeDialog]);

  const handleCancel = useCallback(() => {
    if (dialogState && !dialogState.isLoading) {
      if (dialogState.onCancel) {
        dialogState.onCancel();
      }
      closeDialog();
    }
  }, [dialogState, closeDialog]);

  const getVariantIcon = () => {
    if (!dialogState) return null;
    
    switch (dialogState.variant) {
      case 'warning':
      case 'destructive':
        return <AlertTriangle className="h-6 w-6 text-amber-600" />;
      case 'success':
        return <CheckCircle className="h-6 w-6 text-green-600" />;
      default:
        return <Info className="h-6 w-6 text-blue-600" />;
    }
  };

  const getVariantStyles = () => {
    if (!dialogState) return {};
    
    switch (dialogState.variant) {
      case 'destructive':
        return { 
          buttonClass: 'bg-red-600 hover:bg-red-700 text-white',
          headerClass: 'text-red-900'
        };
      case 'warning':
        return { 
          buttonClass: 'bg-amber-600 hover:bg-amber-700 text-white',
          headerClass: 'text-amber-900'
        };
      case 'success':
        return { 
          buttonClass: 'bg-green-600 hover:bg-green-700 text-white',
          headerClass: 'text-green-900'
        };
      default:
        return { 
          buttonClass: 'bg-blue-600 hover:bg-blue-700 text-white',
          headerClass: 'text-blue-900'
        };
    }
  };

  const dialogElement = dialogState ? (
    <AlertDialog open={dialogState.isOpen} onOpenChange={closeDialog}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <div className="flex items-center gap-3">
            {getVariantIcon()}
            <AlertDialogTitle className={getVariantStyles().headerClass}>
              {dialogState.title}
            </AlertDialogTitle>
          </div>
          <AlertDialogDescription>
            {dialogState.description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel 
            onClick={handleCancel}
            disabled={dialogState.isLoading}
          >
            {dialogState.cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={dialogState.isLoading}
            className={getVariantStyles().buttonClass}
          >
            {dialogState.isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Processing...
              </>
            ) : (
              dialogState.confirmText
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  ) : null;

  return {
    openDialog,
    closeDialog,
    dialogElement,
    isOpen: dialogState?.isOpen || false,
    isLoading: dialogState?.isLoading || false,
  };
}