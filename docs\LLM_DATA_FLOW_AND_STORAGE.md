# LLM Data Flow and Storage Documentation

## Overview

The DRCR system uses LLM (Large Language Model) processing to analyze invoice attachments for prepayment detection and service period extraction. This document explains how LLM extracted data flows through the system and where it's stored.

## Data Flow Architecture

```
Xero Invoice/Bill → Attachments → LLM Processing → Storage Decision
                                                        ↓
                                    ┌─────────────────────────────────┐
                                    │                                 │
                                    ▼                                 ▼
                            ATTACHMENTS Collection          TRANSACTIONS Collection
                            (Always stores ALL              (Only stores IF prepayment
                             LLM extracted data)             detected by LLM)
```

## Storage Collections

### 1. ATTACHMENTS Collection
**Purpose:** Stores ALL LLM processing results regardless of prepayment classification

**Document ID:** `{attachment_id}` (from Xero)

**Key Fields:**
```json
{
  "transaction_id": "xero-invoice-id",
  "entity_id": "entity-uuid",
  "source_system": "XERO",
  "external_id": "xero-attachment-id",
  "file_name": "invoice.pdf",
  "mime_type": "application/pdf",
  "file_size": 245760,
  "gcs_path": "attachments/entity-id/transaction-id/attachment-id",
  "llm_processed": true,
  "llm_extraction_data": {
    // ALL LLM extracted data stored here
    "System_IsPrepayment": false,
    "System_PrepaymentReason": "No service period indicators found",
    "ExpectedServiceStartDate": null,
    "ExpectedServiceEndDate": null,
    "System_TotalValidation": {
      "total_amount_matches": true,
      "currency_matches": true,
      "vendor_name_similarity": 0.95
    },
    "_system_extraction_method": "openai_direct",
    "_system_servicePeriodSource": "not_detected",
    "_system_servicePeriodInferenceConfidence": "low"
  },
  "created_at": "2025-06-04T15:41:10Z",
  "updated_at": "2025-06-04T15:41:10Z"
}
```

### 2. TRANSACTIONS Collection
**Purpose:** Stores business transaction data with selective LLM results

**Document ID:** `{transaction_id}` (from Xero)

**Key Fields:**
```json
{
  "transaction_id": "xero-invoice-id",
  "entity_id": "entity-uuid",
  "client_id": "client-uuid",
  "document_type": "BILL",
  "transaction_type": "ACCPAY",
  // ... other transaction fields ...
  "attachments_processed": 1,
  "llm_processing_completed": true,
  "prepayment_analysis": {
    "gl_based_analysis_completed": true,
    "llm_based_analysis_completed": true,
    "has_prepayment_line_items": false,
    "detection_methods": ["GL_CODING"],
    "prepayment_line_items": [],
    "llm_detections": [], // ONLY populated if LLM detected prepayment
    "confidence_score": 0.8,
    "recommended_action": "no_prepayment",
    "best_service_period": null // ONLY set if LLM found service period
  }
}
```

## LLM Detection Logic

### When LLM Data Gets Copied to TRANSACTIONS

The LLM extracted data is **ONLY** copied from ATTACHMENTS to TRANSACTIONS if:

```python
if attachment_data.get("System_IsPrepayment") == True:
    # Copy LLM data to TRANSACTIONS.prepayment_analysis.llm_detections
```

### Decision Matrix

| LLM Result | ATTACHMENTS Storage | TRANSACTIONS.llm_detections | Reason |
|------------|-------------------|----------------------------|---------|
| `System_IsPrepayment: true` | ✅ Full data | ✅ Copied | Prepayment detected |
| `System_IsPrepayment: false` | ✅ Full data | ❌ Empty array | No prepayment detected |
| LLM processing failed | ❌ No data | ❌ Empty array | Processing error |

## Field Reference

### TRANSACTIONS.prepayment_analysis Fields

| Field | Type | Description | When Populated |
|-------|------|-------------|----------------|
| `gl_based_analysis_completed` | boolean | GL account code analysis done | Always |
| `llm_based_analysis_completed` | boolean | LLM processing completed | When attachments processed |
| `has_prepayment_line_items` | boolean | GL codes indicate prepayment | When GL analysis finds prepayment accounts |
| `detection_methods` | array | Methods used for detection | `["GL_CODING"]`, `["LLM_ANALYSIS"]`, or both |
| `prepayment_line_items` | array | Line items with prepayment GL codes | When GL analysis finds prepayment accounts |
| `llm_detections` | array | LLM prepayment detections | **ONLY when LLM detects prepayment** |
| `confidence_score` | number | Overall confidence (0.0-1.0) | Always |
| `recommended_action` | string | System recommendation | Always |
| `best_service_period` | object | Best service period found | **ONLY when LLM finds service period** |

### llm_detections Array Structure

```json
{
  "extraction_method": "openai_direct",
  "is_prepayment": true,
  "prepayment_reason": "12-month software subscription",
  "service_start_date": "2025-01-01",
  "service_end_date": "2025-12-31",
  "service_period_source": "invoice_text",
  "confidence": "high",
  "total_validation": {
    "total_amount_matches": true,
    "currency_matches": true,
    "vendor_name_similarity": 0.95
  }
}
```

### recommended_action Values

| Value | Meaning | Triggers |
|-------|---------|----------|
| `"create_amortization_schedule"` | Create amortization | GL coding + LLM detection, OR GL coding only |
| `"review_for_prepayment"` | Manual review needed | LLM detection only with confidence ≥ 0.6 |
| `"no_prepayment"` | No prepayment detected | No GL coding, no LLM detection |
| `"pending_analysis"` | Analysis not complete | Initial state |

## Common Scenarios

### Scenario 1: LLM Processed but No Prepayment
```json
{
  "attachments_processed": 1,
  "llm_processing_completed": true,
  "prepayment_analysis": {
    "llm_based_analysis_completed": true,
    "llm_detections": [], // Empty because System_IsPrepayment: false
    "recommended_action": "no_prepayment"
  }
}
```
**Where to find LLM data:** Check ATTACHMENTS collection

### Scenario 2: LLM Detected Prepayment
```json
{
  "attachments_processed": 1,
  "llm_processing_completed": true,
  "prepayment_analysis": {
    "llm_based_analysis_completed": true,
    "llm_detections": [{ /* LLM data copied here */ }],
    "best_service_period": { /* Service period data */ },
    "recommended_action": "review_for_prepayment"
  }
}
```

### Scenario 3: LLM Processing Failed
```json
{
  "attachments_processed": 0,
  "llm_processing_completed": false,
  "prepayment_analysis": {
    "llm_based_analysis_completed": false,
    "llm_detections": [],
    "recommended_action": "no_prepayment"
  }
}
```

## Troubleshooting Guide

### Q: Why is llm_detections empty when attachments_processed > 0?

**A:** The LLM processed the attachment but determined it's NOT a prepayment (`System_IsPrepayment: false`). Check the ATTACHMENTS collection for the full LLM extraction data.

### Q: Where can I see what the LLM actually extracted?

**A:** Always check the ATTACHMENTS collection. The `llm_extraction_data` field contains ALL LLM results regardless of prepayment classification.

### Q: Why is llm_processing_completed true but llm_based_analysis_completed false?

**A:** This indicates an inconsistency. Both should have the same value. Check logs for processing errors.

### Q: How do I find the attachment document for a transaction?

**A:** Query ATTACHMENTS collection where `transaction_id` equals your transaction ID.

## Configuration

### Entity Settings
```json
{
  "enable_llm_prepayment_detection": true, // Enable/disable LLM processing
  "prepayment_asset_account_codes": ["620", "621"] // GL codes for prepayment accounts
}
```

## Performance Considerations

1. **ATTACHMENTS Collection Growth:** Stores all LLM data permanently
2. **TRANSACTIONS Collection Size:** Only stores LLM data when prepayments detected
3. **Query Patterns:** Use ATTACHMENTS for detailed LLM analysis, TRANSACTIONS for business logic
4. **Storage Costs:** Consider archiving old ATTACHMENTS documents

## Related Documentation

- **LLM Integration System Overview:** `docs/DOCUMENTATION_INDEX.md` (lines 37-59)
- **Document Processor Functions:** `drcr_shared_logic/document_processor.py`
- **LLM Utils Implementation:** `cloud_functions/xero_sync_consumer/llm_utils.py`
- **Sync Consumer Main Logic:** `cloud_functions/xero_sync_consumer/main.py`
