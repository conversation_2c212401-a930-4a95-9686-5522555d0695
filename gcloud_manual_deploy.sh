# Step 1: Build Docker image
docker build -t drcr_backend .

# Step 2: Authenticate Google Cloud SDK
gcloud auth login

# Step 3: Set desired project
gcloud config set project drcr-d660a

# Step 4: Tag Docker image
docker tag drcr_backend gcr.io/drcr-d660a/drcr_backend

# Step 5: Push Docker image to Google Container Registry
docker push gcr.io/drcr-d660a/drcr_backend

# Step 6: Deploy to Google Cloud Run with proper resource limits for heavy processing
# Note: --timeout sets both service timeout AND request timeout to the same value
gcloud run deploy drcr-backend \
  --image gcr.io/drcr-d660a/drcr_backend \
  --platform managed \
  --region europe-west2 \
  --allow-unauthenticated \
  --port 8080 \
  --timeout 3600 \
  --memory 2Gi \
  --cpu 2 \
  --concurrency 10 \
  --max-instances 5 \
  --set-env-vars "IS_CLOUD_RUN=1"

echo "Deployed with 1-hour timeout for heavy sync processing"