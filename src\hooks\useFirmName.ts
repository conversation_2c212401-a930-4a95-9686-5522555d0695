import { useMemo } from 'react';
import { useAuthStore } from '../store/auth.store';

export interface UseFirmNameReturn {
  firmName: string;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Custom hook to get firm name from auth store
 * Now uses the firm name directly from the user profile (fetched via /auth/me)
 */
export function useFirmName(): UseFirmNameReturn {
  const { user, isLoading: authLoading, initialize } = useAuthStore();

  const firmName = useMemo(() => {
    // If we have the firm name from the auth store, use it
    if (user?.firmName) {
      return user.firmName;
    }

    // If user has a firm ID but no name, show a fallback
    if (user?.firmId) {
      return `Firm ${user.firmId.slice(-8)}`;
    }

    // Default fallback
    return 'DRCR';
  }, [user?.firmName, user?.firmId]);

  // Refetch function - reinitialize auth to get fresh data
  const refetch = async () => {
    await initialize();
  };

  return {
    firmName,
    isLoading: authLoading,
    error: null, // Auth store handles errors separately
    refetch
  };
}
