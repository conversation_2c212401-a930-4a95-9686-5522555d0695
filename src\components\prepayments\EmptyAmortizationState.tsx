import React from 'react';
import { <PERSON><PERSON><PERSON>t, ArrowUp, ArrowDown, ArrowLeft, ArrowRight, Space, Command } from 'lucide-react';

interface EmptyAmortizationStateProps {
  focusedInvoiceIndex?: number;
  totalInvoices?: number;
}

export function EmptyAmortizationState({ focusedInvoiceIndex = -1, totalInvoices = 0 }: EmptyAmortizationStateProps) {
  return (
    <div className="flex items-center justify-center h-full min-h-[400px]">
      <div className="text-center max-w-md mx-auto p-8">
        <div className="mb-6">
          <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Line Items Selected
          </h3>
          <p className="text-gray-500 mb-6">
            Select line items from an invoice in the left panel to configure amortization schedules
          </p>
        </div>

        <div className="bg-blue-50 rounded-lg p-4 text-left">
          <h4 className="text-sm font-medium text-blue-900 mb-3 flex items-center">
            💡 Navigation Tips
          </h4>
          
          <div className="space-y-2 text-sm text-blue-800">
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <ArrowUp className="w-3 h-3" />
                <ArrowDown className="w-3 h-3" />
              </div>
              <span>Navigate invoices/line items</span>
            </div>
            
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <ArrowLeft className="w-3 h-3" />
                <ArrowRight className="w-3 h-3" />
              </div>
              <span>Expand/collapse invoice</span>
            </div>
            
            <div className="flex items-center gap-2">
              <kbd className="px-1.5 py-0.5 text-xs bg-blue-100 rounded border">Space</kbd>
              <span>Select invoice/line item</span>
            </div>
            
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <Command className="w-3 h-3" />
                <span className="text-xs">+</span>
                <kbd className="px-1.5 py-0.5 text-xs bg-blue-100 rounded border">A</kbd>
              </div>
              <span>Select all line items from current invoice</span>
            </div>
          </div>
        </div>

        <div className="mt-4 text-xs text-gray-400">
          Press <kbd className="px-1 py-0.5 bg-gray-100 rounded">Shift+?</kbd> for all keyboard shortcuts
          {focusedInvoiceIndex >= 0 && (
            <div className="mt-2 text-xs text-blue-600">
              🎯 Focused: Invoice {focusedInvoiceIndex + 1} of {totalInvoices}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}