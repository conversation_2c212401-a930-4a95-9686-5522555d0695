from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import date, datetime
from enum import Enum

class ScheduleStatus(str, Enum):
    PENDING_CONFIGURATION = "pending_configuration"  # LLM-detected schedules needing account configuration
    PROPOSED = "proposed"   # Schedule proposed and ready for review/approval
    CONFIRMED = "confirmed" # Approved and ready for posting to accounting system
    POSTED = "posted"       # Journal successfully posted to accounting system
    SKIPPED = "skipped"     # User chose to skip this schedule
    CANCELLED = "cancelled"
    ERROR = "error"
    # Monthly entry specific statuses
    DUE_FOR_POSTING = "due_for_posting"         # Monthly entry ready to be posted to Xero
    POSTING_FAILED = "posting_failed"           # Monthly entry failed to post to Xero
    JOURNAL_PROPOSED = "journal_proposed"       # Journal created but not yet posted

class Schedule(BaseModel):
    id: str = Field(..., description="Unique identifier for the amortization schedule entry (Firestore Document ID)")
    transaction_id: str = Field(..., description="ID of the parent transaction this schedule belongs to")
    line_item_id: Optional[str] = Field(None, description="ID of the specific line item this schedule refers to, if applicable")
    
    status: ScheduleStatus = Field(..., description="Current status of the schedule entry")
    entry_date: date = Field(..., description="Date the schedule entry is due to be recognized/posted")
    amount: float = Field(..., description="Amount to be amortized for this entry")
    currency: str = Field(..., description="Currency of the amount (e.g., USD)")
    
    description: Optional[str] = Field(None, description="Description for this schedule entry")
    account_code: Optional[str] = Field(None, description="Asset/prepayment account code for this amortization entry")
    expense_account_code: Optional[str] = Field(None, description="Expense account code for this amortization entry")
    
    # Calculation metadata
    calculation_method: Optional[str] = Field(None, description="Amortization calculation method used ('day_based' or 'equal_monthly')")
    detection_method: Optional[str] = Field(None, description="How this schedule was detected ('llm_only' or 'gl_coding')")
    
    # Journal related fields (populated if a journal is created/posted)
    journal_id_external: Optional[str] = Field(None, description="ID of the journal entry in the external accounting system (e.g., Xero JournalID)")
    journal_link_external: Optional[str] = Field(None, description="Direct link to the journal entry in the external accounting system")

    # Monthly entries for master schedules
    monthly_entries: Optional[List[Dict[str, Any]]] = Field(None, description="Monthly breakdown entries for master amortization schedules")

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.now, description="Timestamp of when the schedule entry was created")
    updated_at: datetime = Field(default_factory=datetime.now, description="Timestamp of when the schedule entry was last updated")

    class Config:
        from_attributes = True
        use_enum_values = True # Ensure enum values are used in serialization 