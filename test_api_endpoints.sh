#!/bin/bash

# DRCR Backend API Endpoint Testing Script
# This script tests the API endpoints to ensure they're properly configured
# Run this when the server is running on localhost:8081

BASE_URL="http://localhost:8081"
HEALTH_URL="$BASE_URL/health"

echo "=== DRCR Backend API Endpoint Testing ==="
echo "Base URL: $BASE_URL"
echo

# Test 1: Health check
echo "1. Testing health endpoint..."
if curl -s -f "$HEALTH_URL" > /dev/null; then
    echo "✅ Health endpoint responds"
    echo "Response: $(curl -s "$HEALTH_URL")"
else
    echo "❌ Health endpoint failed or server not running"
    echo "Make sure server is running: cd rest_api && python3 run_server.py"
    exit 1
fi
echo

# Test 2: Root endpoint
echo "2. Testing root endpoint..."
ROOT_RESPONSE=$(curl -s "$BASE_URL/")
if echo "$ROOT_RESPONSE" | grep -q "DRCR Backend API"; then
    echo "✅ Root endpoint responds correctly"
else
    echo "❌ Root endpoint unexpected response"
    echo "Response: $ROOT_RESPONSE"
fi
echo

# Test 3: OpenAPI docs (if debug mode)
echo "3. Testing OpenAPI documentation..."
DOCS_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/docs")
if [ "$DOCS_RESPONSE" == "200" ]; then
    echo "✅ OpenAPI docs available at $BASE_URL/docs"
elif [ "$DOCS_RESPONSE" == "404" ]; then
    echo "ℹ️ OpenAPI docs disabled (production mode)"
else
    echo "⚠️ Unexpected response from docs endpoint: $DOCS_RESPONSE"
fi
echo

# Test 4: Schedule endpoints structure (without authentication)
echo "4. Testing schedule endpoints structure..."
ENDPOINTS_TO_TEST=(
    "GET /schedules/{id} -> 401/403 (auth required)"
    "POST /schedules/calculate-preview -> 401/403 (auth required)"
    "POST /schedules -> 401/403 (auth required)"
    "PUT /schedules/{id}/entries/0 -> 401/403 (auth required)"
)

for endpoint_test in "${ENDPOINTS_TO_TEST[@]}"; do
    endpoint=$(echo "$endpoint_test" | cut -d' ' -f2)
    expected=$(echo "$endpoint_test" | cut -d'>' -f2 | xargs)
    
    # Replace {id} with test-id for structure testing
    test_endpoint=$(echo "$endpoint" | sed 's/{id}/test-id/g')
    
    echo "Testing structure: $endpoint"
    
    case "$test_endpoint" in
        "/schedules/test-id")
            response_code=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL$test_endpoint")
            ;;
        "/schedules/calculate-preview")
            response_code=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$BASE_URL$test_endpoint" -H "Content-Type: application/json" -d '{}')
            ;;
        "/schedules")
            response_code=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$BASE_URL$test_endpoint" -H "Content-Type: application/json" -d '{}')
            ;;
        "/schedules/test-id/entries/0")
            response_code=$(curl -s -o /dev/null -w "%{http_code}" -X PUT "$BASE_URL$test_endpoint" -H "Content-Type: application/json" -d '{}')
            ;;
    esac
    
    if [[ "$response_code" == "401" || "$response_code" == "403" || "$response_code" == "422" ]]; then
        echo "✅ Endpoint exists (returned $response_code - auth/validation required)"
    elif [[ "$response_code" == "404" ]]; then
        echo "❌ Endpoint not found (404)"
    else
        echo "⚠️ Unexpected response: $response_code"
    fi
done
echo

echo "=== Test Summary ==="
echo "✅ Basic API structure tests completed"
echo "✅ Server is responding correctly"
echo "✅ Endpoints are properly registered"
echo
echo "Next steps for full testing:"
echo "1. Set up proper authentication tokens"
echo "2. Create test data in Firestore"
echo "3. Run integration tests with real data"
echo "4. Test the complete workflow: preview → edit → create → post"