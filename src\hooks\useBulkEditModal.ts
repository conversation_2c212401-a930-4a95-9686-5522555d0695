import { useState, useCallback } from 'react';
import { useToast } from './useToast';
import { api } from '@/lib/api';

interface BulkEditData {
  applyToSelected: string[]; // Schedule IDs
  amortizationStartDate?: string;
  numberOfPeriods?: number;
  amortizationAccountCode?: string;
  expenseAccountCode?: string;
  notes?: string;
}

interface BulkScheduleData {
  scheduleId: string;
  lineItemId: string;
  description: string;
  lineAmount: number;
  currentAmortizationAccountCode?: string;
  currentExpenseAccountCode?: string;
  status: string;
}

interface InvoiceData {
  invoiceId: string;
  reference: string;
  lineItems: BulkScheduleData[];
}

export function useBulkEditModal() {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<InvoiceData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const showToast = useToast();

  const openModal = useCallback((invoiceData: InvoiceData) => {
    console.log('🔍 Opening bulk edit modal for invoice:', invoiceData);
    setSelectedInvoice(invoiceData);
    setIsOpen(true);
    setError(null);
  }, []);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setSelectedInvoice(null);
    setError(null);
  }, []);

  const handleSave = useCallback(async (bulkEditData: BulkEditData): Promise<void> => {
    if (!selectedInvoice) {
      throw new Error('No invoice selected');
    }

    setIsLoading(true);
    setError(null);
    
    try {
      const scheduleIds = bulkEditData.applyToSelected;
      console.log('Bulk updating schedules:', scheduleIds);
      
      // Prepare update data (only include fields that have values)
      // Backend expects camelCase for most fields, matching the Firestore document structure
      const updateData: any = {};
      if (bulkEditData.amortizationStartDate) {
        updateData.amortizationStartDate = bulkEditData.amortizationStartDate;
      }
      if (bulkEditData.numberOfPeriods && bulkEditData.numberOfPeriods > 0) {
        // Note: Need to check if backend expects 'numberOfPeriods' or 'number_of_periods'
        updateData.numberOfPeriods = bulkEditData.numberOfPeriods;
      }
      if (bulkEditData.amortizationAccountCode && bulkEditData.amortizationAccountCode !== '__keep_existing__') {
        updateData.amortizationAccountCode = bulkEditData.amortizationAccountCode;
      }
      if (bulkEditData.expenseAccountCode && bulkEditData.expenseAccountCode !== '__keep_existing__') {
        updateData.expenseAccountCode = bulkEditData.expenseAccountCode;
      }
      if (bulkEditData.notes) {
        updateData.notes = bulkEditData.notes;
      }

      // Only proceed if there are actual changes
      if (Object.keys(updateData).length === 0) {
        showToast.showInfo('No changes to save');
        return;
      }

      // Make individual API calls for each schedule (since no bulk endpoint exists)
      const updatePromises = scheduleIds.map(async (scheduleId) => {
        try {
          await api.updateSchedule(scheduleId, updateData);
          return { scheduleId, success: true };
        } catch (error) {
          console.error(`Failed to update schedule ${scheduleId}:`, error);
          return { scheduleId, success: false, error };
        }
      });

      const results = await Promise.all(updatePromises);
      
      // Count successes and failures
      const successful = results.filter(r => r.success);
      const failed = results.filter(r => !r.success);

      if (failed.length === 0) {
        showToast.showSuccess(
          `Updated ${successful.length} schedule${successful.length !== 1 ? 's' : ''} successfully`
        );
        closeModal();
      } else if (successful.length > 0) {
        showToast.showWarning(
          `Updated ${successful.length} schedule${successful.length !== 1 ? 's' : ''}, but ${failed.length} failed`
        );
        setError(`Failed to update ${failed.length} schedule${failed.length !== 1 ? 's' : ''}`);
      } else {
        throw new Error('All schedule updates failed');
      }

    } catch (err) {
      console.error('Bulk edit failed:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to update schedules';
      setError(errorMessage);
      showToast.showError('Bulk edit failed', errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [selectedInvoice, showToast, closeModal]);

  const handleConfirm = useCallback(async (scheduleIds: string[]): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log('Bulk confirming schedules:', scheduleIds);

      // Make individual API calls for each schedule
      const confirmPromises = scheduleIds.map(async (scheduleId) => {
        try {
          await api.confirmSchedule(scheduleId);
          return { scheduleId, success: true };
        } catch (error) {
          console.error(`Failed to confirm schedule ${scheduleId}:`, error);
          return { scheduleId, success: false, error };
        }
      });

      const results = await Promise.all(confirmPromises);
      
      // Count successes and failures
      const successful = results.filter(r => r.success);
      const failed = results.filter(r => !r.success);

      if (failed.length === 0) {
        showToast.showSuccess(
          `Confirmed ${successful.length} schedule${successful.length !== 1 ? 's' : ''} successfully`
        );
        closeModal();
      } else if (successful.length > 0) {
        showToast.showWarning(
          `Confirmed ${successful.length} schedule${successful.length !== 1 ? 's' : ''}, but ${failed.length} failed`
        );
        setError(`Failed to confirm ${failed.length} schedule${failed.length !== 1 ? 's' : ''}`);
      } else {
        throw new Error('All schedule confirmations failed');
      }

    } catch (err) {
      console.error('Bulk confirm failed:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to confirm schedules';
      setError(errorMessage);
      showToast.showError('Bulk confirm failed', errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [showToast, closeModal]);

  const handleSkip = useCallback(async (scheduleIds: string[], reason: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log('Bulk skipping schedules:', scheduleIds, 'reason:', reason);

      // Make individual API calls for each schedule
      const skipPromises = scheduleIds.map(async (scheduleId) => {
        try {
          await api.skipSchedule(scheduleId, reason);
          return { scheduleId, success: true };
        } catch (error) {
          console.error(`Failed to skip schedule ${scheduleId}:`, error);
          return { scheduleId, success: false, error };
        }
      });

      const results = await Promise.all(skipPromises);
      
      // Count successes and failures
      const successful = results.filter(r => r.success);
      const failed = results.filter(r => !r.success);

      if (failed.length === 0) {
        showToast.showSuccess(
          `Skipped ${successful.length} schedule${successful.length !== 1 ? 's' : ''} successfully`
        );
        closeModal();
      } else if (successful.length > 0) {
        showToast.showWarning(
          `Skipped ${successful.length} schedule${successful.length !== 1 ? 's' : ''}, but ${failed.length} failed`
        );
        setError(`Failed to skip ${failed.length} schedule${failed.length !== 1 ? 's' : ''}`);
      } else {
        throw new Error('All schedule skips failed');
      }

    } catch (err) {
      console.error('Bulk skip failed:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to skip schedules';
      setError(errorMessage);
      showToast.showError('Bulk skip failed', errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [showToast, closeModal]);

  return {
    isOpen,
    selectedInvoice,
    isLoading,
    error,
    openModal,
    closeModal,
    handleSave,
    handleConfirm,
    handleSkip
  };
}