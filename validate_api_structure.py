#!/usr/bin/env python3
"""
API Structure Validation for DRCR Backend
Validates that all expected endpoints and schemas are properly defined.
"""

import ast
import re
import os

def extract_endpoints_from_file(file_path):
    """Extract all FastAPI endpoint definitions from a file."""
    try:
        with open(file_path, 'r') as file:
            content = file.read()
        
        # Find all router decorator patterns
        router_pattern = r'@router\.(get|post|put|delete|patch)\("([^"]+)"[^)]*\)'
        endpoints = re.findall(router_pattern, content)
        
        # Also extract function names
        function_pattern = r'async def (\w+)\('
        functions = re.findall(function_pattern, content)
        
        return {
            'endpoints': endpoints,
            'functions': functions,
            'content': content
        }
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return {'endpoints': [], 'functions': [], 'content': ''}

def validate_schedules_endpoints():
    """Validate that all expected schedule endpoints are present."""
    print("=== Schedule Endpoints Validation ===")
    
    schedules_file = 'rest_api/routes/schedules.py'
    if not os.path.exists(schedules_file):
        print(f"❌ File not found: {schedules_file}")
        return False
    
    data = extract_endpoints_from_file(schedules_file)
    
    # Expected endpoints for schedules
    expected_endpoints = [
        ('post', '/calculate-preview'),
        ('post', '/'),  # Create schedule
        ('put', '/{schedule_id}/entries/{entry_index}'),  # Update monthly entry
        ('get', '/{schedule_id}'),  # Get schedule
        ('put', '/{schedule_id}'),  # Update schedule
        ('put', '/{schedule_id}/recalculate'),
        ('post', '/{schedule_id}/confirm'),
        ('post', '/{schedule_id}/skip'),
        ('put', '/{schedule_id}/status'),
        ('post', '/{schedule_id}/entries/{entry_index}/post'),
        ('post', '/{schedule_id}/entries/bulk-post'),
    ]
    
    print(f"Found {len(data['endpoints'])} endpoints in schedules.py:")
    for method, path in data['endpoints']:
        print(f"  {method.upper()} {path}")
    
    missing_endpoints = []
    for expected_method, expected_path in expected_endpoints:
        found = any(method == expected_method and path == expected_path 
                   for method, path in data['endpoints'])
        if found:
            print(f"✅ {expected_method.upper()} {expected_path}")
        else:
            print(f"❌ Missing: {expected_method.upper()} {expected_path}")
            missing_endpoints.append((expected_method, expected_path))
    
    return len(missing_endpoints) == 0

def validate_schemas():
    """Validate that all expected schemas are present."""
    print("\n=== Schema Validation ===")
    
    schemas_file = 'rest_api/schemas/schedule_schemas.py'
    if not os.path.exists(schemas_file):
        print(f"❌ File not found: {schemas_file}")
        return False
    
    with open(schemas_file, 'r') as file:
        content = file.read()
    
    # Expected schema classes
    expected_schemas = [
        'SchedulePreviewRequest',
        'SchedulePreviewResponse',
        'ScheduleCreateRequest',
        'ScheduleCreateResponse',
        'MonthlyEntryUpdateRequest',
        'MonthlyEntryUpdateResponse',
        'ScheduleUpdateRequest',
        'ScheduleUpdateResponse',
        'MonthlyEntry',
        'CalculationMethod'
    ]
    
    missing_schemas = []
    for schema in expected_schemas:
        if f"class {schema}" in content:
            print(f"✅ Found schema: {schema}")
        else:
            print(f"❌ Missing schema: {schema}")
            missing_schemas.append(schema)
    
    return len(missing_schemas) == 0

def validate_imports_and_registration():
    """Validate that routes are properly imported and registered."""
    print("\n=== Import and Registration Validation ===")
    
    main_file = 'rest_api/main.py'
    if not os.path.exists(main_file):
        print(f"❌ File not found: {main_file}")
        return False
    
    with open(main_file, 'r') as file:
        content = file.read()
    
    # Check imports
    import_checks = [
        ('schedules import', 'from rest_api.routes.schedules import router as schedules_router'),
        ('schedules registration', 'app.include_router(schedules_router, prefix="/schedules"')
    ]
    
    all_good = True
    for check_name, pattern in import_checks:
        if pattern in content:
            print(f"✅ {check_name}: Found")
        else:
            print(f"❌ {check_name}: Missing")
            all_good = False
    
    return all_good

def validate_specific_functions():
    """Validate that specific key functions exist."""
    print("\n=== Key Function Validation ===")
    
    schedules_file = 'rest_api/routes/schedules.py'
    data = extract_endpoints_from_file(schedules_file)
    
    expected_functions = [
        'calculate_schedule_preview',
        'create_schedule',
        'update_monthly_entry',
        'get_schedule',
        'update_schedule',
        'post_individual_entry',
        'post_bulk_entries'
    ]
    
    missing_functions = []
    for func in expected_functions:
        if func in data['functions']:
            print(f"✅ Function: {func}")
        else:
            print(f"❌ Missing function: {func}")
            missing_functions.append(func)
    
    return len(missing_functions) == 0

def main():
    print("=== DRCR Backend API Structure Validation ===\n")
    
    # Run all validations
    results = []
    results.append(validate_schedules_endpoints())
    results.append(validate_schemas())
    results.append(validate_imports_and_registration())
    results.append(validate_specific_functions())
    
    print("\n=== Final Results ===")
    if all(results):
        print("✅ All validations passed!")
        print("✅ API structure is complete and properly configured")
        print("✅ Endpoints are ready for testing")
        return True
    else:
        print("❌ Some validations failed")
        print("Review the messages above for details")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)