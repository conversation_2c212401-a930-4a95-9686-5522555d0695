"""
Contact Schemas - Request/response schemas for contact API endpoints with pagination
"""
from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Dict, Any
from ..models.contact import Contact, ContactSummary, ContactCreate, ContactUpdate, ContactType


class ContactFilters(BaseModel):
    """Filters for contact queries"""
    client_id: Optional[str] = Field(None, description="Filter by client ID")
    entity_id: Optional[str] = Field(None, description="Filter by entity ID")
    name_filter: Optional[str] = Field(None, description="Filter by contact name (partial match)")
    contact_type: Optional[ContactType] = Field(None, description="Filter by contact type")
    email_filter: Optional[str] = Field(None, description="Filter by email (partial match)")
    source_system: Optional[str] = Field(None, description="Filter by source system")
    is_active: Optional[bool] = Field(None, description="Filter by active status")
    has_amortization_settings: Optional[bool] = Field(None, description="Filter by presence of amortization settings")
    
    class Config:
        json_schema_extra = {
            "example": {
                "client_id": "client_123",
                "entity_id": "entity_456", 
                "name_filter": "ABC",
                "contact_type": "vendor",
                "email_filter": "@company.com",
                "source_system": "xero",
                "is_active": True,
                "has_amortization_settings": True
            }
        }


class ContactPaginationParams(BaseModel):
    """Pagination parameters for contact queries"""
    page: int = Field(1, ge=1, description="Page number (1-based)")
    limit: int = Field(20, ge=1, le=100, description="Number of items per page")
    sort_by: Optional[str] = Field("name", description="Field to sort by")
    sort_order: Optional[str] = Field("asc", pattern="^(asc|desc)$", description="Sort order")
    
    class Config:
        json_schema_extra = {
            "example": {
                "page": 1,
                "limit": 20,
                "sort_by": "name",
                "sort_order": "asc"
            }
        }


class ContactPaginationMeta(BaseModel):
    """Pagination metadata for contact responses"""
    current_page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")
    total_items: int = Field(..., description="Total number of items")
    total_pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_previous: bool = Field(..., description="Whether there is a previous page")
    
    class Config:
        json_schema_extra = {
            "example": {
                "current_page": 1,
                "page_size": 20,
                "total_items": 150,
                "total_pages": 8,
                "has_next": True,
                "has_previous": False
            }
        }


class ContactListResponse(BaseModel):
    """Response model for paginated contact list"""
    contacts: List[ContactSummary] = Field(..., description="List of contacts")
    pagination: ContactPaginationMeta = Field(..., description="Pagination metadata")
    filters_applied: Optional[ContactFilters] = Field(None, description="Filters that were applied")
    
    class Config:
        json_schema_extra = {
            "example": {
                "contacts": [
                    {
                        "counterparty_id": "contact_123",
                        "client_id": "client_456",
                        "entity_id": "entity_789",
                        "name": "ABC Suppliers Ltd",
                        "contact_type": "vendor",
                        "email_address": "<EMAIL>",
                        "source_system": "xero",
                        "is_active": True,
                        "has_amortization_settings": True,
                        "transaction_count": 25
                    }
                ],
                "pagination": {
                    "current_page": 1,
                    "page_size": 20,
                    "total_items": 150,
                    "total_pages": 8,
                    "has_next": True,
                    "has_previous": False
                }
            }
        }


class ContactDetailResponse(BaseModel):
    """Response model for single contact detail"""
    contact: Contact = Field(..., description="Complete contact information")
    
    # Additional computed data
    transaction_count: Optional[int] = Field(0, description="Number of transactions for this contact")
    recent_transactions: Optional[List[Dict[str, Any]]] = Field(None, description="Recent transactions (summary)")
    amortization_schedules_count: Optional[int] = Field(0, description="Number of amortization schedules")
    
    class Config:
        json_schema_extra = {
            "example": {
                "contact": {
                    "counterparty_id": "contact_123",
                    "client_id": "client_456",
                    "entity_id": "entity_789",
                    "name": "ABC Suppliers Ltd",
                    "contact_type": "vendor",
                    "email_address": "<EMAIL>",
                    "amortization_settings": {
                        "default_expense_account_code": "6100",
                        "default_amortization_months": 12
                    }
                },
                "transaction_count": 25,
                "amortization_schedules_count": 8
            }
        }


class ContactCreateRequest(BaseModel):
    """Request model for creating a contact"""
    contact_data: ContactCreate = Field(..., description="Contact data to create")
    client_id: str = Field(..., description="Client ID to associate the contact with")
    entity_id: str = Field(..., description="Entity ID to associate the contact with")
    
    class Config:
        json_schema_extra = {
            "example": {
                "contact_data": {
                    "name": "New Vendor Corp",
                    "contact_type": "vendor",
                    "email_address": "<EMAIL>",
                    "amortization_settings": {
                        "default_expense_account_code": "6200",
                        "default_amortization_months": 12
                    }
                },
                "client_id": "client_456",
                "entity_id": "entity_789"
            }
        }


class ContactUpdateRequest(BaseModel):
    """Request model for updating a contact"""
    contact_data: ContactUpdate = Field(..., description="Contact data to update")
    
    class Config:
        json_schema_extra = {
            "example": {
                "contact_data": {
                    "name": "Updated Vendor Corp",
                    "email_address": "<EMAIL>",
                    "amortization_settings": {
                        "default_expense_account_code": "6300",
                        "default_amortization_months": 24
                    }
                }
            }
        }


class ContactCreateResponse(BaseModel):
    """Response model for contact creation"""
    message: str = Field(..., description="Success message")
    contact_id: str = Field(..., description="ID of the created contact")
    contact: ContactSummary = Field(..., description="Summary of the created contact")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "Contact created successfully",
                "contact_id": "contact_123",
                "contact": {
                    "counterparty_id": "contact_123",
                    "name": "New Vendor Corp",
                    "contact_type": "vendor",
                    "email_address": "<EMAIL>"
                }
            }
        }


class ContactUpdateResponse(BaseModel):
    """Response model for contact update"""
    message: str = Field(..., description="Success message")
    contact: ContactSummary = Field(..., description="Summary of the updated contact")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "Contact updated successfully",
                "contact": {
                    "counterparty_id": "contact_123",
                    "name": "Updated Vendor Corp",
                    "contact_type": "vendor",
                    "email_address": "<EMAIL>"
                }
            }
        }


class ContactDeleteResponse(BaseModel):
    """Response model for contact deletion"""
    message: str = Field(..., description="Success message")
    contact_id: str = Field(..., description="ID of the deleted contact")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "Contact deleted successfully",
                "contact_id": "contact_123"
            }
        }


class ContactStatistics(BaseModel):
    """Statistics about contacts"""
    total_contacts: int = Field(0, description="Total number of contacts")
    contacts_by_type: Dict[str, int] = Field({}, description="Count of contacts by type")
    contacts_by_source: Dict[str, int] = Field({}, description="Count of contacts by source system")
    active_contacts: int = Field(0, description="Number of active contacts")
    contacts_with_amortization_settings: int = Field(0, description="Contacts with amortization settings")
    recent_contacts_count: int = Field(0, description="Contacts created in the last 30 days")
    
    class Config:
        json_schema_extra = {
            "example": {
                "total_contacts": 450,
                "contacts_by_type": {
                    "vendor": 300,
                    "customer": 120,
                    "supplier": 25,
                    "other": 5
                },
                "contacts_by_source": {
                    "xero": 400,
                    "manual": 50
                },
                "active_contacts": 425,
                "contacts_with_amortization_settings": 180,
                "recent_contacts_count": 15
            }
        }


class ContactStatsResponse(BaseModel):
    """Response model for contact statistics"""
    statistics: ContactStatistics = Field(..., description="Contact statistics")
    generated_at: str = Field(..., description="When the statistics were generated")
    
    class Config:
        json_schema_extra = {
            "example": {
                "statistics": {
                    "total_contacts": 450,
                    "active_contacts": 425
                },
                "generated_at": "2024-01-15T10:30:00Z"
            }
        }


# Query parameter models for API endpoints
class ContactQueryParams(BaseModel):
    """Combined query parameters for contact endpoints"""
    # Pagination
    page: int = Field(1, ge=1, description="Page number")
    limit: int = Field(20, ge=1, le=100, description="Items per page")
    sort_by: Optional[str] = Field("name", description="Sort field")
    sort_order: Optional[str] = Field("asc", pattern="^(asc|desc)$", description="Sort order")
    
    # Filters
    client_id: Optional[str] = None
    entity_id: Optional[str] = None
    name_filter: Optional[str] = None
    contact_type: Optional[ContactType] = None
    email_filter: Optional[str] = None
    source_system: Optional[str] = None
    is_active: Optional[bool] = None
    has_amortization_settings: Optional[bool] = None