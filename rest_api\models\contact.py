"""
Contact/Counterparty Models - Pydantic models for contact/counterparty data
"""
from pydantic import BaseModel, Field, EmailStr, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class ContactType(str, Enum):
    """Type of contact/counterparty"""
    VENDOR = "vendor"
    CUSTOMER = "customer"
    SUPPLIER = "supplier"
    EMPLOYEE = "employee"
    OTHER = "other"


class AddressType(str, Enum):
    """Type of address"""
    BILLING = "billing"
    SHIPPING = "shipping"
    BUSINESS = "business"
    OTHER = "other"


class ContactAddress(BaseModel):
    """Address information for a contact"""
    address_type: Optional[AddressType] = None
    line1: Optional[str] = None
    line2: Optional[str] = None
    city: Optional[str] = None
    region: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None
    
    class Config:
        extra = "allow"


class ContactPhone(BaseModel):
    """Phone information for a contact"""
    phone_type: Optional[str] = None  # e.g., "MOBILE", "OFFICE", "FAX"
    phone_number: Optional[str] = None
    phone_area_code: Optional[str] = None
    phone_country_code: Optional[str] = None
    
    class Config:
        extra = "allow"


class ContactAmortizationSettings(BaseModel):
    """Amortization-specific settings for a contact"""
    default_expense_account_code: Optional[str] = Field(
        None, 
        description="Default expense account code for amortization"
    )
    default_amortization_months: Optional[int] = Field(
        None, 
        ge=1, 
        le=120, 
        description="Default amortization period in months"
    )
    
    class Config:
        extra = "allow"


class Contact(BaseModel):
    """Complete contact/counterparty model"""
    # Core identifiers
    counterparty_id: str = Field(..., description="Unique counterparty identifier")
    client_id: str = Field(..., description="Client this contact belongs to")
    entity_id: str = Field(..., description="Entity this contact belongs to")
    
    # Basic information
    name: str = Field(..., min_length=1, description="Contact name")
    contact_type: Optional[ContactType] = Field(None, description="Type of contact")
    
    # Contact details
    email_address: Optional[EmailStr] = Field(None, description="Primary email address")
    phones: Optional[List[ContactPhone]] = Field(None, description="Phone numbers")
    addresses: Optional[List[ContactAddress]] = Field(None, description="Addresses")
    
    # Business information
    tax_number: Optional[str] = Field(None, description="Tax ID or registration number")
    website: Optional[str] = Field(None, description="Website URL")
    
    # Source system information
    source_system: Optional[str] = Field(None, description="Source system (e.g., 'xero')")
    source_system_id: Optional[str] = Field(None, description="ID in source system")
    source_updated_at_utc: Optional[str] = Field(None, description="Last update time in source system")
    
    # DRCR-specific settings
    amortization_settings: Optional[ContactAmortizationSettings] = Field(
        None, 
        description="Amortization-specific settings"
    )
    
    # System fields
    is_active: bool = Field(True, description="Whether the contact is active")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    # Raw data preservation
    raw_source_data: Optional[Dict[str, Any]] = Field(
        None, 
        description="Raw data from source system"
    )
    
    # Legacy field support (for backwards compatibility)
    system_defaultAmortizationExpenseAccountCode: Optional[str] = Field(
        None, 
        description="Legacy field for default expense account code"
    )
    
    class Config:
        extra = "allow"
        json_schema_extra = {
            "example": {
                "counterparty_id": "contact_123",
                "client_id": "client_456",
                "entity_id": "entity_789",
                "name": "ABC Suppliers Ltd",
                "contact_type": "vendor",
                "email_address": "<EMAIL>",
                "phones": [
                    {
                        "phone_type": "OFFICE",
                        "phone_number": "******-123-4567"
                    }
                ],
                "addresses": [
                    {
                        "address_type": "billing",
                        "line1": "123 Business St",
                        "city": "New York",
                        "region": "NY",
                        "postal_code": "10001",
                        "country": "US"
                    }
                ],
                "amortization_settings": {
                    "default_expense_account_code": "6100",
                    "default_amortization_months": 12
                },
                "source_system": "xero",
                "source_system_id": "xero_contact_id_123",
                "is_active": True
            }
        }

    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Contact name cannot be empty')
        return v.strip()
        
    @validator('email_address', pre=True)
    def validate_email_address(cls, v):
        if v == "" or v is None:
            return None
        return v


class ContactSummary(BaseModel):
    """Simplified contact model for list views"""
    counterparty_id: str
    client_id: str
    entity_id: str
    name: str
    contact_type: Optional[ContactType] = None
    email_address: Optional[EmailStr] = None
    source_system: Optional[str] = None
    is_active: bool = True
    updated_at: Optional[datetime] = None
    
    # Computed fields
    has_amortization_settings: bool = Field(False, description="Whether contact has amortization settings configured")
    transaction_count: Optional[int] = Field(0, description="Number of transactions associated with this contact")
    
    @validator('email_address', pre=True)
    def validate_email_address(cls, v):
        if v == "" or v is None:
            return None
        return v
    
    class Config:
        extra = "allow"


class ContactCreate(BaseModel):
    """Model for creating a new contact"""
    name: str = Field(..., min_length=1, max_length=255)
    contact_type: Optional[ContactType] = None
    email_address: Optional[EmailStr] = None
    phones: Optional[List[ContactPhone]] = None
    addresses: Optional[List[ContactAddress]] = None
    tax_number: Optional[str] = None
    website: Optional[str] = None
    amortization_settings: Optional[ContactAmortizationSettings] = None
    
    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Contact name cannot be empty')
        return v.strip()


class ContactUpdate(BaseModel):
    """Model for updating an existing contact"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    contact_type: Optional[ContactType] = None
    email_address: Optional[EmailStr] = None
    phones: Optional[List[ContactPhone]] = None
    addresses: Optional[List[ContactAddress]] = None
    tax_number: Optional[str] = None
    website: Optional[str] = None
    amortization_settings: Optional[ContactAmortizationSettings] = None
    is_active: Optional[bool] = None
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('Contact name cannot be empty')
        return v.strip() if v else v