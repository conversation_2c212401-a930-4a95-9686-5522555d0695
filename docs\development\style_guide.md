# DRCR Backend Coding Standards & Style Guide

This document outlines the coding standards and style guidelines for developing the DRCR backend. Adhering to these standards ensures code consistency, readability, and maintainability across the project.

## Python Version

*   **Target Python Version**: Python 3.9+ (Confirm with `runtime.txt` or project settings, e.g., `pyproject.toml` if specified). Code should be compatible with this version.

## Code Formatting

*   **Formatter**: [Black](https://github.com/psf/black) is the standard code formatter for this project.
    *   All Python code should be formatted with Black using its default settings.
    *   It's recommended to configure your IDE to format on save using Black.
    *   Run Black from the command line: `black .`
*   **Linter**: [Flake8](https://flake8.pycqa.org/en/latest/) is used for linting to catch style issues, logical errors, and complexity.
    *   Configuration for Flake8 (e.g., max line length, specific errors to ignore/enforce) can be found in `.flake8` or `pyproject.toml`.
    *   Run Flake8 from the command line: `flake8 .`
*   **Line Length**: Maximum line length is typically 88 characters (<PERSON>'s default) or 100-120 characters if explicitly configured in Flake8/Black. Please check project settings.

## Import Ordering

*   **Tool**: [isort](https://pycqa.github.io/isort/) is used to automatically sort and format imports.
*   **Order**: Imports should be grouped in the following order, with a blank line separating each group:
    1.  Standard library imports (e.g., `os`, `sys`, `datetime`).
    2.  Third-party library imports (e.g., `fastapi`, `pydantic`, `firebase_admin`).
    3.  Local application/library specific imports (e.g., from `drcr_shared_logic`, `rest_api.models`).
*   Run isort from the command line: `isort .`
*   Black and isort are often configured to work together.

## Naming Conventions

*   **Variables & Functions**: `snake_case` (e.g., `user_id`, `calculate_total_amount`).
*   **Constants**: `UPPER_SNAKE_CASE` (e.g., `MAX_RETRIES`, `DEFAULT_TIMEOUT`).
*   **Classes & Exceptions**: `PascalCase` (also known as `CapWords`) (e.g., `TransactionService`, `InvalidInputError`).
*   **Modules & Packages**: `snake_case` (lowercase, with underscores if it improves readability, e.g., `xero_connector.py`).
*   **Private Members**: Prefix with a single underscore (`_private_method`) for internal use within a module or class. Use double underscores (`__very_private_method`) for name mangling if strictly necessary, but prefer single underscore.

## Type Hinting

*   **Requirement**: Type hints **must** be used for all function/method signatures (arguments and return types) and for variable declarations where type is not immediately obvious.
*   **Module**: Use the `typing` module (e.g., `from typing import Dict, List, Optional, Any, Union, Callable`).
*   **Clarity**: Aim for specific types over `Any` where possible. Use `Optional[X]` for values that can be `X` or `None`.
*   **Forward References**: For type hints that refer to classes not yet defined, use string literals (e.g., `def get_user(user_id: str) -> 'User':`).

## Docstrings

*   **Requirement**: All public modules, classes, functions, and methods should have docstrings.
*   **Format**: [Google Python Style Docstrings](https://google.github.io/styleguide/pyguide.html#38-comments-and-docstrings) are preferred.
    *   Example:
        ```python
        def get_user_profile(user_id: str) -> Optional[dict]:
            """Retrieves a user profile from the database.

            Args:
                user_id: The unique identifier for the user.

            Returns:
                A dictionary containing the user profile if found, otherwise None.
            """
            # ... implementation ...
        ```
*   Keep docstrings concise but informative.

## Logging

*   **Library**: Use the standard Python `logging` module.
*   **Configuration**: Logging should be configured centrally (e.g., in `drcr_shared_logic/config.py` or `rest_api/app.py`).
*   **Level**: Use appropriate logging levels: `DEBUG` for detailed diagnostic information, `INFO` for general operational entries, `WARNING` for potential issues or unusual events, `ERROR` for errors that prevent normal operation, `CRITICAL` for severe errors.
*   **Content**: Log messages should be informative and include relevant context (e.g., IDs, key values).
*   **Exception Logging**: When catching exceptions, log the full exception information using `logging.exception()` or `logger.error("...", exc_info=True)` to capture the traceback.

## Error Handling & Exceptions

*   **Custom Exceptions**: Define custom exception classes (subclassing standard Python exceptions like `Exception` or `ValueError`) for application-specific errors. These should be clearly named (e.g., `TransactionNotFoundError`, `XeroApiError`). Place them in logical modules (e.g., `drcr_shared_logic.exceptions`).
*   **Informative Messages**: Custom exceptions should have informative messages.
*   **Raising Exceptions**: Raise exceptions rather than returning error codes or `None` for exceptional situations where a function cannot fulfill its contract.
*   **Catching Exceptions**: Be specific when catching exceptions. Avoid broad `except Exception:`.
*   Refer to the main [Error Handling Documentation](../../error_handling/README.md) for API error response formats.

## Asynchronous Code (`async`/`await`)

*   **Usage**: Use `async` and `await` for I/O-bound operations to improve concurrency, especially in the FastAPI application and when interacting with external services or databases that support asynchronous clients.
*   **Libraries**: Utilize asynchronous versions of libraries where available (e.g., `httpx` for async HTTP requests, async database drivers).

## String Formatting

*   **f-Strings**: Prefer f-Strings (formatted string literals) for string formatting due to their readability and performance.
    *   Example: `message = f"User {user_id} not found."`

## General Best Practices

*   **Readability**: Write code that is easy to read and understand. Prioritize clarity over overly clever solutions.
*   **Modularity**: Break down complex logic into smaller, reusable functions or classes.
*   **DRY (Don't Repeat Yourself)**: Avoid duplicating code. Use functions, classes, and shared modules.
*   **Comments**: Use comments to explain complex or non-obvious parts of the code. Docstrings are for *what* code does; comments are for *how* or *why* it does it in a particular way.
*   **Environment Variables**: Do not hardcode sensitive information or environment-specific configurations. Use environment variables (loaded via `.env` locally, and set in the deployment environment).
*   **Security**: Be mindful of security best practices (e.g., input validation, parameterized database queries (though Firestore client libraries handle this), avoiding exposure of sensitive data in logs or error messages beyond what's necessary for debugging by authorized personnel).

---

By adhering to these standards, we aim to create a codebase that is robust, maintainable, and a pleasure to work with. Linters and formatters will be enforced in CI/CD pipelines where possible. 