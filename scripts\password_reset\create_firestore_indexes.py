#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create required Firestore indexes for password reset functionality
"""

import os
from google.cloud import firestore
from dotenv import load_dotenv

def create_indexes():
    """Create required Firestore indexes"""
    
    # Load environment variables
    load_dotenv()
    
    # Initialize Firestore client
    db = firestore.Client()
    
    print("Creating Firestore indexes for password reset functionality...")
    
    # The indexes needed based on the error message:
    # 1. Composite index for password_reset_tokens collection: email + created_at
    # 2. Single field indexes are usually created automatically
    
    print("""
Required Firestore indexes:

1. Collection: password_reset_tokens
   Fields: email (Ascending), created_at (Ascending)
   
2. Collection: password_reset_tokens  
   Fields: user_id (Ascending), used (Ascending)
   
3. Collection: password_reset_tokens
   Fields: token (Ascending), used (Ascending)

Please create these indexes manually in the Firebase Console:
https://console.firebase.google.com/project/drcr-d660a/firestore/indexes

Or use the direct link from the error message:
https://console.firebase.google.com/v1/r/project/drcr-d660a/firestore/indexes?create_composite=Clhwcm9qZWN0cy9kcmNyLWQ2NjBhL2RhdGFiYXNlcy8oZGVmYXVsdCkvY29sbGVjdGlvbkdyb3Vwcy9wYXNzd29yZF9yZXNldF90b2tlbnMvaW5kZXhlcy9fEAEaCQoFZW1haWwQARoOCgpjcmVhdGVkX2F0EAEaDAoIX19uYW1lX18QAQ

The indexes will take a few minutes to build after creation.
""")

if __name__ == "__main__":
    create_indexes() 