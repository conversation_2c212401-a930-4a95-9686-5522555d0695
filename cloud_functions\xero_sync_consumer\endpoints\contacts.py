"""
Contacts Endpoint Handler

Handles Contacts synchronization from Xero to COUNTERPARTIES collection.
Pure metadata sync - no heavy processing required.
"""

import logging
from typing import List
from datetime import datetime, timezone

from google.cloud import firestore

from context import SyncContext
from utils.sync_helpers import _create_audit_log_entry, _update_firestore_sync_timestamp
from utils.bills_processor import _check_if_current_version_exists

logger = logging.getLogger(__name__)

__all__ = ["sync_contacts"]


async def sync_contacts(ctx: SyncContext, requested_endpoints: List[str]) -> int:
    """
    Handle Contacts synchronization from Xero.
    
    Args:
        ctx: Sync context containing all dependencies
        requested_endpoints: List of requested endpoints for sync
        
    Returns:
        Number of contacts processed
        
    Raises:
        Exception: If contacts sync fails
    """
    if "Contacts" not in requested_endpoints and requested_endpoints:
        return 0
    
    try:
        logger.info(f"Starting Contacts sync for entity_id: {ctx.entity_id}")
        
        full_sync = "Contacts" in ctx.force_full_sync_endpoints
        last_sync_timestamp_utc_str = None
        if not full_sync:
            sync_ts_field = "_system_lastSyncTimestampUtc_Contacts"
            last_sync_timestamp_utc_str = ctx.get_entity_setting(sync_ts_field)
        
        contacts_data = await ctx.xero_client.get_records("Contacts", if_modified_since=last_sync_timestamp_utc_str)
        saved_contacts_count = 0
        skipped_contacts_count = 0
        
        for contact in contacts_data:
            contact_id_from_xero = contact.get("ContactID")
            if not contact_id_from_xero:
                logger.warning(f"Contact data missing ContactID for entity {ctx.entity_id}. Skipping: {contact}")
                continue
            
            # CHECK IF WE ALREADY HAVE THE CURRENT VERSION - SKIP PROCESSING IF SO
            new_updated_at = contact.get("UpdatedDateUTC")
            if await _check_if_current_version_exists(ctx.db, contact_id_from_xero, new_updated_at, ctx.entity_id, "COUNTERPARTIES", "contact"):
                skipped_contacts_count += 1
                continue  # Skip all processing for this contact - we already have current version
            
            # Extract contact metadata
            contact_name = contact.get("Name", "Unknown Contact")
            
            # Create counterparty document
            counterparty_doc = {
                "entity_id": ctx.entity_id,
                "client_id": ctx.client_id,
                "source_system": "XERO",
                "contact_id": contact_id_from_xero,
                "name": contact_name,
                "contact_type": contact.get("ContactStatus", "ACTIVE"),
                "email": contact.get("EmailAddress"),
                "phone": contact.get("PhoneNumber"),
                "is_supplier": contact.get("IsSupplier", False),
                "is_customer": contact.get("IsCustomer", False),
                "default_currency": contact.get("DefaultCurrency"),
                "tax_number": contact.get("TaxNumber"),
                "account_number": contact.get("AccountNumber"),
                "addresses": contact.get("Addresses", []),
                "contact_persons": contact.get("ContactPersons", []),
                "raw_xero_data": contact,
                "last_updated_utc": new_updated_at,
                "sync_timestamp": firestore.SERVER_TIMESTAMP
            }

            counterparty_ref = ctx.db.collection("COUNTERPARTIES").document(contact_id_from_xero)
            await counterparty_ref.set(counterparty_doc, merge=True)
            saved_contacts_count += 1
        
        logger.info(f"Contacts sync completed for entity_id: {ctx.entity_id} - Processed: {saved_contacts_count}, Skipped: {skipped_contacts_count}")
        await _update_firestore_sync_timestamp(
            ctx.db, ctx.entity_id, "Contacts", datetime.now(timezone.utc).isoformat(), "Contacts sync successful"
        )
        await _create_audit_log_entry(
            ctx.db, "SYNC", "CONTACTS_SYNC_SUCCESS", ctx.client_id, ctx.entity_id, "SUCCESS", 
            {"contacts_processed": saved_contacts_count, "contacts_skipped": skipped_contacts_count, "syncJobId": ctx.sync_job_id}
        )
        
        return saved_contacts_count
        
    except Exception as e:
        logger.error(f"Contacts sync failed for entity_id: {ctx.entity_id}: {e}", exc_info=True)
        await _create_audit_log_entry(
            ctx.db, "SYNC", "CONTACTS_SYNC_FAILURE", ctx.client_id, ctx.entity_id, "FAILURE", 
            {"error": str(e), "syncJobId": ctx.sync_job_id}
        )
        raise