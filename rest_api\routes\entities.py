from fastapi import APIRouter, Depends, HTTPException, status, Path, Query, Body
from typing import Dict, Any, List, Optional
import uuid
import json
import logging
from datetime import datetime
from google.cloud import firestore
from google.cloud.firestore import SERVER_TIMESTAMP

from ..core.firebase_auth import get_current_user, get_firm_user_with_client_access, AuthUser
from ..dependencies import get_db
from ..utils.audit import create_audit_log_entry
from drcr_shared_logic.clients.xero_client import XeroApiClient
from ..services.entity_analysis_service import EntityAnalysisService
from ..services.bill_aggregation_service import BillAggregationService
import asyncio
import os
from google.cloud import pubsub_v1

# Initialize logger
logger = logging.getLogger(__name__)

router = APIRouter(tags=["Entities"])

@router.get("/")
async def list_entities(
    client_id: str = Query(..., description="Client ID"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """List entities for a client"""
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Query entities for the client
    entities_query = db.collection("ENTITIES").where(filter=firestore.FieldFilter("client_id", "==", client_id))
    entities_docs = await entities_query.get()

    entities = []
    for doc in entities_docs:
        entity_data = doc.to_dict()
        entities.append({
            "entity_id": entity_data.get("entity_id"),
            "entity_name": entity_data.get("entity_name"),
            "type": entity_data.get("type"),
            "status": entity_data.get("status"),
            "connection_details": {
                "status": entity_data.get("connection_details", {}).get("status", "unknown")
            }
        })

    return {"entities": entities}

@router.get("/{entity_id}")
async def get_entity(
    entity_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Get entity details"""
    # Get entity document
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()

    if not entity_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )

    entity_data = entity_doc.to_dict()
    client_id = entity_data.get("client_id")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Get entity settings
    settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
    settings_doc = await settings_ref.get()
    settings_data = settings_doc.to_dict() if settings_doc.exists else {}

    # Get actual last sync timestamp from entity settings
    last_sync_timestamp = await _get_last_sync_timestamp(db, entity_id)

    # Get comprehensive sync status
    comprehensive_status = await get_comprehensive_sync_status(db, entity_id, client_id, entity_data, settings_data)
    sync_status = {
        "is_syncing": comprehensive_status["is_syncing"],
        "current_step": comprehensive_status.get("current_step", "idle"),
        "progress_percentage": comprehensive_status.get("progress_percentage", 0),
        "estimated_remaining": comprehensive_status.get("estimated_remaining"),
        "user_message": comprehensive_status.get("user_message", "Ready"),
        "last_sync_completed": comprehensive_status.get("last_sync_completed"),
        "sync_duration_warning": "Xero operations typically take 5-15 minutes" if comprehensive_status["is_syncing"] else None
    }

    # Combine entity and settings data
    result = {
        "entity_id": entity_data.get("entity_id"),
        "client_id": entity_data.get("client_id"),
        "entity_name": entity_data.get("entity_name"),
        "type": entity_data.get("type"),
        "status": entity_data.get("status"),
        "connection_details": entity_data.get("connection_details", {}),
        "last_sync": last_sync_timestamp,
        "sync_status": sync_status,
        "settings": {
            # Amortization settings
            "prepayment_asset_account_codes": settings_data.get("prepayment_asset_account_codes", []),
            "excluded_pnl_account_codes": settings_data.get("excluded_pnl_account_codes", []),
            "default_amortization_months": settings_data.get("default_amortization_months", 12),
            "amortization_materiality_threshold": settings_data.get("amortization_materiality_threshold", 1000.0),
            "default_expense_account_code": settings_data.get("default_expense_account_code"),

            # Transaction sync settings
            "transaction_sync_start_date": settings_data.get("transaction_sync_start_date"),
            "sync_frequency": settings_data.get("sync_frequency", "daily"),
            "auto_sync_enabled": settings_data.get("auto_sync_enabled", True),

            # Data filtering settings
            "sync_invoices": settings_data.get("sync_invoices", True),
            "sync_bills": settings_data.get("sync_bills", True),
            "sync_payments": settings_data.get("sync_payments", True),
            "sync_bank_transactions": settings_data.get("sync_bank_transactions", True),
            "sync_journal_entries": settings_data.get("sync_journal_entries", True),
            "sync_spend_money": settings_data.get("sync_spend_money", True),

            # Processing settings
            "auto_post_proposed_journals": settings_data.get("auto_post_proposed_journals", False),
            "base_currency_code": settings_data.get("base_currency_code", "USD"),

            # Sync status tracking
            "initial_sync_completed": settings_data.get("initial_sync_completed", False),
            "last_full_sync_date": settings_data.get("last_full_sync_date"),
        }
    }

    return result

@router.put("/{entity_id}/settings")
async def update_entity_settings(
    entity_id: str = Path(...),
    settings_data: Dict[str, Any] = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Update entity settings"""
    # Get entity to check permissions
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()

    if not entity_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )

    entity_data = entity_doc.to_dict()
    client_id = entity_data.get("client_id")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Update entity name if provided
    if "entity_name" in settings_data:
        await entity_ref.update({
            "entity_name": settings_data["entity_name"],
            "updated_at": SERVER_TIMESTAMP
        })

    # Update ENTITY_SETTINGS document
    settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
    settings_doc = await settings_ref.get()

    # Allowable settings fields to update
    allowed_fields = [
        "prepayment_asset_account_codes",
        "excluded_pnl_account_codes",
        "default_expense_account_code",
        "default_amortization_months",
        "amortization_materiality_threshold",
        "transaction_sync_start_date",
        "sync_frequency",
        "auto_sync_enabled",
        "sync_invoices",
        "sync_bills",
        "sync_payments",
        "sync_bank_transactions",
        "sync_journal_entries",
        "sync_spend_money",
        "auto_post_proposed_journals",
        "base_currency_code",
        "initial_sync_completed",
        "last_full_sync_date"
    ]

    update_data = {
        k: v for k, v in settings_data.items()
        if k in allowed_fields
    }

    # Validate transaction_sync_start_date if provided
    if "transaction_sync_start_date" in update_data:
        try:
            sync_start_date = update_data["transaction_sync_start_date"]
            if isinstance(sync_start_date, str):
                # Try to parse the date string
                parsed_date = datetime.fromisoformat(sync_start_date.replace('Z', '+00:00'))
                # Ensure it's not in the future
                if parsed_date.date() > datetime.now().date():
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Transaction sync start date cannot be in the future"
                    )
                # Ensure it's not too far in the past (more than 10 years)
                if parsed_date.year < datetime.now().year - 10:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Transaction sync start date cannot be more than 10 years in the past"
                    )
        except (ValueError, TypeError) as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid transaction_sync_start_date format. Use ISO format (YYYY-MM-DD): {str(e)}"
            )

    # Validate sync_frequency if provided
    if "sync_frequency" in update_data:
        valid_frequencies = ["hourly", "daily", "weekly", "manual"]
        if update_data["sync_frequency"] not in valid_frequencies:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid sync_frequency. Must be one of: {', '.join(valid_frequencies)}"
            )

    # Validate base_currency_code if provided
    if "base_currency_code" in update_data:
        valid_currencies = ["USD", "EUR", "GBP", "CAD", "AUD", "NZD", "JPY", "CHF", "SEK", "NOK", "DKK"]
        if update_data["base_currency_code"] not in valid_currencies:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid base_currency_code. Must be one of: {', '.join(valid_currencies)}"
            )

    # Validate amortization_materiality_threshold if provided
    if "amortization_materiality_threshold" in update_data:
        try:
            threshold = float(update_data["amortization_materiality_threshold"])
            if threshold < 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Amortization materiality threshold must be non-negative"
                )
            if threshold > 1000000:  # 1 million limit
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Amortization materiality threshold cannot exceed 1,000,000"
                )
        except (ValueError, TypeError):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Amortization materiality threshold must be a valid number"
            )

    if update_data:
        update_data["updated_at"] = SERVER_TIMESTAMP

        if settings_doc.exists:
            await settings_ref.update(update_data)
        else:
            # Create new settings document if it doesn't exist
            update_data["entity_id"] = entity_id
            update_data["client_id"] = client_id
            update_data["created_at"] = SERVER_TIMESTAMP
            await settings_ref.set(update_data)

        # Create audit log entry
        await create_audit_log_entry(
            db=db,
            event_category="ENTITY_MANAGEMENT",
            event_type="ENTITY_SETTINGS_UPDATED",
            client_id=client_id,
            entity_id=entity_id,
            status="SUCCESS",
            details={
                "updated_fields": list(update_data.keys()),
                "entity_type": entity_data.get("type")
            },
            user_id=current_user.uid
        )

    # Check if this is an initial setup that should trigger automated sync
    should_trigger_sync = await _should_trigger_automated_sync(
        db, entity_id, client_id, update_data, settings_doc.exists
    )

    if should_trigger_sync:
        # Trigger automated sync in background
        await _trigger_automated_sync_background(
            db, entity_id, client_id, current_user.uid, update_data
        )

        return {
            "message": "Entity settings updated successfully",
            "sync_triggered": True,
            "sync_message": "Automated data synchronization has been initiated based on your settings.",
            "sync_info": {
                "estimated_duration": "5-15 minutes",
                "warning": "Xero operations are typically slow. Please be patient while we sync your data.",
                "status_endpoint": f"/entities/{entity_id}/sync/status",
                "what_happens_next": [
                    "We'll validate your connection to Xero",
                    "Pull your Chart of Accounts and Contacts",
                    "Sync transaction data based on your settings",
                    "Process any prepayment transactions found",
                    "You'll receive real-time updates on progress"
                ]
            }
        }

    return {"message": "Entity settings updated successfully"}

@router.get("/{entity_id}/connection/status")
async def check_entity_connection_status(
    entity_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Check the connection status of an entity"""
    # Get entity document
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()

    if not entity_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )

    entity_data = entity_doc.to_dict()
    client_id = entity_data.get("client_id")
    entity_type = entity_data.get("type")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    if entity_type == "xero":
        # Initialize XeroApiClient
        xero_client = await XeroApiClient.create(
            platform_org_id=entity_id,
            tenant_id=client_id
        )

        # Check connection status
        connection_status = await xero_client.check_connection_status()

        # Update entity status based on connection check
        await entity_ref.update({
            "connection_details.status": connection_status["status"],
            "connection_details.last_checked": SERVER_TIMESTAMP,
            "updated_at": SERVER_TIMESTAMP
        })

        return {
            "entity_id": entity_id,
            "entity_name": entity_data.get("entity_name"),
            "type": entity_type,
            "connection_status": connection_status
        }
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unsupported entity type: {entity_type}"
        )

@router.post("/{entity_id}/connection/disconnect")
async def disconnect_entity(
    entity_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Disconnect an entity using smart disconnect logic"""
    # Get entity document
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()

    if not entity_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )

    entity_data = entity_doc.to_dict()
    client_id = entity_data.get("client_id")
    entity_type = entity_data.get("type")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    if entity_type == "xero":
        # Use XeroService for smart disconnect logic
        from ..services.xero_service import XeroService
        xero_service = XeroService(db)
        
        try:
            result = await xero_service.revoke_connection(entity_id, current_user)
            return result
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=str(e)
            )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unsupported entity type: {entity_type}"
        )

@router.post("/")
async def create_entity(
    entity_data: Dict[str, Any] = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Create a new entity"""
    client_id = entity_data.get("client_id")
    if not client_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="client_id is required"
        )

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Generate entity ID
    entity_id = str(uuid.uuid4())

    # Create entity document
    entity_doc_data = {
        "entity_id": entity_id,
        "client_id": client_id,
        "entity_name": entity_data.get("entity_name"),
        "type": entity_data.get("type", "manual"),
        "status": "pending",
        "connection_details": {
            "status": "disconnected",
            "connection_method": entity_data.get("connection_method", "manual")
        },
        "created_at": SERVER_TIMESTAMP,
        "updated_at": SERVER_TIMESTAMP,
        "created_by": current_user.uid
    }

    entity_ref = db.collection("ENTITIES").document(entity_id)
    await entity_ref.set(entity_doc_data)

    # Create entity settings if provided
    if "settings" in entity_data:
        settings_data = entity_data["settings"]
        settings_data.update({
            "entity_id": entity_id,
            "client_id": client_id,
            "created_at": SERVER_TIMESTAMP,
            "updated_at": SERVER_TIMESTAMP
        })

        settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
        await settings_ref.set(settings_data)

    return {"entity_id": entity_id, "message": "Entity created successfully"}

@router.put("/{entity_id}")
async def update_entity(
    entity_id: str = Path(...),
    entity_data: Dict[str, Any] = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Update entity details"""
    # Get entity to check permissions
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()

    if not entity_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )

    entity_data_current = entity_doc.to_dict()
    client_id = entity_data_current.get("client_id")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Update entity document
    update_data = {}
    allowed_fields = ["entity_name", "status", "type"]

    for field in allowed_fields:
        if field in entity_data:
            update_data[field] = entity_data[field]

    if update_data:
        update_data["updated_at"] = SERVER_TIMESTAMP
        await entity_ref.update(update_data)

    return {"message": "Entity updated successfully"}

@router.delete("/{entity_id}")
async def delete_entity(
    entity_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Delete an entity"""
    # Get entity to check permissions
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()

    if not entity_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )

    entity_data = entity_doc.to_dict()
    client_id = entity_data.get("client_id")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Delete entity settings
    settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
    await settings_ref.delete()

    # Delete entity document
    await entity_ref.delete()

    return {"message": "Entity deleted successfully"}

@router.get("/enums")
async def get_entity_enums():
    """Get entity enums for UI dropdowns"""
    return {
        "entity_types": [
            {"value": "xero", "label": "Xero", "description": "Xero accounting software"},
            {"value": "qbo", "label": "QuickBooks Online", "description": "QuickBooks Online accounting software"},
            {"value": "manual", "label": "Manual Entry", "description": "Manual data entry"}
        ],
        "entity_statuses": [
            {"value": "active", "label": "Active"},
            {"value": "inactive", "label": "Inactive"},
            {"value": "error", "label": "Error"},
            {"value": "syncing", "label": "Syncing"},
            {"value": "pending", "label": "Pending"},
            {"value": "disconnected", "label": "Disconnected"}
        ],
        "connection_statuses": [
            {"value": "connected", "label": "Connected"},
            {"value": "disconnected", "label": "Disconnected"},
            {"value": "error", "label": "Error"},
            {"value": "pending", "label": "Pending"},
            {"value": "expired", "label": "Expired"},
            {"value": "syncing", "label": "Syncing"}
        ],
        "sync_frequencies": [
            {"value": "hourly", "label": "Every Hour", "description": "Sync data every hour"},
            {"value": "daily", "label": "Daily", "description": "Sync data once per day"},
            {"value": "weekly", "label": "Weekly", "description": "Sync data once per week"},
            {"value": "manual", "label": "Manual Only", "description": "Only sync when manually triggered"}
        ],
        "currencies": [
            {"value": "USD", "label": "US Dollar", "symbol": "$"},
            {"value": "EUR", "label": "Euro", "symbol": "€"},
            {"value": "GBP", "label": "British Pound", "symbol": "£"},
            {"value": "CAD", "label": "Canadian Dollar", "symbol": "C$"},
            {"value": "AUD", "label": "Australian Dollar", "symbol": "A$"},
            {"value": "NZD", "label": "New Zealand Dollar", "symbol": "NZ$"},
            {"value": "JPY", "label": "Japanese Yen", "symbol": "¥"},
            {"value": "CHF", "label": "Swiss Franc", "symbol": "CHF"},
            {"value": "SEK", "label": "Swedish Krona", "symbol": "kr"},
            {"value": "NOK", "label": "Norwegian Krone", "symbol": "kr"},
            {"value": "DKK", "label": "Danish Krone", "symbol": "kr"}
        ],
        "amortization_periods": [
            {"value": 1, "label": "1 Month"},
            {"value": 3, "label": "3 Months"},
            {"value": 6, "label": "6 Months"},
            {"value": 12, "label": "12 Months"},
            {"value": 24, "label": "24 Months"},
            {"value": 36, "label": "36 Months"}
        ],
        "sync_data_types": [
            {"value": "invoices", "label": "Invoices", "description": "Customer invoices and sales"},
            {"value": "bills", "label": "Bills", "description": "Vendor bills and purchases"},
            {"value": "payments", "label": "Payments", "description": "Payment transactions"},
            {"value": "bank_transactions", "label": "Bank Transactions", "description": "Bank account transactions"},
            {"value": "journal_entries", "label": "Journal Entries", "description": "Manual journal entries"},
            {"value": "spend_money", "label": "Spend Money / Expenses", "description": "Spend Money transactions (Xero) / Expenses (QuickBooks Online)"}
        ]
    }

@router.post("/{entity_id}/sync/trigger")
async def trigger_entity_sync(
    entity_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Trigger manual sync for an entity"""
    # Get entity to check permissions
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()

    if not entity_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )

    entity_data = entity_doc.to_dict()
    client_id = entity_data.get("client_id")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Get entity settings for sync configuration
    settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
    settings_doc = await settings_ref.get()
    settings_data = settings_doc.to_dict() if settings_doc.exists else {}

    # Update entity status to syncing
    await entity_ref.update({
        "status": "syncing",
        "connection_details.last_sync_triggered": SERVER_TIMESTAMP,
        "connection_details.sync_triggered_by": current_user.uid,
        "updated_at": SERVER_TIMESTAMP
    })

    try:
        # Create sync job ID for tracking
        sync_job_id = str(uuid.uuid4())

        # Determine endpoints to sync based on settings
        endpoints_to_sync = ["Contacts", "Accounts"]  # Always sync these first

        if settings_data.get("sync_invoices", True):
            endpoints_to_sync.append("Invoices")
        if settings_data.get("sync_bills", True):
            endpoints_to_sync.append("Bills")
        if settings_data.get("sync_spend_money", True):
            endpoints_to_sync.append("SpendMoney")
        if settings_data.get("sync_journal_entries", True):
            endpoints_to_sync.append("ManualJournals")

        # Get transaction_sync_start_date from settings
        transaction_sync_start_date = settings_data.get("transaction_sync_start_date")

        # Create Pub/Sub message
        sync_message = {
            "platformOrgId": entity_id,
            "tenantId": client_id,
            "syncJobId": sync_job_id,
            "endpoints": endpoints_to_sync,
            "forceFullSyncEndpoints": [],  # No force full sync for manual triggers unless specified
            "targetDate": datetime.now().strftime("%Y-%m-%d"),
            "reason": "manual_sync_trigger",
            "triggered_by_user": current_user.uid
        }
        
        # Add transactionSyncStartDate if available
        if transaction_sync_start_date:
            sync_message["transactionSyncStartDate"] = transaction_sync_start_date

        # Publish to Pub/Sub topic
        project_id = os.getenv("GCP_PROJECT_ID")
        topic_name = os.getenv("PUBSUB_TOPIC_XERO_SYNC", "xero-sync-topic")

        if project_id:
            publisher = pubsub_v1.PublisherClient()
            topic_path = publisher.topic_path(project_id, topic_name)
            message_data = json.dumps(sync_message).encode("utf-8")

            # Publish message
            future = publisher.publish(topic_path, message_data)
            message_id = future.result(timeout=10)  # Wait for publish confirmation

            # Log successful trigger
            await create_audit_log_entry(
                db=db,
                event_category="SYNC",
                event_type="MANUAL_SYNC_TRIGGERED",
                client_id=client_id,
                entity_id=entity_id,
                status="SUCCESS",
                details={
                    "sync_job_id": sync_job_id,
                    "message_id": message_id,
                    "endpoints": endpoints_to_sync,
                    "triggered_by": current_user.uid
                },
                user_id=current_user.uid
            )

            logger.info(f"Manual sync triggered for entity {entity_id}, job: {sync_job_id}")
            
            return {
                "message": "Sync triggered successfully",
                "sync_job_id": sync_job_id,
                "endpoints": endpoints_to_sync,
                "estimated_duration": "5-15 minutes"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="GCP_PROJECT_ID not configured, cannot trigger sync"
            )

    except Exception as e:
        logger.error(f"Failed to trigger manual sync for entity {entity_id}: {e}")
        
        # Update entity status back to error
        await entity_ref.update({
            "status": "error",
            "connection_details.last_sync_error": str(e),
            "updated_at": SERVER_TIMESTAMP
        })
        
        # Log failed trigger
        await create_audit_log_entry(
            db=db,
            event_category="SYNC",
            event_type="MANUAL_SYNC_TRIGGER_FAILED",
            client_id=client_id,
            entity_id=entity_id,
            status="FAILURE",
            details={
                "error": str(e),
                "triggered_by": current_user.uid
            },
            user_id=current_user.uid
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to trigger sync: {str(e)}"
        )

@router.get("/{entity_id}/sync/status")
async def get_sync_status(
    entity_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Get detailed sync status for an entity with real-time updates"""
    # Get entity to check permissions
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()

    if not entity_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )

    entity_data = entity_doc.to_dict()
    client_id = entity_data.get("client_id")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Get entity settings for sync configuration
    settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
    settings_doc = await settings_ref.get()
    settings_data = settings_doc.to_dict() if settings_doc.exists else {}

    # Get recent audit logs for sync status
    audit_query = (
        db.collection("AUDIT_LOG")
        .where(filter=firestore.FieldFilter("entityId", "==", entity_id))
        .where(filter=firestore.FieldFilter("eventCategory", "==", "SYNC"))
        .order_by("timestamp", direction="DESCENDING")
        .limit(10)
    )
    audit_docs = await audit_query.get()

    recent_sync_events = []
    current_sync_job = None
    last_completed_sync = None

    for doc in audit_docs:
        audit_data = doc.to_dict()
        event_type = audit_data.get("eventType", "")

        recent_sync_events.append({
            "event_type": event_type,
            "status": audit_data.get("status"),
            "timestamp": audit_data.get("timestamp"),
            "details": audit_data.get("details", {})
        })

        # Track current sync job
        if event_type == "XERO_SYNC_JOB_STARTED" and not current_sync_job:
            current_sync_job = audit_data.get("details", {}).get("syncJobId")

        # Track last completed sync
        if event_type == "XERO_SYNC_JOB_COMPLETED" and not last_completed_sync:
            last_completed_sync = audit_data.get("timestamp")

    # Use master sync status function
    comprehensive_status = await get_comprehensive_sync_status(db, entity_id, client_id, entity_data, settings_data)

    # Return comprehensive sync status with user-friendly messaging
    return {
        "entity_id": entity_id,
        "entity_name": entity_data.get("entity_name"),
        "sync_status": {
            "current_status": comprehensive_status["current_operation"],
            "is_syncing": comprehensive_status["is_syncing"],
            "current_sync_job_id": comprehensive_status.get("current_sync_job_id"),
            "last_completed_sync": comprehensive_status.get("last_completed_sync"),
            "initial_sync_completed": settings_data.get("initial_sync_completed", False)
        },
        "progress_info": {
            "estimated_duration": "5-15 minutes for initial sync, 2-5 minutes for regular sync",
            "current_step": _get_current_sync_step(recent_sync_events),
            "steps_completed": _count_completed_steps(recent_sync_events),
            "total_steps": 5,
            "user_message": _get_user_friendly_status_message(recent_sync_events, entity_data)
        },
        "performance_warning": {
            "message": "Xero operations are typically slow due to API rate limits",
            "tips": [
                "Please be patient during the sync process",
                "You can safely close this page - sync will continue in background",
                "Check back in 5-10 minutes for completion status",
                "Large datasets may take longer to process"
            ]
        },
        "recent_events": recent_sync_events[:5],  # Last 5 events for debugging
        "next_scheduled_sync": _calculate_next_sync_time(settings_data),
        "sync_configuration": {
            "auto_sync_enabled": settings_data.get("auto_sync_enabled", True),
            "sync_frequency": settings_data.get("sync_frequency", "daily"),
            "data_types_enabled": {
                "invoices": settings_data.get("sync_invoices", True),
                "bills": settings_data.get("sync_bills", True),
                "payments": settings_data.get("sync_payments", True),
                "bank_transactions": settings_data.get("sync_bank_transactions", True),
                "journal_entries": settings_data.get("sync_journal_entries", True),
                "spend_money": settings_data.get("sync_spend_money", True)
            }
        }
    }

@router.post("/{entity_id}/connection/test")
async def test_entity_connection(
    entity_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Test entity connection"""
    # Get entity to check permissions
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()

    if not entity_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )

    entity_data = entity_doc.to_dict()
    client_id = entity_data.get("client_id")
    entity_type = entity_data.get("type")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    if entity_type == "xero":
        try:
            # Initialize XeroApiClient
            xero_client = await XeroApiClient.create(
                platform_org_id=entity_id,
                tenant_id=client_id
            )

            # Test connection
            connection_status = await xero_client.check_connection_status()

            return {
                "success": connection_status["status"] == "connected",
                "status": connection_status["status"],
                "message": connection_status.get("message", "Connection test completed"),
                "tested_at": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                "success": False,
                "status": "error",
                "message": str(e),
                "tested_at": datetime.utcnow().isoformat()
            }
    else:
        return {
            "success": True,
            "status": "connected",
            "message": f"{entity_type} entities don't require connection testing",
            "tested_at": datetime.utcnow().isoformat()
        }

@router.post("/{entity_id}/oauth/initiate")
async def initiate_oauth_connection(
    entity_id: str = Path(...),
    oauth_data: Dict[str, Any] = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Initiate OAuth connection for an entity"""
    # Get entity to check permissions
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()

    if not entity_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )

    entity_data = entity_doc.to_dict()
    client_id = entity_data.get("client_id")
    provider = oauth_data.get("provider")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    if provider == "xero":
        # For Xero, redirect to the existing Xero OAuth flow
        return {
            "authorization_url": f"/xero/connect/initiate/{client_id}",
            "state": entity_id,
            "provider": "xero"
        }
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"OAuth provider '{provider}' not supported"
        )

@router.post("/{entity_id}/oauth/callback")
async def complete_oauth_connection(
    entity_id: str = Path(...),
    callback_data: Dict[str, Any] = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Complete OAuth connection with callback data"""
    # Get entity to check permissions
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()

    if not entity_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )

    entity_data = entity_doc.to_dict()
    client_id = entity_data.get("client_id")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Update entity status
    await entity_ref.update({
        "status": "active",
        "connection_details.status": "connected",
        "connection_details.connected_at": SERVER_TIMESTAMP,
        "updated_at": SERVER_TIMESTAMP
    })

    return {
        "success": True,
        "entity_id": entity_id,
        "status": "connected",
        "message": "OAuth connection completed successfully"
    }

@router.post("/{entity_id}/oauth/revoke")
async def revoke_oauth_connection(
    entity_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Revoke OAuth connection for an entity"""
    # Get entity to check permissions
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()

    if not entity_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )

    entity_data = entity_doc.to_dict()
    client_id = entity_data.get("client_id")
    entity_type = entity_data.get("type")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    if entity_type == "xero":
        # For Xero entities, use the existing Xero revoke endpoint
        try:
            # Initialize XeroApiClient
            xero_client = await XeroApiClient.create(
                platform_org_id=entity_id,
                tenant_id=client_id
            )

            # Revoke tokens
            await xero_client.revoke_tokens()
        except Exception as e:
            # Log error but continue with local cleanup
            pass

    # Update entity status
    await entity_ref.update({
        "status": "inactive",
        "connection_details.status": "disconnected",
        "connection_details.disconnected_at": SERVER_TIMESTAMP,
        "updated_at": SERVER_TIMESTAMP
    })

    return {"message": "OAuth connection revoked successfully"}

@router.get("/{entity_id}/dashboard")
async def get_entity_dashboard(
    entity_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Get entity dashboard data with comprehensive sync status"""
    # Get entity to check permissions
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()

    if not entity_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )

    entity_data = entity_doc.to_dict()
    client_id = entity_data.get("client_id")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Get entity settings for sync configuration
    settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
    settings_doc = await settings_ref.get()
    settings_data = settings_doc.to_dict() if settings_doc.exists else {}

    # Use MASTER sync status function - SINGLE SOURCE OF TRUTH
    sync_status = await get_comprehensive_sync_status(db, entity_id, client_id, entity_data, settings_data)

    # Get data counts
    data_summary = await _get_entity_data_summary(db, entity_id, client_id)

    # Get actual last sync timestamp from entity settings
    last_sync_timestamp = await _get_last_sync_timestamp(db, entity_id)

    return {
        "entity_id": entity_id,
        "entity_name": entity_data.get("entity_name"),
        "health_score": 85,
        "connection_status": entity_data.get("connection_details", {}).get("status", "unknown"),
        "last_sync": last_sync_timestamp,
        "data_summary": data_summary,
        "recent_activity": sync_status.get("recent_activity", []),
        "sync_status": {
            **sync_status,
            "performance_info": {
                "message": "Xero operations typically take 5-15 minutes",
                "current_duration": sync_status.get("current_duration"),
                "estimated_remaining": sync_status.get("estimated_remaining"),
                "can_close_page": True,
                "background_processing": True
            }
        }
    }

@router.get("/{entity_id}/health")
async def get_entity_health_metrics(
    entity_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Get entity health metrics"""
    # Get entity to check permissions
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()

    if not entity_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )

    entity_data = entity_doc.to_dict()
    client_id = entity_data.get("client_id")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Mock health metrics for now
    # TODO: Implement actual health metrics calculation
    return {
        "entity_id": entity_id,
        "overall_score": 85,
        "connection_health": 90,
        "data_quality": 80,
        "sync_performance": 85,
        "error_rate": 5,
        "last_calculated": datetime.utcnow().isoformat(),
        "metrics": {
            "uptime_percentage": 99.5,
            "avg_sync_time": 120,
            "failed_syncs_24h": 0,
            "data_completeness": 95
        }
    }

async def get_comprehensive_sync_status(db, entity_id: str, client_id: str, entity_data: dict, settings_data: dict):
    """
    MASTER sync status function - SINGLE SOURCE OF TRUTH
    Returns comprehensive sync status with all needed data
    """
    # Get recent audit events
    audit_query = (
        db.collection("AUDIT_LOG")
        .where(filter=firestore.FieldFilter("entityId", "==", entity_id))
        .where(filter=firestore.FieldFilter("eventCategory", "==", "SYNC"))
        .order_by("timestamp", direction="DESCENDING")
        .limit(10)
    )
    audit_docs = await audit_query.get()
    
    recent_sync_events = []
    current_sync_job = None
    last_completed_sync = None
    current_duration = None
    estimated_remaining = None
    
    for doc in audit_docs:
        audit_data = doc.to_dict()
        event_type = audit_data.get("eventType", "")
        
        recent_sync_events.append({
            "eventType": event_type,
            "status": audit_data.get("status"),
            "timestamp": audit_data.get("timestamp"),
            "details": audit_data.get("details", {})
        })
        
        # Track current sync job
        if event_type == "XERO_SYNC_JOB_STARTED" and not current_sync_job:
            current_sync_job = audit_data.get("details", {}).get("syncJobId")
            # Calculate current duration if sync is active
            timestamp = audit_data.get("timestamp")
            if timestamp:
                from datetime import datetime
                try:
                    start_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    duration = datetime.utcnow() - start_time.replace(tzinfo=None)
                    current_duration = f"{int(duration.total_seconds() // 60)} minutes"
                    
                    # Estimate remaining time (rough estimate)
                    if duration.total_seconds() < 300:  # Less than 5 minutes
                        estimated_remaining = "8-12 minutes"
                    elif duration.total_seconds() < 600:  # Less than 10 minutes
                        estimated_remaining = "3-7 minutes"
                    else:
                        estimated_remaining = "1-3 minutes"
                except:
                    pass

        # Track last completed sync
        if event_type == "XERO_SYNC_JOB_COMPLETED" and not last_completed_sync:
            last_completed_sync = audit_data.get("timestamp")
    
    # Determine sync job status using our fixed logic
    sync_jobs = {}  # {syncJobId: latest_status}
    
    for event in recent_sync_events:
        event_type = event.get("eventType", "")
        status = event.get('status', '').upper()
        sync_job_id = event.get('details', {}).get('syncJobId')
        
        if sync_job_id and 'SYNC_JOB' in event_type:
            # Only update if we haven't seen this job yet (events are newest first)
            if sync_job_id not in sync_jobs:
                if 'COMPLETED' in event_type or status == 'SUCCESS':
                    sync_jobs[sync_job_id] = 'completed'
                elif 'STARTED' in event_type or status == 'STARTED':
                    sync_jobs[sync_job_id] = 'running'
    
    # Determine basic sync status
    is_syncing = False
    current_operation = "Ready"
    sync_progress = 0
    
    # Check if ANY sync job is still running
    for job_status in sync_jobs.values():
        if job_status == 'running':
            is_syncing = True
            current_operation = "Synchronizing data"
            sync_progress = 25
            break
    
    # All jobs completed or no jobs found
    if not is_syncing and sync_jobs:
        current_operation = "Sync completed"
        sync_progress = 100
    
    return {
        # Basic sync status
        "is_syncing": is_syncing,
        "sync_started_at": None,
        "sync_progress": sync_progress,
        "current_operation": current_operation,
        "estimated_completion": None,
        
        # Dashboard-specific data
        "current_sync_job_id": current_sync_job,
        "last_completed_sync": last_completed_sync,
        "current_duration": current_duration,
        "estimated_remaining": estimated_remaining,
        "recent_activity": recent_sync_events[:5],
        
        # Step tracking
        "current_step": _get_current_sync_step(recent_sync_events),
        "steps_completed": _count_completed_steps(recent_sync_events),
        "total_steps": 5,
        "user_message": _get_user_friendly_status_message(recent_sync_events, entity_data),
        
        # Settings
        "next_scheduled_sync": _calculate_next_sync_time(settings_data),
        "sync_configuration": {
            "auto_sync_enabled": settings_data.get("auto_sync_enabled", True),
            "sync_frequency": settings_data.get("sync_frequency", "daily"),
            "data_types_enabled": {
                "invoices": settings_data.get("sync_invoices", True),
                "bills": settings_data.get("sync_bills", True),
                "payments": settings_data.get("sync_payments", True),
                "bank_transactions": settings_data.get("sync_bank_transactions", True),
                "journal_entries": settings_data.get("sync_journal_entries", True),
                "spend_money": settings_data.get("sync_spend_money", True)
            }
        }
    }


def _determine_sync_status(recent_sync_events, entity_data, settings_data):
    """DEPRECATED - Use get_comprehensive_sync_status instead"""
    sync_jobs = {}  # {syncJobId: latest_status}
    
    for event in recent_sync_events:
        event_type = event.get("eventType", "")
        status = event.get('status', '').upper()
        sync_job_id = event.get('details', {}).get('syncJobId')
        
        if sync_job_id and 'SYNC_JOB' in event_type:
            # Only update if we haven't seen this job yet (events are newest first)
            if sync_job_id not in sync_jobs:
                if 'COMPLETED' in event_type or status == 'SUCCESS':
                    sync_jobs[sync_job_id] = 'completed'
                elif 'STARTED' in event_type or status == 'STARTED':
                    sync_jobs[sync_job_id] = 'running'
    
    # Check if ANY sync job is still running
    for job_status in sync_jobs.values():
        if job_status == 'running':
            return {
                "is_syncing": True,
                "sync_started_at": None,
                "sync_progress": 25,
                "current_operation": "Synchronizing data",
                "estimated_completion": None
            }
    
    # All jobs completed or no jobs found
    if sync_jobs:
        return {
            "is_syncing": False,
            "sync_started_at": None,
            "sync_progress": 100,
            "current_operation": "Sync completed",
            "estimated_completion": None
        }
    else:
        return {
            "is_syncing": False,
            "sync_started_at": None,
            "sync_progress": 0,
            "current_operation": "Ready",
            "estimated_completion": None
        }

def _get_current_sync_step(recent_sync_events):
    """Get the current sync step based on recent events"""
    if not recent_sync_events:
        return "idle"
    
    latest_event = recent_sync_events[0]
    # Handle both camelCase (Firebase) and snake_case field names
    event_type = latest_event.get("eventType", latest_event.get("event_type", ""))
    
    # Handle actual event types from Firebase audit logs
    if event_type == "XERO_SYNC_JOB_STARTED" or "SYNC_STARTED" in event_type:
        return "connecting"
    elif event_type == "ACCOUNTS_SYNC_SUCCESS" or "ACCOUNTS_SYNC" in event_type:
        return "accounts"
    elif event_type == "CONTACTS_SYNC_SUCCESS" or "CONTACTS_SYNC" in event_type:
        return "contacts"
    elif event_type == "TRANSACTIONS_SYNC_SUCCESS" or "TRANSACTIONS_SYNC" in event_type:
        return "transactions"
    elif event_type == "XERO_SYNC_JOB_COMPLETED" or "SYNC_COMPLETED" in event_type:
        return "completed"
    elif "ERROR" in event_type or "FAILED" in event_type or "FAILURE" in event_type:
        return "error"
    else:
        return "idle"

def _count_completed_steps(recent_sync_events):
    """Count the number of completed sync steps"""
    completed_steps = 0
    step_events = [
        "ACCOUNTS_SYNC_SUCCESS",
        "CONTACTS_SYNC_SUCCESS", 
        "TRANSACTIONS_SYNC_SUCCESS",
        "XERO_SYNC_JOB_COMPLETED",
        # Also handle potential variations
        "CHART_OF_ACCOUNTS_SYNC_COMPLETED",
        "CONTACTS_SYNC_COMPLETED", 
        "TRANSACTIONS_SYNC_COMPLETED"
    ]
    
    for event in recent_sync_events:
        # Handle both camelCase (Firebase) and snake_case field names
        event_type = event.get("eventType", event.get("event_type", ""))
        if event_type in step_events or "SYNC_SUCCESS" in event_type:
            completed_steps += 1
    
    return completed_steps

def _get_user_friendly_status_message(recent_sync_events, entity_data):
    """Get a user-friendly status message"""
    if not recent_sync_events:
        return "Ready to sync"
    
    latest_event = recent_sync_events[0]
    event_type = latest_event.get("event_type", "")
    
    if event_type == "XERO_SYNC_JOB_STARTED":
        return "Connecting to Xero and initializing sync..."
    elif event_type == "CHART_OF_ACCOUNTS_SYNC_STARTED":
        return "Syncing Chart of Accounts..."
    elif event_type == "CONTACTS_SYNC_STARTED":
        return "Syncing Contacts..."
    elif event_type == "TRANSACTIONS_SYNC_STARTED":
        return "Syncing Transactions..."
    elif event_type == "XERO_SYNC_JOB_COMPLETED":
        return "Sync completed successfully"
    elif "ERROR" in event_type or "FAILED" in event_type:
        return "Sync failed - please check connection and try again"
    else:
        entity_status = entity_data.get("status", "unknown")
        if entity_status == "syncing":
            return "Sync in progress..."
        else:
            return "Ready"

def _calculate_next_sync_time(settings_data):
    """Calculate the next scheduled sync time"""
    sync_frequency = settings_data.get("sync_frequency", "daily")
    auto_sync_enabled = settings_data.get("auto_sync_enabled", True)
    
    if not auto_sync_enabled:
        return None
    
    from datetime import datetime, timedelta
    
    now = datetime.utcnow()
    
    if sync_frequency == "hourly":
        next_sync = now + timedelta(hours=1)
    elif sync_frequency == "daily":
        next_sync = now + timedelta(days=1)
    elif sync_frequency == "weekly":
        next_sync = now + timedelta(weeks=1)
    else:
        return None  # Manual sync only
    
    return next_sync.isoformat()

async def _get_dashboard_sync_status(db, entity_id: str, client_id: str):
    """Get sync status information for dashboard display"""
    try:
        # Get recent audit logs for sync status
        audit_query = (
            db.collection("AUDIT_LOG")
            .where(filter=firestore.FieldFilter("entityId", "==", entity_id))
            .where(filter=firestore.FieldFilter("eventCategory", "==", "SYNC"))
            .order_by("timestamp", direction="DESCENDING")
            .limit(10)
        )
        audit_docs = await audit_query.get()

        recent_activity = []
        current_sync_job = None
        last_completed_sync = None
        current_duration = None
        estimated_remaining = None

        for doc in audit_docs:
            audit_data = doc.to_dict()
            event_type = audit_data.get("eventType", "")
            
            recent_activity.append({
                "event_type": event_type,
                "status": audit_data.get("status"),
                "timestamp": audit_data.get("timestamp"),
                "details": audit_data.get("details", {})
            })

            # Track current sync job
            if event_type == "XERO_SYNC_JOB_STARTED" and not current_sync_job:
                current_sync_job = audit_data.get("details", {}).get("syncJobId")
                # Calculate current duration if sync is active
                timestamp = audit_data.get("timestamp")
                if timestamp:
                    from datetime import datetime
                    try:
                        start_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        duration = datetime.utcnow() - start_time.replace(tzinfo=None)
                        current_duration = f"{int(duration.total_seconds() // 60)} minutes"
                        
                        # Estimate remaining time (rough estimate)
                        if duration.total_seconds() < 300:  # Less than 5 minutes
                            estimated_remaining = "8-12 minutes"
                        elif duration.total_seconds() < 600:  # Less than 10 minutes
                            estimated_remaining = "3-7 minutes"
                        else:
                            estimated_remaining = "1-3 minutes"
                    except:
                        pass

            # Track last completed sync
            if event_type == "XERO_SYNC_JOB_COMPLETED" and not last_completed_sync:
                last_completed_sync = audit_data.get("timestamp")

        return {
            "recent_activity": recent_activity[:5],  # Last 5 events
            "current_sync_job": current_sync_job,
            "last_completed_sync": last_completed_sync,
            "current_duration": current_duration,
            "estimated_remaining": estimated_remaining
        }

    except Exception as e:
        logger.error(f"Error getting dashboard sync status for entity {entity_id}: {e}")
        return {
            "recent_activity": [],
            "current_sync_job": None,
            "last_completed_sync": None,
            "current_duration": None,
            "estimated_remaining": None
        }

async def _get_entity_data_summary(db, entity_id: str, client_id: str):
    """Get summary of entity data counts"""
    try:
        data_summary = {
            "total_transactions": 0,
            "total_invoices": 0,
            "total_bills": 0,
            "total_contacts": 0,
            "total_accounts": 0,
            "pending_items": 0,
            "processed_items": 0
        }

        # Get counts from different collections
        # This is a simplified version - in practice you might want to cache these counts
        
        # Count Chart of Accounts
        try:
            accounts_query = db.collection("CHART_OF_ACCOUNTS").where(filter=firestore.FieldFilter("entity_id", "==", entity_id))
            accounts_count = len([doc async for doc in accounts_query.stream()])
            data_summary["total_accounts"] = accounts_count
        except:
            pass

        # Count Contacts  
        try:
            contacts_query = db.collection("CONTACTS").where(filter=firestore.FieldFilter("entity_id", "==", entity_id))
            contacts_count = len([doc async for doc in contacts_query.stream()])
            data_summary["total_contacts"] = contacts_count
        except:
            pass

        # For transactions, invoices, bills - these might be in different collections
        # Add actual counts based on your data structure
        
        return data_summary

    except Exception as e:
        logger.error(f"Error getting entity data summary for entity {entity_id}: {e}")
        return {
            "total_transactions": 0,
            "total_invoices": 0, 
            "total_bills": 0,
            "total_contacts": 0,
            "total_accounts": 0,
            "pending_items": 0,
            "processed_items": 0
        }

async def _get_last_sync_timestamp(db, entity_id: str) -> str:
    """
    Get the most recent sync timestamp from entity settings.
    Returns the timestamp as an ISO string or None if never synced.
    """
    try:
        # Get entity settings to check for sync timestamps
        settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
        settings_doc = await settings_ref.get()

        if not settings_doc.exists:
            return None

        settings_data = settings_doc.to_dict()

        # Look for the most recent sync timestamp
        sync_timestamps = []

        # Check for various sync timestamp fields
        timestamp_fields = [
            "_system_lastSyncTimestampUtc_Accounts",
            "_system_lastSyncTimestampUtc_Contacts",
            "_system_lastSyncTimestampUtc_Transactions",
            "_system_lastSyncTimestampUtc_Bills",
            "_system_lastSyncTimestampUtc_Invoices",
            "_system_lastSyncTimestampUtc_JournalEntries",
            "_system_lastSyncTimestampUtc_SpendMoney"
        ]

        for field in timestamp_fields:
            if field in settings_data and settings_data[field]:
                sync_timestamps.append(settings_data[field])

        if sync_timestamps:
            # Return the most recent timestamp
            return max(sync_timestamps)

        return None

    except Exception as e:
        print(f"Error getting last sync timestamp for entity {entity_id}: {e}")
        return None

@router.get("/{entity_id}/accounts")
async def get_entity_accounts(
    entity_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Get chart of accounts for an entity"""
    # Get entity to check permissions
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()

    if not entity_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )

    entity_data = entity_doc.to_dict()
    client_id = entity_data.get("client_id")
    entity_type = entity_data.get("type")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    if entity_type == "xero":
        try:
            # First, try to get accounts from Firestore cache
            accounts_query = db.collection("CHART_OF_ACCOUNTS").where(filter=firestore.FieldFilter("entity_id", "==", entity_id))
            accounts_docs = await accounts_query.get()

            if accounts_docs:
                # Return cached accounts from Firestore
                formatted_accounts = []
                for doc in accounts_docs:
                    account_data = doc.to_dict()
                    formatted_accounts.append({
                        "account_id": account_data.get("account_id"),
                        "code": account_data.get("code"),
                        "name": account_data.get("name"),
                        "type": account_data.get("type"),
                        "class": account_data.get("class"),
                        "status": account_data.get("status"),
                        "description": account_data.get("description")
                    })

                logger.info(f"Returned {len(formatted_accounts)} cached accounts for entity {entity_id}")
                return {"accounts": formatted_accounts}

            # Fallback: If no cached data, fetch from Xero (but this should be rare)
            logger.warning(f"No cached chart of accounts found for entity {entity_id}, falling back to Xero API")

            # Initialize XeroApiClient
            xero_client = await XeroApiClient.create(
                platform_org_id=entity_id,
                tenant_id=client_id
            )

            # Get accounts from Xero using the correct method
            accounts = await xero_client.get_records("Accounts")

            # Transform to expected format
            formatted_accounts = []
            for account in accounts:
                formatted_accounts.append({
                    "account_id": account.get("AccountID"),
                    "code": account.get("Code"),
                    "name": account.get("Name"),
                    "type": account.get("Type"),
                    "class": account.get("Class"),
                    "status": account.get("Status"),
                    "description": account.get("Description")
                })

            # Cache the accounts in Firestore for future use
            batch = db.batch()
            for account in accounts:
                account_doc_id = f"{entity_id}_{account.get('AccountID')}"
                account_ref = db.collection("CHART_OF_ACCOUNTS").document(account_doc_id)
                account_data = {
                    "entity_id": entity_id,
                    "client_id": client_id,
                    "account_id": account.get("AccountID"),
                    "code": account.get("Code"),
                    "name": account.get("Name"),
                    "type": account.get("Type"),
                    "class": account.get("Class"),
                    "status": account.get("Status"),
                    "description": account.get("Description"),
                    "source_system": "XERO",
                    "raw_xero_data": account,
                    "created_at": SERVER_TIMESTAMP,
                    "updated_at": SERVER_TIMESTAMP,
                    "source_updated_at_utc": account.get("UpdatedDateUTC")
                }
                batch.set(account_ref, account_data)

            await batch.commit()
            logger.info(f"Cached {len(accounts)} accounts for entity {entity_id}")

            return {"accounts": formatted_accounts}
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to fetch accounts: {str(e)}"
            )
    else:
        # For non-Xero entities, return empty accounts list
        return {"accounts": []}

@router.get("/{entity_id}/configuration/status")
async def get_entity_configuration_status(
    entity_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Get entity configuration status and recommendations"""
    # Get entity to check permissions
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()

    if not entity_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entity not found"
        )

    entity_data = entity_doc.to_dict()
    client_id = entity_data.get("client_id")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Get entity settings
    settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
    settings_doc = await settings_ref.get()
    settings_data = settings_doc.to_dict() if settings_doc.exists else {}

    # Check configuration completeness
    configuration_checks = {
        "transaction_sync_start_date": {
            "configured": bool(settings_data.get("transaction_sync_start_date")),
            "required": True,
            "title": "Transaction Sync Start Date",
            "description": "Set the date from which to start syncing transaction data",
            "recommendation": "Choose a date that balances data completeness with sync performance. Typically 1-2 years back is sufficient."
        },
        "prepayment_asset_accounts": {
            "configured": bool(settings_data.get("prepayment_asset_account_codes")),
            "required": False,
            "title": "Prepayment Asset Accounts",
            "description": "Configure which accounts should be treated as prepayment assets for amortization",
            "recommendation": "Select accounts like 'Prepaid Expenses', 'Prepaid Insurance', etc."
        },
        "excluded_pnl_accounts": {
            "configured": bool(settings_data.get("excluded_pnl_account_codes")),
            "required": False,
            "title": "Excluded P&L Accounts",
            "description": "Configure which P&L accounts to exclude from amortization processing",
            "recommendation": "Exclude accounts that shouldn't be amortized like 'Bank Fees', 'Interest Expense', etc."
        },
        "sync_preferences": {
            "configured": bool(settings_data.get("sync_frequency")),
            "required": True,
            "title": "Sync Preferences",
            "description": "Configure how often data should be synchronized",
            "recommendation": "Daily sync is recommended for most businesses"
        },
        "base_currency": {
            "configured": bool(settings_data.get("base_currency_code")),
            "required": True,
            "title": "Base Currency",
            "description": "Set the base currency for your entity",
            "recommendation": "This should match your primary accounting currency"
        }
    }

    # Calculate overall completion
    total_checks = len(configuration_checks)
    required_checks = sum(1 for check in configuration_checks.values() if check["required"])
    configured_required = sum(1 for check in configuration_checks.values() if check["required"] and check["configured"])
    configured_total = sum(1 for check in configuration_checks.values() if check["configured"])

    completion_percentage = (configured_total / total_checks) * 100
    required_completion_percentage = (configured_required / required_checks) * 100 if required_checks > 0 else 100

    # Determine next steps
    next_steps = []
    if not settings_data.get("transaction_sync_start_date"):
        next_steps.append({
            "action": "set_sync_start_date",
            "title": "Set Transaction Sync Start Date",
            "description": "Choose when to start syncing transaction data",
            "priority": "high"
        })

    if not settings_data.get("sync_frequency"):
        next_steps.append({
            "action": "configure_sync_frequency",
            "title": "Configure Sync Frequency",
            "description": "Set how often data should be synchronized",
            "priority": "high"
        })

    if not settings_data.get("prepayment_asset_account_codes"):
        next_steps.append({
            "action": "configure_prepayment_accounts",
            "title": "Configure Prepayment Accounts",
            "description": "Set up accounts for amortization processing",
            "priority": "medium"
        })

    if configured_required == required_checks and not settings_data.get("initial_sync_completed"):
        next_steps.append({
            "action": "trigger_initial_sync",
            "title": "Start Initial Transaction Sync",
            "description": "Begin syncing transaction data from your accounting system",
            "priority": "high"
        })

    return {
        "entity_id": entity_id,
        "entity_name": entity_data.get("entity_name"),
        "entity_type": entity_data.get("type"),
        "configuration_status": {
            "completion_percentage": round(completion_percentage, 1),
            "required_completion_percentage": round(required_completion_percentage, 1),
            "is_ready_for_sync": required_completion_percentage == 100,
            "configured_items": configured_total,
            "total_items": total_checks,
            "required_items": required_checks,
            "configured_required_items": configured_required
        },
        "configuration_checks": configuration_checks,
        "next_steps": next_steps,
        "recommendations": {
            "sync_start_date": "For most businesses, syncing data from 1-2 years back provides good balance between completeness and performance",
            "sync_frequency": "Daily sync is recommended for active businesses, weekly for smaller operations",
            "prepayment_setup": "Configure prepayment accounts if you have recurring expenses like insurance, software subscriptions, or rent"
        }
    }


# Helper functions for automated sync and status tracking

async def _should_trigger_automated_sync(
    db, entity_id: str, client_id: str, update_data: dict, settings_existed: bool
) -> bool:
    """
    Determine if automated sync should be triggered based on entity settings changes.

    Triggers sync when:
    1. Initial setup is completed (required fields are configured)
    2. Sync configuration changes that require data refresh
    3. First time setup after entity connection
    """
    # Check if this is initial setup completion
    if not settings_existed:
        # New settings document - check if required fields are present
        required_fields = ["transaction_sync_start_date", "sync_frequency", "base_currency_code"]
        has_required = all(field in update_data for field in required_fields)
        if has_required:
            logger.info(f"Initial setup completed for entity {entity_id}, triggering automated sync")
            return True

    # Check if sync-related settings changed
    sync_trigger_fields = [
        "transaction_sync_start_date",
        "sync_frequency",
        "auto_sync_enabled",
        "sync_invoices",
        "sync_bills",
        "sync_payments",
        "sync_bank_transactions",
        "sync_journal_entries",
        "sync_spend_money"
    ]

    sync_settings_changed = any(field in update_data for field in sync_trigger_fields)
    if sync_settings_changed and update_data.get("auto_sync_enabled", True):
        logger.info(f"Sync settings changed for entity {entity_id}, triggering automated sync")
        return True

    return False


async def _trigger_automated_sync_background(
    db, entity_id: str, client_id: str, user_id: str, update_data: dict
):
    """
    Trigger automated sync in background with comprehensive status tracking.
    """
    try:
        # Update entity status to indicate sync is starting
        entity_ref = db.collection("ENTITIES").document(entity_id)
        await entity_ref.update({
            "status": "syncing",
            "connection_details.last_sync_triggered": SERVER_TIMESTAMP,
            "connection_details.sync_triggered_by": user_id,
            "updated_at": SERVER_TIMESTAMP
        })

        # Create sync job ID for tracking
        sync_job_id = str(uuid.uuid4())

        # Determine endpoints to sync based on settings
        endpoints_to_sync = ["Contacts", "Accounts"]  # Always sync these first

        if update_data.get("sync_invoices", True):
            endpoints_to_sync.append("Invoices")
        if update_data.get("sync_bills", True):
            endpoints_to_sync.append("Bills")
        if update_data.get("sync_spend_money", True):
            endpoints_to_sync.append("SpendMoney")
        if update_data.get("sync_journal_entries", True):
            endpoints_to_sync.append("ManualJournals")

        # Get current entity settings to include transaction_sync_start_date
        settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
        settings_doc = await settings_ref.get()
        settings_data = settings_doc.to_dict() if settings_doc.exists else {}
        
        # Get transaction_sync_start_date from settings (either from update_data or existing settings)
        transaction_sync_start_date = update_data.get("transaction_sync_start_date") or settings_data.get("transaction_sync_start_date")

        # Create Pub/Sub message
        sync_message = {
            "platformOrgId": entity_id,
            "tenantId": client_id,
            "syncJobId": sync_job_id,
            "endpoints": endpoints_to_sync,
            "forceFullSyncEndpoints": endpoints_to_sync,  # Force full sync for automated triggers
            "targetDate": datetime.now().strftime("%Y-%m-%d"),
            "reason": "automated_settings_sync",
            "triggered_by_user": user_id,
            "settings_updated": list(update_data.keys())
        }
        
        # Add transactionSyncStartDate if available
        if transaction_sync_start_date:
            sync_message["transactionSyncStartDate"] = transaction_sync_start_date

        # Publish to Pub/Sub topic
        project_id = os.getenv("GCP_PROJECT_ID")
        topic_name = os.getenv("PUBSUB_TOPIC_XERO_SYNC", "xero-sync-topic")

        if project_id:
            publisher = pubsub_v1.PublisherClient()
            topic_path = publisher.topic_path(project_id, topic_name)
            message_data = json.dumps(sync_message).encode("utf-8")

            # Publish async
            future = publisher.publish(topic_path, message_data)

            # Don't wait for result - fire and forget
            asyncio.create_task(_log_sync_trigger_result(
                db, client_id, entity_id, sync_job_id, future, user_id
            ))

            logger.info(f"Automated sync triggered for entity {entity_id}, job: {sync_job_id}")
        else:
            logger.error("GCP_PROJECT_ID not set, cannot publish sync message")

    except Exception as e:
        logger.error(f"Failed to trigger automated sync for entity {entity_id}: {e}")
        # Update entity status back to previous state
        try:
            await entity_ref.update({
                "status": "error",
                "connection_details.last_sync_error": str(e),
                "updated_at": SERVER_TIMESTAMP
            })
        except:
            pass


async def _log_sync_trigger_result(
    db, client_id: str, entity_id: str, sync_job_id: str, future, user_id: str
):
    """Log the result of sync trigger attempt"""
    try:
        message_id = future.result(timeout=10)  # Wait up to 10 seconds
        await create_audit_log_entry(
            db=db,
            event_category="SYNC",
            event_type="AUTOMATED_SYNC_TRIGGERED",
            client_id=client_id,
            entity_id=entity_id,
            status="SUCCESS",
            details={
                "sync_job_id": sync_job_id,
                "message_id": message_id,
                "triggered_by": user_id,
                "reason": "automated_settings_sync"
            },
            user_id=user_id
        )
    except Exception as e:
        logger.error(f"Failed to log sync trigger result: {e}")
        await create_audit_log_entry(
            db=db,
            event_category="SYNC",
            event_type="AUTOMATED_SYNC_TRIGGER_FAILED",
            client_id=client_id,
            entity_id=entity_id,
            status="FAILURE",
            details={
                "sync_job_id": sync_job_id,
                "error": str(e),
                "triggered_by": user_id
            },
            user_id=user_id
        )


# === ENTITY SETUP WIZARD ENDPOINTS ===

@router.get("/{entity_id}/analysis/wizard")
async def get_entity_wizard_analysis(
    entity_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Get entity analysis data for setup wizard"""
    try:
        # Get entity to check permissions
        entity_ref = db.collection("ENTITIES").document(entity_id)
        entity_doc = await entity_ref.get()

        if not entity_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entity not found"
            )

        entity_data = entity_doc.to_dict()
        client_id = entity_data.get("client_id")

        # Check if user has access to this client
        await get_firm_user_with_client_access(client_id, current_user)

        # Initialize analysis service and get wizard data
        analysis_service = EntityAnalysisService(db)
        analysis_result = await analysis_service.analyze_entity_for_wizard(entity_id)

        return analysis_result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get wizard analysis for entity {entity_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to analyze entity for wizard"
        )


@router.get("/{entity_id}/analysis/bill-aggregates")
async def get_entity_bill_aggregates(
    entity_id: str = Path(...),
    date_range_months: int = Query(12, description="Number of months to include in analysis"),
    amount_threshold: float = Query(0, description="Minimum bill amount to include (0 = no threshold)"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Get aggregated bill data for interactive cost estimation"""
    try:
        # Get entity to check permissions
        entity_ref = db.collection("ENTITIES").document(entity_id)
        entity_doc = await entity_ref.get()

        if not entity_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entity not found"
            )

        entity_data = entity_doc.to_dict()
        client_id = entity_data.get("client_id")

        # Check if user has access to this client
        await get_firm_user_with_client_access(client_id, current_user)

        # Initialize aggregation service and get bill aggregates
        aggregation_service = BillAggregationService(db)
        aggregates_result = await aggregation_service.get_bill_aggregates_for_wizard(
            entity_id, 
            date_range_months,
            amount_threshold
        )

        return aggregates_result

    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to get bill aggregates for entity {entity_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get bill aggregates"
        )


@router.post("/{entity_id}/setup/complete")
async def complete_entity_setup(
    entity_id: str = Path(...),
    setup_data: Dict[str, Any] = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Complete entity setup wizard and save configuration"""
    try:
        # Get entity to check permissions
        entity_ref = db.collection("ENTITIES").document(entity_id)
        entity_doc = await entity_ref.get()

        if not entity_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entity not found"
            )

        entity_data = entity_doc.to_dict()
        client_id = entity_data.get("client_id")

        # Check if user has access to this client
        await get_firm_user_with_client_access(client_id, current_user)

        # Extract setup configuration
        ai_scanning_enabled = setup_data.get("enable_ai_scanning", False)
        sync_settings = setup_data.get("sync_settings", {})
        account_settings = setup_data.get("account_settings", {})

        # Update entity settings
        settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
        settings_doc = await settings_ref.get()

        settings_update = {
            # Sync settings
            "transaction_sync_start_date": sync_settings.get("sync_start_date"),
            "sync_frequency": sync_settings.get("sync_frequency", "daily"),
            "auto_sync_enabled": sync_settings.get("auto_sync_enabled", True),
            
            # Data types to sync
            "sync_invoices": sync_settings.get("sync_invoices", True),
            "sync_bills": sync_settings.get("sync_bills", True),
            "sync_payments": sync_settings.get("sync_payments", True),
            "sync_bank_transactions": sync_settings.get("sync_bank_transactions", True),
            "sync_journal_entries": sync_settings.get("sync_journal_entries", True),
            "sync_spend_money": sync_settings.get("sync_spend_money", True),

            # Account configuration
            "prepayment_asset_account_codes": account_settings.get("prepayment_asset_accounts", []),
            "excluded_pnl_account_codes": account_settings.get("excluded_accounts", []),
            "default_expense_account_code": account_settings.get("default_expense_account"),
            # Currency removed - use entity.base_currency as single source of truth

            # AI and processing settings
            "ai_scanning_enabled": ai_scanning_enabled,
            "scanning_amount_threshold": setup_data.get("scanning_threshold", 100.0),
            "amortization_materiality_threshold": setup_data.get("materiality_threshold", 1000.0),
            "enable_llm_prepayment_detection": ai_scanning_enabled,
            "max_attachment_size_mb": setup_data.get("max_attachment_size", 30),
            "supported_attachment_types": ["application/pdf", "image/jpeg", "image/png", "image/jpg"],
            "auto_post_proposed_journals": setup_data.get("auto_post_journals", False),
            
            # Wizard completion tracking
            "setup_wizard_completed": True,
            "setup_completed_at": SERVER_TIMESTAMP,
            "setup_completed_by": current_user.uid,
            
            # Standard fields
            "updated_at": SERVER_TIMESTAMP
        }

        if settings_doc.exists:
            await settings_ref.update(settings_update)
        else:
            # Create new settings document
            settings_update.update({
                "entity_id": entity_id,
                "client_id": client_id,
                "created_at": SERVER_TIMESTAMP
            })
            await settings_ref.set(settings_update)

        # Create audit log entry
        await create_audit_log_entry(
            db=db,
            event_category="ENTITY_MANAGEMENT",
            event_type="SETUP_WIZARD_COMPLETED",
            client_id=client_id,
            entity_id=entity_id,
            status="SUCCESS",
            details={
                "ai_scanning_enabled": ai_scanning_enabled,
                "sync_frequency": sync_settings.get("sync_frequency"),
                "prepayment_accounts_configured": len(account_settings.get("prepayment_asset_accounts", [])),
                "entity_type": entity_data.get("type")
            },
            user_id=current_user.uid
        )

        # Trigger appropriate sync based on settings
        should_trigger_sync = ai_scanning_enabled or sync_settings.get("auto_sync_enabled", True)
        
        if should_trigger_sync:
            # Use existing automated sync trigger logic
            await _trigger_automated_sync_background(
                db, entity_id, client_id, current_user.uid, settings_update
            )

            return {
                "message": "Entity setup completed successfully",
                "sync_triggered": True,
                "sync_info": {
                    "estimated_duration": "5-15 minutes",
                    "ai_scanning_enabled": ai_scanning_enabled,
                    "sync_frequency": sync_settings.get("sync_frequency", "daily")
                }
            }
        else:
            return {
                "message": "Entity setup completed successfully",
                "sync_triggered": False
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to complete entity setup for {entity_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to complete entity setup"
        )