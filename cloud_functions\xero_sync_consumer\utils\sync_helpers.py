import os
import logging
import asyncio
import re
import uuid
import json
import httpx
from typing import Optional, Any, Dict
from google.cloud import storage, firestore

# Configure basic logging
logger = logging.getLogger(__name__)
GCP_PROJECT_ID = os.getenv("GCP_PROJECT_ID")

async def _upload_to_gcs(
    bucket_name: str,
    destination_blob_name: str,
    file_bytes: bytes,
    content_type: Optional[str] = None,
):
    """Uploads bytes to a GCS bucket asynchronously."""
    try:
        storage_client = storage.Client(project=GCP_PROJECT_ID)
        loop = asyncio.get_running_loop()

        def upload_sync():
            bucket = storage_client.bucket(bucket_name)
            blob = bucket.blob(destination_blob_name)
            blob.upload_from_string(
                data=file_bytes,
                content_type=content_type or "application/octet-stream",
            )
            logger.info(
                f"Successfully uploaded to gs://{bucket_name}/{destination_blob_name}"
            )

        await loop.run_in_executor(None, upload_sync)
    except Exception as e:
        logger.error(
            f"Failed to upload gs://{bucket_name}/{destination_blob_name} to GCS: {e}",
            exc_info=True,
        )

def get_other_secret(secret_name_in_secret_manager: str) -> Optional[str]:
    """Fetches a generic secret from environment."""
    secret_value = os.getenv(secret_name_in_secret_manager.upper().replace("-", "_"))
    logger.info(
        f"Attempting to get secret: {secret_name_in_secret_manager}. Found: {'Yes' if secret_value else 'No'}"
    )
    if not secret_value:
        logger.warning(
            f"Secret {secret_name_in_secret_manager} not found in environment."
        )
    return secret_value

def _normalize_to_float(value: Any) -> Optional[float]:
    if value is None:
        return None
    try:
        normalized_str = re.sub(r"[^\d.\-]", "", str(value))
        if not normalized_str or normalized_str == "-":
            return None
        return float(normalized_str)
    except (ValueError, TypeError):
        logger.warning(f"Could not normalize '{value}' to float.")
        return None

async def _create_audit_log_entry(
    db: firestore.AsyncClient,
    event_category: str,
    event_type: str,
    client_id: Optional[str],
    entity_id: str,
    status: str, # e.g., "SUCCESS", "FAILURE", "STARTED", "INFO"
    details: Dict[str, Any],
    user_id: Optional[str] = None,
    source_ip: Optional[str] = None, # Optional: for user-initiated actions via API
):
    """Creates an audit log entry in Firestore."""
    try:
        log_entry_id = str(uuid.uuid4())
        log_data = {
            "timestamp": firestore.SERVER_TIMESTAMP,
            "eventCategory": event_category, # e.g., "SYNC", "USER_MANAGEMENT", "SYSTEM"
            "eventType": event_type,         # e.g., "XERO_SYNC_JOB_STARTED", "USER_LOGIN_SUCCESS"
            "clientId": client_id,
            "entityId": entity_id,
            "userId": user_id,
            "sourceIp": source_ip,
            "status": status,
            "details": details, # A dictionary for structured information
        }
        # Remove None fields to keep documents clean
        log_data = {k: v for k, v in log_data.items() if v is not None}

        audit_log_ref = db.collection("AUDIT_LOG").document(log_entry_id)
        await audit_log_ref.set(log_data)
        logger.info(f"Audit log created: {event_category} - {event_type} for entity {entity_id}. ID: {log_entry_id}")
    except Exception as e_audit:
        logger.error(f"Failed to create audit log for {event_category} - {event_type} for entity {entity_id}: {e_audit}", exc_info=True)

async def _update_firestore_sync_timestamp(
    db: firestore.AsyncClient,
    entity_id: str, # Changed from platform_org_id
    sync_endpoint: str,
    timestamp_to_set: str,
    reason: str,
):
    """Updates the specific last sync timestamp in ENTITY_SETTINGS for an entity and endpoint."""
    try:
        entity_settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id) # Use entity_id
        sync_timestamp_field_name = f"_system_lastSyncTimestampUtc_{sync_endpoint.replace('/', '_').lstrip('_')}"
        update_data = {
            sync_timestamp_field_name: timestamp_to_set,
            "_system_lastSyncTimestampUtc_reason": reason,
            "_system_updatedAt": firestore.SERVER_TIMESTAMP,
        }
        await entity_settings_ref.set(update_data, merge=True)
        logger.info(
            f"Successfully updated sync timestamp field {sync_timestamp_field_name} to {timestamp_to_set} for entity {entity_id} due to: {reason}."
        )
    except Exception as e_fs_ts_update:
        logger.error(
            f"Failed to update {sync_timestamp_field_name} for entity {entity_id} to {timestamp_to_set}: {e_fs_ts_update}",
            exc_info=True,
        )

async def _update_entity_settings(
    db: firestore.AsyncClient,
    entity_id: str,
    updates: Dict[str, Any],
    reason: str
):
    """Updates entity settings with the provided changes."""
    try:
        entity_settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
        update_data = {
            **updates,
            "_system_updatedAt": firestore.SERVER_TIMESTAMP,
            "_system_lastUpdateReason": reason
        }
        await entity_settings_ref.set(update_data, merge=True)
        logger.info(f"Successfully updated entity settings for entity {entity_id}: {reason}")
    except Exception as e:
        logger.error(f"Failed to update entity settings for entity {entity_id}: {e}", exc_info=True)


async def _trigger_heavy_processing_stage(ctx) -> None:
    """
    Trigger heavy processing stage via FastAPI endpoint.
    This function calls the FastAPI Cloud Run service to handle OCR/LLM analysis.
    
    Moved from bills.py to sync_helpers.py for shared access by main.py and endpoints.
    """
    logger.info(f"Triggering heavy processing stage for entity {ctx.entity_id}")
    
    try:
        # Get FastAPI endpoint URL from settings
        fastapi_base_url = ctx.settings.FASTAPI_BASE_URL
        if not fastapi_base_url:
            fastapi_base_url = "https://drcr-d660a-rest-api.uc.r.appspot.com"
        if fastapi_base_url.endswith("/"):
            fastapi_base_url = fastapi_base_url[:-1]
        
        # Query for bills that need heavy processing
        bills_query = ctx.db.collection("TRANSACTIONS").where(
            filter=firestore.FieldFilter("entity_id", "==", ctx.entity_id)
        ).where(
            filter=firestore.FieldFilter("processing_status", "==", "metadata_loaded")
        ).where(
            filter=firestore.FieldFilter("has_potential_prepayments", "==", True)
        )
        
        bills_to_process = []
        async for bill_doc in bills_query.stream():
            bill_data = bill_doc.to_dict()
            bills_to_process.append({
                "transaction_id": bill_doc.id,
                "entity_id": bill_data.get("entity_id"),
                "client_id": bill_data.get("client_id"),
                "has_potential_prepayments": bill_data.get("has_potential_prepayments", False)
            })
        
        if not bills_to_process:
            logger.info(f"No bills requiring heavy processing found for entity {ctx.entity_id}")
            return
        
        logger.info(f"Found {len(bills_to_process)} bills requiring heavy processing for entity {ctx.entity_id}")
        
        # Convert to JSON string and back to handle Firestore objects
        try:
            entity_settings_json = json.loads(json.dumps(ctx.entity_settings, default=str))
            bills_to_process_json = json.loads(json.dumps(bills_to_process, default=str))
        except Exception as e:
            logger.error(f"JSON serialization error: {e}")
            # Fallback: just pass the objects and let httpx handle it
            entity_settings_json = ctx.entity_settings
            bills_to_process_json = bills_to_process
        
        # Check if prepayment detector should run for this entity
        run_detector = ctx.entity_settings.get("enable_prepayment_release_detector", True)
        
        payload = {
            "sync_job_id": ctx.sync_job_id,
            "entity_settings": entity_settings_json,
            "bills_to_process": bills_to_process_json,
            "run_prepayment_detector": run_detector  # Conditional on entity settings
        }
        
        # Make HTTP request to FastAPI endpoint
        endpoint_url = f"{fastapi_base_url}/sync/process-heavy/{ctx.entity_id}"
        
        # Use httpx for async HTTP requests
        async with httpx.AsyncClient(timeout=30.0) as client:
            # Simple headers for internal service-to-service calls
            headers = {
                "Content-Type": "application/json",
                "X-Sync-Source": "cloud-function",
                "X-Sync-Job-ID": ctx.sync_job_id
            }
            
            # Optional: Add service account authentication if configured
            if ctx.settings.SERVICE_ACCOUNT_TOKEN:
                headers["Authorization"] = f"Bearer {ctx.settings.SERVICE_ACCOUNT_TOKEN}"
            
            logger.debug(f"Calling FastAPI endpoint: {endpoint_url}")
            logger.debug(f"Payload keys: {list(payload.keys())}")
            logger.debug(f"Bills to process count: {len(payload.get('bills_to_process', []))}")
            
            response = await client.post(
                endpoint_url,
                json=payload,
                headers=headers
            )
            
            logger.debug(f"Response status: {response.status_code}")
            logger.debug(f"Response text: {response.text[:200]}...")
            
            if response.status_code == 200:
                response_data = response.json()
                logger.info(f"Heavy processing triggered successfully for entity {ctx.entity_id}: {response_data}")
                
                # Create audit log entry for successful trigger
                await _create_audit_log_entry(
                    ctx.db, "SYNC", "HEAVY_PROCESSING_TRIGGERED", ctx.client_id, ctx.entity_id, "SUCCESS",
                    {
                        "sync_job_id": ctx.sync_job_id,
                        "fastapi_response": response_data,
                        "bills_count": len(bills_to_process),
                        "endpoint_url": endpoint_url
                    }
                )
            else:
                logger.error(f"Failed to trigger heavy processing for entity {ctx.entity_id}: {response.status_code} - {response.text}")
                
                # Create audit log entry for failed trigger
                await _create_audit_log_entry(
                    ctx.db, "SYNC", "HEAVY_PROCESSING_TRIGGER_FAILED", ctx.client_id, ctx.entity_id, "FAILURE",
                    {
                        "sync_job_id": ctx.sync_job_id,
                        "error_code": response.status_code,
                        "error_message": response.text,
                        "bills_count": len(bills_to_process),
                        "endpoint_url": endpoint_url
                    }
                )
    
    except Exception as e:
        logger.error(f"Error triggering heavy processing for entity {ctx.entity_id}: {e}", exc_info=True)
        
        # Create audit log entry for exception
        try:
            await _create_audit_log_entry(
                ctx.db, "SYNC", "HEAVY_PROCESSING_TRIGGER_ERROR", ctx.client_id, ctx.entity_id, "FAILURE",
                {
                    "sync_job_id": ctx.sync_job_id,
                    "error": str(e),
                    "error_type": type(e).__name__
                }
            )
        except Exception as audit_error:
            logger.error(f"Failed to create audit log for heavy processing trigger error: {audit_error}") 