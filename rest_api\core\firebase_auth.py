import os
import firebase_admin
from firebase_admin import auth, credentials
from firebase_admin.exceptions import FirebaseError
# Import specific Firebase exceptions for more granular error handling
from firebase_admin.auth import ExpiredIdTokenError, InvalidIdTokenError, RevokedIdTokenError, UserDisabledError, CertificateFetchError
from fastapi import Depends, HTTPException, status, Path
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, Optional, List
from google.cloud import firestore
from datetime import datetime
from ..models.error_codes import AppErrorCode # Import AppErrorCode

# Initialize Firebase Admin SDK
cred_path = os.getenv("FIREBASE_CREDENTIALS_PATH")
if not cred_path and os.path.exists("firebase-credentials.json"):
    cred_path = "firebase-credentials.json"

if cred_path:
    cred = credentials.Certificate(cred_path)
    firebase_admin.initialize_app(cred)
else:
    # Use Application Default Credentials if no explicit path provided
    # This works in GCP environments where the service account is implicit
    firebase_admin.initialize_app()

# HTTP Bearer scheme for token authentication, with auto_error=False
security = HTTPBearer(auto_error=False)

# Firestore client
gcp_project_id = os.getenv("GCP_PROJECT_ID")
if not gcp_project_id:
    # This initial ValueError should ideally be caught at app startup or handled differently
    # For now, it will be caught by the generic_exception_handler if it occurs during a request
    raise ValueError("GCP_PROJECT_ID environment variable is not set") 
db = firestore.Client(project=gcp_project_id)

class AuthUser:
    """Class to represent an authenticated user with role and permissions"""
    def __init__(
        self,
        uid: str,
        email: str,
        display_name: Optional[str] = None,
        firm_id: Optional[str] = None,
        client_id: Optional[str] = None,
        role: Optional[str] = None,
        assigned_client_ids: Optional[List[str]] = None,
        permissions: Optional[Dict[str, bool]] = None
    ):
        self.uid = uid
        self.email = email
        self.display_name = display_name
        self.firm_id = firm_id
        self.client_id = client_id
        self.role = role
        self.assigned_client_ids = assigned_client_ids or []
        self.permissions = permissions or {}


async def get_current_user(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> AuthUser:
    """Verify Firebase token and return user info"""
    if credentials is None:
        # This means HTTPBearer failed to validate the scheme or extract credentials (e.g., header missing, malformed)
        exc = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing authentication credentials.",
            headers={"WWW-Authenticate": "Bearer"},
        )
        # exc.code = AppErrorCode.AUTHENTICATION_REQUIRED # Keep commented for now
        raise exc
    
    token = credentials.credentials
    if not token: # Token part is empty, e.g., "Bearer " (credentials object is not None, but token string is empty)
        exc = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated. Token is missing.",
            headers={"WWW-Authenticate": "Bearer"},
        )
        # exc.code = AppErrorCode.AUTHENTICATION_REQUIRED # Keep commented for now
        raise exc
        
    try:
        decoded_token = auth.verify_id_token(token)
        uid = decoded_token.get("uid")
        email = decoded_token.get("email", "")
        display_name = decoded_token.get("name")

        if not uid:
            exc = HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials: UID missing from token."
            )
            # exc.code = AppErrorCode.TOKEN_INVALID # Keep commented for now
            raise exc

        # Get user's role and firm/client associations from Firestore
        # First check FIRM_USERS
        firm_user_ref = db.collection("FIRM_USERS").where(filter=firestore.FieldFilter("user_id", "==", uid)).limit(1)
        firm_user_docs = firm_user_ref.stream()

        for doc in firm_user_docs:
            firm_user_data = doc.to_dict()
            # Update user's last login
            doc.reference.update({"last_login": datetime.now()})

            return AuthUser(
                uid=uid,
                email=email,
                display_name=display_name or firm_user_data.get("display_name"),
                firm_id=firm_user_data.get("firm_id"),
                role=firm_user_data.get("role"),
                assigned_client_ids=firm_user_data.get("assigned_client_ids", [])
            )

        # If not in FIRM_USERS, check CLIENT_USERS
        client_user_ref = db.collection("CLIENT_USERS").where(filter=firestore.FieldFilter("user_id", "==", uid)).limit(1)
        client_user_docs = client_user_ref.stream()

        for doc in client_user_docs:
            client_user_data = doc.to_dict()
            # Update user's last login
            doc.reference.update({"last_login": datetime.now()})

            return AuthUser(
                uid=uid,
                email=email,
                display_name=display_name or client_user_data.get("display_name"),
                client_id=client_user_data.get("client_id"),
                role=client_user_data.get("role")
            )

        # User authenticated but not in our database yet
        return AuthUser(uid=uid, email=email, display_name=display_name)

    except ExpiredIdTokenError as e:
        exc = HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=f"Token has expired: {e}")
        # exc.code = AppErrorCode.TOKEN_EXPIRED # Keep commented for now
        raise exc
    except InvalidIdTokenError as e: # Catches various invalid token issues not covered by other specific ones
        exc = HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=f"Token is invalid: {e}")
        # exc.code = AppErrorCode.TOKEN_INVALID # Keep commented for now
        raise exc
    except RevokedIdTokenError as e:
        exc = HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=f"Token has been revoked: {e}")
        # exc.code = AppErrorCode.TOKEN_INVALID # Keep commented for now
        raise exc
    except UserDisabledError as e:
        exc = HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=f"User account is disabled: {e}")
        # exc.code = AppErrorCode.AUTHENTICATION_FAILED # Keep commented for now
        raise exc
    except CertificateFetchError as e: # Error fetching Firebase public keys for token verification
        exc = HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error fetching auth certificates: {e}")
        # exc.code = AppErrorCode.INTERNAL_SERVER_ERROR # Keep commented for now
        raise exc
    except FirebaseError as e: # Catch-all for other Firebase errors not specified above
        exc = HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=f"Firebase authentication error: {e}")
        # exc.code = AppErrorCode.AUTHENTICATION_FAILED # Keep commented for now
        raise exc
    except Exception as e: # Generic catch-all for unexpected errors during the process
        raise # Re-raise to be caught by the global generic_exception_handler


async def get_firm_admin(current_user: AuthUser = Depends(get_current_user)) -> AuthUser:
    """Check if user has firm_admin role"""
    if not current_user.role:
        exc = HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied: User role not assigned."
        )
        exc.code = AppErrorCode.ROLE_NOT_ASSIGNED
        raise exc
    if current_user.role != "firm_admin":
        exc = HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required."
        )
        exc.code = AppErrorCode.ADMIN_PRIVILEGES_REQUIRED
        raise exc
    return current_user

async def get_firm_user_with_client_access(client_id: str, current_user: AuthUser = Depends(get_current_user)) -> AuthUser:
    """Check if firm user has access to the specified client"""
    if current_user.role == "firm_admin":
        # Firm admins have access to all clients
        return current_user

    if not current_user.role:
        exc = HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied: User role not assigned."
        )
        exc.code = AppErrorCode.ROLE_NOT_ASSIGNED # Or FORBIDDEN_ACTION
        raise exc

    if current_user.assigned_client_ids and client_id in current_user.assigned_client_ids:
        return current_user

    exc = HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail=f"You do not have access to client ID: {client_id}."
    )
    exc.code = AppErrorCode.CLIENT_ACCESS_DENIED
    raise exc