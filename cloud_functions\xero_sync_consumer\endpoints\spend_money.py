"""
SpendMoney Endpoint Handler

Handles SpendMoney transactions (BankTransactions with Type="SPEND") from Xero.
Includes prepayment detection based on GL account coding.
"""

import logging
from typing import List
from datetime import datetime, timezone

from google.cloud import firestore

from context import SyncContext
from utils.sync_helpers import _create_audit_log_entry, _update_firestore_sync_timestamp
from utils.bills_processor import _check_if_current_version_exists

logger = logging.getLogger(__name__)

__all__ = ["sync_spend_money"]


async def sync_spend_money(ctx: SyncContext, requested_endpoints: List[str]) -> int:
    """
    Handle SpendMoney transactions synchronization from Xero.
    
    Args:
        ctx: Sync context containing all dependencies
        requested_endpoints: List of requested endpoints for sync
        
    Returns:
        Number of spend money transactions processed
        
    Raises:
        Exception: If spend money sync fails
    """
    if "SpendMoney" not in requested_endpoints and requested_endpoints:
        return 0
    
    # Check if spend money sync is enabled for this entity
    if not ctx.should_sync_endpoint("SpendMoney", True):
        logger.info(f"SpendMoney sync disabled for entity_id: {ctx.entity_id}")
        return 0
    
    try:
        logger.info(f"Starting SpendMoney sync for entity_id: {ctx.entity_id}")
        
        full_sync = "SpendMoney" in ctx.force_full_sync_endpoints
        last_sync_timestamp_utc_str = None
        transaction_sync_start_date = ctx.message_data.get("transactionSyncStartDate")
        
        # Get last sync timestamp for incremental sync
        sync_ts_field = "_system_lastSyncTimestampUtc_SpendMoney"
        last_sync_timestamp_utc_str = ctx.get_entity_setting(sync_ts_field) if not full_sync else None
        
        # Build where filter for SPEND type bank transactions with amount threshold
        scanning_threshold = ctx.get_scanning_threshold()
        where_filter = f'Type=="SPEND" AND Total>={scanning_threshold}'
        
        # Handle date-based filtering if provided
        use_date_filter = False
        if transaction_sync_start_date and not full_sync:
            where_filter += f' AND Date>=DateTime({transaction_sync_start_date})'
            use_date_filter = True
            logger.info(f"Using date-based filter for SpendMoney: Date>={transaction_sync_start_date}")
        
        logger.info(f"Fetching SpendMoney (BankTransactions) with filter: {where_filter}")
        spend_money_data = await ctx.xero_client.get_records(
            "BankTransactions",
            where_filter=where_filter,
            if_modified_since=last_sync_timestamp_utc_str if not use_date_filter else None
        )
        
        saved_spend_money_count = 0
        skipped_spend_money_count = 0
        prepayments_detected_count = 0
        
        # Use batch write for better performance
        batch = ctx.db.batch()
        batch_count = 0
        batch_size = ctx.get_entity_setting("batch_size", 50)
        
        for spend_money in spend_money_data:
            bank_transaction_id = spend_money.get("BankTransactionID")
            if not bank_transaction_id:
                logger.warning(f"SpendMoney data missing BankTransactionID for entity {ctx.entity_id}. Skipping: {spend_money}")
                continue
            
            # CHECK IF WE ALREADY HAVE THE CURRENT VERSION - SKIP PROCESSING IF SO
            new_updated_at = spend_money.get("UpdatedDateUTC")
            if await _check_if_current_version_exists(ctx.db, bank_transaction_id, new_updated_at, ctx.entity_id, "TRANSACTIONS", "spend_money"):
                skipped_spend_money_count += 1
                continue
            
            # Create transaction document
            transaction_doc = {
                "entity_id": ctx.entity_id,
                "client_id": ctx.client_id,
                "source_system": "XERO",
                "transaction_id": bank_transaction_id,
                "transaction_type": "spend_money",
                "type": spend_money.get("Type"),
                "contact": spend_money.get("Contact", {}),
                "date": spend_money.get("Date"),
                "status": spend_money.get("Status"),
                "line_amount_types": spend_money.get("LineAmountTypes"),
                "line_items": spend_money.get("LineItems", []),
                "sub_total": spend_money.get("SubTotal"),
                "total_tax": spend_money.get("TotalTax"),
                "total": spend_money.get("Total"),
                "currency_code": spend_money.get("CurrencyCode"),
                "bank_account": spend_money.get("BankAccount", {}),
                "reference": spend_money.get("Reference"),
                "is_reconciled": spend_money.get("IsReconciled", False),
                "url": spend_money.get("Url"),
                "has_attachments": spend_money.get("HasAttachments", False),
                "raw_xero_data": spend_money,
                "last_updated_utc": new_updated_at,
                "created_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP,
                "source_updated_at_utc": spend_money.get("UpdatedDateUTC"),
            }
            
            # Apply prepayment detection logic similar to Bills
            # Check if any line items are coded to prepayment asset accounts
            prepayment_asset_codes = set(ctx.get_entity_setting("prepayment_asset_account_codes", []))
            has_prepayment_coding = False
            
            for line_item in spend_money.get("LineItems", []):
                account_code = line_item.get("AccountCode", "")
                if account_code in prepayment_asset_codes:
                    has_prepayment_coding = True
                    break
            
            if has_prepayment_coding:
                transaction_doc["contains_prepayments"] = True
                prepayments_detected_count += 1
                # Note: For now, we'll detect but not auto-process attachments/LLM analysis
            
            # Store in TRANSACTIONS collection
            transaction_ref = ctx.db.collection("TRANSACTIONS").document(bank_transaction_id)
            batch.set(transaction_ref, transaction_doc, merge=True)
            saved_spend_money_count += 1
            batch_count += 1
            
            # Commit batch when it reaches size limit
            if batch_count >= batch_size:
                await batch.commit()
                batch = ctx.db.batch()  # Reset batch
                batch_count = 0
        
        # Commit remaining documents
        if batch_count > 0:
            await batch.commit()
        
        logger.info(f"SpendMoney sync completed for entity_id: {ctx.entity_id} - Processed: {saved_spend_money_count}, Skipped: {skipped_spend_money_count}, Prepayments detected: {prepayments_detected_count}")
        await _update_firestore_sync_timestamp(
            ctx.db, ctx.entity_id, "SpendMoney", datetime.now(timezone.utc).isoformat(), "SpendMoney sync successful"
        )
        await _create_audit_log_entry(
            ctx.db, "SYNC", "SPEND_MONEY_SYNC_SUCCESS", ctx.client_id, ctx.entity_id, "SUCCESS",
            {"spend_money_processed": saved_spend_money_count, "spend_money_skipped": skipped_spend_money_count, 
             "prepayments_detected": prepayments_detected_count, "syncJobId": ctx.sync_job_id}
        )
        
        return saved_spend_money_count
        
    except Exception as e:
        logger.error(f"SpendMoney sync failed for entity_id: {ctx.entity_id}: {e}", exc_info=True)
        await _create_audit_log_entry(
            ctx.db, "SYNC", "SPEND_MONEY_SYNC_FAILURE", ctx.client_id, ctx.entity_id, "FAILURE",
            {"error": str(e), "syncJobId": ctx.sync_job_id}
        )
        raise