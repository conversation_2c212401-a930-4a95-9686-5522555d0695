"""
Contact Service - Business logic for contact/counterparty operations
"""
from typing import List, Optional, Dict, Any, Tuple
from google.cloud import firestore
from google.cloud.firestore import SERVER_TIMESTAMP
import uuid
from datetime import datetime, timezone, timedelta
import logging

from ..models.contact import Contact, ContactSummary, ContactCreate, ContactUpdate, ContactType
from ..core.firebase_auth import AuthUser

logger = logging.getLogger(__name__)


class ContactService:
    """Service for handling contact/counterparty business logic"""
    
    def __init__(self, db):
        self.db = db
    
    async def list_contacts_paginated(
        self,
        client_id: Optional[str] = None,
        entity_id: Optional[str] = None,
        name_filter: Optional[str] = None,
        contact_type: Optional[ContactType] = None,
        email_filter: Optional[str] = None,
        source_system: Optional[str] = None,
        is_active: Optional[bool] = None,
        has_amortization_settings: Optional[bool] = None,
        page: int = 1,
        limit: int = 20,
        sort_by: str = "name",
        sort_order: str = "asc",
        user_client_ids: Optional[List[str]] = None
    ) -> Tuple[List[ContactSummary], int, int]:
        """
        List contacts with pagination and filtering.
        Returns: (contacts, total_count, total_pages)
        """
        offset = (page - 1) * limit
        
        # Build base query
        if client_id:
            base_query = self.db.collection("COUNTERPARTIES").where(
                filter=firestore.FieldFilter("client_id", "==", client_id)
            )
        elif user_client_ids:
            # For users with limited access, filter by their assigned client IDs
            if len(user_client_ids) == 1:
                base_query = self.db.collection("COUNTERPARTIES").where(
                    filter=firestore.FieldFilter("client_id", "==", user_client_ids[0])
                )
            else:
                base_query = self.db.collection("COUNTERPARTIES").where(
                    filter=firestore.FieldFilter("client_id", "in", user_client_ids)
                )
        else:
            base_query = self.db.collection("COUNTERPARTIES")

        # Apply filters
        current_query = base_query
        
        if entity_id:
            current_query = current_query.where(
                filter=firestore.FieldFilter("entity_id", "==", entity_id)
            )
        
        if source_system:
            current_query = current_query.where(
                filter=firestore.FieldFilter("source_system", "==", source_system)
            )
        
        if is_active is not None:
            current_query = current_query.where(
                filter=firestore.FieldFilter("is_active", "==", is_active)
            )

        # Apply sorting
        sort_direction = "ASCENDING" if sort_order.lower() == "asc" else "DESCENDING"
        current_query = current_query.order_by(sort_by, direction=sort_direction)
        
        # Get all matching documents for filtering and counting
        all_docs = current_query.stream()
        
        contacts_data = []
        async for doc in all_docs:
            contact_data = doc.to_dict()
            contact_data["counterparty_id"] = doc.id
            
            # Apply client-side filters (for fields that don't support Firestore queries)
            if self._should_include_contact(
                contact_data, name_filter, contact_type, email_filter, has_amortization_settings
            ):
                contacts_data.append(contact_data)
        
        # Apply pagination
        total_count = len(contacts_data)
        total_pages = (total_count + limit - 1) // limit if total_count > 0 else 0
        paginated_data = contacts_data[offset:offset + limit]
        
        # Transform to ContactSummary models
        contacts = []
        for contact_data in paginated_data:
            try:
                # Get transaction count for this contact
                transaction_count = await self._get_transaction_count(contact_data["counterparty_id"])
                
                contact_summary = ContactSummary(
                    counterparty_id=contact_data["counterparty_id"],
                    client_id=contact_data.get("client_id", ""),
                    entity_id=contact_data.get("entity_id", ""),
                    name=contact_data.get("name", ""),
                    contact_type=contact_data.get("contact_type"),
                    email_address=contact_data.get("email_address"),
                    source_system=contact_data.get("source_system"),
                    is_active=contact_data.get("is_active", True),
                    updated_at=contact_data.get("updated_at"),
                    has_amortization_settings=self._has_amortization_settings(contact_data),
                    transaction_count=transaction_count
                )
                contacts.append(contact_summary)
            except Exception as e:
                logger.warning(f"Could not transform contact {contact_data.get('counterparty_id')}: {e}")
                continue
        
        return contacts, total_count, total_pages
    
    def _should_include_contact(
        self,
        contact_data: Dict[str, Any],
        name_filter: Optional[str],
        contact_type: Optional[ContactType],
        email_filter: Optional[str],
        has_amortization_settings: Optional[bool]
    ) -> bool:
        """Check if contact should be included based on client-side filters"""
        
        if name_filter:
            name = contact_data.get("name", "").lower()
            if name_filter.lower() not in name:
                return False
        
        if contact_type:
            if contact_data.get("contact_type") != contact_type:
                return False
        
        if email_filter:
            email = contact_data.get("email_address", "").lower()
            if email_filter.lower() not in email:
                return False
        
        if has_amortization_settings is not None:
            has_settings = self._has_amortization_settings(contact_data)
            if has_amortization_settings != has_settings:
                return False
        
        return True
    
    def _has_amortization_settings(self, contact_data: Dict[str, Any]) -> bool:
        """Check if contact has amortization settings configured"""
        amortization_settings = contact_data.get("amortization_settings")
        legacy_field = contact_data.get("_system_defaultAmortizationExpenseAccountCode")
        
        if amortization_settings and isinstance(amortization_settings, dict):
            return bool(amortization_settings.get("default_expense_account_code"))
        
        return bool(legacy_field)
    
    async def _get_transaction_count(self, counterparty_id: str) -> int:
        """Get count of transactions for a contact"""
        try:
            transactions_query = self.db.collection("TRANSACTIONS").where(
                filter=firestore.FieldFilter("contact_id", "==", counterparty_id)
            )
            transaction_docs = transactions_query.stream()
            count = 0
            async for _ in transaction_docs:
                count += 1
            return count
        except Exception as e:
            logger.warning(f"Could not get transaction count for contact {counterparty_id}: {e}")
            return 0
    
    async def get_contact_by_id(
        self,
        contact_id: str,
        current_user: AuthUser,
        include_related_data: bool = True
    ) -> Optional[Contact]:
        """Get a single contact by ID with optional related data"""
        try:
            contact_ref = self.db.collection("COUNTERPARTIES").document(contact_id)
            contact_doc = await contact_ref.get()
            
            if not contact_doc.exists:
                return None
            
            contact_data = contact_doc.to_dict()
            contact_data["counterparty_id"] = contact_doc.id
            
            # Check if user has access to this contact's client
            client_id = contact_data.get("client_id")
            if not await self._user_has_client_access(current_user, client_id):
                return None
            
            # Transform to Contact model
            contact = self._transform_to_contact_model(contact_data)
            
            return contact
            
        except Exception as e:
            logger.error(f"Error retrieving contact {contact_id}: {e}")
            return None
    
    def _transform_to_contact_model(self, contact_data: Dict[str, Any]) -> Contact:
        """Transform Firestore data to Contact model"""
        # Handle legacy amortization settings
        amortization_settings = contact_data.get("amortization_settings")
        if not amortization_settings and contact_data.get("_system_defaultAmortizationExpenseAccountCode"):
            amortization_settings = {
                "default_expense_account_code": contact_data["_system_defaultAmortizationExpenseAccountCode"]
            }
        
        return Contact(
            counterparty_id=contact_data["counterparty_id"],
            client_id=contact_data.get("client_id", ""),
            entity_id=contact_data.get("entity_id", ""),
            name=contact_data.get("name", ""),
            contact_type=contact_data.get("contact_type"),
            email_address=contact_data.get("email_address"),
            phones=contact_data.get("phones"),
            addresses=contact_data.get("addresses"),
            tax_number=contact_data.get("tax_number"),
            website=contact_data.get("website"),
            source_system=contact_data.get("source_system"),
            source_system_id=contact_data.get("source_system_id"),
            source_updated_at_utc=contact_data.get("source_updated_at_utc"),
            amortization_settings=amortization_settings,
            is_active=contact_data.get("is_active", True),
            created_at=contact_data.get("created_at"),
            updated_at=contact_data.get("updated_at"),
            raw_source_data=contact_data.get("raw_source_data"),
            _system_defaultAmortizationExpenseAccountCode=contact_data.get("_system_defaultAmortizationExpenseAccountCode")
        )
    
    async def create_contact(
        self,
        contact_data: ContactCreate,
        client_id: str,
        entity_id: str,
        current_user: AuthUser
    ) -> Tuple[str, Contact]:
        """Create a new contact"""
        try:
            # Check if user has access to this client
            if not await self._user_has_client_access(current_user, client_id):
                raise ValueError("User does not have access to this client")
            
            # Generate contact ID
            contact_id = str(uuid.uuid4())
            
            # Prepare contact document
            contact_doc = {
                "counterparty_id": contact_id,
                "client_id": client_id,
                "entity_id": entity_id,
                "name": contact_data.name,
                "contact_type": contact_data.contact_type,
                "email_address": contact_data.email_address,
                "phones": contact_data.phones,
                "addresses": contact_data.addresses,
                "tax_number": contact_data.tax_number,
                "website": contact_data.website,
                "amortization_settings": contact_data.amortization_settings.dict() if contact_data.amortization_settings else None,
                "source_system": "manual",  # Manually created contacts
                "is_active": True,
                "created_at": SERVER_TIMESTAMP,
                "updated_at": SERVER_TIMESTAMP
            }
            
            # Remove None values
            contact_doc = {k: v for k, v in contact_doc.items() if v is not None}
            
            # Save to Firestore
            contact_ref = self.db.collection("COUNTERPARTIES").document(contact_id)
            await contact_ref.set(contact_doc)
            
            # Transform to Contact model for response
            contact_doc["counterparty_id"] = contact_id
            contact = self._transform_to_contact_model(contact_doc)
            
            return contact_id, contact
            
        except Exception as e:
            logger.error(f"Error creating contact: {e}")
            raise
    
    async def update_contact(
        self,
        contact_id: str,
        contact_data: ContactUpdate,
        current_user: AuthUser
    ) -> Contact:
        """Update an existing contact"""
        try:
            contact_ref = self.db.collection("COUNTERPARTIES").document(contact_id)
            contact_doc = await contact_ref.get()
            
            if not contact_doc.exists:
                raise ValueError("Contact not found")
            
            current_data = contact_doc.to_dict()
            client_id = current_data.get("client_id")
            
            # Check if user has access to this contact's client
            if not await self._user_has_client_access(current_user, client_id):
                raise ValueError("User does not have access to this contact")
            
            # Prepare update data
            update_data = {"updated_at": SERVER_TIMESTAMP}
            
            if contact_data.name is not None:
                update_data["name"] = contact_data.name
            if contact_data.contact_type is not None:
                update_data["contact_type"] = contact_data.contact_type
            if contact_data.email_address is not None:
                update_data["email_address"] = contact_data.email_address
            if contact_data.phones is not None:
                update_data["phones"] = contact_data.phones
            if contact_data.addresses is not None:
                update_data["addresses"] = contact_data.addresses
            if contact_data.tax_number is not None:
                update_data["tax_number"] = contact_data.tax_number
            if contact_data.website is not None:
                update_data["website"] = contact_data.website
            if contact_data.amortization_settings is not None:
                update_data["amortization_settings"] = contact_data.amortization_settings.dict()
            if contact_data.is_active is not None:
                update_data["is_active"] = contact_data.is_active
            
            # Update in Firestore
            await contact_ref.update(update_data)
            
            # Get updated document and return
            updated_doc = await contact_ref.get()
            updated_data = updated_doc.to_dict()
            updated_data["counterparty_id"] = contact_id
            
            return self._transform_to_contact_model(updated_data)
            
        except Exception as e:
            logger.error(f"Error updating contact {contact_id}: {e}")
            raise
    
    async def delete_contact(
        self,
        contact_id: str,
        current_user: AuthUser,
        soft_delete: bool = True
    ) -> bool:
        """Delete a contact (soft delete by default)"""
        try:
            contact_ref = self.db.collection("COUNTERPARTIES").document(contact_id)
            contact_doc = await contact_ref.get()
            
            if not contact_doc.exists:
                raise ValueError("Contact not found")
            
            current_data = contact_doc.to_dict()
            client_id = current_data.get("client_id")
            
            # Check if user has access to this contact's client
            if not await self._user_has_client_access(current_user, client_id):
                raise ValueError("User does not have access to this contact")
            
            # Check if contact has associated transactions
            transaction_count = await self._get_transaction_count(contact_id)
            if transaction_count > 0 and not soft_delete:
                raise ValueError(f"Cannot hard delete contact with {transaction_count} associated transactions")
            
            if soft_delete:
                # Soft delete - mark as inactive
                await contact_ref.update({
                    "is_active": False,
                    "updated_at": SERVER_TIMESTAMP
                })
            else:
                # Hard delete
                await contact_ref.delete()
            
            return True
            
        except Exception as e:
            logger.error(f"Error deleting contact {contact_id}: {e}")
            raise
    
    async def get_contact_statistics(
        self,
        client_id: Optional[str] = None,
        entity_id: Optional[str] = None,
        current_user: Optional[AuthUser] = None
    ) -> Dict[str, Any]:
        """Get statistics about contacts"""
        try:
            base_query = self.db.collection("COUNTERPARTIES")
            
            # Apply filters
            if client_id:
                query = base_query.where(filter=firestore.FieldFilter("client_id", "==", client_id))
            elif current_user and current_user.role != "firm_admin":
                # Limit to user's assigned clients
                user_client_ids = current_user.assigned_client_ids or []
                if not user_client_ids:
                    return self._empty_statistics()
                query = base_query.where(filter=firestore.FieldFilter("client_id", "in", user_client_ids))
            else:
                query = base_query
            
            if entity_id:
                query = query.where(filter=firestore.FieldFilter("entity_id", "==", entity_id))
            
            # Get all contacts for statistics
            all_docs = query.stream()
            
            stats = {
                "total_contacts": 0,
                "contacts_by_type": {},
                "contacts_by_source": {},
                "active_contacts": 0,
                "contacts_with_amortization_settings": 0,
                "recent_contacts_count": 0
            }
            
            recent_cutoff = datetime.now(timezone.utc) - timedelta(days=30)
            
            async for doc in all_docs:
                contact_data = doc.to_dict()
                stats["total_contacts"] += 1
                
                # Count by type
                contact_type = contact_data.get("contact_type", "unknown")
                stats["contacts_by_type"][contact_type] = stats["contacts_by_type"].get(contact_type, 0) + 1
                
                # Count by source
                source_system = contact_data.get("source_system", "unknown")
                stats["contacts_by_source"][source_system] = stats["contacts_by_source"].get(source_system, 0) + 1
                
                # Count active
                if contact_data.get("is_active", True):
                    stats["active_contacts"] += 1
                
                # Count with amortization settings
                if self._has_amortization_settings(contact_data):
                    stats["contacts_with_amortization_settings"] += 1
                
                # Count recent
                created_at = contact_data.get("created_at")
                if created_at and isinstance(created_at, datetime) and created_at > recent_cutoff:
                    stats["recent_contacts_count"] += 1
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting contact statistics: {e}")
            return self._empty_statistics()
    
    def _empty_statistics(self) -> Dict[str, Any]:
        """Return empty statistics structure"""
        return {
            "total_contacts": 0,
            "contacts_by_type": {},
            "contacts_by_source": {},
            "active_contacts": 0,
            "contacts_with_amortization_settings": 0,
            "recent_contacts_count": 0
        }
    
    async def _user_has_client_access(self, user: AuthUser, client_id: str) -> bool:
        """Check if user has access to a specific client"""
        if user.role == "firm_admin":
            # Firm admins have access to all clients in their firm
            return True
        
        # Other users only have access to assigned clients
        assigned_client_ids = user.assigned_client_ids or []
        return client_id in assigned_client_ids
    
    async def get_user_client_ids(self, current_user: AuthUser, requested_client_id: Optional[str] = None) -> List[str]:
        """Get list of client IDs the user has access to"""
        if current_user.role == "firm_admin":
            if requested_client_id:
                return [requested_client_id]
            
            # Get all clients for the firm
            clients_query = self.db.collection("CLIENTS").where(
                filter=firestore.FieldFilter("firm_id", "==", current_user.firm_id)
            )
            client_docs = clients_query.stream()
            client_ids = []
            async for doc in client_docs:
                client_ids.append(doc.id)
            return client_ids
        
        # Regular users only get their assigned clients
        user_client_ids = current_user.assigned_client_ids or []
        if requested_client_id and requested_client_id in user_client_ids:
            return [requested_client_id]
        
        return user_client_ids