# DRCR Backend

This is the backend application for the DRCR (Debit Reconciliation & Categorization Resource) system - a comprehensive financial data management platform that automates prepayment processing, Xero integration, and amortization schedule generation.

## Technologies Used

- **Python 3.9+** - Core programming language
- **FastAPI** - Modern, fast web framework for building APIs
- **Google Cloud Platform** - Cloud infrastructure and services
  - Cloud Functions - Serverless compute for background processing
  - Cloud Run - Containerized API hosting
  - Firestore - NoSQL document database
  - Pub/Sub - Messaging and event-driven architecture
- **Xero API** - Accounting platform integration
- **Firebase Authentication** - User authentication and authorization
- **Terraform** - Infrastructure as Code
- **Docker** - Containerization for deployment

## Getting Started

### Prerequisites

- Python 3.9 or higher
- pip (Python package manager)
- Google Cloud SDK (gcloud CLI)
- Docker (for containerized deployment)
- Git

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd drcr_back
```

2. Create and activate a Python virtual environment:
```bash
python -m venv .venv
# On Windows:
.venv\Scripts\activate
# On macOS/Linux:
source .venv/bin/activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up environment variables:
   - Copy `.env.example` to `.env` (if available)
   - Configure required environment variables (see Configuration section)

### Development

To start the FastAPI development server:

```bash
cd rest_api
python run_server.py
```

Or for optimized performance (no auto-reload):
```powershell
.\scripts\deployment\start_fast.ps1
```

The API will be available at `http://localhost:8080` with interactive documentation at `http://localhost:8080/docs`.

## Project Structure

### **Core Application**
- `src/components`: Reusable UI components
  - `src/components/ui`: Shadcn UI components
  - `src/components/auth`: Authentication-related components
  - `src/components/layout`: Layout components
- `src/pages`: Page components
- `src/lib`: Utility functions and libraries
- `src/store`: Zustand state stores
- `src/types`: TypeScript type definitions
- `src/providers`: React context providers
- `src/assets`: Static assets like images and icons

### **Documentation**
- `docs/`: Comprehensive project documentation
  - `FRONTEND_REFACTORING_GUIDE.md`: Code improvement and refactoring guide
  - `CODEBASE_INDEX.md`: Complete codebase structure overview
  - `ISSUES_REPORT.md`: Known issues and solutions
  - `DEVELOPMENT_GUIDELINES.md`: Coding standards and best practices
  - `FRONTEND_BACKEND_INTEGRATION.md`: API integration documentation
  - `PERFORMANCE_OPTIMIZATION_GUIDE.md`: Performance optimization guide

### **Scripts & Utilities**
- `scripts/`: Deployment and utility scripts
  - `deploy.ps1`: Firebase Hosting deployment script
  - `get_firebase_config.py`: Firebase configuration utility
  - `package_backup.json`: Dependency backup for recovery

### **Configuration Files**
- `vite.config.ts`: Vite build configuration
- `tailwind.config.js`: Tailwind CSS configuration
- `tsconfig.json`: TypeScript configuration
- `components.json`: Shadcn UI component configuration
- `firebase.json`: Firebase Hosting configuration

## Authentication

The application uses Firebase Authentication for user management. The authentication flow is handled by the `AuthProvider` component and the `useAuthStore` hook.

## API Integration

API calls are made using the `api` client in `src/lib/api.ts`. The client automatically handles authentication tokens and error handling.

## Documentation

For detailed information about development, architecture, and integration:

- **Development Guidelines**: `docs/DEVELOPMENT_GUIDELINES.md`
- **Codebase Overview**: `docs/CODEBASE_INDEX.md`
- **API Integration**: `docs/FRONTEND_BACKEND_INTEGRATION.md`
- **Known Issues**: `docs/ISSUES_REPORT.md`
- **Refactoring Guide**: `docs/FRONTEND_REFACTORING_GUIDE.md`
- **Performance Optimization**: `docs/PERFORMANCE_OPTIMIZATION_GUIDE.md`

## Performance Optimization

The DRCR frontend has been extensively optimized for performance:

* **Bundle Size**: Reduced from ~2.5MB to ~800KB (68% reduction)
* **Load Times**: First Contentful Paint improved from ~3.2s to ~1.1s (66% improvement)
* **Code Splitting**: React.lazy() implementation for all page components
* **API Optimization**: Request caching, deduplication, and reduced timeouts
* **Build Optimization**: Manual chunk splitting and asset organization
* **Development Experience**: Optimized HMR and file watching

**Key Features**:
- Intelligent API caching with 5-minute TTL
- Request deduplication within 5-second windows
- Lazy loading with Suspense for better UX
- Optimized Vite configuration for faster builds

For detailed performance metrics and optimization strategies, see `docs/PERFORMANCE_OPTIMIZATION_GUIDE.md`.

## Related Projects

- **Backend API**: `/d:/Projects/drcr_back/` - FastAPI backend with Xero integration
- **Shared Documentation**: Both projects share Firebase configuration and deployment processes
