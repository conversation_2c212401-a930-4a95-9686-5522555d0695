#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix Firestore queries that use deprecated positional arguments.
Updates .where("field", "==", value) to .where(filter=firestore.FieldFilter("field", "==", value))
"""
import os
import re
import sys

def fix_firestore_queries(file_path):
    """Fix Firestore where queries in a Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern to match .where("field", "==", value) patterns
        # This is a basic pattern - manual review is still recommended
        where_pattern = r'\.where\(\s*["\']([^"\']+)["\']\s*,\s*["\']([^"\']+)["\']\s*,\s*([^)]+)\)'
        
        def replace_where(match):
            field = match.group(1)
            operator = match.group(2)
            value = match.group(3)
            return f'.where(filter=firestore.FieldFilter("{field}", "{operator}", {value}))'
        
        # Replace the patterns
        content = re.sub(where_pattern, replace_where, content)
        
        # Check if we need to add firestore import
        if content != original_content and 'from google.cloud import firestore' not in content:
            # Find the import section and add firestore import
            import_pattern = r'(from google\.cloud\.firestore import [^\n]+)'
            
            def add_firestore_import(match):
                return f'from google.cloud import firestore\n{match.group(1)}'
            
            if re.search(import_pattern, content):
                content = re.sub(import_pattern, add_firestore_import, content)
            else:
                # Add import at the top after other google.cloud imports
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if line.startswith('from google.cloud'):
                        lines.insert(i, 'from google.cloud import firestore')
                        break
                else:
                    # If no google.cloud imports found, add after typing imports
                    for i, line in enumerate(lines):
                        if line.startswith('from typing'):
                            lines.insert(i + 1, 'from google.cloud import firestore')
                            break
                content = '\n'.join(lines)
        
        # Write back only if changes were made
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed {file_path}")
            return True
        else:
            print(f"ℹ️  No changes needed in {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return False

def main():
    """Fix Firestore queries in all Python files."""
    if len(sys.argv) > 1:
        # Fix specific files
        files_to_fix = sys.argv[1:]
    else:
        # Find all Python files with .where( patterns
        files_to_fix = []
        for root, dirs, files in os.walk('.'):
            # Skip certain directories
            if any(skip in root for skip in ['.git', '__pycache__', '.venv', 'node_modules']):
                continue
                
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if '.where(' in content and 'firestore.FieldFilter' not in content:
                                files_to_fix.append(file_path)
                    except Exception:
                        continue
    
    if not files_to_fix:
        print("No files found that need fixing.")
        return
    
    print(f"Found {len(files_to_fix)} files to check/fix:")
    for file_path in files_to_fix:
        print(f"  - {file_path}")
    
    print("\nProcessing files...")
    fixed_count = 0
    for file_path in files_to_fix:
        if fix_firestore_queries(file_path):
            fixed_count += 1
    
    print(f"\n✅ Fixed {fixed_count} out of {len(files_to_fix)} files.")
    print("\nPlease review the changes and test your application!")
    print("You may need to manually adjust complex query patterns.")

if __name__ == "__main__":
    main()