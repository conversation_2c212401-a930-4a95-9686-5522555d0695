import { api } from '@/lib/api';
import { auth } from '@/lib/firebase';
import type { ClientSummary } from '@/lib/api';
import type {
  ClientCreate,
  ClientUpdate,
  ClientResponse,
  ClientListResponse,
  ClientFilters,
  ClientWizardStep1,
  ClientWizardStep2,
  ClientWizardStep3,
  CreateClientResponse,
  UpdateClientResponse,
  DeleteClientResponse,
  ClientEnums
} from '@/types/client.types';

// Get the API base URL from environment or default to localhost:8081
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://localhost:8081';

// Helper function to get auth token
const getAuthToken = async (): Promise<string | null> => {
  const user = auth.currentUser;
  if (user) {
    return await user.getIdToken();
  }
  return null;
};

export interface CreateClientData {
  name: string;
  contact_name?: string;
  contact_email?: string;
  contact_phone?: string;
  [key: string]: any;
}

export interface UpdateClientData {
  name?: string;
  contact_name?: string;
  contact_email?: string;
  contact_phone?: string;
  [key: string]: any;
}

export class ClientsService {
  /**
   * Get all clients for the current user's firm (legacy method)
   */
  static async getClients(): Promise<ClientSummary[]> {
    try {
      const response = await api.getClients();
      return response.clients;
    } catch (error) {
      console.error('Error fetching clients:', error);
      throw new Error('Failed to fetch clients');
    }
  }

  /**
   * Get clients with enhanced filtering and pagination
   */
  static async getClientsEnhanced(filters?: ClientFilters): Promise<ClientListResponse> {
    try {
      const response = await api.getClientsSummary(filters);
      return response as ClientListResponse;
    } catch (error) {
      console.error('Error fetching clients:', error);
      throw new Error('Failed to fetch clients');
    }
  }

  /**
   * Create a new client (legacy method)
   */
  static async createClient(clientData: CreateClientData): Promise<{
    message: string;
    client_id: string;
    name: string;
  }> {
    try {
      return await api.createClient(clientData);
    } catch (error) {
      console.error('Error creating client:', error);
      throw new Error('Failed to create client');
    }
  }

  /**
   * Create a new client with enhanced data structure
   */
  static async createClientEnhanced(clientData: ClientCreate): Promise<CreateClientResponse> {
    try {
      const token = await getAuthToken();
      const response = await fetch(`${API_BASE_URL}/clients`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(clientData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating client:', error);
      throw error;
    }
  }

  /**
   * Create a client using the multi-step wizard
   */
  static async createClientFromWizard(
    step1: ClientWizardStep1,
    step2: ClientWizardStep2,
    step3: ClientWizardStep3
  ): Promise<CreateClientResponse> {
    try {
      const token = await getAuthToken();
      const response = await fetch(`${API_BASE_URL}/clients/wizard`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          step1_data: step1,
          step2_data: step2,
          step3_data: step3,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating client from wizard:', error);
      throw error;
    }
  }

  /**
   * Get a specific client by ID (legacy method)
   */
  static async getClient(clientId: string): Promise<ClientSummary> {
    try {
      return await api.getClient(clientId);
    } catch (error) {
      console.error('Error fetching client:', error);
      throw new Error('Failed to fetch client details');
    }
  }

  /**
   * Get detailed client information by ID
   */
  static async getClientDetails(clientId: string): Promise<ClientResponse> {
    try {
      const token = await getAuthToken();
      const response = await fetch(`${API_BASE_URL}/clients/${clientId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching client details:', error);
      throw error;
    }
  }

  /**
   * Update a client (legacy method)
   */
  static async updateClient(clientId: string, clientData: UpdateClientData): Promise<void> {
    try {
      await api.updateClient(clientId, clientData);
    } catch (error) {
      console.error('Error updating client:', error);
      throw new Error('Failed to update client');
    }
  }

  /**
   * Update a client with enhanced data structure
   */
  static async updateClientEnhanced(clientId: string, clientData: ClientUpdate): Promise<UpdateClientResponse> {
    try {
      const token = await getAuthToken();
      const response = await fetch(`${API_BASE_URL}/clients/${clientId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(clientData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating client:', error);
      throw error;
    }
  }

  /**
   * Delete a client (soft delete)
   */
  static async deleteClient(clientId: string): Promise<DeleteClientResponse> {
    try {
      const token = await getAuthToken();
      const response = await fetch(`${API_BASE_URL}/clients/${clientId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error deleting client:', error);
      throw error;
    }
  }

  /**
   * Get client enum options for forms
   */
  static async getClientEnums(): Promise<ClientEnums> {
    try {
      const token = await getAuthToken();
      const [typesResponse, sizesResponse, statusesResponse] = await Promise.all([
        fetch(`${API_BASE_URL}/clients/enums/client-types`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }),
        fetch(`${API_BASE_URL}/clients/enums/client-sizes`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }),
        fetch(`${API_BASE_URL}/clients/enums/client-statuses`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }),
      ]);

      const [types, sizes, statuses] = await Promise.all([
        typesResponse.json(),
        sizesResponse.json(),
        statusesResponse.json(),
      ]);

      return {
        client_types: types.client_types,
        client_sizes: sizes.client_sizes,
        client_statuses: statuses.client_statuses,
      };
    } catch (error) {
      console.error('Error fetching client enums:', error);
      throw new Error('Failed to fetch client options');
    }
  }
}
