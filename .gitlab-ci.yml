# GitLab CI/CD Pipeline for DRCR Backend Application

stages:
  - install
  - test
  - build
  - deploy
  - cleanup

variables:
  PYTHON_VERSION: "3.11"
  CACHE_KEY: "drcr-v1"

# =============================================================================
# BACKEND JOBS
# =============================================================================

# Install backend dependencies
install_backend:
  stage: install
  image: python:${PYTHON_VERSION}-slim
  before_script:
    - chmod +x ci/install_backend.sh || true
  script:
    - pwd
    - ./ci/install_backend.sh
  cache:
    key: ${CACHE_KEY}-backend
    paths:
      - backend/.venv/
  artifacts:
    paths:
      - backend/.venv/
    expire_in: 1 hour
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

# Test backend
test_backend:
  stage: test
  image: python:${PYTHON_VERSION}-slim
  dependencies:
    - install_backend
  before_script:
    - chmod +x ci/test_backend.sh || true
  script:
    - pwd
    - ./ci/test_backend.sh
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: backend/coverage.xml
    paths:
      - backend/htmlcov/
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

# Build backend Docker image
build_backend:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "/certs/client"
  dependencies:
    - install_backend
  before_script:
    - chmod +x ./ci/build_backend.sh || true
  script:
    - pwd
    - ./ci/build_backend.sh
  rules:
    - if: $CI_COMMIT_BRANCH == "main"

# Deploy Backend to Google Cloud Run (Production)
deploy_backend_production:
  stage: deploy
  image: google/cloud-sdk:alpine
  dependencies:
    - build_backend
  before_script:
    - chmod +x ci/prepare_deploy_backend_production.sh || true
    - chmod +x ci/deploy_backend_production.sh || true
  script:
    - pwd
    - ./ci/prepare_deploy_backend_production.sh
    - ./ci/deploy_backend_production.sh
  environment:
    name: production/backend
    url: $BACKEND_URL
  variables:
    GIT_STRATEGY: clone  # Ensure full repository is cloned
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual

# Cleanup job
cleanup:
  stage: cleanup
  image: alpine:latest
  script:
    - echo "Cleaning up temporary files..."
    - rm -rf backend/.venv || true
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
  when: always
