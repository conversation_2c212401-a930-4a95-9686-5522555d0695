import React, { useState, useEffect, useCallback, useRef } from 'react';
import { cn } from '@/lib/utils';

interface ResizablePanelProps {
  leftPanel: React.ReactNode;
  rightPanel: React.ReactNode;
  defaultLeftWidth?: number;
  minLeftWidth?: number;
  maxLeftWidth?: number;
  onResize?: (leftWidth: number) => void;
  className?: string;
}

export function ResizablePanel({
  leftPanel,
  rightPanel,
  defaultLeftWidth = 320,
  minLeftWidth = 280,
  maxLeftWidth = 600,
  onResize,
  className,
}: ResizablePanelProps) {
  const [leftWidth, setLeftWidth] = useState(() => {
    // Try to get saved width from localStorage
    const saved = localStorage.getItem('resizable-panel-width');
    if (saved) {
      const parsed = parseInt(saved, 10);
      if (!isNaN(parsed) && parsed >= minLeftWidth && parsed <= maxLeftWidth) {
        return parsed;
      }
    }
    return defaultLeftWidth;
  });
  
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const isDragging = useRef(false);

  // Save width to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('resizable-panel-width', leftWidth.toString());
    onResize?.(leftWidth);
  }, [leftWidth, onResize]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
    isDragging.current = true;
    
    const startX = e.clientX;
    const startWidth = leftWidth;

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging.current) return;
      
      const deltaX = e.clientX - startX;
      const newWidth = Math.min(
        maxLeftWidth,
        Math.max(minLeftWidth, startWidth + deltaX)
      );
      
      setLeftWidth(newWidth);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      isDragging.current = false;
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      
      // Remove global cursor override
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    // Add global cursor override during resize
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [leftWidth, minLeftWidth, maxLeftWidth]);

  const handleDoubleClick = useCallback(() => {
    setLeftWidth(defaultLeftWidth);
  }, [defaultLeftWidth]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'ArrowLeft') {
      e.preventDefault();
      setLeftWidth(prev => Math.max(minLeftWidth, prev - 10));
    } else if (e.key === 'ArrowRight') {
      e.preventDefault();
      setLeftWidth(prev => Math.min(maxLeftWidth, prev + 10));
    } else if (e.key === 'Home') {
      e.preventDefault();
      setLeftWidth(minLeftWidth);
    } else if (e.key === 'End') {
      e.preventDefault();
      setLeftWidth(maxLeftWidth);
    } else if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      setLeftWidth(defaultLeftWidth);
    }
  }, [minLeftWidth, maxLeftWidth, defaultLeftWidth]);

  return (
    <div 
      ref={containerRef}
      className={cn("flex h-full overflow-hidden", className)}
    >
      {/* Left Panel */}
      <div 
        className="flex-shrink-0 flex flex-col overflow-hidden"
        style={{ width: `${leftWidth}px` }}
      >
        {leftPanel}
      </div>

      {/* Resize Handle */}
      <div
        className={cn(
          "flex-shrink-0 w-1 bg-gray-200 hover:bg-gray-300 cursor-col-resize transition-colors duration-150 relative group border-r border-gray-200",
          isResizing && "bg-blue-400"
        )}
        onMouseDown={handleMouseDown}
        onDoubleClick={handleDoubleClick}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="separator"
        aria-orientation="vertical"
        aria-valuenow={leftWidth}
        aria-valuemin={minLeftWidth}
        aria-valuemax={maxLeftWidth}
        aria-label="Resize sidebar"
        title="Drag to resize sidebar, double-click to reset"
      >
        {/* Visual indicator */}
        <div className="absolute inset-y-0 left-0 w-1 bg-transparent group-hover:bg-gray-400 transition-colors duration-150" />
        
        {/* Expanded hover area for easier interaction */}
        <div className="absolute inset-y-0 -left-1 -right-1 w-3" />
      </div>

      {/* Right Panel */}
      <div className="flex-1 min-w-0 flex flex-col overflow-hidden">
        {rightPanel}
      </div>
    </div>
  );
}