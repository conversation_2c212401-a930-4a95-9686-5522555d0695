name: Code Quality

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  lint:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 ruff mypy
    
    - name: Check main.py line count limit
      run: |
        LOC=$(wc -l < cloud_functions/xero_sync_consumer/main.py)
        echo "main.py has $LOC lines"
        if [ $LOC -gt 1300 ]; then
          echo "ERROR: main.py has $LOC lines (limit: 1300 - Phase 2 modularization complete)"
          echo "All endpoints extracted (Bills, Invoices, Contacts, Accounts, ManualJournals, SpendMoney, Payments, BankTransactions)"
          echo "Next: Extract helper functions to reach <500 LOC target"
          exit 1
        fi
        echo "✅ Line count check passed (current: $LOC, limit: 1300)"
    
    - name: Run ruff (unused imports)
      run: |
        ruff --select F401 cloud_functions/xero_sync_consumer/
        
    - name: Run flake8 basic checks
      run: |
        flake8 cloud_functions/xero_sync_consumer/ --count --select=E9,F63,F7,F82 --show-source --statistics
        
    - name: Run mypy type checking (experimental)
      continue-on-error: true
      run: |
        mypy cloud_functions/xero_sync_consumer/endpoints/ --ignore-missing-imports --show-error-codes