# Cloud Scheduler jobs for automated entity syncing

# Create a Pub/Sub topic for scheduled sync triggers
resource "google_pubsub_topic" "scheduled_sync_topic" {
  name = "scheduled-sync-topic"
  
  depends_on = [google_project_service.services]
}

# Create a subscription for scheduled sync processing
resource "google_pubsub_subscription" "scheduled_sync_subscription" {
  name  = "scheduled-sync-subscription"
  topic = google_pubsub_topic.scheduled_sync_topic.name
  
  # Configure message retention
  message_retention_duration = "604800s"  # 7 days
  
  # Configure retry policy
  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "600s"  # 10 minutes
  }
  
  # Configure dead letter policy
  dead_letter_policy {
    dead_letter_topic     = google_pubsub_topic.scheduled_sync_dead_letter_topic.id
    max_delivery_attempts = 3
  }
  
  depends_on = [google_pubsub_topic.scheduled_sync_dead_letter_topic]
}

# Create a dead letter topic for failed scheduled sync messages
resource "google_pubsub_topic" "scheduled_sync_dead_letter_topic" {
  name = "scheduled-sync-dead-letter"
  
  depends_on = [google_project_service.services]
}

# Create a subscription for the scheduled sync dead letter topic
resource "google_pubsub_subscription" "scheduled_sync_dead_letter_subscription" {
  name  = "scheduled-sync-dead-letter-subscription"
  topic = google_pubsub_topic.scheduled_sync_dead_letter_topic.name
  
  # Configure message retention
  message_retention_duration = "604800s"  # 7 days
  
  depends_on = [google_pubsub_topic.scheduled_sync_dead_letter_topic]
}

# Hourly sync scheduler job
resource "google_cloud_scheduler_job" "hourly_sync" {
  name             = "hourly-entity-sync"
  description      = "Trigger hourly sync for entities configured with hourly sync frequency"
  schedule         = "0 * * * *"  # Every hour at minute 0
  time_zone        = "UTC"
  attempt_deadline = "320s"

  pubsub_target {
    topic_name = google_pubsub_topic.scheduled_sync_topic.id
    data       = base64encode(jsonencode({
      sync_frequency = "hourly"
      trigger_type   = "scheduled"
      timestamp      = "{{.timestamp}}"
    }))
  }

  depends_on = [google_pubsub_topic.scheduled_sync_topic]
}

# Daily sync scheduler job
resource "google_cloud_scheduler_job" "daily_sync" {
  name             = "daily-entity-sync"
  description      = "Trigger daily sync for entities configured with daily sync frequency"
  schedule         = "0 2 * * *"  # Every day at 2:00 AM UTC
  time_zone        = "UTC"
  attempt_deadline = "320s"

  pubsub_target {
    topic_name = google_pubsub_topic.scheduled_sync_topic.id
    data       = base64encode(jsonencode({
      sync_frequency = "daily"
      trigger_type   = "scheduled"
      timestamp      = "{{.timestamp}}"
    }))
  }

  depends_on = [google_pubsub_topic.scheduled_sync_topic]
}

# Weekly sync scheduler job
resource "google_cloud_scheduler_job" "weekly_sync" {
  name             = "weekly-entity-sync"
  description      = "Trigger weekly sync for entities configured with weekly sync frequency"
  schedule         = "0 3 * * 1"  # Every Monday at 3:00 AM UTC
  time_zone        = "UTC"
  attempt_deadline = "320s"

  pubsub_target {
    topic_name = google_pubsub_topic.scheduled_sync_topic.id
    data       = base64encode(jsonencode({
      sync_frequency = "weekly"
      trigger_type   = "scheduled"
      timestamp      = "{{.timestamp}}"
    }))
  }

  depends_on = [google_pubsub_topic.scheduled_sync_topic]
}

# Output the topic names for use in cloud functions
output "scheduled_sync_topic_name" {
  value = google_pubsub_topic.scheduled_sync_topic.name
}

output "scheduled_sync_subscription_name" {
  value = google_pubsub_subscription.scheduled_sync_subscription.name
} 