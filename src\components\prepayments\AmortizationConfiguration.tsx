import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Label } from '../ui/label';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Button } from '../ui/button';
import { Settings2, Calendar, Building } from 'lucide-react';
import { api } from '../../lib/api';

interface AmortizationConfig {
  method: string;
  startDate: string;
  endDate: string;
  prepaymentAccount: string;
  expenseAccount: string;
  numberOfPeriods: number;
}

interface Account {
  account_id: string;
  code: string;
  name: string;
  type: string;
  class: string;
  status: string;
}

interface AmortizationConfigurationProps {
  config: AmortizationConfig;
  onChange: (updates: Partial<AmortizationConfig>) => void;
  disabled: boolean;
  entityId?: string;
}

export function AmortizationConfiguration({
  config,
  onChange,
  disabled,
  entityId,
}: AmortizationConfigurationProps) {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [isLoadingAccounts, setIsLoadingAccounts] = useState(false);

  // Load chart of accounts
  useEffect(() => {
    const loadAccounts = async () => {
      console.log('AmortizationConfiguration: Loading accounts for entityId:', entityId);
      if (!entityId) {
        console.log('AmortizationConfiguration: No entityId, skipping account load');
        setAccounts([]);
        return;
      }

      try {
        setIsLoadingAccounts(true);
        console.log('AmortizationConfiguration: Fetching accounts from API...');
        const response = await api.get(`/entities/${entityId}/accounts`);
        console.log('AmortizationConfiguration: API response:', response);
        const accountsData = (response as any).accounts || [];
        console.log('AmortizationConfiguration: Setting accounts:', accountsData);
        setAccounts(accountsData);
      } catch (error) {
        console.error('AmortizationConfiguration: Failed to load accounts:', error);
        setAccounts([]);
      } finally {
        setIsLoadingAccounts(false);
      }
    };

    loadAccounts();
  }, [entityId]);

  // Filter accounts by type
  console.log('AmortizationConfiguration: All accounts:', accounts);
  console.log('AmortizationConfiguration: Account types:', accounts.map(a => a.type));
  console.log('AmortizationConfiguration: Account statuses:', accounts.map(a => a.status));
  
  const prepaymentAccounts = accounts.filter(
    account => account.status === 'ACTIVE'
  );
  
  const expenseAccounts = accounts.filter(
    account => account.status === 'ACTIVE'
  );
  
  console.log('AmortizationConfiguration: Filtered prepayment accounts:', prepaymentAccounts);
  console.log('AmortizationConfiguration: Filtered expense accounts:', expenseAccounts);

  const formatAccountOption = (account: Account) => {
    return `${account.code} - ${account.name}`;
  };

  const handleMethodChange = (value: string) => {
    onChange({ method: value });
  };

  const handleStartDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange({ startDate: event.target.value });
  };

  const handlePeriodsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const periods = parseInt(event.target.value) || 1;
    onChange({ numberOfPeriods: Math.max(1, Math.min(60, periods)) });
  };

  const handlePrepaymentAccountChange = (value: string) => {
    onChange({ prepaymentAccount: value });
  };

  const handleExpenseAccountChange = (value: string) => {
    onChange({ expenseAccount: value });
  };


  if (disabled) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings2 className="h-5 w-5" />
            Schedule Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <Settings2 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-lg font-medium mb-2">No Bills Selected</p>
            <p className="text-sm">Select bills from the left sidebar to view schedule configuration</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings2 className="h-5 w-5" />
          Amortization Configuration
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Amortization Method */}
          <div className="space-y-2">
            <Label htmlFor="method">Amortization Method</Label>
            <Select 
              value={config.method} 
              onValueChange={handleMethodChange}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="straight_line">
                  Straight Line (Equal monthly amounts)
                </SelectItem>
                <SelectItem value="declining_balance" disabled>
                  Declining Balance (Coming soon)
                </SelectItem>
                <SelectItem value="sum_of_years" disabled>
                  Sum of Years (Coming soon)
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Number of Periods */}
          <div className="space-y-2">
            <Label htmlFor="periods">Number of Periods (Months)</Label>
            <Input
              id="periods"
              type="number"
              min="1"
              max="60"
              value={config.numberOfPeriods}
              onChange={handlePeriodsChange}
              disabled={disabled}
              className="w-full"
            />
          </div>

          {/* Start Date */}
          <div className="space-y-2">
            <Label htmlFor="startDate">Start Date</Label>
            <div className="relative">
              <Calendar className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                id="startDate"
                type="date"
                value={config.startDate}
                onChange={handleStartDateChange}
                disabled={disabled}
                className="pl-10"
              />
            </div>
          </div>

          {/* End Date - Display only */}
          <div className="space-y-2">
            <Label>End Date</Label>
            <Input
              value={config.endDate}
              disabled
              className="bg-muted"
            />
            <div className="text-xs text-gray-500">Calculated automatically</div>
          </div>
        </div>

        {/* Account Selectors */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Prepayment Account */}
          <div className="space-y-2">
            <Label htmlFor="prepaymentAccount">
              <div className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                Prepayment Account
              </div>
            </Label>
            <Select 
              value={config.prepaymentAccount} 
              onValueChange={handlePrepaymentAccountChange}
              disabled={disabled || isLoadingAccounts}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select prepayment account" />
              </SelectTrigger>
              <SelectContent>
                {/* Show current value if it's not in the available accounts */}
                {config.prepaymentAccount && 
                 !prepaymentAccounts.some(acc => acc.code === config.prepaymentAccount) && (
                  <SelectItem value={config.prepaymentAccount}>
                    {config.prepaymentAccount} - (Current from schedules)
                  </SelectItem>
                )}
                {prepaymentAccounts.filter(account => account.code && account.code.trim() !== '').map(account => (
                  <SelectItem key={account.account_id} value={account.code}>
                    {formatAccountOption(account)}
                  </SelectItem>
                ))}
                {prepaymentAccounts.length === 0 && !isLoadingAccounts && (
                  <SelectItem value="no-accounts" disabled>
                    No prepayment accounts available
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Expense Account */}
          <div className="space-y-2">
            <Label htmlFor="expenseAccount">
              <div className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                Expense Account
              </div>
            </Label>
            <Select 
              value={config.expenseAccount} 
              onValueChange={handleExpenseAccountChange}
              disabled={disabled || isLoadingAccounts}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select expense account" />
              </SelectTrigger>
              <SelectContent>
                {/* Show current value if it's not in the available accounts */}
                {config.expenseAccount && 
                 !expenseAccounts.some(acc => acc.code === config.expenseAccount) && (
                  <SelectItem value={config.expenseAccount}>
                    {config.expenseAccount} - (Current from schedules)
                  </SelectItem>
                )}
                {expenseAccounts.filter(account => account.code && account.code.trim() !== '').map(account => (
                  <SelectItem key={account.account_id} value={account.code}>
                    {formatAccountOption(account)}
                  </SelectItem>
                ))}
                {expenseAccounts.length === 0 && !isLoadingAccounts && (
                  <SelectItem value="no-expense-accounts" disabled>
                    No expense accounts available
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Configuration Status */}
        {disabled && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <p className="text-sm text-yellow-800">
              Select bills from the left sidebar to configure amortization settings.
            </p>
          </div>
        )}

        {!disabled && (!config.prepaymentAccount || !config.expenseAccount) && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm text-blue-800">
              Complete the account selection to finalize the amortization configuration.
            </p>
          </div>
        )}

      </CardContent>
    </Card>
  );
}