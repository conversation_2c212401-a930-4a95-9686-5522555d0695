# **Large Sync Optimization Implementation Plan**

## **Executive Summary**

Current sync implementation works well for small-medium datasets but has critical bottlenecks for large syncs (1000+ transactions). This document outlines the specific optimizations needed and implementation approach.

## **Current State Analysis**

### **✅ Already Implemented (Well Done)**
- **Xero API Pagination**: Handles unlimited records via page-by-page fetching
- **Rate Limit Handling**: Automatic retry with `Retry-After` header respect
- **Rate Limit Monitoring**: Proactive warnings when approaching limits
- **Batch Firestore Writes**: 100-500 records per batch to optimize writes

### **🔴 Critical Bottlenecks for Large Syncs**

| Issue | Current Impact | Cost at 5000 Records |
|-------|---------------|---------------------|
| Individual version checks | 1 query per record | $0.30 + 60 seconds |
| Memory loading all records | All records in memory | 250MB+ memory usage |
| No progress tracking | Full restart on timeout | Lost work + duplicate processing |

### **✅ LLM Costs - Already Optimized**

**Real LLM Pricing**:
- **Mistral OCR**: $0.001 per page
- **OpenAI GPT-4.1-mini**: ~$0.15/1M input tokens, ~$0.60/1M output tokens

**Cost for 1000 bills with attachments**:
```
Mistral OCR: 1000 pages × $0.001 = $1.00
OpenAI: 500 calls × (1500 tokens × $0.15/1M + 200 tokens × $0.60/1M) = $0.18
Total LLM Cost: ~$1.20 for 1000 bills
```

**Conclusion**: LLM costs are negligible and **not a bottleneck**.

## **Implementation Plan**

### **Phase 1: Batch Version Checking (High Priority)**
**Impact**: Reduce 5000 queries → 1 query + in-memory comparison

#### **Implementation**
```python
async def _batch_check_existing_versions(
    db: firestore.AsyncClient,
    record_ids: List[str],
    entity_id: str,
    collection_name: str
) -> Dict[str, str]:
    """
    Batch fetch existing versions for all records.
    
    Returns:
        Dict mapping record_id -> source_updated_at_utc
    """
    existing_versions = {}
    
    # Firestore 'in' queries limited to 10 items, so batch them
    for i in range(0, len(record_ids), 10):
        batch_ids = record_ids[i:i+10]
        
        query = db.collection(collection_name).where("entity_id", "==", entity_id)
        if collection_name == "TRANSACTIONS":
            query = query.where("transaction_id", "in", batch_ids)
        elif collection_name == "COUNTERPARTIES":
            query = query.where("counterparty_id", "in", batch_ids)
        elif collection_name == "CHART_OF_ACCOUNTS":
            # For accounts, check document IDs directly
            docs = await asyncio.gather(*[
                db.collection(collection_name).document(doc_id).get()
                for doc_id in batch_ids
            ])
            for doc in docs:
                if doc.exists:
                    data = doc.to_dict()
                    existing_versions[doc.id] = data.get("source_updated_at_utc")
            continue
            
        docs = await query.get()
        for doc in docs:
            data = doc.to_dict()
            record_id = data.get("transaction_id") or data.get("counterparty_id")
            if record_id:
                existing_versions[record_id] = data.get("source_updated_at_utc")
    
    return existing_versions
```

#### **Usage Update**
```python
# Before processing loop
record_ids = [bill.get("InvoiceID") for bill in bills_data if bill.get("InvoiceID")]
existing_versions = await _batch_check_existing_versions(db, record_ids, entity_id, "TRANSACTIONS")

# In processing loop
for bill in bills_data:
    bill_id = bill.get("InvoiceID")
    if not bill_id:
        continue
        
    # Fast in-memory version check
    existing_version = existing_versions.get(bill_id)
    new_version = _normalize_xero_date(bill.get("UpdatedDateUTC"))
    
    if existing_version and _normalize_xero_date(existing_version) == new_version:
        skipped_bills_count += 1
        continue
```

**Benefits**: 
- **Cost**: $0.30 → $0.01 (97% reduction)
- **Time**: 60 seconds → 2 seconds (97% reduction)

### **Phase 2: Streaming Processing (High Priority)**
**Impact**: Prevent memory overflow and enable progress tracking

#### **Implementation**
```python
async def process_bills_sync_streaming(
    db: firestore.AsyncClient,
    xero_client,
    entity_id: str,
    client_id: str,
    entity_settings: Dict[str, Any],
    message_data: Dict[str, Any],
    force_full_sync_endpoints: List[str],
    sync_job_id: str,
    processed_prepayments_count: int,
    chunk_size: int = 100
) -> tuple[int, int]:
    """
    Stream-process bills in chunks to manage memory and enable progress tracking.
    """
    total_processed = 0
    total_skipped = 0
    current_page = 1
    
    while True:
        # Fetch one page at a time
        page_data = await xero_client.get_records(
            "Invoices", 
            where_filter=where_filter,
            if_modified_since=last_sync_timestamp_utc_str,
            page=current_page
        )
        
        if not page_data:
            break
            
        # Batch version check for this page
        page_ids = [bill.get("InvoiceID") for bill in page_data if bill.get("InvoiceID")]
        existing_versions = await _batch_check_existing_versions(db, page_ids, entity_id, "TRANSACTIONS")
        
        # Process this page
        page_processed, page_skipped = await _process_bills_page(
            page_data, existing_versions, db, entity_settings, entity_id, client_id
        )
        
        total_processed += page_processed
        total_skipped += page_skipped
        
        # Update progress tracking
        await _update_sync_progress(db, entity_id, sync_job_id, current_page, total_processed)
        
        current_page += 1
        
        # Memory cleanup
        del page_data, existing_versions
        
    return total_processed, total_skipped
```

**Benefits**:
- **Memory**: 250MB → 25MB (90% reduction)
- **Resumability**: Can restart from last processed page
- **Progress Visibility**: Real-time sync progress tracking

### **Phase 3: Progress Tracking & Resumption (Medium Priority)**
**Impact**: Handle function timeouts gracefully

#### **Implementation**
```python
async def _update_sync_progress(
    db: firestore.AsyncClient,
    entity_id: str,
    sync_job_id: str,
    current_page: int,
    processed_count: int
):
    """Track sync progress for resumption."""
    progress_ref = db.collection("SYNC_PROGRESS").document(f"{entity_id}_{sync_job_id}")
    await progress_ref.set({
        "entity_id": entity_id,
        "sync_job_id": sync_job_id,
        "current_page": current_page,
        "processed_count": processed_count,
        "last_updated": firestore.SERVER_TIMESTAMP,
        "status": "in_progress"
    }, merge=True)

async def _get_sync_progress(
    db: firestore.AsyncClient,
    entity_id: str,
    sync_job_id: str
) -> Dict[str, Any]:
    """Get existing sync progress."""
    progress_ref = db.collection("SYNC_PROGRESS").document(f"{entity_id}_{sync_job_id}")
    doc = await progress_ref.get()
    if doc.exists:
        return doc.to_dict()
    return {"current_page": 1, "processed_count": 0}
```

**Benefits**:
- **Reliability**: No lost work on timeout
- **Efficiency**: Resume from where left off
- **Monitoring**: Real-time progress visibility

## **Implementation Priority & Timeline**

### **Week 1: Phase 1 - Batch Version Checking**
- **Files to modify**: `bills_processor.py`, `main.py`
- **Testing**: Verify query reduction with logging
- **Rollout**: Deploy to staging first

### **Week 2-3: Phase 2 - Streaming Processing**
- **Files to modify**: Major refactor of sync logic
- **Testing**: Large dataset testing
- **Rollout**: Gradual rollout with feature flag

### **Week 4: Phase 3 - Progress Tracking**
- **Files to modify**: Add progress tracking infrastructure
- **Testing**: Timeout simulation testing
- **Rollout**: Production deployment

## **Success Metrics**

| Metric | Current | Target | Measurement |
|--------|---------|--------|-------------|
| Version check cost | $0.30/5k records | $0.01/5k records | Firestore usage logs |
| Memory usage | 250MB | <50MB | Cloud Function metrics |
| Timeout failures | 20% | <5% | Error rate monitoring |
| Sync duration | 8-12 minutes | 4-6 minutes | Audit log timestamps |

## **Risk Mitigation**

### **Deployment Strategy**
1. **Feature flags** for each optimization
2. **A/B testing** with small vs. large syncs
3. **Rollback plan** for each phase
4. **Monitoring alerts** for performance regressions

### **Testing Strategy**
1. **Load testing** with 10k+ record datasets
2. **Cost monitoring** in staging environment
3. **Timeout simulation** testing
4. **Memory profiling** during large syncs

## **Next Steps**

1. **Approve implementation plan**
2. **Set up staging environment** for large dataset testing
3. **Create feature flags** for gradual rollout
4. **Begin Phase 1 implementation** (batch version checking)

## **Key Findings**

### **What's Already Optimized**
- **Xero API handling**: Pagination, rate limiting, retry logic
- **LLM costs**: Already minimal at ~$1.20 per 1000 bills
- **Firestore batch writes**: Efficient batching implemented

### **Real Bottlenecks**
1. **Version check query volume**: 5000 individual reads
2. **Memory usage**: Loading all records at once
3. **Function timeouts**: No resumption capability

This plan addresses the real bottlenecks while preserving the already-solid Xero API handling and acknowledging that LLM costs are not a concern.