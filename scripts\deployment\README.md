# Deployment Scripts

This directory contains scripts for deployment, server management, and development workflow.

## Files

- `start_fast.ps1` - Start backend server with performance optimizations (no auto-reload)
- `deploy.ps1` - Deploy application to production environment
- `delete_test_files.ps1` - Clean up test files and temporary data

## Usage

### Fast Development Server
```powershell
# Start server with optimizations for better performance
.\scripts\deployment\start_fast.ps1
```
This disables auto-reload and sets optimal environment variables for development.

### Production Deployment
```powershell
# Deploy to production
.\scripts\deployment\deploy.ps1
```
Handles production deployment with proper environment configuration.

### Cleanup Test Files
```powershell
# Remove test files and temporary data
.\scripts\deployment\delete_test_files.ps1
```
Cleans up development artifacts before deployment.

## Performance Optimizations

The `start_fast.ps1` script includes:
- Disabled auto-reload for faster development
- Optimized environment variables
- Proper Google Cloud project configuration
- Database connection pooling settings

## Environment Configuration

Scripts automatically set:
- `DISABLE_RELOAD=true` - Disables file watching for performance
- `GOOGLE_CLOUD_PROJECT=drcr-d660a` - Sets GCP project ID
- `GCP_PROJECT_ID=drcr-d660a` - Alternative project ID variable

## Prerequisites

- PowerShell execution policy allows script execution
- Valid environment variables in `.env` file
- Google Cloud credentials configured
- Firebase project access 