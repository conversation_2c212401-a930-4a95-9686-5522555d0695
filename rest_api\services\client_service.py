from typing import Dict, Any, List, Optional, <PERSON><PERSON>
import uuid
from datetime import datetime
from google.cloud.firestore import SERVER_TIMESTAMP
from google.cloud import firestore

from ..schemas.client_schemas import (
    ClientCreate, ClientUpdate, ClientResponse, ClientSummaryEnhanced,
    ClientWizardStep1, ClientWizardStep2, ClientWizardStep3,
    ClientStatus, ClientType, ClientSize
)
from ..core.firebase_auth import AuthUser

class ClientService:
    """Service layer for client management operations"""
    
    def __init__(self, db: firestore.AsyncClient):
        self.db = db
    
    async def create_client(
        self, 
        client_data: ClientCreate, 
        current_user: AuthUser
    ) -> Tuple[str, str]:
        """
        Create a new client for the firm
        Returns: (client_id, client_name)
        """
        if not current_user.firm_id:
            raise ValueError("User is not associated with a firm")
        
        # Generate new client ID
        client_id = str(uuid.uuid4())
        
        # Prepare client document data
        client_doc = {
            "client_id": client_id,
            "firm_id": current_user.firm_id,
            "name": client_data.name,
            "status": ClientStatus.ACTIVE.value,
            "created_at": SERVER_TIMESTAMP,
            "updated_at": SERVER_TIMESTAMP,
            "created_by": current_user.uid
        }
        
        # Add optional fields if provided
        if client_data.client_type:
            client_doc["client_type"] = client_data.client_type.value
        if client_data.client_size:
            client_doc["client_size"] = client_data.client_size.value
        if client_data.industry:
            client_doc["industry"] = client_data.industry
        if client_data.website:
            client_doc["website"] = client_data.website
        if client_data.description:
            client_doc["description"] = client_data.description
        if client_data.tax_id:
            client_doc["tax_id"] = client_data.tax_id
        
        # Add contact information
        if client_data.primary_contact:
            client_doc["primary_contact"] = client_data.primary_contact.dict(exclude_none=True)
        if client_data.billing_contact:
            client_doc["billing_contact"] = client_data.billing_contact.dict(exclude_none=True)
        
        # Add address information
        if client_data.business_address:
            client_doc["business_address"] = client_data.business_address.dict(exclude_none=True)
        if client_data.billing_address:
            client_doc["billing_address"] = client_data.billing_address.dict(exclude_none=True)
        
        # Add settings
        if client_data.settings:
            client_doc["settings"] = client_data.settings.dict(exclude_none=True)
        
        # Add custom fields
        if client_data.custom_fields:
            client_doc["custom_fields"] = client_data.custom_fields
        
        # Write to Firestore
        client_ref = self.db.collection("CLIENTS").document(client_id)
        await client_ref.set(client_doc)
        
        # Create audit log entry
        await self._create_audit_log_entry(
            event_category="CLIENT_MANAGEMENT",
            event_type="CLIENT_CREATED",
            client_id=client_id,
            status="SUCCESS",
            details={
                "client_name": client_data.name,
                "client_type": client_data.client_type.value if client_data.client_type else None,
                "created_by": current_user.uid
            },
            user_id=current_user.uid
        )
        
        return client_id, client_data.name
    
    async def get_clients_for_firm(
        self, 
        current_user: AuthUser,
        page: int = 1,
        limit: int = 20,
        name_filter: Optional[str] = None,
        status_filter: Optional[str] = None,
        client_type_filter: Optional[str] = None
    ) -> Tuple[List[ClientSummaryEnhanced], Dict[str, Any]]:
        """
        Get clients for the current user's firm with filtering and pagination
        Returns: (clients, pagination_info)
        """
        if not current_user.firm_id:
            raise ValueError("User is not associated with a firm")
        
        # Build base query
        query = self.db.collection("CLIENTS").where(filter=firestore.FieldFilter("firm_id", "==", current_user.firm_id))
        
        # Apply filters
        if status_filter and status_filter != "all":
            query = query.where(filter=firestore.FieldFilter("status", "==", status_filter))
        if client_type_filter and client_type_filter != "all":
            query = query.where(filter=firestore.FieldFilter("client_type", "==", client_type_filter))
        
        # Get all matching documents
        client_docs = await query.get()
        all_clients = []
        
        for doc in client_docs:
            client_data = doc.to_dict()
            
            # Apply name filter if specified
            if name_filter and name_filter.lower() not in client_data.get("name", "").lower():
                continue
            
            # Get entity counts for this client
            entities_count, active_entities_count, error_count = await self._get_entity_counts(
                client_data.get("client_id")
            )
            
            # Get pending items count for this client
            pending_items_count = await self._get_pending_items_count(
                client_data.get("client_id")
            )
            
            # Calculate overall status with proper priority
            overall_status = "ok"
            if error_count > 0:
                overall_status = "error"
            elif pending_items_count > 0:
                overall_status = "action_needed"
            
            client_summary = ClientSummaryEnhanced(
                client_id=client_data.get("client_id"),
                name=client_data.get("name"),
                status=ClientStatus(client_data.get("status", "active")),
                client_type=ClientType(client_data.get("client_type")) if client_data.get("client_type") else None,
                industry=client_data.get("industry"),
                entities_count=entities_count,
                active_entities_count=active_entities_count,
                pending_items_count=pending_items_count,
                error_count=error_count,
                last_activity=client_data.get("updated_at"),
                overall_status=overall_status
            )
            
            all_clients.append(client_summary)
        
        # Apply pagination
        total_items = len(all_clients)
        total_pages = (total_items + limit - 1) // limit
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_clients = all_clients[start_idx:end_idx]
        
        pagination_info = {
            "current_page": page,
            "page_size": limit,
            "total_items": total_items,
            "total_pages": total_pages
        }
        
        return paginated_clients, pagination_info
    
    async def get_client_by_id(
        self, 
        client_id: str, 
        current_user: AuthUser
    ) -> ClientResponse:
        """Get detailed client information by ID"""
        # Get client document
        client_ref = self.db.collection("CLIENTS").document(client_id)
        client_doc = await client_ref.get()
        
        if not client_doc.exists:
            raise ValueError("Client not found")
        
        client_data = client_doc.to_dict()
        
        # Check if user has access to this client
        if client_data.get("firm_id") != current_user.firm_id:
            raise ValueError("Access denied to this client")
        
        # Get entity counts
        entities_count, active_entities_count, _ = await self._get_entity_counts(client_id)
        
        # Build response
        response_data = {
            "client_id": client_data.get("client_id"),
            "firm_id": client_data.get("firm_id"),
            "name": client_data.get("name"),
            "status": ClientStatus(client_data.get("status", "active")),
            "entities_count": entities_count,
            "active_entities_count": active_entities_count
        }
        
        # Add optional fields
        optional_fields = [
            "client_type", "client_size", "industry", "website", 
            "description", "tax_id", "primary_contact", "billing_contact",
            "business_address", "billing_address", "settings", "custom_fields",
            "created_at", "updated_at"
        ]
        
        for field in optional_fields:
            if field in client_data:
                if field in ["client_type", "client_size"]:
                    # Convert enum fields
                    if field == "client_type" and client_data[field]:
                        response_data[field] = ClientType(client_data[field])
                    elif field == "client_size" and client_data[field]:
                        response_data[field] = ClientSize(client_data[field])
                else:
                    response_data[field] = client_data[field]
        
        return ClientResponse(**response_data)
    
    async def update_client(
        self, 
        client_id: str, 
        client_data: ClientUpdate, 
        current_user: AuthUser
    ) -> None:
        """Update client information"""
        # Get existing client
        client_ref = self.db.collection("CLIENTS").document(client_id)
        client_doc = await client_ref.get()
        
        if not client_doc.exists:
            raise ValueError("Client not found")
        
        existing_data = client_doc.to_dict()
        
        # Check permissions
        if existing_data.get("firm_id") != current_user.firm_id:
            raise ValueError("Access denied to this client")
        
        # Build update data
        update_data = {"updated_at": SERVER_TIMESTAMP}
        
        # Update fields that are provided
        if client_data.name is not None:
            update_data["name"] = client_data.name
        if client_data.client_type is not None:
            update_data["client_type"] = client_data.client_type.value
        if client_data.client_size is not None:
            update_data["client_size"] = client_data.client_size.value
        if client_data.industry is not None:
            update_data["industry"] = client_data.industry
        if client_data.website is not None:
            update_data["website"] = client_data.website
        if client_data.description is not None:
            update_data["description"] = client_data.description
        if client_data.status is not None:
            update_data["status"] = client_data.status.value
        if client_data.tax_id is not None:
            update_data["tax_id"] = client_data.tax_id
        
        # Update contact information
        if client_data.primary_contact is not None:
            update_data["primary_contact"] = client_data.primary_contact.dict(exclude_none=True)
        if client_data.billing_contact is not None:
            update_data["billing_contact"] = client_data.billing_contact.dict(exclude_none=True)
        
        # Update address information
        if client_data.business_address is not None:
            update_data["business_address"] = client_data.business_address.dict(exclude_none=True)
        if client_data.billing_address is not None:
            update_data["billing_address"] = client_data.billing_address.dict(exclude_none=True)
        
        # Update settings
        if client_data.settings is not None:
            update_data["settings"] = client_data.settings.dict(exclude_none=True)
        
        # Update custom fields
        if client_data.custom_fields is not None:
            update_data["custom_fields"] = client_data.custom_fields
        
        # Apply update
        await client_ref.update(update_data)
        
        # Create audit log entry
        await self._create_audit_log_entry(
            event_category="CLIENT_MANAGEMENT",
            event_type="CLIENT_UPDATED",
            client_id=client_id,
            status="SUCCESS",
            details={
                "updated_fields": list(update_data.keys()),
                "updated_by": current_user.uid
            },
            user_id=current_user.uid
        )
    
    async def delete_client(
        self, 
        client_id: str, 
        current_user: AuthUser
    ) -> None:
        """Soft delete a client (set status to inactive)"""
        # Get existing client
        client_ref = self.db.collection("CLIENTS").document(client_id)
        client_doc = await client_ref.get()
        
        if not client_doc.exists:
            raise ValueError("Client not found")
        
        existing_data = client_doc.to_dict()
        
        # Check permissions
        if existing_data.get("firm_id") != current_user.firm_id:
            raise ValueError("Access denied to this client")
        
        # Check if client has active entities
        entities_count, active_entities_count, _ = await self._get_entity_counts(client_id)
        if active_entities_count > 0:
            raise ValueError("Cannot delete client with active entities. Please disconnect entities first.")
        
        # Soft delete by setting status to inactive
        await client_ref.update({
            "status": ClientStatus.INACTIVE.value,
            "updated_at": SERVER_TIMESTAMP,
            "deleted_by": current_user.uid,
            "deleted_at": SERVER_TIMESTAMP
        })
        
        # Create audit log entry
        await self._create_audit_log_entry(
            event_category="CLIENT_MANAGEMENT",
            event_type="CLIENT_DELETED",
            client_id=client_id,
            status="SUCCESS",
            details={
                "client_name": existing_data.get("name"),
                "deleted_by": current_user.uid
            },
            user_id=current_user.uid
        )
    
    async def create_client_from_wizard(
        self,
        step1_data: ClientWizardStep1,
        step2_data: ClientWizardStep2,
        step3_data: ClientWizardStep3,
        current_user: AuthUser
    ) -> Tuple[str, str]:
        """Create client from multi-step wizard data"""
        # Combine all wizard steps into a single ClientCreate object
        client_create = ClientCreate(
            name=step1_data.name,
            client_type=step1_data.client_type,
            client_size=step1_data.client_size,
            industry=step1_data.industry,
            primary_contact=step2_data.primary_contact,
            business_address=step2_data.business_address,
            website=step2_data.website,
            settings=step3_data.settings,
            tax_id=step3_data.tax_id,
            description=step3_data.description
        )
        
        return await self.create_client(client_create, current_user)
    
    async def _get_entity_counts(self, client_id: str) -> Tuple[int, int, int]:
        """
        Get entity counts for a client
        Returns: (total_entities, active_entities, error_entities)
        """
        try:
            entities_query = self.db.collection("ENTITIES").where(filter=firestore.FieldFilter("client_id", "==", client_id))
            entities_docs = await entities_query.get()
            
            total_count = 0
            active_count = 0
            error_count = 0
            
            for doc in entities_docs:
                entity_data = doc.to_dict()
                total_count += 1
                
                connection_status = entity_data.get("connection_details", {}).get("status", "unknown")
                if connection_status == "active":
                    active_count += 1
                elif connection_status in ["error", "disconnected"]:
                    error_count += 1
            
            return total_count, active_count, error_count
        except Exception as e:
            print(f"Error fetching entity counts for client {client_id}: {e}")
            return 0, 0, 0
    
    async def _get_pending_items_count(self, client_id: str) -> int:
        """
        Calculate the number of amortization schedules that require user action for a client
        Returns: count of schedules with pending_configuration or proposed status
        """
        try:
            # For now, return 0 to avoid database performance issues
            # TODO: Implement efficient pending items calculation
            return 0
            
        except Exception as e:
            print(f"Error calculating pending items for client {client_id}: {e}")
            return 0
    
    async def _create_audit_log_entry(
        self,
        event_category: str,
        event_type: str,
        client_id: str,
        status: str,
        details: Dict[str, Any],
        user_id: Optional[str] = None,
        entity_id: Optional[str] = None,
        source_ip: Optional[str] = None
    ) -> None:
        """Create an audit log entry"""
        try:
            audit_entry = {
                "event_category": event_category,
                "event_type": event_type,
                "client_id": client_id,
                "status": status,
                "details": details,
                "timestamp": SERVER_TIMESTAMP,
                "user_id": user_id,
                "entity_id": entity_id,
                "source_ip": source_ip
            }
            
            # Remove None values
            audit_entry = {k: v for k, v in audit_entry.items() if v is not None}
            
            # Write to audit log collection
            audit_ref = self.db.collection("AUDIT_LOG").document()
            await audit_ref.set(audit_entry)
        except Exception as e:
            print(f"Error creating audit log entry: {e}")
            # Don't raise exception as this shouldn't break the main operation 