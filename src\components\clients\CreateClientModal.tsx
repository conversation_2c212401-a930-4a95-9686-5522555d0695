import React, { useState, useEffect } from 'react';
import { DraggableDialog, DraggableDialogContent, DraggableDialogHeader, DraggableDialogTitle, DraggableDialogFooter } from '@/components/ui/draggable-dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, Circle, ArrowLeft, ArrowRight, Building2, Users, Settings, AlertCircle } from 'lucide-react';
import { useClientStore } from '@/store/client.store';
import type {
  ClientWizardStep1,
  ClientWizardStep2,
  ClientWizardStep3,
  ClientType,
  ClientSize,
  ContactInfo,
  ClientAddress,
  ClientSettings
} from '@/types/client.types';

interface CreateClientModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (clientId: string) => void;
}

interface WizardStep {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  completed: boolean;
}

export function CreateClientModal({ isOpen, onClose, onSuccess }: CreateClientModalProps) {
  const { 
    isCreating, 
    error, 
    clientEnums, 
    createClientFromWizard, 
    fetchClientEnums, 
    clearError 
  } = useClientStore();

  // Wizard state
  const [currentStep, setCurrentStep] = useState(1);
  const [step1Data, setStep1Data] = useState<ClientWizardStep1>({
    name: '',
    client_type: undefined,
    client_size: undefined,
    industry: ''
  });
  const [step2Data, setStep2Data] = useState<ClientWizardStep2>({
    primary_contact: {
      name: '',
      email: '',
      phone: '',
      title: ''
    },
    business_address: {
      street: '',
      city: '',
      state: '',
      postal_code: '',
      country: 'GB'
    },
    website: ''
  });
  const [step3Data, setStep3Data] = useState<ClientWizardStep3>({
    settings: {
      fiscal_year_end: '04-05',
      default_currency: 'GBP',
      timezone: 'Europe/London',
      date_format: 'DD/MM/YYYY',
      number_format: 'UK'
    },
    tax_id: '',
    description: ''
  });

  // Load client enums on mount
  useEffect(() => {
    if (isOpen && !clientEnums) {
      fetchClientEnums();
    }
  }, [isOpen, clientEnums, fetchClientEnums]);

  // Clear error when modal opens
  useEffect(() => {
    if (isOpen) {
      clearError();
    }
  }, [isOpen, clearError]);

  const steps: WizardStep[] = [
    {
      id: 1,
      title: 'Basic Information',
      description: 'Client name, type, and industry',
      icon: <Building2 className="h-4 w-4" />,
      completed: Boolean(step1Data.name && step1Data.client_type)
    },
    {
      id: 2,
      title: 'Contact & Address',
      description: 'Contact details and business address',
      icon: <Users className="h-4 w-4" />,
      completed: Boolean(step2Data.primary_contact?.name && step2Data.primary_contact?.email)
    },
    {
      id: 3,
      title: 'Settings & Preferences',
      description: 'Configuration and additional details',
      icon: <Settings className="h-4 w-4" />,
      completed: Boolean(step3Data.settings?.fiscal_year_end)
    }
  ];

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    try {
      const clientId = await createClientFromWizard(step1Data, step2Data, step3Data);
      onSuccess?.(clientId);
      handleClose();
    } catch (error) {
      // Error is handled by the store
      console.error('Failed to create client:', error);
    }
  };

  const handleClose = () => {
    // Reset form data
    setCurrentStep(1);
    setStep1Data({
      name: '',
      client_type: undefined,
      client_size: undefined,
      industry: ''
    });
    setStep2Data({
      primary_contact: {
        name: '',
        email: '',
        phone: '',
        title: ''
      },
      business_address: {
        street: '',
        city: '',
        state: '',
        postal_code: '',
        country: 'GB'
      },
      website: ''
    });
    setStep3Data({
      settings: {
        fiscal_year_end: '04-05',
        default_currency: 'GBP',
        timezone: 'Europe/London',
        date_format: 'DD/MM/YYYY',
        number_format: 'UK'
      },
      tax_id: '',
      description: ''
    });
    clearError();
    onClose();
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return Boolean(step1Data.name.trim());
      case 2:
        return Boolean(step2Data.primary_contact?.name?.trim() && step2Data.primary_contact?.email?.trim());
      case 3:
        return true; // Step 3 is optional
      default:
        return false;
    }
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-between mb-8">
      {steps.map((step, index) => (
        <React.Fragment key={step.id}>
          <div className="flex flex-col items-center">
            <div className={`
              flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
              ${currentStep === step.id 
                ? 'border-blue-500 bg-blue-500 text-white' 
                : step.completed 
                  ? 'border-green-500 bg-green-500 text-white'
                  : 'border-gray-300 bg-white text-gray-400'
              }
            `}>
              {step.completed && currentStep !== step.id ? (
                <CheckCircle className="h-5 w-5" />
              ) : (
                step.icon
              )}
            </div>
            <div className="mt-2 text-center">
              <div className={`text-sm font-medium ${
                currentStep === step.id ? 'text-blue-600' : 'text-gray-500'
              }`}>
                {step.title}
              </div>
              <div className="text-xs text-gray-400 max-w-24">
                {step.description}
              </div>
            </div>
          </div>
          {index < steps.length - 1 && (
            <div className={`flex-1 h-0.5 mx-4 ${
              steps[index + 1].completed || currentStep > step.id 
                ? 'bg-green-500' 
                : 'bg-gray-200'
            }`} />
          )}
        </React.Fragment>
      ))}
    </div>
  );

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <Label htmlFor="client-name" className="text-sm font-medium">
          Client Name *
        </Label>
        <Input
          id="client-name"
          value={step1Data.name}
          onChange={(e) => setStep1Data({ ...step1Data, name: e.target.value })}
          placeholder="Enter client name"
          className="mt-1"
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="client-type" className="text-sm font-medium">
            Client Type
          </Label>
          <Select
            value={step1Data.client_type}
            onValueChange={(value) => setStep1Data({ ...step1Data, client_type: value as ClientType })}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select client type" />
            </SelectTrigger>
            <SelectContent>
              {clientEnums?.client_types.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="client-size" className="text-sm font-medium">
            Client Size
          </Label>
          <Select
            value={step1Data.client_size}
            onValueChange={(value) => setStep1Data({ ...step1Data, client_size: value as ClientSize })}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select client size" />
            </SelectTrigger>
            <SelectContent>
              {clientEnums?.client_sizes.map((size) => (
                <SelectItem key={size.value} value={size.value}>
                  {size.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor="industry" className="text-sm font-medium">
          Industry
        </Label>
        <Input
          id="industry"
          value={step1Data.industry}
          onChange={(e) => setStep1Data({ ...step1Data, industry: e.target.value })}
          placeholder="e.g., Financial Services, Retail, Professional Services"
          className="mt-1"
        />
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Primary Contact</CardTitle>
          <CardDescription>Main point of contact for this client</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="contact-name" className="text-sm font-medium">
                Contact Name *
              </Label>
              <Input
                id="contact-name"
                value={step2Data.primary_contact?.name || ''}
                onChange={(e) => setStep2Data({
                  ...step2Data,
                  primary_contact: { ...step2Data.primary_contact!, name: e.target.value }
                })}
                placeholder="Full name"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="contact-title" className="text-sm font-medium">
                Title
              </Label>
              <Input
                id="contact-title"
                value={step2Data.primary_contact?.title || ''}
                onChange={(e) => setStep2Data({
                  ...step2Data,
                  primary_contact: { ...step2Data.primary_contact!, title: e.target.value }
                })}
                placeholder="Job title"
                className="mt-1"
              />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="contact-email" className="text-sm font-medium">
                Email *
              </Label>
              <Input
                id="contact-email"
                type="email"
                value={step2Data.primary_contact?.email || ''}
                onChange={(e) => setStep2Data({
                  ...step2Data,
                  primary_contact: { ...step2Data.primary_contact!, email: e.target.value }
                })}
                placeholder="<EMAIL>"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="contact-phone" className="text-sm font-medium">
                Phone
              </Label>
              <Input
                id="contact-phone"
                value={step2Data.primary_contact?.phone || ''}
                onChange={(e) => setStep2Data({
                  ...step2Data,
                  primary_contact: { ...step2Data.primary_contact!, phone: e.target.value }
                })}
                placeholder="+44 20 7946 0958"
                className="mt-1"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Business Address</CardTitle>
          <CardDescription>Primary business location</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="street" className="text-sm font-medium">
              Street Address
            </Label>
            <Input
              id="street"
              value={step2Data.business_address?.street || ''}
              onChange={(e) => setStep2Data({
                ...step2Data,
                business_address: { ...step2Data.business_address!, street: e.target.value }
              })}
              placeholder="123 Main Street"
              className="mt-1"
            />
          </div>
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="city" className="text-sm font-medium">
                City
              </Label>
              <Input
                id="city"
                value={step2Data.business_address?.city || ''}
                onChange={(e) => setStep2Data({
                  ...step2Data,
                  business_address: { ...step2Data.business_address!, city: e.target.value }
                })}
                placeholder="City"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="state" className="text-sm font-medium">
                County
              </Label>
              <Input
                id="state"
                value={step2Data.business_address?.state || ''}
                onChange={(e) => setStep2Data({
                  ...step2Data,
                  business_address: { ...step2Data.business_address!, state: e.target.value }
                })}
                placeholder="Greater London"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="postal-code" className="text-sm font-medium">
                Postcode
              </Label>
              <Input
                id="postal-code"
                value={step2Data.business_address?.postal_code || ''}
                onChange={(e) => setStep2Data({
                  ...step2Data,
                  business_address: { ...step2Data.business_address!, postal_code: e.target.value }
                })}
                placeholder="SW1A 1AA"
                className="mt-1"
              />
            </div>
          </div>
          <div>
            <Label htmlFor="website" className="text-sm font-medium">
              Website
            </Label>
            <Input
              id="website"
              value={step2Data.website || ''}
              onChange={(e) => setStep2Data({ ...step2Data, website: e.target.value })}
              placeholder="https://www.example.com"
              className="mt-1"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Client Settings</CardTitle>
          <CardDescription>Configure preferences and defaults</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="fiscal-year-end" className="text-sm font-medium">
                Fiscal Year End
              </Label>
              <Input
                id="fiscal-year-end"
                value={step3Data.settings?.fiscal_year_end || ''}
                onChange={(e) => setStep3Data({
                  ...step3Data,
                  settings: { ...step3Data.settings!, fiscal_year_end: e.target.value }
                })}
                placeholder="04-05"
                className="mt-1"
              />
              <p className="text-xs text-gray-500 mt-1">Format: MM-DD (UK tax year: 04-05)</p>
            </div>
            <div>
              <Label htmlFor="currency" className="text-sm font-medium">
                Default Currency
              </Label>
              <Select
                value={step3Data.settings?.default_currency}
                onValueChange={(value) => setStep3Data({
                  ...step3Data,
                  settings: { ...step3Data.settings!, default_currency: value }
                })}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="GBP">GBP - British Pound</SelectItem>
                  <SelectItem value="EUR">EUR - Euro</SelectItem>
                  <SelectItem value="USD">USD - US Dollar</SelectItem>
                  <SelectItem value="CAD">CAD - Canadian Dollar</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div>
            <Label htmlFor="timezone" className="text-sm font-medium">
              Timezone
            </Label>
            <Select
              value={step3Data.settings?.timezone}
              onValueChange={(value) => setStep3Data({
                ...step3Data,
                settings: { ...step3Data.settings!, timezone: value }
              })}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Europe/London">GMT/BST - London</SelectItem>
                <SelectItem value="Europe/Dublin">GMT/IST - Dublin</SelectItem>
                <SelectItem value="Europe/Paris">CET/CEST - Paris</SelectItem>
                <SelectItem value="Europe/Berlin">CET/CEST - Berlin</SelectItem>
                <SelectItem value="America/New_York">EST/EDT - New York</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Additional Information</CardTitle>
          <CardDescription>Optional details about the client</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="tax-id" className="text-sm font-medium">
              Company Number / VAT Number
            </Label>
            <Input
              id="tax-id"
              value={step3Data.tax_id || ''}
              onChange={(e) => setStep3Data({ ...step3Data, tax_id: e.target.value })}
              placeholder="12345678 or GB123456789"
              className="mt-1"
            />
          </div>
          <div>
            <Label htmlFor="description" className="text-sm font-medium">
              Description
            </Label>
            <Textarea
              id="description"
              value={step3Data.description || ''}
              onChange={(e) => setStep3Data({ ...step3Data, description: e.target.value })}
              placeholder="Additional notes about this client..."
              className="mt-1"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <DraggableDialog open={isOpen} onOpenChange={handleClose}>
      <DraggableDialogContent className="flex flex-col p-0 gap-0">
        <DraggableDialogHeader className="p-6 border-b flex-shrink-0">
          <DraggableDialogTitle className="text-xl font-semibold">Create New Client</DraggableDialogTitle>
        </DraggableDialogHeader>

        <div className="flex flex-col flex-grow overflow-hidden">
          {/* Step Indicator - Fixed at top */}
          <div className="flex-shrink-0 p-6 pb-4">
            {renderStepIndicator()}

            {error && (
              <Alert variant="destructive" className="mt-4">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </div>

          {/* Scrollable Content Area */}
          <div className="flex-1 overflow-y-auto px-6">
            <div className="pb-6">
              {currentStep === 1 && renderStep1()}
              {currentStep === 2 && renderStep2()}
              {currentStep === 3 && renderStep3()}
            </div>
          </div>

          {/* Footer - Fixed at bottom */}
          <DraggableDialogFooter className="border-t p-6 flex-shrink-0">
            <div className="flex justify-between w-full">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 1}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <div className="flex gap-2">
                <Button variant="outline" onClick={handleClose}>
                  Cancel
                </Button>
                
                {currentStep < 3 ? (
                  <Button
                    onClick={handleNext}
                    disabled={!canProceed()}
                  >
                    Next
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    onClick={handleSubmit}
                    disabled={isCreating || !canProceed()}
                  >
                    {isCreating ? 'Creating...' : 'Create Client'}
                  </Button>
                )}
              </div>
            </div>
          </DraggableDialogFooter>
        </div>
      </DraggableDialogContent>
    </DraggableDialog>
  );
} 