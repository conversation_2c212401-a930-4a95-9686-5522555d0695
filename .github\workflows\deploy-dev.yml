name: CI/CD Pipeline

on:
  push:
    branches:
      - dev

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ vars.NODE_VERSION }}

      - name: Clean previous installation
        run: |
          echo "Cleaning previous installation..."
          rm -rf node_modules package-lock.json .npm
      
      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/package.json') }}
          restore-keys: |
            ${{ runner.os }}-node-
      
      - name: Install dependencies
        run: |
          echo "Installing dependencies..."
          npm install --no-audit
      
      - name: Verify Rollup native dependencies
        run: |
          echo "Verifying Rollup native dependencies..."
          npm ls @rollup/rollup-linux-x64-gnu || echo "Native binary not found, but continuing..."

      - name: Generate .env file for Vite
        run: |
          echo "VITE_API_BASE_URL=${{ vars.DEV_VITE_API_BASE_URL }}" >> .env
          echo "VITE_FIREBASE_API_KEY=${{ secrets.VITE_FIREBASE_API_KEY }}" >> .env
          echo "VITE_FIREBASE_AUTH_DOMAIN=${{ vars.DEV_VITE_FIREBASE_AUTH_DOMAIN }}" >> .env
          echo "VITE_FIREBASE_PROJECT_ID=${{ vars.VITE_FIREBASE_PROJECT_ID }}" >> .env
          echo "VITE_FIREBASE_STORAGE_BUCKET=${{ vars.VITE_FIREBASE_STORAGE_BUCKET }}" >> .env
          echo "VITE_FIREBASE_MESSAGING_SENDER_ID=${{ vars.VITE_FIREBASE_MESSAGING_SENDER_ID }}" >> .env
          echo "VITE_FIREBASE_APP_ID=${{ vars.VITE_FIREBASE_APP_ID }}" >> .env

          echo "Contents of .env: "
          echo "::add-mask::false"
          cat .env
      
      - name: Build application
        run: |
          echo "Building application..."
          npm run build

      - name: Verify presence of .firebaserc
        run: |
          echo "Checking if .firebaserc exists:"
          if [ ! -f .firebaserc ]; then
            echo "ERROR: .firebaserc is missing!"
            exit 1
          else
            echo ".firebaserc found."
          fi
      
      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            dist/
            firebase.json
            .firebaserc
            .*

  deploy-production:
    name: Deploy to Firebase Hosting (Production)
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts

      - name: Generate .firebaserc if missing
        run: |
          if [ ! -f .firebaserc ]; then
            echo "File .firebaserc not found, generating from VITE_FIREBASE_PROJECT_ID environment variable..."
            echo '{"projects": {"default": "${{ vars.VITE_FIREBASE_PROJECT_ID }}"}}' > .firebaserc
            echo ".firebaserc generated successfully!"
          else
            echo ".firebaserc already exists, skipping generation."
          fi

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ vars.NODE_VERSION }}

      - name: Install Firebase CLI
        run: |
          echo "Installing Firebase CLI..."
          npm install -g firebase-tools
      
      - name: Deploy to Firebase Hosting
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        run: |
          echo "Checking if FIREBASE_TOKEN is set..."
          if [ -z "$FIREBASE_TOKEN" ]; then 
            echo "ERROR: FIREBASE_TOKEN is not set. Please add it in GitHub Secrets."
            exit 1
          fi

          echo "Deploying to Firebase Hosting (Production)..."
          firebase deploy --only hosting --token "$FIREBASE_TOKEN" --non-interactive
      
      - name: Verify Deployment
        run: |
          echo "Deployment completed!"
          echo "Application is now live at: ${{ vars.DEV_FIREBASE_HOSTING_URL }}"

  deploy-dev:
    name: Deploy to Firebase Hosting (Development)
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts

      - name: Generate .firebaserc if missing
        run: |
          if [ ! -f .firebaserc ]; then
            echo "File .firebaserc not found, generating from VITE_FIREBASE_PROJECT_ID environment variable..."
            echo '{"projects": {"default": "${{ vars.VITE_FIREBASE_PROJECT_ID }}"}}' > .firebaserc
            echo ".firebaserc (development) generated successfully!"
          else
            echo ".firebaserc already exists, skipping generation."
          fi

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ vars.NODE_VERSION }}

      - name: Install Firebase CLI
        run: |
          echo "Installing Firebase CLI..."
          npm install -g firebase-tools
      
      - name: Deploy to Firebase Hosting (Dev)
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        run: |
          echo "Checking if FIREBASE_TOKEN is set..."
          if [ -z "$FIREBASE_TOKEN" ]; then 
            echo "ERROR: FIREBASE_TOKEN is not set. Please add it in GitHub Secrets."
            exit 1
          fi

          echo "Deploying to Firebase Hosting (Development)..."
          firebase deploy --only hosting --token "$FIREBASE_TOKEN" --non-interactive --project="${{ vars.FIREBASE_DEV_PROJECT_ID }}"
      
      - name: Verify Deployment
        run: |
          echo "Deployment to Development completed!"
          echo "Application is live at: dev.${{ vars.DEV_FIREBASE_HOSTING_URL }}"