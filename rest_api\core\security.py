from datetime import datetime, timedelta, timezone
from typing import Optional

from jose import JW<PERSON>rror, jwt
from passlib.context import Crypt<PERSON>ontext

from .config import settings # Import the settings instance

# Password hashing context
# We specify that bcrypt is the default scheme.
# deprecated="auto" will mark older hash schemes as deprecated for auto-migration if needed.
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verifies a plain password against a hashed password."""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hashes a plain password."""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Creates a new JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        # Use the default expiration time from settings
        expire = datetime.now(timezone.utc) + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

# JWT token decoding with clock skew tolerance
def decode_access_token(token: str):
    """Decode and validate JWT access token with clock skew tolerance."""
    try:
        # Add leeway parameter to handle clock differences (5 minutes tolerance)
        payload = jwt.decode(
            token, 
            settings.SECRET_KEY, 
            algorithms=[settings.ALGORITHM],
            leeway=300  # Allow 5 minutes clock difference
        )
        username: str = payload.get("sub")
        if username is None:
            return None # Or raise an exception
        return username # Or the full payload
    except JWTError:
        return None # Or raise an exception 