#!/usr/bin/env python3
"""
Fix Inconsistent Schedule Status Migration Script

This script fixes schedules where the schedule-level status doesn't match
the monthly entries status. This can happen with older schedules that were
created before the consistency fixes.

Usage:
    python scripts/utilities/fix_inconsistent_schedule_status.py [--dry-run]

Options:
    --dry-run    Show what would be changed without making actual changes
"""

import asyncio
import argparse
import sys
import os
from typing import List, Dict, Any

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from google.cloud import firestore
from google.cloud.firestore import SERVER_TIMESTAMP


async def main():
    parser = argparse.ArgumentParser(description='Fix inconsistent schedule status')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show what would be changed without making actual changes')
    args = parser.parse_args()

    # Initialize Firestore
    project_id = os.getenv("GCP_PROJECT_ID")
    if not project_id:
        print("❌ Error: GCP_PROJECT_ID environment variable not set")
        print("   Please set it using: $env:GCP_PROJECT_ID=\"drcr-d660a\"")
        sys.exit(1)
    
    db = firestore.AsyncClient(project=project_id)
    
    print(f"🔧 Fixing inconsistent schedule status (dry-run: {args.dry_run})")
    print("=" * 60)
    
    # Get all schedules
    schedules_ref = db.collection("AMORTIZATION_SCHEDULES")
    schedules_snapshot = await schedules_ref.get()
    
    inconsistent_schedules = []
    fixed_schedules = []
    
    for schedule_doc in schedules_snapshot:
        schedule_data = schedule_doc.to_dict()
        schedule_id = schedule_doc.id
        schedule_status = schedule_data.get("status")
        monthly_entries = schedule_data.get("monthlyEntries", [])
        
        if not monthly_entries:
            continue
            
        # Check if any monthly entries have different status than schedule
        inconsistent_entries = []
        for entry in monthly_entries:
            entry_status = entry.get("status")
            if entry_status != schedule_status:
                inconsistent_entries.append({
                    "month": entry.get("monthDate"),
                    "entry_status": entry_status,
                    "schedule_status": schedule_status
                })
        
        if inconsistent_entries:
            inconsistent_schedules.append({
                "schedule_id": schedule_id,
                "schedule_status": schedule_status,
                "inconsistent_entries": len(inconsistent_entries),
                "total_entries": len(monthly_entries),
                "data": schedule_data
            })
            
            print(f"📋 Schedule {schedule_id[:8]}...")
            print(f"   Schedule Status: {schedule_status}")
            print(f"   Inconsistent Entries: {len(inconsistent_entries)}/{len(monthly_entries)}")
            
            if not args.dry_run:
                from datetime import datetime, timezone
                
                # Fix the inconsistency by updating monthly entries to match schedule status
                updated_monthly_entries = []
                current_timestamp = datetime.now(timezone.utc)
                
                for entry in monthly_entries:
                    updated_entry = entry.copy()
                    updated_entry["status"] = schedule_status
                    if schedule_status in ["skipped", "confirmed", "cancelled"]:
                        updated_entry["lastActionTimestamp"] = current_timestamp
                    updated_monthly_entries.append(updated_entry)
                
                # Update the schedule
                await schedules_ref.document(schedule_id).update({
                    "monthlyEntries": updated_monthly_entries,
                    "updated_at": current_timestamp
                })
                
                fixed_schedules.append(schedule_id)
                print(f"   ✅ Fixed - All entries now status: {schedule_status}")
            else:
                print(f"   🔍 Would fix - All entries would be set to: {schedule_status}")
            
            print()
    
    print("=" * 60)
    print(f"📊 Summary:")
    print(f"   Total schedules checked: {len(schedules_snapshot)}")
    print(f"   Inconsistent schedules found: {len(inconsistent_schedules)}")
    
    if args.dry_run:
        print(f"   Would fix: {len(inconsistent_schedules)} schedules")
        print()
        print("💡 Run without --dry-run to apply fixes")
    else:
        print(f"   Fixed: {len(fixed_schedules)} schedules")
        if len(fixed_schedules) > 0:
            print()
            print("✅ Migration completed successfully!")
            print("   All schedule statuses are now consistent with monthly entries")
        else:
            print()
            print("ℹ️  No schedules needed fixing")

    await db.close()


if __name__ == "__main__":
    asyncio.run(main())