from fastapi import APIRouter, Depends, HTTPException, Path, Query, Body, status
from typing import List, Optional, Dict, Any
from google.cloud.firestore import SERVER_TIMESTAMP
import uuid
from datetime import datetime, timezone

from pydantic import BaseModel

from ..core.firebase_auth import get_current_user, get_firm_user_with_client_access, AuthUser
from ..dependencies import get_db
from ..models import invoice as invoice_models, schedule as schedule_models, attachment as attachment_models
from ..models.error_codes import AppErrorCode
from ..utils.transformers import (
    firestore_transaction_to_invoice_model, 
    firestore_to_schedule_model,
    firestore_to_attachment_model
)
from ..schemas.transaction_schemas import (
    PaginatedTransactionsResponse,
    DashboardTransactionItem,
    PaginatedDashboardResponse
)
from ..services.transaction_service import TransactionService

import logging
logger = logging.getLogger(__name__)

router = APIRouter(tags=["Transactions"])

# Define the response model for paginated transactions
class PaginatedTransactionsResponse(BaseModel):
    transactions: List[invoice_models.Invoice]
    total: int
    page: int
    limit: int
    total_pages: int

# Define response models for the dashboard endpoint
class DashboardTransactionItem(BaseModel):
    transaction: invoice_models.Invoice
    schedules: List[schedule_models.Schedule]
    # We can add other specific dashboard-related computed fields here if needed in future

class PaginatedDashboardResponse(BaseModel):
    transactions: List[DashboardTransactionItem]
    total: int
    page: int
    limit: int
    total_pages: int

def get_transaction_service(db = Depends(get_db)) -> TransactionService:
    """Dependency to get transaction service instance"""
    return TransactionService(db)

@router.get("/", response_model=PaginatedTransactionsResponse)
async def list_transactions(
    client_id: str = Query(..., description="Client ID"),
    entity_id: Optional[str] = Query(None, description="Entity ID (optional)"),
    transaction_type: Optional[str] = Query(None, description="Filter by transaction type (e.g., 'ACCPAY_INVOICE', 'SPEND_MONEY')"),
    status: Optional[str] = Query(None, description="Filter by status (matches Xero status string for now)"),
    page: int = Query(1, description="Page number", ge=1),
    limit: int = Query(50, description="Number of records to return", ge=1, le=100),
    current_user: AuthUser = Depends(get_current_user),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """
    List transactions with pagination and filtering.
    Transactions are transformed into the standard Invoice model.
    """
    await get_firm_user_with_client_access(client_id, current_user)

    transactions, total, total_pages = await transaction_service.list_transactions_paginated(
        client_id=client_id,
        entity_id=entity_id,
        transaction_type=transaction_type,
        status=status,
        page=page,
        limit=limit
    )

    return PaginatedTransactionsResponse(
        transactions=transactions,
        total=total,
        page=page,
        limit=limit,
        total_pages=total_pages
    )

@router.get("/dashboard", response_model=PaginatedDashboardResponse)
async def get_dashboard_transactions(
    client_id: Optional[str] = Query(None, description="Filter by client ID"),
    entity_id: Optional[str] = Query(None, description="Filter by entity ID"),
    status: Optional[str] = Query(None, description="Filter by transaction status (raw Xero status)"),
    transaction_type: Optional[str] = Query(None, description="Filter by transaction type (e.g., 'ACCPAY_INVOICE', 'SPEND_MONEY')"),
    require_action: bool = Query(False, description="Only show transactions requiring action (based on schedule status)"),
    status_filters: Optional[List[str]] = Query(None, description="Filter by schedule status (e.g., 'pending_configuration', 'proposed')"),
    page: int = Query(1, description="Page number", ge=1),
    limit: int = Query(20, description="Items per page", ge=1, le=100),
    current_user: AuthUser = Depends(get_current_user),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """
    Get transactions for the dashboard with their associated amortization schedules.
    Filters by client and entity, respecting user's assigned clients.
    Returns data transformed into Pydantic models.
    """
    user_client_ids = await transaction_service.get_user_client_ids(current_user, client_id)

    if client_id:
        await get_firm_user_with_client_access(client_id, current_user)

    dashboard_items, total, total_pages = await transaction_service.get_dashboard_transactions(
        user_client_ids=user_client_ids,
        entity_id=entity_id,
        status=status,
        transaction_type=transaction_type,
        require_action=require_action,
        status_filters=status_filters,
        page=page,
        limit=limit
    )

    # Convert to DashboardTransactionItem objects
    formatted_items = [
        DashboardTransactionItem(
            transaction=item["transaction"],
            schedules=item["schedules"]
        )
        for item in dashboard_items
    ]

    return PaginatedDashboardResponse(
        transactions=formatted_items,
        total=total,
        page=page,
        limit=limit,
        total_pages=total_pages
    )

@router.get("/{transaction_id}", response_model=invoice_models.Invoice)
async def get_transaction(
    transaction_id: str = Path(..., description="Transaction ID"),
    current_user: AuthUser = Depends(get_current_user),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """
    Get a single transaction by ID.
    Returns transaction data transformed into the standard Invoice model.
    """
    transaction = await transaction_service.get_transaction_by_id(transaction_id)
    
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "error_code": AppErrorCode.TRANSACTION_NOT_FOUND,
                "message": f"Transaction with ID {transaction_id} not found"
            }
        )
    
    # Verify user has access to this transaction's client
    await get_firm_user_with_client_access(transaction.client_id, current_user)
    
    return transaction

@router.get("/{transaction_id}/schedules", response_model=List[schedule_models.Schedule])
async def get_transaction_schedules(
    transaction_id: str = Path(..., description="Transaction ID"),
    current_user: AuthUser = Depends(get_current_user),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """
    Get amortization schedules for a specific transaction.
    """
    # First verify the transaction exists and user has access
    transaction = await transaction_service.get_transaction_by_id(transaction_id)
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "error_code": AppErrorCode.TRANSACTION_NOT_FOUND,
                "message": f"Transaction with ID {transaction_id} not found"
            }
        )
    
    await get_firm_user_with_client_access(transaction.client_id, current_user)
    
    schedules = await transaction_service.get_transaction_schedules(transaction_id)
    return schedules

@router.get("/{transaction_id}/attachments", response_model=List[attachment_models.Attachment])
async def get_transaction_attachments(
    transaction_id: str = Path(..., description="Transaction ID"),
    current_user: AuthUser = Depends(get_current_user),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """
    Get attachments for a specific transaction.
    """
    # First verify the transaction exists and user has access
    transaction = await transaction_service.get_transaction_by_id(transaction_id)
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "error_code": AppErrorCode.TRANSACTION_NOT_FOUND,
                "message": f"Transaction with ID {transaction_id} not found"
            }
        )
    
    await get_firm_user_with_client_access(transaction.client_id, current_user)
    
    attachments = await transaction_service.get_transaction_attachments(transaction_id)
    return attachments

@router.post("/", response_model=invoice_models.Invoice, status_code=status.HTTP_201_CREATED)
async def create_transaction(
    invoice_create_data: invoice_models.InvoiceCreate = Body(...),
    client_id: str = Query(..., description="Client ID for this transaction"),
    entity_id: str = Query(..., description="Entity ID for this transaction"),
    current_user: AuthUser = Depends(get_current_user),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """
    Create a new transaction.
    Returns the created transaction data transformed into the standard Invoice model.
    """
    await get_firm_user_with_client_access(client_id, current_user)

    try:
        transaction = await transaction_service.create_transaction(
            invoice_data=invoice_create_data,
            client_id=client_id,
            entity_id=entity_id,
            current_user=current_user
        )
        
        logger.info(f"Transaction created successfully with ID: {transaction.id}")
        return transaction
        
    except Exception as e:
        logger.error(f"Failed to create transaction: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error_code": AppErrorCode.TRANSACTION_CREATION_FAILED,
                "message": f"Failed to create transaction: {str(e)}"
            }
        )

@router.put("/{transaction_id}", response_model=invoice_models.Invoice)
async def update_transaction(
    transaction_id: str = Path(..., description="Transaction ID"),
    invoice_update_data: invoice_models.InvoiceUpdate = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """
    Update an existing transaction.
    Returns the updated transaction data transformed into the standard Invoice model.
    """
    # First verify the transaction exists and user has access
    existing_transaction = await transaction_service.get_transaction_by_id(transaction_id)
    if not existing_transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "error_code": AppErrorCode.TRANSACTION_NOT_FOUND,
                "message": f"Transaction with ID {transaction_id} not found"
            }
        )
    
    await get_firm_user_with_client_access(existing_transaction.client_id, current_user)
    
    try:
        updated_transaction = await transaction_service.update_transaction(
            transaction_id=transaction_id,
            invoice_data=invoice_update_data,
            current_user=current_user
        )
        
        if not updated_transaction:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error_code": AppErrorCode.TRANSACTION_NOT_FOUND,
                    "message": f"Transaction with ID {transaction_id} not found"
                }
            )
        
        logger.info(f"Transaction updated successfully: {transaction_id}")
        return updated_transaction
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update transaction {transaction_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error_code": AppErrorCode.TRANSACTION_UPDATE_FAILED,
                "message": f"Failed to update transaction: {str(e)}"
            }
        )

@router.delete("/{transaction_id}")
async def delete_transaction(
    transaction_id: str = Path(..., description="Transaction ID"),
    current_user: AuthUser = Depends(get_current_user),
    transaction_service: TransactionService = Depends(get_transaction_service)
):
    """
    Delete a transaction.
    Returns success message.
    """
    # First verify the transaction exists and user has access
    existing_transaction = await transaction_service.get_transaction_by_id(transaction_id)
    if not existing_transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "error_code": AppErrorCode.TRANSACTION_NOT_FOUND,
                "message": f"Transaction with ID {transaction_id} not found"
            }
        )
    
    await get_firm_user_with_client_access(existing_transaction.client_id, current_user)
    
    try:
        success = await transaction_service.delete_transaction(transaction_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error_code": AppErrorCode.TRANSACTION_NOT_FOUND,
                    "message": f"Transaction with ID {transaction_id} not found"
                }
            )
        
        logger.info(f"Transaction deleted successfully: {transaction_id}")
        return {"message": f"Transaction {transaction_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete transaction {transaction_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error_code": AppErrorCode.TRANSACTION_DELETION_FAILED,
                "message": f"Failed to delete transaction: {str(e)}"
            }
        )
