"""
LLM Processing Service for FastAPI

Handles AI-powered document processing including OCR, LLM analysis, and prepayment detection.
Migrated from Cloud Functions to avoid import path issues.
"""
import os
import logging
import asyncio
import copy
import time
from typing import Dict, Any, Optional, List
from google.cloud import firestore

from drcr_shared_logic.clients.xero_client import XeroApiClient
from drcr_shared_logic.document_processor import (
    extract_data_with_openai_direct,
    is_direct_openai_attempt_sufficient,
    process_pdf_with_mistral_ocr,
    process_image_with_mistral_ocr,
    extract_structured_data_with_openai,
    post_process_extracted_invoice_data,
)
from .sync_helpers import _upload_to_gcs

logger = logging.getLogger(__name__)
GCP_PROJECT_ID = os.getenv("GCP_PROJECT_ID")


def get_attachment_firestore_id(entity_id: str, transaction_id: str, attachment_id: str) -> str:
    """Generate consistent Firestore document ID for attachments to prevent key collisions."""
    return f"{entity_id}_{transaction_id}_{attachment_id}"


def _truncate_billing_info(billing_info: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
    """Truncate services_used array to prevent Firestore document size bombs on high-page documents."""
    if not billing_info or not isinstance(billing_info, dict):
        return billing_info
    
    # Deep copy to avoid modifying original (shallow copy not sufficient for nested dicts)
    truncated = copy.deepcopy(billing_info)
    services_used = billing_info.get("services_used", [])
    
    # Truncate to last 10 entries if too large (edge case: hundreds of pages)
    if isinstance(services_used, list) and len(services_used) > 10:
        logger.warning(f"Truncating services_used from {len(services_used)} to 10 entries to prevent Firestore size limit")
        truncated["services_used"] = services_used[-10:]  # Keep most recent entries
        truncated["services_used_truncated"] = True
        truncated["original_services_count"] = len(services_used)
    
    return truncated


def _create_lightweight_extraction_summary(processed_data: Dict[str, Any]) -> dict:
    """Create a lightweight summary of LLM extraction data to avoid Firestore 1MB limit."""
    if not processed_data:
        return {}
    
    # Keep only essential fields for Firestore, strip heavy data
    summary = {
        # Core extraction results
        "InvoiceNumber": processed_data.get("InvoiceNumber"),
        "Date": processed_data.get("Date"),
        "Total": processed_data.get("Total"),
        "System_IsPrepayment": processed_data.get("System_IsPrepayment"),
        "System_PrepaymentReason": processed_data.get("System_PrepaymentReason"),
        "ExpectedServiceStartDate": processed_data.get("ExpectedServiceStartDate"),
        "ExpectedServiceEndDate": processed_data.get("ExpectedServiceEndDate"),
        
        # System metadata (keep lightweight)
        "_system_extraction_method": processed_data.get("_system_extraction_method"),
        "_system_servicePeriodSource": processed_data.get("_system_servicePeriodSource"),
        "_system_servicePeriodInferenceConfidence": processed_data.get("_system_servicePeriodInferenceConfidence"),
        
        # Truncated OCR text (not full text)
        "_system_ocr_text_preview": processed_data.get("_system_ocr_text", "")[:200] + "..." if processed_data.get("_system_ocr_text", "") else None,
        
        # Validation summary (not full details)
        "System_TotalValidation": {
            "match": processed_data.get("System_TotalValidation", {}).get("match"),
            "llm_extracted_total": processed_data.get("System_TotalValidation", {}).get("llm_extracted_total"),
            "xero_total_amount": processed_data.get("System_TotalValidation", {}).get("xero_total_amount")
        } if processed_data.get("System_TotalValidation") else None,
        
        # Keep billing and fallback info (truncate services_used to prevent size bombs)
        "_system_billing_info": _truncate_billing_info(processed_data.get("_system_billing_info")),
        "_system_fallback_info": processed_data.get("_system_fallback_info")
    }
    
    # Remove None values to keep document clean
    return {k: v for k, v in summary.items() if v is not None}


def get_secret(secret_name: str) -> Optional[str]:
    """Fetches a secret from environment variables."""
    secret_value = os.getenv(secret_name.upper().replace("-", "_"))
    logger.info(
        f"Attempting to get secret: {secret_name}. Found: {'Yes' if secret_value else 'No'}"
    )
    if not secret_value:
        logger.warning(f"Secret {secret_name} not found in environment.")
    return secret_value


# REMOVED: process_single_attachment function
# This was dead code with NameError (upload_to_gcs vs _upload_to_gcs)
# Functionality is fully covered by fetch_and_process_attachments
# If concurrent processing is needed, use asyncio.gather() in the main loop


async def process_attachment_with_llm_fallback(
    attachment_bytes: bytes,
    mime_type: str,
    xero_transaction_data: Dict[str, Any],
    file_name_for_logging: str = "document",
    entity_id: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """
    Processes attachment with LLM using fallback logic:
    1. Try OpenAI direct extraction
    2. If validation fails, fall back to Mistral OCR + structured extraction
    """
    # Initialize fallback tracking variables
    fallback_reason = None
    openai_attempt_made = False
    openai_failure_details = None
    
    try:
        logger.info(f"Attempting OpenAI direct extraction for {file_name_for_logging}")
        # Add timeout to prevent hanging on OpenAI API
        openai_result = await asyncio.wait_for(
            extract_data_with_openai_direct(
                attachment_bytes, mime_type, file_name_for_logging, entity_id
            ),
            timeout=120.0  # 2 minute timeout for OpenAI
        )
        openai_attempt_made = True
        
        if openai_result:
            validated_data = await asyncio.wait_for(
                post_process_extracted_invoice_data(
                    openai_result, xero_transaction_data, file_name_for_logging
                ),
                timeout=30.0  # 30 second timeout for post-processing
            )
            if is_direct_openai_attempt_sufficient(validated_data):
                logger.info(f"OpenAI direct extraction successful for {file_name_for_logging}")
                
                # Charge OpenAI credits since it worked and no fallback needed
                if entity_id:
                    from drcr_shared_logic.document_processor import get_client_id_from_entity, check_and_deduct_credits, get_document_page_count
                    client_id = await get_client_id_from_entity(entity_id)
                    if client_id:
                        page_count = get_document_page_count(attachment_bytes, mime_type)
                        credit_result = await check_and_deduct_credits(client_id, page_count, "openai", "direct_extraction")
                        
                        # Add billing info to validated_data
                        validated_data["_system_billing_info"] = {
                            "page_count": page_count,
                            "total_credits_deducted": credit_result.get("credits_deducted", 0),
                            "services_used": [credit_result],
                            "extraction_method": "openai_direct"
                        }
                
                validated_data["_system_extraction_method"] = "openai_direct"
                return validated_data
            else:
                # OpenAI extracted data but failed validation
                fallback_reason = "openai_validation_failed"
                
                # Get specific validation failure reason
                total_validation = validated_data.get("System_TotalValidation", {})
                if total_validation.get("match") is False:
                    fallback_reason = "openai_total_mismatch"
                    openai_failure_details = f"LLM: {total_validation.get('llm_extracted_total')}, Xero: {total_validation.get('xero_total_amount')}"
                
                # Check for missing critical fields
                critical_fields = ["InvoiceNumber", "Date", "Total"]
                missing_fields = [field for field in critical_fields if not validated_data.get(field)]
                if missing_fields:
                    fallback_reason = "openai_missing_fields"
                    openai_failure_details = f"Missing: {', '.join(missing_fields)}"
                
                logger.info(f"OpenAI direct extraction failed validation for {file_name_for_logging}: {fallback_reason}")
        else:
            # OpenAI returned None/empty result
            fallback_reason = "openai_extraction_failed"
            openai_failure_details = "OpenAI returned null or empty result"
            logger.info(f"OpenAI direct extraction returned no data for {file_name_for_logging}")
        
        logger.info(f"Falling back to Mistral OCR for {file_name_for_logging} (reason: {fallback_reason})")
        mistral_api_key = get_secret("MISTRAL_API_KEY")
        if not mistral_api_key:
            logger.warning(f"Mistral API key not available for fallback processing of {file_name_for_logging}")
            return None
        
        if mime_type == "application/pdf":
            mistral_result = await asyncio.wait_for(
                process_pdf_with_mistral_ocr(
                    attachment_bytes, mistral_api_key, file_name_for_logging, entity_id
                ),
                timeout=180.0  # 3 minute timeout for Mistral PDF OCR
            )
        else:
            mistral_result = await asyncio.wait_for(
                process_image_with_mistral_ocr(
                    attachment_bytes, mistral_api_key, file_name_for_logging, entity_id
                ),
                timeout=120.0  # 2 minute timeout for Mistral image OCR
            )
        
        if mistral_result and mistral_result.get("text"):
            # Mistral OCR already charged 1 credit per page (includes OpenAI structured extraction)
            page_count = mistral_result.get("_system_billing_info", {}).get("page_count", 1)
            
            # OpenAI structured extraction after OCR: NO ADDITIONAL CHARGE
            openai_billing = []
            if entity_id:
                from drcr_shared_logic.document_processor import get_client_id_from_entity, check_and_deduct_credits
                client_id = await get_client_id_from_entity(entity_id)
                if client_id:
                    # This should charge 0 credits (included in Mistral OCR cost)
                    openai_credit_result = await check_and_deduct_credits(client_id, page_count, "openai", "structured_extraction")
                    openai_billing.append(openai_credit_result)
            
            structured_data = await asyncio.wait_for(
                extract_structured_data_with_openai(
                    mistral_result["text"], file_name_for_logging=file_name_for_logging
                ),
                timeout=90.0  # 90 second timeout for OpenAI structured extraction
            )
            if structured_data:
                validated_data = await asyncio.wait_for(
                    post_process_extracted_invoice_data(
                        structured_data, xero_transaction_data, file_name_for_logging
                    ),
                    timeout=30.0  # 30 second timeout for post-processing
                )
                if validated_data:
                    logger.info(f"Mistral OCR + OpenAI structured extraction successful for {file_name_for_logging}")
                    
                    # Combine billing information from both services
                    mistral_billing = mistral_result.get("_system_billing_info", {})
                    all_services = mistral_billing.get("services_used", []) + openai_billing
                    
                    combined_billing = {
                        "page_count": mistral_billing.get("page_count", 1),
                        "total_credits_deducted": sum(s.get("credits_deducted", 0) for s in all_services),
                        "services_used": all_services,
                        "extraction_method": "mistral_ocr_structured"
                    }
                    
                    validated_data["_system_extraction_method"] = "mistral_ocr_structured"
                    validated_data["_system_ocr_text"] = mistral_result["text"][:1000]
                    validated_data["_system_billing_info"] = combined_billing
                    
                    # Add fallback tracking information
                    validated_data["_system_fallback_info"] = {
                        "fallback_reason": fallback_reason,
                        "openai_attempt_made": openai_attempt_made,
                        "openai_failure_details": openai_failure_details
                    }
                    
                    return validated_data
        
        logger.warning(f"Both OpenAI direct and Mistral OCR failed for {file_name_for_logging}")
        return None
        
    except asyncio.TimeoutError:
        logger.error(f"LLM processing timeout for {file_name_for_logging}")
        return None
    except Exception as e_process:
        # If we hit an exception before trying OpenAI, record that
        if not openai_attempt_made:
            fallback_reason = "openai_api_failure"
            openai_failure_details = f"Exception before OpenAI attempt: {str(e_process)}"
        
        logger.error(f"Error in LLM processing for {file_name_for_logging}: {e_process}", exc_info=True)
        return None


async def fetch_and_process_attachments(
    xero_client: XeroApiClient,
    transaction_id: str,
    transaction_data: Dict[str, Any],
    entity_settings: Dict[str, Any],
    entity_id: str,
    db: firestore.AsyncClient,
    file_name_for_logging: str = "document",
    enable_concurrent_processing: bool = True
) -> List[Dict[str, Any]]:
    """
    Fetches attachments from Xero and processes them with LLM for data extraction.
    Now supports concurrent processing with Xero API rate limiting via semaphore.
    """
    processed_attachments = []
    
    if not entity_settings.get("enable_llm_prepayment_detection", True):
        logger.info(f"LLM prepayment detection disabled for entity {entity_id}")
        return processed_attachments
    
    try:
        document_type = transaction_data.get("Type", "")
        if document_type == "ACCPAY":
            xero_transaction_type = "Bills"
        elif document_type == "SPEND":
            xero_transaction_type = "BankTransactions"
        else:
            xero_transaction_type = "Invoices" 
        
        logger.info(f"Fetching attachments for {xero_transaction_type}/{transaction_id}")
        attachments = await xero_client.get_attachments(transaction_id, xero_transaction_type)
        
        if not attachments:
            logger.info(f"No attachments found for {xero_transaction_type}/{transaction_id}")
            return processed_attachments
        
        logger.info(f"Found {len(attachments)} attachments for {xero_transaction_type}/{transaction_id}")
        
        # Filter valid attachments first
        valid_attachments = []
        for attachment in attachments:
            attachment_id = attachment.get("AttachmentID")
            file_name = attachment.get("FileName", "unknown")
            mime_type = attachment.get("MimeType", "application/octet-stream")
            file_size = attachment.get("ContentLength", 0)
            
            # Size check
            max_size_mb = entity_settings.get("max_attachment_size_mb", 30)
            if file_size > max_size_mb * 1024 * 1024:
                logger.warning(f"Attachment {attachment_id} ({file_name}) is too large: {file_size} bytes")
                continue
            
            # Type check
            supported_types = entity_settings.get("supported_attachment_types", [
                "application/pdf", "image/jpeg", "image/png", "image/jpg"
            ])
            if mime_type not in supported_types:
                logger.info(f"Attachment {attachment_id} ({file_name}) type {mime_type} not supported for LLM processing")
                continue
                
            valid_attachments.append(attachment)
        
        if not valid_attachments:
            logger.info(f"No valid attachments to process for {xero_transaction_type}/{transaction_id}")
            return processed_attachments
        
        logger.info(f"Processing {len(valid_attachments)} valid attachments for {xero_transaction_type}/{transaction_id}")
        
        # Memory footprint safety check: limit concurrent processing for large attachment counts
        max_concurrent_attachments = entity_settings.get("max_concurrent_attachments", 10)
        max_total_bytes = entity_settings.get("max_concurrent_attachment_bytes", 200 * 1024 * 1024)  # 200MB default
        
        if len(valid_attachments) > max_concurrent_attachments:
            logger.warning(f"Large attachment count ({len(valid_attachments)}) exceeds max_concurrent_attachments ({max_concurrent_attachments}), falling back to sequential processing")
            enable_concurrent_processing = False
        
        # Check total file size for memory safety
        total_bytes = sum(attachment.get("ContentLength", 0) for attachment in valid_attachments)
        if total_bytes > max_total_bytes:
            logger.warning(f"Total attachment size ({total_bytes / 1024 / 1024:.1f} MB) exceeds max_concurrent_attachment_bytes ({max_total_bytes / 1024 / 1024:.1f} MB), falling back to sequential processing")
            enable_concurrent_processing = False
        
        # Process attachments concurrently or sequentially based on flag
        if enable_concurrent_processing and len(valid_attachments) > 1:
            # Concurrent processing with Xero API rate limiting (60/min)
            # One-slot semaphore + small sleep = guaranteed 60/min compliance
            xero_semaphore = asyncio.Semaphore(1)  # Serialize Xero downloads to respect rate limit
            
            async def process_single_attachment_concurrent(attachment):
                return await _process_single_attachment_with_semaphore(
                    xero_client, attachment, transaction_id, xero_transaction_type,
                    transaction_data, entity_settings, entity_id, file_name_for_logging,
                    xero_semaphore  # Don't pass db - we'll batch write later
                )
            
            # Process all attachments concurrently
            logger.info(f"[PERF] Starting concurrent processing of {len(valid_attachments)} attachments")
            concurrent_start = time.time()
            
            attachment_results = await asyncio.gather(
                *[process_single_attachment_concurrent(attachment) for attachment in valid_attachments],
                return_exceptions=True
            )
            
            concurrent_duration = time.time() - concurrent_start
            logger.info(f"[PERF] Completed concurrent attachment processing in {concurrent_duration:.2f}s")
            
            # Collect successful results and prepare for batch write
            attachment_docs_to_write = []
            for result in attachment_results:
                if isinstance(result, Exception):
                    logger.error(f"Attachment processing failed: {result}", exc_info=result)
                elif result and isinstance(result, tuple):
                    # Result is (lightweight_summary, attachment_doc) tuple
                    lightweight_summary, attachment_doc = result
                    processed_attachments.append(lightweight_summary)
                    attachment_docs_to_write.append(attachment_doc)
                elif result:
                    # Legacy single result format (fallback)
                    processed_attachments.append(result)
            
            # Batch write attachment documents to Firestore
            if attachment_docs_to_write:
                logger.info(f"[PERF] Batch writing {len(attachment_docs_to_write)} attachment documents to Firestore")
                batch_start = time.time()
                
                await _batch_write_attachments(db, attachment_docs_to_write)
                
                batch_duration = time.time() - batch_start
                logger.info(f"[PERF] Completed batch Firestore write in {batch_duration:.2f}s")
        else:
            # Sequential processing (original working pattern)
            logger.info(f"[PERF] Processing {len(valid_attachments)} attachments sequentially")
            for attachment in valid_attachments:
                try:
                    result = await _process_single_attachment_sequential(
                        xero_client, attachment, transaction_id, xero_transaction_type,
                        transaction_data, entity_settings, entity_id, file_name_for_logging, db
                    )
                    if result:
                        processed_attachments.append(result)
                except Exception as e_attachment:
                    logger.error(f"Error processing attachment {attachment.get('AttachmentID', 'unknown')}: {e_attachment}", exc_info=True)
        
        logger.info(f"Successfully processed {len(processed_attachments)} attachments for {xero_transaction_type}/{transaction_id}")
        
    except Exception as e_fetch:
        logger.error(f"Error fetching attachments for transaction {transaction_id}: {e_fetch}", exc_info=True)
    
    return processed_attachments


async def perform_combined_prepayment_analysis(
    transaction_data: Dict[str, Any],
    gl_prepayment_lines: List[Dict[str, Any]],
    processed_attachments: List[Dict[str, Any]],
    entity_settings: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Performs combined prepayment analysis using both GL coding and LLM extraction.
    Returns comprehensive prepayment analysis results.
    """
    analysis = {
        "gl_based_analysis_completed": True,
        "llm_based_analysis_completed": len(processed_attachments) > 0,
        "has_prepayment_line_items": len(gl_prepayment_lines) > 0,
        "detection_methods": [],
        "prepayment_line_items": [],
        "llm_detections": [],
        "confidence_score": 0.0,
        "recommended_action": "no_prepayment"
    }
    
    if gl_prepayment_lines:
        analysis["detection_methods"].append("GL_CODING")
        analysis["prepayment_line_items"] = [
            {
                "line_item_id": line.get("LineItemID"),
                "account_code": line.get("AccountCode"),
                "amount": line.get("LineAmount"),
                "description": line.get("Description"),
                "detection_method": "GL_CODING"
            } for line in gl_prepayment_lines
        ]
        analysis["confidence_score"] += 0.8
    
    llm_detected_prepayments = []
    best_service_period = None
    highest_confidence = 0.0
    
    for attachment_data in processed_attachments:
        if attachment_data.get("System_IsPrepayment"):
            llm_detected_prepayments.append({
                "extraction_method": attachment_data.get("_system_extraction_method"),
                "is_prepayment": attachment_data.get("System_IsPrepayment"),
                "prepayment_reason": attachment_data.get("System_PrepaymentReason"),
                "service_start_date": attachment_data.get("ExpectedServiceStartDate"),
                "service_end_date": attachment_data.get("ExpectedServiceEndDate"),
                "service_period_source": attachment_data.get("_system_servicePeriodSource"),
                "confidence": attachment_data.get("_system_servicePeriodInferenceConfidence", "unknown"),
                "total_validation": attachment_data.get("System_TotalValidation", {})
            })
            
            confidence_map = {"high": 0.9, "medium": 0.6, "low": 0.3}
            current_confidence = confidence_map.get(
                attachment_data.get("_system_servicePeriodInferenceConfidence", "low"), 0.3
            )
            
            if current_confidence > highest_confidence:
                highest_confidence = current_confidence
                best_service_period = {
                    "start_date": attachment_data.get("ExpectedServiceStartDate"),
                    "end_date": attachment_data.get("ExpectedServiceEndDate"),
                    "source": attachment_data.get("_system_servicePeriodSource"),
                    "confidence": attachment_data.get("_system_servicePeriodInferenceConfidence")
                }
    
    if llm_detected_prepayments:
        analysis["detection_methods"].append("LLM_ANALYSIS")
        analysis["llm_detections"] = llm_detected_prepayments
        analysis["llm_based_analysis_completed"] = True
        analysis["confidence_score"] += highest_confidence
        analysis["best_service_period"] = best_service_period
    
    if gl_prepayment_lines and llm_detected_prepayments:
        analysis["recommended_action"] = "create_amortization_schedule"
        analysis["confidence_score"] = min(analysis["confidence_score"], 1.0)
    elif gl_prepayment_lines:
        analysis["recommended_action"] = "create_amortization_schedule"
    elif llm_detected_prepayments and highest_confidence >= 0.6:
        # Create amortization schedule for high-confidence LLM detections
        analysis["recommended_action"] = "create_amortization_schedule"
    else:
        analysis["recommended_action"] = "no_prepayment"
    
    return analysis


async def _process_single_attachment_with_semaphore(
    xero_client: XeroApiClient,
    attachment: Dict[str, Any],
    transaction_id: str,
    xero_transaction_type: str,
    transaction_data: Dict[str, Any],
    entity_settings: Dict[str, Any],
    entity_id: str,
    file_name_for_logging: str,
    xero_semaphore: asyncio.Semaphore
) -> Optional[tuple]:
    """Process single attachment with concurrent rate limiting via semaphore."""
    attachment_id = attachment.get("AttachmentID")
    file_name = attachment.get("FileName", "unknown")
    mime_type = attachment.get("MimeType", "application/octet-stream")
    file_size = attachment.get("ContentLength", 0)
    
    try:
        logger.info(f"[CONCURRENT] Processing attachment {attachment_id} ({file_name})")
        
        # Download with semaphore rate limiting (intentionally blocks during sleep for rate limiting)
        async with xero_semaphore:
            logger.info(f"[CONCURRENT] Downloading attachment {attachment_id} ({file_name})")
            attachment_bytes = await xero_client.download_attachment(transaction_id, attachment_id, xero_transaction_type)
            
            if not attachment_bytes:
                logger.warning(f"Failed to download attachment {attachment_id}")
                return None
            
            # Enforce 60/min rate limit: 1.05s = 57/min with safety margin
            # Sleep inside semaphore intentionally serializes downloads while allowing concurrent LLM processing
            await asyncio.sleep(1.05)
        
        # LLM processing (not rate limited, can run concurrently)
        processed_data = await process_attachment_with_llm_fallback(
            attachment_bytes, mime_type, transaction_data, f"{file_name_for_logging}_{file_name}", entity_id
        )
        
        if not processed_data:
            logger.warning(f"Failed to process attachment {attachment_id} ({file_name}) with LLM")
            return None
        
        # GCS upload (can run concurrently)
        gcs_bucket = os.getenv("GCS_BUCKET_NAME", "drcr-attachments")
        gcs_path = f"attachments/{entity_id}/{transaction_id}/{attachment_id}"
        
        try:
            await _upload_to_gcs(gcs_bucket, gcs_path, attachment_bytes, mime_type)
            processed_data["gcs_path"] = gcs_path
            logger.info(f"[CONCURRENT] Successfully uploaded attachment {attachment_id} to GCS")
        except Exception as e_gcs:
            logger.error(f"Failed to upload attachment {attachment_id} to GCS: {e_gcs}")
            processed_data["gcs_path"] = None
        
        # Create Firestore document
        billing_info = processed_data.get("_system_billing_info", {})
        fallback_info = processed_data.get("_system_fallback_info", {})
        
        attachment_doc = {
            "transaction_id": transaction_id,
            "entity_id": entity_id,
            "source_system": "XERO",
            "external_id": attachment_id,
            "file_name": file_name,
            "mime_type": mime_type,
            "file_size": file_size,
            "gcs_path": processed_data.get("gcs_path"),
            "gcs_upload_status": "success" if processed_data.get("gcs_path") else "failed",
            "llm_processed": True,
            "llm_extraction_data": _create_lightweight_extraction_summary(processed_data),
            "page_count": billing_info.get("page_count", 1),
            "billing_summary": {
                "total_credits_deducted": billing_info.get("total_credits_deducted", 0),
                "services_used": billing_info.get("services_used", []),
                "extraction_method": billing_info.get("extraction_method", "unknown"),
                "billing_processed_at": firestore.SERVER_TIMESTAMP
            },
            "fallback_reason": fallback_info.get("fallback_reason"),
            "openai_attempt_made": fallback_info.get("openai_attempt_made", False),
            "openai_failure_details": fallback_info.get("openai_failure_details"),
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        
        # Prepare attachment document for batch writing
        attachment_firestore_id = get_attachment_firestore_id(entity_id, transaction_id, attachment_id)
        attachment_doc_with_id = {
            "firestore_id": attachment_firestore_id,
            "document": attachment_doc
        }
        
        # Return lightweight summary
        lightweight_summary = {
            "attachment_id": attachment_id,
            "System_IsPrepayment": processed_data.get("System_IsPrepayment"),
            "System_PrepaymentReason": processed_data.get("System_PrepaymentReason"),
            "ExpectedServiceStartDate": processed_data.get("ExpectedServiceStartDate"),
            "ExpectedServiceEndDate": processed_data.get("ExpectedServiceEndDate"),
            "_system_extraction_method": processed_data.get("_system_extraction_method"),
            "_system_servicePeriodSource": processed_data.get("_system_servicePeriodSource"),
            "_system_servicePeriodInferenceConfidence": processed_data.get("_system_servicePeriodInferenceConfidence"),
            "_system_billing_info": _truncate_billing_info(processed_data.get("_system_billing_info")),
            "_system_fallback_info": processed_data.get("_system_fallback_info"),
            "System_TotalValidation": {
                "match": processed_data.get("System_TotalValidation", {}).get("match"),
                "llm_extracted_total": processed_data.get("System_TotalValidation", {}).get("llm_extracted_total"), 
                "xero_total_amount": processed_data.get("System_TotalValidation", {}).get("xero_total_amount")
            } if processed_data.get("System_TotalValidation") else {}
        }
        
        logger.info(f"[CONCURRENT] Successfully processed attachment {attachment_id} ({file_name})")
        return (lightweight_summary, attachment_doc_with_id)
        
    except Exception as e:
        logger.error(f"[CONCURRENT] Error processing attachment {attachment_id}: {e}", exc_info=True)
        return None


async def _process_single_attachment_sequential(
    xero_client: XeroApiClient,
    attachment: Dict[str, Any],
    transaction_id: str,
    xero_transaction_type: str,
    transaction_data: Dict[str, Any],
    entity_settings: Dict[str, Any],
    entity_id: str,
    file_name_for_logging: str,
    db: firestore.AsyncClient
) -> Optional[Dict[str, Any]]:
    """Process single attachment sequentially (original working pattern)."""
    attachment_id = attachment.get("AttachmentID")
    file_name = attachment.get("FileName", "unknown")
    mime_type = attachment.get("MimeType", "application/octet-stream")
    file_size = attachment.get("ContentLength", 0)
    
    try:
        logger.info(f"Downloading attachment {attachment_id} ({file_name})")
        attachment_bytes = await xero_client.download_attachment(transaction_id, attachment_id, xero_transaction_type)
        
        if not attachment_bytes:
            logger.warning(f"Failed to download attachment {attachment_id}")
            return None
        
        # Rate limit buffer for Xero downloads (60/min limit) - prevents 429 errors
        await asyncio.sleep(1.05)  # Match concurrent path rate limiting
        
        processed_data = await process_attachment_with_llm_fallback(
            attachment_bytes, mime_type, transaction_data, f"{file_name_for_logging}_{file_name}", entity_id
        )
        
        if not processed_data:
            logger.warning(f"Failed to process attachment {attachment_id} ({file_name}) with LLM")
            return None
        
        # GCS upload
        gcs_bucket = os.getenv("GCS_BUCKET_NAME", "drcr-attachments")
        gcs_path = f"attachments/{entity_id}/{transaction_id}/{attachment_id}"
        
        try:
            await _upload_to_gcs(gcs_bucket, gcs_path, attachment_bytes, mime_type)
            processed_data["gcs_path"] = gcs_path
            logger.info(f"Successfully uploaded attachment {attachment_id} to GCS: gs://{gcs_bucket}/{gcs_path}")
        except Exception as e_gcs:
            logger.error(f"Failed to upload attachment {attachment_id} to GCS: {e_gcs}")
            processed_data["gcs_path"] = None
        
        # Create Firestore document
        billing_info = processed_data.get("_system_billing_info", {})
        fallback_info = processed_data.get("_system_fallback_info", {})
        
        attachment_doc = {
            "transaction_id": transaction_id,
            "entity_id": entity_id,
            "source_system": "XERO",
            "external_id": attachment_id,
            "file_name": file_name,
            "mime_type": mime_type,
            "file_size": file_size,
            "gcs_path": processed_data.get("gcs_path"),
            "gcs_upload_status": "success" if processed_data.get("gcs_path") else "failed",
            "llm_processed": True,
            "llm_extraction_data": _create_lightweight_extraction_summary(processed_data),
            "page_count": billing_info.get("page_count", 1),
            "billing_summary": {
                "total_credits_deducted": billing_info.get("total_credits_deducted", 0),
                "services_used": billing_info.get("services_used", []),
                "extraction_method": billing_info.get("extraction_method", "unknown"),
                "billing_processed_at": firestore.SERVER_TIMESTAMP
            },
            "fallback_reason": fallback_info.get("fallback_reason"),
            "openai_attempt_made": fallback_info.get("openai_attempt_made", False),
            "openai_failure_details": fallback_info.get("openai_failure_details"),
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        
        # Use collision-proof attachment ID
        attachment_firestore_id = get_attachment_firestore_id(entity_id, transaction_id, attachment_id)
        await db.collection("ATTACHMENTS").document(attachment_firestore_id).set(attachment_doc, merge=True)
        
        # Return lightweight summary
        lightweight_summary = {
            "attachment_id": attachment_id,
            "System_IsPrepayment": processed_data.get("System_IsPrepayment"),
            "System_PrepaymentReason": processed_data.get("System_PrepaymentReason"),
            "ExpectedServiceStartDate": processed_data.get("ExpectedServiceStartDate"),
            "ExpectedServiceEndDate": processed_data.get("ExpectedServiceEndDate"),
            "_system_extraction_method": processed_data.get("_system_extraction_method"),
            "_system_servicePeriodSource": processed_data.get("_system_servicePeriodSource"),
            "_system_servicePeriodInferenceConfidence": processed_data.get("_system_servicePeriodInferenceConfidence"),
            "_system_billing_info": _truncate_billing_info(processed_data.get("_system_billing_info")),
            "_system_fallback_info": processed_data.get("_system_fallback_info"),
            "System_TotalValidation": {
                "match": processed_data.get("System_TotalValidation", {}).get("match"),
                "llm_extracted_total": processed_data.get("System_TotalValidation", {}).get("llm_extracted_total"), 
                "xero_total_amount": processed_data.get("System_TotalValidation", {}).get("xero_total_amount")
            } if processed_data.get("System_TotalValidation") else {}
        }
        
        logger.info(f"Successfully processed attachment {attachment_id} ({file_name})")
        return lightweight_summary
        
    except Exception as e:
        logger.error(f"Error processing attachment {attachment_id}: {e}", exc_info=True)
        return None


async def _batch_write_attachments(db: firestore.AsyncClient, attachment_docs: List[Dict[str, Any]]):
    """Batch write attachment documents to Firestore with retry for transient failures."""
    if not attachment_docs:
        return
    
    from google.api_core import exceptions as api_exceptions
    import random
    
    batch_size = 500  # Firestore batch limit
    total_docs = len(attachment_docs)
    
    for i in range(0, total_docs, batch_size):
        batch_docs = attachment_docs[i:i + batch_size]
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                batch = db.batch()
                
                for doc_data in batch_docs:
                    firestore_id = doc_data["firestore_id"]
                    document = doc_data["document"]
                    
                    doc_ref = db.collection("ATTACHMENTS").document(firestore_id)
                    batch.set(doc_ref, document, merge=True)
                
                await batch.commit()
                logger.info(f"[BATCH] Committed {len(batch_docs)} attachment documents to Firestore")
                break  # Success, exit retry loop
                
            except (api_exceptions.Aborted, api_exceptions.DeadlineExceeded, api_exceptions.ServiceUnavailableError) as e:
                if attempt == max_retries - 1:
                    logger.error(f"[BATCH] Failed to batch write after {max_retries} attempts: {e}", exc_info=True)
                    raise
                
                # Exponential backoff with jitter
                delay = (2 ** attempt) + random.uniform(0, 1)
                logger.warning(f"[BATCH] Transient error, retrying in {delay:.2f}s (attempt {attempt + 1}/{max_retries}): {e}")
                await asyncio.sleep(delay)
                
            except Exception as e:
                logger.error(f"[BATCH] Non-retryable error in batch write: {e}", exc_info=True)
                raise
    
    logger.info(f"[BATCH] Successfully wrote {total_docs} attachment documents in batches")