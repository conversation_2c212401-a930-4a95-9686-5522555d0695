import os
import logging
from typing import Dict, List, Optional
from google.cloud import secretmanager

logger = logging.getLogger(__name__)

class SecretLoader:
    """
    Utility to load secrets from Google Cloud Secret Manager into environment variables
    at application startup. This provides security benefits of Secret Manager with
    cost efficiency of environment variables.
    """
    
    def __init__(self, project_id: Optional[str] = None):
        self.project_id = project_id or os.getenv("GCP_PROJECT_ID")
        if not self.project_id:
            raise ValueError("GCP_PROJECT_ID must be provided or set in environment")
        
        self.client = secretmanager.SecretManagerServiceClient()
        logger.info(f"SecretLoader initialized for project: {self.project_id}")
    
    def load_secret_to_env(self, secret_name: str, env_var_name: Optional[str] = None) -> bool:
        """
        Load a single secret from Secret Manager into an environment variable.
        
        Args:
            secret_name: Name of the secret in Secret Manager
            env_var_name: Environment variable name (defaults to secret_name in uppercase)
            
        Returns:
            True if successful, False otherwise
        """
        if not env_var_name:
            env_var_name = secret_name.upper().replace("-", "_")
        
        # Skip if already set in environment (allows local override)
        if os.getenv(env_var_name):
            logger.info(f"Environment variable {env_var_name} already set, skipping Secret Manager load")
            return True
        
        try:
            name = f"projects/{self.project_id}/secrets/{secret_name}/versions/latest"
            response = self.client.access_secret_version(request={"name": name})
            secret_value = response.payload.data.decode("UTF-8")
            
            os.environ[env_var_name] = secret_value
            logger.info(f"Successfully loaded secret '{secret_name}' into environment variable '{env_var_name}'")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load secret '{secret_name}': {e}")
            return False
    
    def load_secrets_to_env(self, secret_mappings: Dict[str, str]) -> Dict[str, bool]:
        """
        Load multiple secrets from Secret Manager into environment variables.
        
        Args:
            secret_mappings: Dict mapping secret_name -> env_var_name
            
        Returns:
            Dict mapping secret_name -> success_status
        """
        results = {}
        for secret_name, env_var_name in secret_mappings.items():
            results[secret_name] = self.load_secret_to_env(secret_name, env_var_name)
        
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        logger.info(f"Loaded {successful}/{total} secrets successfully")
        
        return results
    
    def load_production_secrets(self) -> bool:
        """
        Load all production secrets that should come from Secret Manager.
        Returns True if all critical secrets were loaded successfully.
        """
        # Define which secrets should be loaded from Secret Manager in production
        secret_mappings = {
            "xero-client-secret": "XERO_CLIENT_SECRET",
            "openai-api-key": "OPENAI_API_KEY", 
            "mistral-api-key": "MISTRAL_API_KEY",
            "token-encryption-key": "TOKEN_ENCRYPTION_KEY",
            "api-secret-key": "SECRET_KEY"
        }
        
        results = self.load_secrets_to_env(secret_mappings)
        
        # Check if critical secrets were loaded
        critical_secrets = ["xero-client-secret", "token-encryption-key"]
        critical_loaded = all(results.get(secret, False) for secret in critical_secrets)
        
        if not critical_loaded:
            logger.error("Failed to load one or more critical secrets")
            return False
        
        logger.info("All production secrets loaded successfully")
        return True


def load_secrets_at_startup(project_id: Optional[str] = None) -> bool:
    """
    Convenience function to load secrets at application startup.
    
    Args:
        project_id: GCP project ID (optional, will use GCP_PROJECT_ID env var if not provided)
        
    Returns:
        True if successful, False otherwise
    """
    try:
        loader = SecretLoader(project_id)
        return loader.load_production_secrets()
    except Exception as e:
        logger.error(f"Failed to initialize secret loading: {e}")
        return False 