"""
Bill Aggregation Service - Enhanced cost estimation for entity setup wizard
Provides compact aggregated bill data for interactive frontend filtering
"""
from typing import Dict, Any, List, Optional
import logging
import json
import os
from datetime import datetime, timedelta
from collections import defaultdict
from google.cloud import firestore

from drcr_shared_logic.clients.xero_client import XeroApiClient

logger = logging.getLogger(__name__)


class BillAggregationService:
    """Service for aggregating bill data for interactive cost estimation"""

    def __init__(self, db):
        self.db = db
        
        # Account classification configuration - Xero UK chart of accounts
        self.account_ranges = {
            "prepayment_candidates": [
                {"start": 620, "end": 629, "description": "Prepayments (Current Assets)"}
            ],
            "revenue_accounts": [
                {"start": 200, "end": 299, "description": "Revenue & Income"}
            ],
            "fixed_asset_accounts": [
                {"start": 710, "end": 799, "description": "Fixed Assets & Depreciation"}
            ],
            "bank_accounts": [
                {"start": 0, "end": 99, "description": "Bank Accounts (No specific range - named accounts)"}
            ],
            "current_assets": [
                {"start": 610, "end": 699, "description": "Current Assets (Debtors, Prepayments, Inventory)"}
            ],
            "classic_exclusions": [
                {"start": 493, "end": 494, "description": "Travel Expenses"},
                {"start": 404, "end": 404, "description": "Bank Fees"},
                {"start": 401, "end": 401, "description": "Audit & Accountancy Fees"},
                {"start": 441, "end": 441, "description": "Legal Expenses"},
                {"start": 412, "end": 412, "description": "Consulting"},
                {"start": 420, "end": 424, "description": "Entertainment Expenses"}
            ],
            "expense_accounts": [
                {"start": 310, "end": 329, "description": "Direct Costs"},
                {"start": 400, "end": 499, "description": "Overhead Expenses"},
                {"start": 500, "end": 509, "description": "Tax Expenses"}
            ],
            "current_liabilities": [
                {"start": 800, "end": 899, "description": "Current Liabilities"}
            ],
            "long_term_liabilities": [
                {"start": 900, "end": 929, "description": "Non-current Liabilities"}
            ],
            "equity_accounts": [
                {"start": 950, "end": 999, "description": "Equity & Retained Earnings"}
            ],
            # Fallback ranges for prepayment keyword search
            "current_assets_fallback": [
                {"start": 610, "end": 699, "description": "Current Assets (keyword search for prepaid/advance/deposit)"}
            ]
        }
        
        # Load from config file if available
        self.load_account_ranges_from_config()

    def update_account_ranges(self, new_ranges: Dict[str, List[Dict[str, Any]]]):
        """Update account classification ranges - useful for customization"""
        self.account_ranges.update(new_ranges)
        logger.info(f"Updated account ranges: {list(new_ranges.keys())}")

    def get_account_ranges(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get current account classification ranges"""
        return self.account_ranges.copy()
    
    def load_account_ranges_from_config(self, config_path: str = None):
        """Load account ranges from JSON configuration file"""
        if config_path is None:
            # Default to config file in same directory structure
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(os.path.dirname(current_dir), "config", "account_ranges.json")
        
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    if "account_classification_ranges" in config:
                        self.account_ranges = config["account_classification_ranges"]
                        logger.info(f"Loaded account ranges from {config_path}")
                    else:
                        logger.warning(f"Config file {config_path} missing 'account_classification_ranges' key")
            else:
                logger.warning(f"Account ranges config file not found at {config_path}, using defaults")
        except Exception as e:
            logger.error(f"Failed to load account ranges from {config_path}: {e}, using defaults")

    async def get_bill_aggregates_for_wizard(self, entity_id: str, date_range_months: int = 12, amount_threshold: float = 0) -> Dict[str, Any]:
        """
        Get aggregated bill data for wizard cost estimation
        Returns compact data suitable for frontend filtering
        """
        try:
            # Get entity data
            entity_ref = self.db.collection("ENTITIES").document(entity_id)
            entity_doc = await entity_ref.get()
            
            if not entity_doc.exists:
                raise ValueError("Entity not found")
            
            entity_data = entity_doc.to_dict()
            entity_type = entity_data.get("type")
            
            if entity_type != "xero":
                # For non-Xero entities, return basic structure
                return self._get_basic_aggregates(entity_data)
            
            # For Xero entities, get detailed aggregation
            return await self._aggregate_xero_bills(entity_id, entity_data, date_range_months, amount_threshold)
            
        except Exception as e:
            logger.error(f"Failed to get bill aggregates for entity {entity_id}: {e}")
            raise

    async def _aggregate_xero_bills(self, entity_id: str, entity_data: Dict[str, Any], date_range_months: int, amount_threshold: float = 0) -> Dict[str, Any]:
        """Aggregate bills from Xero API with enhanced categorization"""
        try:
            client_id = entity_data.get("client_id")
            
            # Initialize Xero client
            xero_client = await XeroApiClient.create(
                platform_org_id=entity_id,
                tenant_id=client_id
            )
            
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=date_range_months * 30)  # Approximate months
            
            # Get bills with minimal data for aggregation
            bills = await self._fetch_bills_for_aggregation(xero_client, start_date, end_date)
            logger.info(f"Fetched {len(bills)} bills for entity {entity_id}")
            
            # Get account classifications
            account_classifications = await self._get_enhanced_account_classifications(entity_id)
            logger.info(f"Account classifications: {[(k, len(v)) for k, v in account_classifications.items() if isinstance(v, list)]}")
            
            # Aggregate the bills data
            aggregates = self._process_bill_aggregates(bills, account_classifications, amount_threshold)
            logger.info(f"Aggregated data by month: {list(aggregates.keys())}")
            
            return {
                "aggregates": aggregates,
                "account_classifications": account_classifications,
                "date_range": {
                    "start_date": start_date.strftime("%Y-%m-%d"),
                    "end_date": end_date.strftime("%Y-%m-%d"),
                    "months": date_range_months
                },
                "total_bills": len(bills),
                "total_with_attachments": len([b for b in bills if b.get("HasAttachments")])
            }
            
        except Exception as e:
            logger.error(f"Failed to aggregate Xero bills: {e}")
            # Return basic structure on error
            return self._get_basic_aggregates(entity_data)

    async def _fetch_bills_for_aggregation(self, xero_client: XeroApiClient, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """Fetch bills with minimal data needed for aggregation"""
        try:
            # Format dates for Xero API using DateTime() function format
            start_date_str = f"DateTime({start_date.year},{start_date.month},{start_date.day})"
            end_date_str = f"DateTime({end_date.year},{end_date.month},{end_date.day})"
            
            # Query for bills in date range with proper Xero DateTime format
            where_clause = f'Type=="ACCPAY" AND Date>={start_date_str} AND Date<={end_date_str}'
            logger.info(f"Fetching bills with query: {where_clause}")
            
            # Fetch bills with minimal fields
            bills = await xero_client.get_records(
                record_type="Invoices",
                where_filter=where_clause,
                order="Date DESC"
            )
            logger.info(f"Xero API returned {len(bills) if bills else 0} raw bills")
            
            # If no bills returned, try a simpler query
            if not bills:
                logger.warning("No bills found with date filter, trying without date filter...")
                try:
                    simple_bills = await xero_client.get_records(
                        record_type="Invoices",
                        where_filter='Type=="ACCPAY"',
                        order="Date DESC"
                    )
                    logger.info(f"Total ACCPAY bills in system: {len(simple_bills) if simple_bills else 0}")
                    if simple_bills and len(simple_bills) > 0:
                        latest_bill = simple_bills[0]
                        logger.info(f"Latest bill date: {latest_bill.get('Date')}, Amount: {latest_bill.get('Total')}")
                except Exception as e:
                    logger.error(f"Failed to fetch simple bills: {e}")
            
            # Extract only needed fields for aggregation
            processed_bills = []
            for bill in bills if bills else []:
                processed_bill = {
                    "InvoiceID": bill.get("InvoiceID"),
                    "Date": bill.get("Date"),
                    "Contact": bill.get("Contact", {}).get("Name", "Unknown"),
                    "Total": float(bill.get("Total", 0)),
                    "HasAttachments": bill.get("HasAttachments", False),
                    "LineItems": []
                }
                
                # Process line items for account codes
                for line_item in bill.get("LineItems", []):
                    if line_item.get("AccountCode"):
                        processed_bill["LineItems"].append({
                            "AccountCode": line_item.get("AccountCode"),
                            "LineAmount": float(line_item.get("LineAmount", 0)),
                            "Description": line_item.get("Description", "")
                        })
                
                processed_bills.append(processed_bill)
            
            logger.info(f"Fetched {len(processed_bills)} bills for aggregation")
            if processed_bills:
                bills_with_attachments = len([b for b in processed_bills if b.get("HasAttachments")])
                logger.info(f"Bills with attachments: {bills_with_attachments}/{len(processed_bills)}")
                # Log some sample account codes
                sample_account_codes = set()
                for bill in processed_bills[:5]:  # First 5 bills
                    for line_item in bill.get("LineItems", []):
                        if line_item.get("AccountCode"):
                            sample_account_codes.add(line_item.get("AccountCode"))
                logger.info(f"Sample account codes found: {list(sample_account_codes)}")
            return processed_bills
            
        except Exception as e:
            logger.warning(f"Failed to fetch bills for aggregation: {e}")
            return []

    async def _get_enhanced_account_classifications(self, entity_id: str) -> Dict[str, Any]:
        """Get enhanced account classifications for filtering"""
        try:
            # Query synced chart of accounts
            accounts_query = self.db.collection("CHART_OF_ACCOUNTS").where(
                filter=firestore.FieldFilter("entity_id", "==", entity_id)
            )
            accounts_docs = await accounts_query.get()
            
            classifications = {
                "prepayment_candidates": [],
                "exclude_recommended": [],
                "classic_exclusions": [],
                "revenue_accounts": [],
                "fixed_asset_accounts": [],
                "bank_accounts": [],
                "expense_accounts": [],
                "current_assets": [],
                "current_liabilities": [],
                "long_term_liabilities": [],
                "equity_accounts": [],
                "all_accounts": {}
            }
            
            for doc in accounts_docs:
                account = doc.to_dict()
                account_code = account.get("code")
                account_name = account.get("name", "").lower()
                account_type = account.get("type", "").upper()
                account_class = account.get("class", "").upper()
                
                # Store basic account info
                classifications["all_accounts"][account_code] = {
                    "name": account.get("name"),
                    "type": account_type,
                    "class": account_class
                }
                
                # Classify accounts for filtering
                self._classify_account(account, classifications)
            
            return classifications
            
        except Exception as e:
            logger.warning(f"Failed to get enhanced account classifications: {e}")
            return {
                "prepayment_candidates": [],
                "exclude_recommended": [],
                "classic_exclusions": [],
                "revenue_accounts": [],
                "fixed_asset_accounts": [],
                "bank_accounts": [],
                "expense_accounts": [],
                "current_assets": [],
                "current_liabilities": [],
                "long_term_liabilities": [],
                "equity_accounts": [],
                "all_accounts": {}
            }

    def _classify_account(self, account: Dict[str, Any], classifications: Dict[str, List]):
        """Classify account into appropriate categories based on configurable account code ranges"""
        account_code = account.get("code", "")
        account_name = account.get("name", "").lower()
        account_type = account.get("type", "").upper()
        account_class = account.get("class", "").upper()
        
        # Extract numeric part of account code for range checking
        numeric_code = self._extract_account_number(account_code)
        
        classified = False
        
        if numeric_code:
            # Check each category's configured ranges
            for category, ranges in self.account_ranges.items():
                if category == "current_assets_fallback":
                    continue  # Handle this separately
                    
                for range_config in ranges:
                    if range_config["start"] <= numeric_code <= range_config["end"]:
                        # Handle overlapping ranges - classic_exclusions takes priority over expense_accounts
                        if category == "classic_exclusions":
                            # Remove from expense_accounts if already added
                            if account_code in classifications["expense_accounts"]:
                                classifications["expense_accounts"].remove(account_code)
                            classifications[category].append(account_code)
                            classified = True
                            break
                        elif category != "expense_accounts" or not classified:
                            classifications[category].append(account_code)
                            classified = True
                            break
                
                if classified:
                    break
            
            # Special handling for current assets fallback with keyword search
            if not classified:
                for range_config in self.account_ranges["current_assets_fallback"]:
                    if range_config["start"] <= numeric_code <= range_config["end"]:
                        # Check for prepayment keywords in current assets
                        if any(keyword in account_name for keyword in [
                            "prepaid", "prepayment", "advance", "deposit", "deferred"
                        ]):
                            classifications["prepayment_candidates"].append(account_code)
                            classified = True
                            break
        
        # Fallback to account type if no numeric code found or not classified by ranges
        if not classified:
            self._classify_by_account_type(account, classifications, account_code, account_name, account_type, account_class)
    
    def _extract_account_number(self, account_code: str) -> Optional[int]:
        """Extract numeric part from account code"""
        try:
            import re
            # Handle various formats: "1400", "1400-01", "PREPAID-1400", etc.
            code_match = re.search(r'(\d{3,4})', str(account_code))
            if code_match:
                return int(code_match.group(1))
        except (ValueError, AttributeError):
            pass
        return None
    
    def _classify_by_account_type(self, account: Dict[str, Any], classifications: Dict[str, List], 
                                  account_code: str, account_name: str, account_type: str, account_class: str):
        """Fallback classification using Xero account types"""
        if account_type in ["REVENUE", "INCOME"] or account_class == "REVENUE":
            classifications["revenue_accounts"].append(account_code)
        elif account_type in ["FIXED", "FIXEDASSET"]:
            classifications["fixed_asset_accounts"].append(account_code)
        elif account_type in ["BANK", "CURRENT"]:
            classifications["bank_accounts"].append(account_code)
        elif account_type in ["EXPENSE", "OVERHEADS"] or account_class == "EXPENSE":
            # Final keyword check for travel/entertainment if not already classified
            if any(keyword in account_name for keyword in [
                "travel", "entertainment", "bank fee", "interest", "meal", "parking"
            ]):
                classifications["classic_exclusions"].append(account_code)
            else:
                classifications["expense_accounts"].append(account_code)
        elif account_type in ["ASSET", "CURRENTASSET"]:
            # Check for prepayment keywords in asset accounts
            if any(keyword in account_name for keyword in [
                "prepaid", "prepayment", "advance", "deposit", "deferred"
            ]):
                classifications["prepayment_candidates"].append(account_code)

    def _process_bill_aggregates(self, bills: List[Dict[str, Any]], account_classifications: Dict[str, Any], amount_threshold: float = 0) -> Dict[str, Any]:
        """Process bills into monthly aggregates"""
        aggregates = defaultdict(lambda: {
            "by_account": defaultdict(lambda: {"bills": 0, "with_attachments": 0, "total_amount": 0}),
            "by_supplier": defaultdict(lambda: {"bills": 0, "with_attachments": 0, "total_amount": 0}),
            "total_bills": 0,
            "total_with_attachments": 0,
            "total_amount": 0
        })
        
        for bill in bills:
            # Parse date to get month key
            bill_date = bill.get("Date", "")
            if isinstance(bill_date, str):
                try:
                    # Handle Xero's Microsoft date format: /Date(*************+0000)/
                    if bill_date.startswith('/Date(') and bill_date.endswith(')/'):
                        # Extract timestamp from /Date(*************+0000)/
                        timestamp_str = bill_date[6:-2]  # Remove /Date( and )/
                        # Remove timezone offset if present
                        if '+' in timestamp_str or '-' in timestamp_str:
                            timestamp_str = timestamp_str.split('+')[0].split('-')[0]
                        timestamp_ms = int(timestamp_str)
                        # Convert from milliseconds to seconds
                        date_obj = datetime.fromtimestamp(timestamp_ms / 1000)
                    else:
                        # Try ISO format as fallback
                        date_obj = datetime.fromisoformat(bill_date.replace('Z', '+00:00'))
                    
                    month_key = date_obj.strftime("%Y-%m")
                except Exception as e:
                    logger.warning(f"Failed to parse bill date '{bill_date}': {e}")
                    continue  # Skip bills with invalid dates
            else:
                continue
            
            # Filter by amount threshold
            bill_total = float(bill.get("Total", 0))
            if amount_threshold > 0 and bill_total < amount_threshold:
                continue  # Skip bills below threshold
            
            # Basic aggregation
            month_data = aggregates[month_key]
            month_data["total_bills"] += 1
            month_data["total_amount"] += bill.get("Total", 0)
            
            if bill.get("HasAttachments"):
                month_data["total_with_attachments"] += 1
            
            # Aggregate by supplier
            supplier = bill.get("Contact", "Unknown")
            supplier_data = month_data["by_supplier"][supplier]
            supplier_data["bills"] += 1
            supplier_data["total_amount"] += bill.get("Total", 0)
            if bill.get("HasAttachments"):
                supplier_data["with_attachments"] += 1
            
            # Aggregate by account code
            for line_item in bill.get("LineItems", []):
                account_code = line_item.get("AccountCode")
                if account_code:
                    account_data = month_data["by_account"][account_code]
                    account_data["bills"] += 1
                    account_data["total_amount"] += line_item.get("LineAmount", 0)
                    
                    # Count attachment at line item level if bill has attachments
                    if bill.get("HasAttachments"):
                        account_data["with_attachments"] += 1
        
        # Convert defaultdicts to regular dicts for JSON serialization
        return {month: {
            "by_account": dict(data["by_account"]),
            "by_supplier": dict(data["by_supplier"]),
            "total_bills": data["total_bills"],
            "total_with_attachments": data["total_with_attachments"],
            "total_amount": data["total_amount"]
        } for month, data in aggregates.items()}

    def _get_basic_aggregates(self, entity_data: Dict[str, Any]) -> Dict[str, Any]:
        """Return basic aggregate structure for non-Xero entities"""
        return {
            "aggregates": {},
            "account_classifications": {
                "prepayment_candidates": [],
                "exclude_recommended": [],
                "classic_exclusions": [],
                "revenue_accounts": [],
                "fixed_asset_accounts": [],
                "bank_accounts": [],
                "expense_accounts": [],
                "all_accounts": {}
            },
            "date_range": {
                "start_date": datetime.now().strftime("%Y-%m-%d"),
                "end_date": datetime.now().strftime("%Y-%m-%d"),
                "months": 0
            },
            "total_bills": 0,
            "total_with_attachments": 0
        }