# Utility Scripts

This directory contains utility scripts for development, configuration, and maintenance.

## Files

- `generate_encryption_key.py` - Generate Fernet encryption keys for token storage
- `fix_xero_config.py` - Fix and validate Xero API configuration
- `generate_token.py` - Generate Firebase authentication tokens for testing
- `firebase_id_token_clean.txt` - Clean Firebase ID token for testing

## Usage

### Generate Encryption Key
```bash
python scripts/utilities/generate_encryption_key.py
```
Use the generated key in your `.env` file as `TOKEN_ENCRYPTION_KEY`.

### Fix Xero Configuration
```bash
python scripts/utilities/fix_xero_config.py
```
Validates and fixes common Xero API configuration issues.

### Generate Test Token
```bash
python scripts/utilities/generate_token.py
```
Generates Firebase authentication tokens for API testing.

## Security Notes

- **Never commit encryption keys** to version control
- Store sensitive tokens in environment variables or secure storage
- Rotate encryption keys periodically in production
- Use test tokens only in development environments

## Environment Variables Required

- `FIREBASE_CREDENTIALS_PATH` - Path to Firebase service account key
- `TOKEN_ENCRYPTION_KEY` - Fernet encryption key for token storage
- `XERO_CLIENT_ID` - Xero API client ID
- `XERO_CLIENT_SECRET` - Xero API client secret 