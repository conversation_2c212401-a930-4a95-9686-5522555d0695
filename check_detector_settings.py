#!/usr/bin/env python3
"""
Check if prepayment detector is enabled for entity
"""
import asyncio
import os
import sys

# Add project root to path
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Set environment
if not os.getenv("GOOGLE_CLOUD_PROJECT"):
    os.environ["GOOGLE_CLOUD_PROJECT"] = "drcr-d660a"

from google.cloud import firestore

async def check_detector_settings():
    """Check if detector is enabled for the test entity"""
    
    db = firestore.AsyncClient()
    entity_id = "8ead108d-f6a2-41e4-b2a8-962024248a66"
    
    print(f"=== Checking Detector Settings for Entity {entity_id} ===")
    
    # Check ENTITY_SETTINGS
    entity_settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
    entity_settings_doc = await entity_settings_ref.get()
    
    if entity_settings_doc.exists:
        settings = entity_settings_doc.to_dict()
        detector_enabled = settings.get("enable_prepayment_release_detector", "NOT_SET")
        print(f"ENTITY_SETTINGS.enable_prepayment_release_detector: {detector_enabled}")
    else:
        print("ENTITY_SETTINGS document does not exist")
    
    # Check ENTITIES.settings
    entity_ref = db.collection("ENTITIES").document(entity_id)
    entity_doc = await entity_ref.get()
    
    if entity_doc.exists:
        entity_data = entity_doc.to_dict()
        settings = entity_data.get("settings", {})
        detector_enabled = settings.get("enable_prepayment_release_detector", "NOT_SET")
        print(f"ENTITIES.settings.enable_prepayment_release_detector: {detector_enabled}")
    else:
        print("ENTITIES document does not exist")
    
    # Check recent sync jobs
    print(f"\n=== Recent Sync Jobs ===")
    sync_jobs_query = db.collection("SYNC_JOBS").where(
        "entity_id", "==", entity_id
    ).order_by("started_at", direction=firestore.Query.DESCENDING).limit(3)
    
    async for job_doc in sync_jobs_query.stream():
        job_data = job_doc.to_dict()
        job_id = job_doc.id
        detector_detections = job_data.get("detector_detections", "NOT_SET")
        detector_completed = job_data.get("detector_completed_at", "NOT_SET")
        detector_error = job_data.get("detector_error", "NONE")
        status = job_data.get("status", "UNKNOWN")
        
        print(f"Job {job_id[:8]}: status={status}, detector_detections={detector_detections}, completed_at={detector_completed}, error={detector_error}")

if __name__ == "__main__":
    asyncio.run(check_detector_settings())