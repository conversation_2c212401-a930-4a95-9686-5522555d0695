#!/usr/bin/env python3
"""
Regenerate KSP schedule with new hybrid amortization logic
"""

import asyncio
import os
import sys
from datetime import datetime, date
from google.cloud import firestore

# Add project root to path
project_root = os.path.abspath('.')
if project_root not in sys.path:
    sys.path.insert(0, project_root)

async def regenerate_ksp_schedule():
    print("Starting KSP schedule regeneration...")
    db = firestore.AsyncClient()
    print("Firestore client created")
    
    # KSP transaction details
    transaction_id = "60d8b891-aabb-4dc4-9cc9-5563eb68e8ca"
    old_schedule_id = "685ccf33-31eb-4f50-94ff-50085bbb7af4"
    entity_id = "a8e46b01-de7d-42bb-ab01-d1a395573d51"
    
    print(f"Regenerating KSP schedule...")
    print(f"Transaction ID: {transaction_id}")
    print(f"Old Schedule ID: {old_schedule_id}")
    print(f"Entity ID: {entity_id}")
    
    # First, check if the old schedule exists
    old_schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(old_schedule_id)
    old_schedule_doc = await old_schedule_ref.get()
    
    if old_schedule_doc.exists:
        old_data = old_schedule_doc.to_dict()
        print(f"\nOld schedule found:")
        print(f"  Amount: €{old_data.get('originalAmount')}")
        print(f"  Periods: {old_data.get('numberOfPeriods')}")
        print(f"  Start: {old_data.get('amortizationStartDate')}")
        print(f"  End: {old_data.get('amortizationEndDate')}")
        print(f"  Monthly entries: {len(old_data.get('monthlyEntries', []))}")
        
        # Delete the old schedule
        await old_schedule_ref.delete()
        print(f"✅ Deleted old schedule {old_schedule_id}")
    else:
        print(f"⚠️  Old schedule {old_schedule_id} not found")
    
    # Now trigger regeneration by calling the cloud function logic
    # We'll simulate the LLM detection that created the original schedule
    
    # Get entity settings to check materiality threshold
    entity_ref = db.collection('ENTITY_SETTINGS').document(entity_id)
    entity_doc = await entity_ref.get()
    entity_settings = entity_doc.to_dict() if entity_doc.exists else {}
    
    materiality_threshold = entity_settings.get("amortization_materiality_threshold", 1000.0)
    print(f"\nEntity materiality threshold: €{materiality_threshold}")
    
    # KSP bill details
    amount = 79.5
    start_date = date(2025, 5, 8)
    end_date = date(2026, 5, 7)
    
    print(f"\nKSP bill details:")
    print(f"  Amount: €{amount}")
    print(f"  Start: {start_date}")
    print(f"  End: {end_date}")
    print(f"  Method: {'Day-based' if amount >= materiality_threshold else '12-month equal'}")
    
    # Import the schedule generation function
    try:
        from cloud_functions.xero_sync_consumer.main import _generate_and_save_amortization_schedule
        
        # Simulate the line item data
        line_item_data = {
            "LineItemID": "52baf216-504d-4ead-abb7-b2dee1e74959",
            "Description": "One-year Premium membership",
            "LineAmount": amount,
            "AccountCode": "447"  # Original account code from bill
        }
        
        # Simulate parent invoice data
        parent_invoice_data = {
            "InvoiceID": transaction_id,
            "InvoiceNumber": "PRM225050000279",
            "Contact": {"Name": "KSP Rechtsanwalt"}
        }
        
        # Generate new schedule with hybrid logic
        new_schedule_id = await _generate_and_save_amortization_schedule(
            db=db,
            invoice_id=transaction_id,
            line_item_data=line_item_data,
            master_period_start_date=start_date,
            master_period_end_date=end_date,
            entity_settings=entity_settings,
            client_id="49687fc2-556d-4116-b441-505771881a01",
            entity_id=entity_id,
            parent_invoice_data=parent_invoice_data,
            llm_service_start_date=start_date.isoformat(),
            llm_service_end_date=end_date.isoformat(),
            is_llm_detected=True
        )
        
        if new_schedule_id:
            print(f"✅ Generated new schedule: {new_schedule_id}")
            print(f"📋 Old schedule ID: {old_schedule_id}")
            print(f"🆕 New schedule ID: {new_schedule_id}")
            
            # Verify the new schedule
            new_schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(new_schedule_id)
            new_schedule_doc = await new_schedule_ref.get()
            
            if new_schedule_doc.exists:
                new_data = new_schedule_doc.to_dict()
                print(f"\nNew schedule details:")
                print(f"  Amount: €{new_data.get('originalAmount')}")
                print(f"  Periods: {new_data.get('numberOfPeriods')}")
                print(f"  Start: {new_data.get('amortizationStartDate')}")
                print(f"  End: {new_data.get('amortizationEndDate')}")
                print(f"  Monthly entries: {len(new_data.get('monthlyEntries', []))}")
                
                # Show first few monthly entries
                monthly_entries = new_data.get('monthlyEntries', [])
                print(f"\nFirst 3 monthly entries:")
                for i, entry in enumerate(monthly_entries[:3]):
                    month_date = entry.get('monthDate')
                    amount = entry.get('amount')
                    print(f"  {i+1}. {month_date} - €{amount}")
                
                if len(monthly_entries) > 3:
                    print(f"  ... and {len(monthly_entries) - 3} more entries")
                
                # Verify total
                total_amount = sum(entry.get('amount', 0) for entry in monthly_entries)
                print(f"\nTotal distributed: €{total_amount}")
                print(f"Original amount:   €{new_data.get('originalAmount')}")
                
                if abs(total_amount - new_data.get('originalAmount', 0)) < 0.01:
                    print("✅ Amounts match!")
                else:
                    print("⚠️  Amount mismatch!")
                
                # Compare with old schedule
                if old_schedule_doc.exists:
                    print(f"\n📊 Comparison:")
                    print(f"  Old periods: {old_data.get('numberOfPeriods')} vs New periods: {new_data.get('numberOfPeriods')}")
                    old_entries = old_data.get('monthlyEntries', [])
                    new_entries = new_data.get('monthlyEntries', [])
                    print(f"  Old entries: {len(old_entries)} vs New entries: {len(new_entries)}")
                    
                    if len(old_entries) > 0 and len(new_entries) > 0:
                        old_first_amount = old_entries[0].get('amount', 0)
                        new_first_amount = new_entries[0].get('amount', 0)
                        print(f"  First month amount: €{old_first_amount} vs €{new_first_amount}")
                        
                        if len(old_entries) == len(new_entries):
                            amounts_same = all(
                                abs(old_entries[i].get('amount', 0) - new_entries[i].get('amount', 0)) < 0.01
                                for i in range(len(old_entries))
                            )
                            print(f"  Monthly amounts identical: {'✅ Yes' if amounts_same else '❌ No'}")
                        else:
                            print(f"  Different number of entries - amounts will differ")
            else:
                print(f"❌ Could not verify new schedule {new_schedule_id}")
        else:
            print("❌ Failed to generate new schedule")
            
    except Exception as e:
        print(f"❌ Error generating schedule: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(regenerate_ksp_schedule()) 