# Confirm & Skip Schedule Actions - Test Plan

## Overview
This document outlines the comprehensive testing plan for the confirm and skip schedule actions in the prepayments dashboard.

## Test Scenarios

### 1. Confirm Schedule Action

#### 1.1 Valid Confirmations
- **Test**: Confirm schedule from `pending_configuration` status
- **Expected**: Status updates to `confirmed`, success toast appears, button shows loading spinner during action
- **Backend**: Creates Xero journals for due entries

- **Test**: Confirm schedule from `pending_review` status  
- **Expected**: Status updates to `confirmed`, success toast appears, data refreshes without page reload

- **Test**: Confirm schedule from `pending_confirmation` status
- **Expected**: Status updates to `confirmed`, success toast appears, journals posted to Xero

#### 1.2 Invalid Confirmations
- **Test**: Attempt to confirm schedule missing account codes
- **Expected**: Backend validation error, error toast with helpful message

- **Test**: Attempt to confirm already confirmed schedule
- **Expected**: Backend validation error, clear error message about current status

- **Test**: Network failure during confirmation
- **Expected**: Error toast appears, retry option provided, button returns to normal state

#### 1.3 Loading States
- **Test**: Click confirm button
- **Expected**: <PERSON><PERSON> shows spinner immediately, becomes disabled, loading toast appears

- **Test**: Multiple rapid clicks on confirm button
- **Expected**: Only one API call is made, button remains disabled until completion

### 2. Skip Schedule Action

#### 2.1 Valid Skips
- **Test**: Skip schedule from `pending_configuration` status
- **Expected**: Confirmation dialog appears with warning icon, skip with reason after confirmation

- **Test**: Skip schedule from `pending_review` status
- **Expected**: Confirmation dialog appears, status updates to `skipped` after confirmation

- **Test**: Skip schedule from `pending_confirmation` status  
- **Expected**: Confirmation dialog appears, status updates to `skipped` after confirmation

#### 2.2 Skip Confirmation Dialog
- **Test**: Click skip button
- **Expected**: Warning dialog appears with orange warning icon, "Skip Schedule" title, destructive styling

- **Test**: Click cancel in skip dialog
- **Expected**: Dialog closes, no API call made, schedule status unchanged

- **Test**: Click confirm in skip dialog
- **Expected**: API call made with default reason, loading state in dialog, success toast on completion

#### 2.3 Invalid Skips
- **Test**: Attempt to skip already posted schedule
- **Expected**: Backend validation error, error toast with clear message

- **Test**: Network failure during skip
- **Expected**: Error toast appears, dialog stays open for retry, button returns to normal state

### 3. User Experience

#### 3.1 Visual Feedback
- **Test**: Loading states for buttons
- **Expected**: Spinner icons replace action icons, buttons become disabled

- **Test**: Toast notifications
- **Expected**: Success toasts are green, error toasts are red, loading toasts show spinner

- **Test**: Confirmation dialogs
- **Expected**: Warning icon for destructive actions, proper button styling, loading states

#### 3.2 Data Refresh
- **Test**: Successful confirm/skip action
- **Expected**: Dashboard data refreshes without page reload, status updates visible immediately

- **Test**: Multiple schedules on same page
- **Expected**: Only the acted-upon schedule updates, other schedules remain unchanged

#### 3.3 Error Recovery
- **Test**: Error during action
- **Expected**: Clear error message with suggested next steps, option to retry

- **Test**: Validation errors
- **Expected**: Specific error message explaining what needs to be fixed

### 4. Bulk Actions

#### 4.1 Invoice-Level Actions
- **Test**: Bulk confirm all schedules for an invoice
- **Expected**: Placeholder toast notification (feature coming soon)

- **Test**: Bulk skip all schedules for an invoice
- **Expected**: Placeholder toast notification (feature coming soon)

#### 4.2 Supplier-Level Actions
- **Test**: Bulk confirm all schedules for a supplier
- **Expected**: Placeholder toast notification (feature coming soon)

### 5. Edge Cases

#### 5.1 Concurrent Actions
- **Test**: Multiple users acting on same schedule
- **Expected**: Backend handles concurrency, appropriate error messages

#### 5.2 Status Changes During Action
- **Test**: Schedule status changes while user has page open
- **Expected**: Action fails gracefully with appropriate error message

#### 5.3 Permission Issues
- **Test**: User loses access during action
- **Expected**: Clear error message about permission issue

### 6. Integration Testing

#### 6.1 Backend Integration
- **Test**: Confirm action creates Xero journals
- **Expected**: Journals appear in Xero with correct account codes and amounts

- **Test**: Skip action records audit trail
- **Expected**: Skip reason and user recorded in backend audit logs

#### 6.2 Status Transitions
- **Test**: All valid status transitions work
- **Expected**: Status updates correctly in both frontend and backend

- **Test**: Invalid status transitions blocked
- **Expected**: Clear error messages for invalid transitions

### 7. Performance Testing

#### 7.1 Response Times
- **Test**: Action response times under normal load
- **Expected**: Actions complete within 2-3 seconds, loading states provide feedback

#### 7.2 Large Datasets
- **Test**: Actions on schedules with many monthly entries
- **Expected**: Performance remains acceptable, progress feedback provided

## Test Data Requirements

### Schedule Statuses Needed
- Schedules in `pending_configuration` status (LLM-detected)
- Schedules in `pending_review` status (ready for review)
- Schedules in `pending_confirmation` status (ready for final confirmation)
- Schedules in `confirmed` status (for negative testing)
- Schedules in `posted` status (for negative testing)

### Account Configuration
- Schedules with complete account codes
- Schedules missing expense account codes
- Schedules missing amortization account codes
- Schedules with invalid account codes

### Network Conditions
- Normal network conditions
- Slow network conditions
- Network failure scenarios
- Intermittent connectivity

## Success Criteria

✅ **Functional Requirements**
- All valid status transitions work correctly
- Invalid actions are properly blocked with clear messages
- Backend integration creates correct journal entries

✅ **User Experience Requirements**  
- Loading states provide immediate feedback
- Error messages are clear and actionable
- No page reloads required for data updates
- Confirmation dialogs prevent accidental actions

✅ **Performance Requirements**
- Actions complete within 3 seconds under normal conditions
- UI remains responsive during API calls
- Bulk operations show progress feedback

✅ **Reliability Requirements**
- Network failures are handled gracefully
- Concurrent actions don't corrupt data
- Error recovery allows users to retry failed actions

## Test Environment Setup

### Frontend Testing
- React development server running
- Sonner toast notifications configured
- shadcn/ui components available
- Firebase authentication working

### Backend Testing
- FastAPI server running with schedule endpoints
- Firestore database with test schedule data
- Xero integration configured for journal creation
- Proper user permissions configured

### Test Users
- User with `firm_admin` role
- User with `client_user` role  
- User with limited permissions
- User with expired session

## Known Limitations

1. **Skip Reason**: Currently uses default reason instead of user input
2. **Bulk Actions**: Not yet implemented (placeholder notifications)
3. **Real-time Updates**: Status changes by other users not reflected in real-time
4. **Offline Support**: No offline action queuing

## Future Enhancements

1. **Enhanced Skip Reason**: Modal dialog for custom skip reasons
2. **Bulk Action Implementation**: Full bulk confirm/skip functionality
3. **Real-time Status Sync**: WebSocket updates for concurrent user actions
4. **Action History**: Detailed audit trail in UI
5. **Undo Support**: Allow undoing recent actions within time window