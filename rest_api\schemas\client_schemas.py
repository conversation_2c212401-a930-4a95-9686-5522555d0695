from pydantic import BaseModel, Field, EmailStr, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class ClientStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING = "pending"

class ClientType(str, Enum):
    CORPORATION = "corporation"
    LLC = "llc"
    PARTNERSHIP = "partnership"
    SOLE_PROPRIETORSHIP = "sole_proprietorship"
    NON_PROFIT = "non_profit"
    OTHER = "other"

class ClientSize(str, Enum):
    SMALL = "small"  # 1-10 employees
    MEDIUM = "medium"  # 11-50 employees
    LARGE = "large"  # 51+ employees

class ContactInfo(BaseModel):
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    title: Optional[str] = None

class ClientAddress(BaseModel):
    street: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = "US"

class ClientSettings(BaseModel):
    fiscal_year_end: Optional[str] = Field(None, description="MM-DD format")
    default_currency: Optional[str] = Field("USD", description="ISO currency code")
    timezone: Optional[str] = Field("America/New_York", description="IANA timezone")
    date_format: Optional[str] = Field("MM/DD/YYYY", description="Preferred date format")
    number_format: Optional[str] = Field("US", description="Number formatting locale")

class ClientCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    client_type: Optional[ClientType] = None
    client_size: Optional[ClientSize] = None
    industry: Optional[str] = None
    website: Optional[str] = None
    description: Optional[str] = None
    
    # Contact Information
    primary_contact: Optional[ContactInfo] = None
    billing_contact: Optional[ContactInfo] = None
    
    # Address Information
    business_address: Optional[ClientAddress] = None
    billing_address: Optional[ClientAddress] = None
    
    # Tax Information
    tax_id: Optional[str] = None
    
    # Settings
    settings: Optional[ClientSettings] = None
    
    # Custom fields for additional data
    custom_fields: Optional[Dict[str, Any]] = None

    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('Client name cannot be empty')
        return v.strip()

class ClientUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    client_type: Optional[ClientType] = None
    client_size: Optional[ClientSize] = None
    industry: Optional[str] = None
    website: Optional[str] = None
    description: Optional[str] = None
    status: Optional[ClientStatus] = None
    
    # Contact Information
    primary_contact: Optional[ContactInfo] = None
    billing_contact: Optional[ContactInfo] = None
    
    # Address Information
    business_address: Optional[ClientAddress] = None
    billing_address: Optional[ClientAddress] = None
    
    # Tax Information
    tax_id: Optional[str] = None
    
    # Settings
    settings: Optional[ClientSettings] = None
    
    # Custom fields for additional data
    custom_fields: Optional[Dict[str, Any]] = None

    @validator('name')
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('Client name cannot be empty')
        return v.strip() if v else v

class ClientResponse(BaseModel):
    client_id: str
    firm_id: str
    name: str
    client_type: Optional[ClientType] = None
    client_size: Optional[ClientSize] = None
    industry: Optional[str] = None
    website: Optional[str] = None
    description: Optional[str] = None
    status: ClientStatus
    
    # Contact Information
    primary_contact: Optional[ContactInfo] = None
    billing_contact: Optional[ContactInfo] = None
    
    # Address Information
    business_address: Optional[ClientAddress] = None
    billing_address: Optional[ClientAddress] = None
    
    # Tax Information
    tax_id: Optional[str] = None
    
    # Settings
    settings: Optional[ClientSettings] = None
    
    # Custom fields
    custom_fields: Optional[Dict[str, Any]] = None
    
    # Metadata
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    # Related data counts
    entities_count: Optional[int] = 0
    active_entities_count: Optional[int] = 0

class ClientSummaryEnhanced(BaseModel):
    client_id: str
    name: str
    status: ClientStatus
    client_type: Optional[ClientType] = None
    industry: Optional[str] = None
    entities_count: int = 0
    active_entities_count: int = 0
    pending_items_count: int = 0
    error_count: int = 0
    last_activity: Optional[datetime] = None
    overall_status: str = "ok"  # ok, action_needed, error

class ClientListResponse(BaseModel):
    clients: List[ClientSummaryEnhanced]
    pagination: Dict[str, Any]

class ClientWizardStep1(BaseModel):
    """Basic client information - Step 1 of wizard"""
    name: str = Field(..., min_length=1, max_length=255)
    client_type: Optional[ClientType] = None
    client_size: Optional[ClientSize] = None
    industry: Optional[str] = None

class ClientWizardStep2(BaseModel):
    """Contact and address information - Step 2 of wizard"""
    primary_contact: Optional[ContactInfo] = None
    business_address: Optional[ClientAddress] = None
    website: Optional[str] = None

class ClientWizardStep3(BaseModel):
    """Settings and preferences - Step 3 of wizard"""
    settings: Optional[ClientSettings] = None
    tax_id: Optional[str] = None
    description: Optional[str] = None 