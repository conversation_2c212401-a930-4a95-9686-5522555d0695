from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime
from enum import Enum

class AttachmentSourceSystem(str, Enum):
    XERO = "Xero"
    API = "API"
    MANUAL = "Manual"
    UNKNOWN = "Unknown"

class Attachment(BaseModel):
    id: str = Field(..., description="Unique identifier for the attachment (Firestore Document ID)")
    transaction_id: str = Field(..., description="ID of the parent transaction this attachment belongs to")
    
    file_name: str = Field(..., description="Original name of the uploaded file")
    content_type: str = Field(..., description="MIME type of the file (e.g., application/pdf, image/jpeg)")
    size_bytes: int = Field(..., description="Size of the file in bytes")
    
    gcs_path: Optional[str] = Field(None, description="Full path to the file in Google Cloud Storage (e.g., gs://bucket-name/path/to/file)")
    download_url: Optional[str] = Field(None, description="A publicly accessible download URL, if generated and available (e.g., signed URL)")
    
    source_system: AttachmentSourceSystem = Field(default=AttachmentSourceSystem.UNKNOWN, description="System from which the attachment originated")
    external_id: Optional[str] = Field(None, description="ID of the attachment in the external system (e.g., Xero AttachmentID)")

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.now, description="Timestamp of when the attachment record was created")
    # updated_at could be added if attachments are mutable in their metadata

    class Config:
        from_attributes = True
        use_enum_values = True 