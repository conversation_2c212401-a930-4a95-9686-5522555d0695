import { useState, useCallback } from 'react';
import type { BulkLineItem } from '@/components/prepayments/BulkEditScheduleModal';
import { useToast } from './useToast';
import { api } from '@/lib/api';

interface Transaction {
  id: string;
  document_number: string;
  line_items: Array<{
    id: string | number;
    description: string;
    amount?: number;
    unit_amount: number;
    account_code?: string;
    account_id?: string;
    quantity: number;
    tax_amount?: number;
    tax_type?: string;
  }>;
}

interface BulkCreateData {
  invoiceId: string;
  reference: string;
  lineItems: BulkLineItem[];
}

export function useBulkCreateModal(loadAccounts?: () => Promise<void>) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<BulkCreateData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const showToast = useToast();

  const openModal = useCallback((transaction: Transaction) => {
    console.log('🔥 Opening modal for transaction:', transaction);
    
    // Convert transaction line items to BulkLineItem format
    const lineItems: BulkLineItem[] = transaction.line_items.map(lineItem => ({
      lineItemId: String(lineItem.id), // Convert to string in case it's a number
      description: lineItem.description || 'No description',
      lineAmount: lineItem.amount || lineItem.unit_amount || 0,
      status: 'pending_configuration',
      scheduleId: '',
      currentAmortizationAccountCode: '',
      currentExpenseAccountCode: lineItem.account_code || '',
      selected: true
    }));

    setSelectedTransaction({
      invoiceId: transaction.id,
      reference: transaction.document_number,
      lineItems: lineItems
    });
    setIsOpen(true);
    setError(null);
    
    // Load accounts in background after modal opens
    if (loadAccounts) {
      loadAccounts().catch(console.error);
    }
  }, [loadAccounts]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setSelectedTransaction(null);
    setError(null);
  }, []);

  const handleSave = useCallback(async (bulkEditData: any): Promise<void> => {
    if (!selectedTransaction) {
      throw new Error('No transaction selected');
    }

    setIsLoading(true);
    setError(null);
    
    try {
      // Extract line item IDs from the temporary schedule IDs
      const lineItemIds = bulkEditData.applyToSelected.map((scheduleId: string) => {
        // scheduleId format: "temp-{lineItemId}-{index}"
        const parts = scheduleId.split('-');
        return parts[1]; // Extract the original line item ID
      });

      // Prepare API request data
      const apiData = {
        line_item_ids: lineItemIds,
        amortization_start_date: bulkEditData.amortizationStartDate,
        number_of_periods: bulkEditData.numberOfPeriods,
        amortization_account_code: bulkEditData.amortizationAccountCode,
        expense_account_code: bulkEditData.expenseAccountCode,
        notes: bulkEditData.notes || undefined,
      };

      console.log('Creating schedules with data:', apiData);

      // Make API call to create schedules
      const response = await api.createBulkSchedules(selectedTransaction.invoiceId, apiData);
      
      console.log('Schedules created successfully:', response);
      showToast.showSuccess(
        `Created ${response.total_created} amortization schedule${response.total_created !== 1 ? 's' : ''} successfully`
      );
      closeModal();
    } catch (err) {
      console.error('Failed to create schedules:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to create schedules';
      setError(errorMessage);
      showToast.showError('Failed to create schedules', errorMessage);
      throw err; // Re-throw to indicate failure
    } finally {
      setIsLoading(false);
    }
  }, [selectedTransaction, showToast, closeModal]);

  const handleConfirm = useCallback(async (scheduleIds: string[]) => {
    // For creation, we don't have scheduleIds yet
    console.log('Confirming creation for scheduleIds:', scheduleIds);
    closeModal();
  }, [closeModal]);

  const handleSkip = useCallback(async (scheduleIds: string[], reason: string) => {
    closeModal();
    showToast.showInfo('Schedule creation cancelled', reason);
  }, [closeModal, showToast]);

  return {
    isOpen,
    selectedTransaction,
    isLoading,
    error,
    openModal,
    closeModal,
    handleSave,
    handleConfirm,
    handleSkip
  };
}