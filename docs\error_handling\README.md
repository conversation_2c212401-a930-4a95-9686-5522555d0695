# Error Handling in the DRCR API

Effective error handling is essential for a robust and developer-friendly API. The DRCR API implements a standardized approach to communicate errors, ensuring that client applications can understand and appropriately respond to issues.

## Philosophy

Our error handling philosophy is built on these core principles:

*   **Clarity**: Error messages should be clear, concise, and provide actionable information where possible.
*   **Consistency**: Error responses follow a standardized format across all endpoints.
*   **Specificity**: Application-specific error codes are used to pinpoint the exact nature of an error beyond standard HTTP status codes.
*   **Security**: Error messages avoid exposing sensitive system information.

## Standardized Error Response Format

When an error occurs (typically HTTP status codes 4xx or 5xx), the DRCR API returns a JSON response body with the following structure:

```json
{
  "detail": {
    "message": "A human-readable description of the error.",
    "app_error_code": "APPLICATION_SPECIFIC_ERROR_CODE",
    "context": {
      "field_name": "Additional context if the error is related to a specific field or entity."
    }
  }
}
```

*   **`detail`** (Object): Contains the detailed error information.
    *   **`message`** (String): A human-readable message describing the error. This message is intended for developers and logging, not necessarily for direct display to end-users.
    *   **`app_error_code`** (String, Optional): An application-specific error code that provides a more granular classification of the error. See `error_codes.md` for a list of these codes. This field is present for most 4xx and 5xx errors originating from application logic.
    *   **`context`** (Object, Optional): Provides additional context about the error. For validation errors, this might include the field that failed validation and the reason. For other errors, it might include relevant identifiers.

## HTTP Status Codes

The DRCR API uses standard HTTP status codes to indicate the general nature of the request's outcome. Key categories include:

*   **`2xx` (Successful)**: Indicate that the request was successfully received, understood, and accepted.
    *   `200 OK`: Standard response for successful HTTP requests.
    *   `201 Created`: The request has been fulfilled and resulted in a new resource being created.
    *   `204 No Content`: The server successfully processed the request and is not returning any content.
*   **`4xx` (Client Errors)**: Indicate that the client seems to have erred.
    *   `400 Bad Request`: The server cannot or will not process the request due to something that is perceived to be a client error (e.g., malformed request syntax, invalid request message framing, or deceptive request routing). Often accompanied by an `app_error_code` for more details.
    *   `401 Unauthorized`: The request has not been applied because it lacks valid authentication credentials for the target resource.
    *   `403 Forbidden`: The server understood the request but refuses to authorize it. The client does not have access rights to the content.
    *   `404 Not Found`: The server can't find the requested resource.
    *   `409 Conflict`: The request could not be completed due to a conflict with the current state of the target resource (e.g., trying to create a duplicate resource).
    *   `422 Unprocessable Entity`: The server understands the content type of the request entity, and the syntax of the request entity is correct, but it was unable to process the contained instructions (e.g., validation errors).
*   **`5xx` (Server Errors)**: Indicate that the server failed to fulfill an apparently valid request.
    *   `500 Internal Server Error`: A generic error message, given when an unexpected condition was encountered and no more specific message is suitable.
    *   `503 Service Unavailable`: The server is currently unable to handle the request due to a temporary overload or maintenance of the server.

## Next Steps

*   For a detailed list of application-specific error codes, see [Application Error Codes](./error_codes.md).
*   For examples of common error scenarios and how to handle them, see [Error Handling Examples](./examples.md). 