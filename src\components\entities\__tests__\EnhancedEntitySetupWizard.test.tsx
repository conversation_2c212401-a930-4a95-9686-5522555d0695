import { render, screen, waitFor } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { EnhancedEntitySetupWizard } from '../EnhancedEntitySetupWizard';

// Mock the API client
vi.mock('@/lib/api', () => ({
  ApiClient: vi.fn().mockImplementation(() => ({
    getEntityBillAggregates: vi.fn()
  }))
}));

// Mock Sonner toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn()
  }
}));

// Mock react-router-dom params
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ clientId: 'test-client', entityId: 'test-entity' }),
    useNavigate: () => vi.fn()
  };
});

const mockBillAggregates = {
  aggregates: {},
  account_classifications: {
    prepayment_candidates: ['400', '410'],
    exclude_recommended: ['200', '210'],
    classic_exclusions: ['800', '810'],
    revenue_accounts: ['200'],
    fixed_asset_accounts: ['120'],
    bank_accounts: ['110'],
    expense_accounts: ['400', '500'],
    all_accounts: {
      '400': { name: 'Office Expenses', type: 'EXPENSE', class: 'EXPENSE' },
      '410': { name: 'Insurance', type: 'EXPENSE', class: 'EXPENSE' },
      '200': { name: 'Sales', type: 'REVENUE', class: 'REVENUE' },
      '210': { name: 'Other Income', type: 'REVENUE', class: 'REVENUE' },
      '800': { name: 'Bank Fees', type: 'EXPENSE', class: 'EXPENSE' },
      '810': { name: 'Travel Expenses', type: 'EXPENSE', class: 'EXPENSE' },
      '120': { name: 'Equipment', type: 'FIXED', class: 'ASSET' },
      '110': { name: 'Bank Account', type: 'BANK', class: 'ASSET' },
      '500': { name: 'Utilities', type: 'EXPENSE', class: 'EXPENSE' }
    }
  },
  date_range: {
    start_date: '2024-01-01',
    end_date: '2024-12-31',
    months: 12
  },
  total_bills: 100,
  total_with_attachments: 75
};

const renderComponent = () => {
  return render(
    <BrowserRouter>
      <EnhancedEntitySetupWizard />
    </BrowserRouter>
  );
};

describe('EnhancedEntitySetupWizard', () => {
  let mockGetEntityBillAggregates: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.clearAllMocks();
    const { ApiClient } = require('@/lib/api');
    mockGetEntityBillAggregates = vi.fn();
    ApiClient.mockImplementation(() => ({
      getEntityBillAggregates: mockGetEntityBillAggregates
    }));
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  it('should show loading skeleton when account classifications are not loaded', async () => {
    // Mock API to return data but delay it
    mockGetEntityBillAggregates.mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve(mockBillAggregates), 100))
    );

    renderComponent();

    // Should show loading skeletons initially
    expect(screen.getByText('Include in Scanning')).toBeInTheDocument();
    expect(screen.getByText('Exclude from Scanning')).toBeInTheDocument();
    
    // Should show skeleton elements (animate-pulse class)
    const skeletonElements = document.querySelectorAll('.animate-pulse');
    expect(skeletonElements.length).toBeGreaterThan(0);
  });

  it('should show account categories after data loads', async () => {
    mockGetEntityBillAggregates.mockResolvedValue(mockBillAggregates);

    renderComponent();

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Prepayment Candidates')).toBeInTheDocument();
    });

    // Should show actual account categories
    expect(screen.getByText('Revenue & Sales')).toBeInTheDocument();
    expect(screen.getByText('Fixed Assets')).toBeInTheDocument();
    expect(screen.getByText('Bank & Cash Accounts')).toBeInTheDocument();
  });

  it('should handle invalid API response gracefully', async () => {
    // Mock API to return invalid data
    mockGetEntityBillAggregates.mockResolvedValue({
      // Missing account_classifications
      aggregates: {},
      date_range: { start_date: '2024-01-01', end_date: '2024-12-31', months: 12 }
    });

    renderComponent();

    // Should not crash and show error state
    await waitFor(() => {
      expect(screen.getByText('Failed to load bill data. Please try again.')).toBeInTheDocument();
    });
  });

  it('should handle API response with missing all_accounts', async () => {
    const invalidData = {
      ...mockBillAggregates,
      account_classifications: {
        ...mockBillAggregates.account_classifications,
        all_accounts: null // Invalid all_accounts
      }
    };

    // Spy on console.warn to verify the warning is logged
    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

    mockGetEntityBillAggregates.mockResolvedValue(invalidData);

    renderComponent();

    // Should handle gracefully and not crash
    await waitFor(() => {
      // Component should still render but with empty account lists
      expect(screen.getByText('Account Categories')).toBeInTheDocument();
    });

    // Should log a warning about missing all_accounts
    expect(consoleSpy).toHaveBeenCalledWith(
      'Missing or invalid all_accounts in account_classifications, using empty object'
    );

    consoleSpy.mockRestore();
  });

  it('should filter out invalid account codes', async () => {
    const dataWithInvalidCodes = {
      ...mockBillAggregates,
      account_classifications: {
        ...mockBillAggregates.account_classifications,
        prepayment_candidates: ['400', '', null, undefined, '410'], // Mix of valid and invalid
      }
    };

    mockGetEntityBillAggregates.mockResolvedValue(dataWithInvalidCodes);

    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('Prepayment Candidates')).toBeInTheDocument();
    });

    // Should only show valid accounts
    // Implementation will filter out empty/null/undefined codes
  });

  it('should complete the full wizard flow successfully', async () => {
    // Mock the complete setup API call
    const mockCompleteEntitySetup = vi.fn().mockResolvedValue({ success: true });
    const { ApiClient } = require('@/lib/api');
    ApiClient.mockImplementation(() => ({
      getEntityBillAggregates: mockGetEntityBillAggregates,
      completeEntitySetup: mockCompleteEntitySetup
    }));

    mockGetEntityBillAggregates.mockResolvedValue(mockBillAggregates);

    renderComponent();

    // Wait for initial data load
    await waitFor(() => {
      expect(screen.getByText('Prepayment Candidates')).toBeInTheDocument();
    });

    // Should be on step 1 initially
    expect(screen.getByText('Interactive Cost Estimation')).toBeInTheDocument();
    expect(screen.getByText('Configure Scanning Scope')).toBeInTheDocument();

    // Click Next to go to step 2
    const nextButton = screen.getByText('Next');
    nextButton.click();

    // Should now be on step 2
    await waitFor(() => {
      expect(screen.getByText('Sync Configuration')).toBeInTheDocument();
    });

    // Click Next to go to step 3
    const nextButton2 = screen.getByText('Next');
    nextButton2.click();

    // Should now be on step 3 (Review & Complete)
    await waitFor(() => {
      expect(screen.getByText('Review & Complete')).toBeInTheDocument();
    });

    // Click Complete Setup
    const completeButton = screen.getByText('Complete Setup');
    completeButton.click();

    // Should call the complete setup API
    await waitFor(() => {
      expect(mockCompleteEntitySetup).toHaveBeenCalledWith('test-entity', expect.objectContaining({
        enable_ai_scanning: expect.any(Boolean),
        sync_settings: expect.objectContaining({
          sync_frequency: expect.any(String),
          auto_sync_enabled: expect.any(Boolean)
        }),
        account_settings: expect.objectContaining({
          excluded_accounts: expect.any(Array)
        })
      }));
    });
  });

  it('should handle component unmount during API call gracefully', async () => {
    let resolvePromise: (value: any) => void;
    const pendingPromise = new Promise(resolve => {
      resolvePromise = resolve;
    });

    mockGetEntityBillAggregates.mockReturnValue(pendingPromise);

    const { unmount } = renderComponent();

    // Unmount component while API call is pending
    unmount();

    // Resolve the promise after unmount
    resolvePromise!(mockBillAggregates);

    // Should not crash or throw errors
    // This test mainly ensures no memory leaks or setState on unmounted component
  });

  it('should debounce amount threshold changes', async () => {
    vi.useFakeTimers();

    mockGetEntityBillAggregates.mockResolvedValue(mockBillAggregates);

    renderComponent();

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText('Account Categories')).toBeInTheDocument();
    });

    // Clear the initial call
    mockGetEntityBillAggregates.mockClear();

    // Change amount threshold multiple times quickly
    const amountInput = screen.getByDisplayValue('20') as HTMLInputElement;
    
    // Simulate rapid changes
    amountInput.value = '25';
    amountInput.dispatchEvent(new Event('change', { bubbles: true }));
    
    amountInput.value = '30';
    amountInput.dispatchEvent(new Event('change', { bubbles: true }));
    
    amountInput.value = '35';
    amountInput.dispatchEvent(new Event('change', { bubbles: true }));

    // Fast forward 300ms (less than debounce delay)
    vi.advanceTimersByTime(300);
    
    // Should not have called API yet
    expect(mockGetEntityBillAggregates).not.toHaveBeenCalled();

    // Fast forward past debounce delay
    vi.advanceTimersByTime(300);

    // Should have called API only once
    await waitFor(() => {
      expect(mockGetEntityBillAggregates).toHaveBeenCalledTimes(1);
    });

    vi.useRealTimers();
  });

  it('should trigger polling when COA data is empty', async () => {
    vi.useFakeTimers();

    // Mock empty COA response (simulates race condition)
    const emptyCoaData = {
      aggregates: {},
      account_classifications: {
        prepayment_candidates: [],
        exclude_recommended: [],
        classic_exclusions: [],
        revenue_accounts: [],
        fixed_asset_accounts: [],
        bank_accounts: [],
        expense_accounts: [],
        all_accounts: {} // Empty accounts object
      },
      date_range: { start_date: '2024-01-01', end_date: '2024-12-31', months: 12 },
      total_bills: 0,
      total_with_attachments: 0
    };

    // First call returns empty data, second call returns real data
    mockGetEntityBillAggregates
      .mockResolvedValueOnce(emptyCoaData)
      .mockResolvedValueOnce(mockBillAggregates);

    renderComponent();

    // Should show polling UI
    await waitFor(() => {
      expect(screen.getByText('Waiting for entity sync to complete...')).toBeInTheDocument();
      expect(screen.getByText('Attempt 1 of 4')).toBeInTheDocument();
    });

    // Fast forward to trigger first polling attempt (2 seconds)
    vi.advanceTimersByTime(2000);

    // Should complete polling and show data
    await waitFor(() => {
      expect(screen.getByText('Prepayment Candidates')).toBeInTheDocument();
    });

    // Should have made 2 API calls (initial + 1 polling attempt)
    expect(mockGetEntityBillAggregates).toHaveBeenCalledTimes(2);

    vi.useRealTimers();
  });

  it('should show timeout error after max polling attempts', async () => {
    vi.useFakeTimers();

    // Mock empty COA response for all attempts
    const emptyCoaData = {
      aggregates: {},
      account_classifications: {
        prepayment_candidates: [],
        exclude_recommended: [],
        classic_exclusions: [],
        revenue_accounts: [],
        fixed_asset_accounts: [],
        bank_accounts: [],
        expense_accounts: [],
        all_accounts: {}
      },
      date_range: { start_date: '2024-01-01', end_date: '2024-12-31', months: 12 },
      total_bills: 0,
      total_with_attachments: 0
    };

    mockGetEntityBillAggregates.mockResolvedValue(emptyCoaData);

    renderComponent();

    // Should show polling UI
    await waitFor(() => {
      expect(screen.getByText('Waiting for entity sync to complete...')).toBeInTheDocument();
    });

    // Fast forward through all polling attempts (2s + 4s + 8s + 16s = 30s)
    vi.advanceTimersByTime(32000);

    // Should show timeout error
    await waitFor(() => {
      expect(screen.getByText(/Chart of Accounts data is still loading/)).toBeInTheDocument();
    });

    // Should have made 5 API calls (initial + 4 polling attempts)
    expect(mockGetEntityBillAggregates).toHaveBeenCalledTimes(5);

    vi.useRealTimers();
  });

  it('should display elapsed time during polling', async () => {
    vi.useFakeTimers();

    const emptyCoaData = {
      aggregates: {},
      account_classifications: {
        prepayment_candidates: [],
        exclude_recommended: [],
        classic_exclusions: [],
        revenue_accounts: [],
        fixed_asset_accounts: [],
        bank_accounts: [],
        expense_accounts: [],
        all_accounts: {}
      },
      date_range: { start_date: '2024-01-01', end_date: '2024-12-31', months: 12 },
      total_bills: 0,
      total_with_attachments: 0
    };

    mockGetEntityBillAggregates.mockResolvedValue(emptyCoaData);

    renderComponent();

    // Should show initial polling state
    await waitFor(() => {
      expect(screen.getByText('Elapsed time: 0s')).toBeInTheDocument();
    });

    // Fast forward 5 seconds
    vi.advanceTimersByTime(5000);

    // Should show updated elapsed time
    await waitFor(() => {
      expect(screen.getByText('Elapsed time: 5s')).toBeInTheDocument();
    });

    vi.useRealTimers();
  });

  it('should clean up polling timers on unmount', async () => {
    vi.useFakeTimers();

    const emptyCoaData = {
      aggregates: {},
      account_classifications: {
        prepayment_candidates: [],
        exclude_recommended: [],
        classic_exclusions: [],
        revenue_accounts: [],
        fixed_asset_accounts: [],
        bank_accounts: [],
        expense_accounts: [],
        all_accounts: {}
      },
      date_range: { start_date: '2024-01-01', end_date: '2024-12-31', months: 12 },
      total_bills: 0,
      total_with_attachments: 0
    };

    mockGetEntityBillAggregates.mockResolvedValue(emptyCoaData);

    const { unmount } = renderComponent();

    // Should start polling
    await waitFor(() => {
      expect(screen.getByText('Waiting for entity sync to complete...')).toBeInTheDocument();
    });

    // Spy on clearTimeout and clearInterval
    const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout');
    const clearIntervalSpy = vi.spyOn(global, 'clearInterval');

    // Unmount component while polling
    unmount();

    // Should clean up timers
    expect(clearTimeoutSpy).toHaveBeenCalled();
    expect(clearIntervalSpy).toHaveBeenCalled();

    clearTimeoutSpy.mockRestore();
    clearIntervalSpy.mockRestore();
    vi.useRealTimers();
  });
});