import React from 'react';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Checkbox } from '../ui/checkbox';
import { ScrollArea } from '../ui/scroll-area';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from '../ui/dropdown-menu';
import { 
  Search, 
  FileText, 
  AlertCircle, 
  ChevronRight, 
  ChevronDown,
  Calendar,
  Paperclip,
  Loader2,
  Settings2
} from 'lucide-react';
import type { 
  SupplierNode, 
  InvoiceNode, 
  LineItemNode, 
  ExpandedState,
  SelectionState
} from '../../types/hierarchical-bills.types';
import { 
  calculateSupplierSelectionState, 
  calculateInvoiceSelectionState 
} from '../../types/hierarchical-bills.types';

interface Client {
  clientId: string;
  clientName: string;
}

interface Entity {
  entityId: string;
  entityName: string;
  connectionStatus: string;
}

interface HierarchicalBillsListProps {
  suppliers: SupplierNode[];
  expandedState: ExpandedState;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  onToggleExpand: (type: 'supplier' | 'invoice', id: string) => void;
  onToggleSelection: (type: 'supplier' | 'invoice' | 'lineItem', id: string) => void;
  onSelectAll: (selected: boolean) => void;
  onAttachmentClick?: (invoiceId: string, attachmentId: string) => void;
  isLoading: boolean;
  error: string | null;
  
  // Client/Entity selection props
  clients: Client[];
  entities: Entity[];
  selectedClientId: string | null;
  selectedEntityId: string | null;
  isLoadingClients: boolean;
  isLoadingEntities: boolean;
  onClientChange: (clientId: string) => void;
  onEntityChange: (entityId: string) => void;
  
  // Status filter props
  selectedStatusFilters: string[];
  onStatusFiltersChange: (filters: string[]) => void;
  
  // Keyboard navigation props
  focusedInvoiceIndex?: number;
  navigationMode?: 'invoice' | 'lineitem';
  focusedLineItemIndex?: number;
  
  // Pagination props
  pagination?: {
    hasMore: boolean;
    isLoadingMore: boolean;
    totalItems: number;
    currentPage: number;
  };
  onLoadMore?: () => void;
}

export function HierarchicalBillsList({
  suppliers,
  expandedState,
  searchTerm,
  onSearchChange,
  onToggleExpand,
  onToggleSelection,
  onSelectAll,
  onAttachmentClick,
  isLoading,
  error,
  clients,
  entities,
  selectedClientId,
  selectedEntityId,
  isLoadingClients,
  isLoadingEntities,
  onClientChange,
  onEntityChange,
  selectedStatusFilters,
  onStatusFiltersChange,
  focusedInvoiceIndex,
  navigationMode = 'invoice',
  focusedLineItemIndex = -1,
  pagination,
  onLoadMore,
}: HierarchicalBillsListProps) {
  const formatCurrency = (amount: number, currencyCode: string = 'GBP') => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: currencyCode,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'action_needed':
        return 'bg-red-100 text-red-700 border-red-200';
      case 'proposed':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'confirmed':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'validation_error':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusDot = (status: string) => {
    switch (status.toLowerCase()) {
      case 'action_needed':
        return 'bg-red-500';
      case 'proposed':
        return 'bg-blue-500';
      case 'confirmed':
        return 'bg-green-500';
      case 'validation_error':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  const calculateOverallSelection = (): SelectionState => {
    const supplierStates = suppliers.map(calculateSupplierSelectionState);
    const allSelected = supplierStates.every(state => state === 'all');
    const noneSelected = supplierStates.every(state => state === 'none');
    
    if (allSelected) return 'all';
    if (noneSelected) return 'none';
    return 'partial';
  };

  const getTotalSelectedCount = () => {
    let count = 0;
    suppliers.forEach(supplier => {
      supplier.invoices.forEach(invoice => {
        invoice.lineItems.forEach(lineItem => {
          if (lineItem.isSelected) count++;
        });
      });
    });
    return count;
  };

  const getTotalItemCount = () => {
    let count = 0;
    suppliers.forEach(supplier => {
      supplier.invoices.forEach(invoice => {
        count += invoice.lineItems.length;
      });
    });
    return count;
  };

  const overallSelection = calculateOverallSelection();
  const selectedCount = getTotalSelectedCount();
  const totalCount = getTotalItemCount();

  if (error) {
    return (
      <div className="p-4 text-center">
        <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
        <p className="text-sm text-red-600">{error}</p>
      </div>
    );
  }

  const renderLineItem = (lineItem: LineItemNode, invoiceId: string, lineItemIndex: number, invoiceIndex: number) => {
    const isFocused = navigationMode === 'lineitem' && 
                     focusedInvoiceIndex === invoiceIndex && 
                     focusedLineItemIndex === lineItemIndex;
    
    return (
      <div
        key={lineItem.lineItemId}
        className={`flex items-start space-x-3 p-2 pl-14 rounded-sm transition-colors cursor-pointer ${
          isFocused ? 'ring-2 ring-green-500 bg-green-50' :
          lineItem.isSelected ? 'bg-blue-50 hover:bg-blue-100' : 'hover:bg-gray-50'
        }`}
        onClick={() => onToggleSelection('lineItem', lineItem.lineItemId)}
      >
      <Checkbox
        checked={lineItem.isSelected}
        onCheckedChange={(checked) => 
          onToggleSelection('lineItem', lineItem.lineItemId)
        }
        onClick={(e) => e.stopPropagation()}
        className="mt-1"
      />
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <p className="text-sm font-medium truncate pr-2">
            {lineItem.description}
          </p>
          <p className="text-sm font-semibold">
            {formatCurrency(lineItem.lineAmount)}
          </p>
        </div>
        
        {lineItem.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-1">
            {lineItem.tags.map((tag, index) => (
              <Badge 
                key={index}
                variant="secondary" 
                className="text-xs px-1 py-0"
              >
                {tag}
              </Badge>
            ))}
          </div>
        )}
        
        <div className="text-xs text-gray-500">
          {lineItem.expenseAccountCode && (
            <span>Expense: {lineItem.expenseAccountCode}</span>
          )}
        </div>
      </div>
    </div>
    );
  };

  const renderInvoice = (invoice: InvoiceNode, supplierId: string, invoiceIndex: number) => {
    const isExpanded = expandedState.invoices.has(invoice.invoiceId);
    const selectionState = calculateInvoiceSelectionState(invoice);
    const isFocused = focusedInvoiceIndex === invoiceIndex;
    
    return (
      <div key={invoice.invoiceId} className="ml-4">
        <div
          data-invoice-id={invoice.invoiceId}
          className={`flex items-center space-x-3 p-2 pl-6 rounded-sm transition-colors cursor-pointer ${
            isFocused ? 'ring-2 ring-blue-500 bg-blue-50' : 
            invoice.isSelected ? 'bg-blue-50 hover:bg-blue-100' : 'hover:bg-gray-50'
          }`}
          onClick={() => onToggleSelection('invoice', invoice.invoiceId)}
        >
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0"
            onClick={(e) => {
              e.stopPropagation();
              onToggleExpand('invoice', invoice.invoiceId);
            }}
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </Button>
          
          <Checkbox
            checked={selectionState === 'all'}
            ref={(el) => {
              if (el) (el as any).indeterminate = selectionState === 'partial';
            }}
            onCheckedChange={(checked) => 
              onToggleSelection('invoice', invoice.invoiceId)
            }
            onClick={(e) => e.stopPropagation()}
          />
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center space-x-2">
                <p className="text-sm font-medium truncate">
                  {invoice.reference}
                </p>
                {invoice.hasAttachment && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      if (onAttachmentClick && invoice.attachmentId) {
                        onAttachmentClick(invoice.invoiceId, invoice.attachmentId);
                      }
                    }}
                    className="p-1 hover:bg-gray-200 rounded transition-colors"
                    title="View attachment"
                  >
                    <Paperclip className="h-3 w-3 text-blue-600 hover:text-blue-800" />
                  </button>
                )}
              </div>
              <p className="text-sm font-semibold">
                {formatCurrency(invoice.totalAmount, invoice.currencyCode)}
              </p>
            </div>
            
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(invoice.invoiceDate)}</span>
              <Badge 
                variant="outline" 
                className={`text-xs ${getStatusColor(invoice.status)}`}
              >
                {invoice.status.replace('_', ' ')}
              </Badge>
            </div>
          </div>
        </div>
        
        {isExpanded && (
          <div className="ml-2">
            {invoice.lineItems.map((lineItem, lineItemIndex) => 
              renderLineItem(lineItem, invoice.invoiceId, lineItemIndex, invoiceIndex)
            )}
          </div>
        )}
      </div>
    );
  };

  const renderSupplier = (supplier: SupplierNode) => {
    const isExpanded = expandedState.suppliers.has(supplier.supplierId);
    const selectionState = calculateSupplierSelectionState(supplier);
    
    return (
      <div key={supplier.supplierId} className="mb-1">
        <div
          className={`flex items-center space-x-3 p-3 rounded-md transition-colors cursor-pointer ${
            supplier.isSelected ? 'bg-blue-50 hover:bg-blue-100' : 'hover:bg-gray-50'
          }`}
          onClick={() => onToggleExpand('supplier', supplier.supplierId)}
        >
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0"
            onClick={(e) => {
              e.stopPropagation();
              onToggleExpand('supplier', supplier.supplierId);
            }}
          >
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <div 
                className={`w-2 h-2 rounded-full ${getStatusDot(supplier.status)}`}
              />
              <p className="font-semibold text-base truncate">
                {supplier.supplierName}
              </p>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>{supplier.invoiceCount} invoice{supplier.invoiceCount !== 1 ? 's' : ''}</span>
                <span>Total: {formatCurrency(supplier.totalAmount, 'GBP')}</span>
              </div>
              
              <Badge 
                variant="outline" 
                className={`text-xs ${getStatusColor(supplier.status)}`}
              >
                {supplier.status.replace('_', ' ')}
              </Badge>
            </div>
          </div>
        </div>
        
        {isExpanded && (
          <div className="mt-2 space-y-1">
            {supplier.invoices.map((invoice, localIndex) => {
              // Calculate global invoice index
              let globalIndex = 0;
              let found = false;
              for (const s of suppliers) {
                for (const inv of s.invoices) {
                  if (inv.invoiceId === invoice.invoiceId) {
                    found = true;
                    break;
                  }
                  globalIndex++;
                }
                if (found) break;
              }
              return renderInvoice(invoice, supplier.supplierId, globalIndex);
            })}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Bills for Amortization</h2>
          <Badge variant="secondary" className="ml-2">
            {suppliers.length} supplier{suppliers.length !== 1 ? 's' : ''}
          </Badge>
        </div>

        {/* Client/Entity/Status Selection and Search Bar */}
        <div className="space-y-3">
          {/* Client/Entity/Status Dropdowns - Full Width */}
          <div className="flex flex-row gap-2 items-center w-full">
            {/* Client Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="outline" 
                  className="flex-1 justify-between bg-white text-xs h-8" 
                  disabled={isLoadingClients}
                >
                  {isLoadingClients ? <Loader2 className="mr-1 h-3 w-3 animate-spin" /> : null}
                  {selectedClientId ? clients?.find(c => c.clientId === selectedClientId)?.clientName : 'Select Client'}
                  {!isLoadingClients && <ChevronDown className="ml-1 h-3 w-3" />}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-[300px] z-50">
                <DropdownMenuLabel>Clients</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {isLoadingClients && <DropdownMenuItem disabled>Loading...</DropdownMenuItem>}
                {!isLoadingClients && clients && clients.map((client) => (
                  <DropdownMenuItem 
                    key={client.clientId} 
                    onSelect={() => onClientChange(client.clientId)}
                  >
                    {client.clientName}
                  </DropdownMenuItem>
                ))}
                {!isLoadingClients && (!clients || clients.length === 0) && (
                  <DropdownMenuItem disabled>No clients found</DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
            
            {/* Entity Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="outline" 
                  className="flex-1 justify-between bg-white text-xs h-8" 
                  disabled={!selectedClientId || isLoadingEntities}
                >
                  {isLoadingEntities ? <Loader2 className="mr-1 h-3 w-3 animate-spin" /> : null}
                  {selectedEntityId ? entities?.find(e => e.entityId === selectedEntityId)?.entityName : 'Select Entity'}
                  {!isLoadingEntities && <ChevronDown className="ml-1 h-3 w-3" />}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-[300px] z-50">
                <DropdownMenuLabel>
                  Entities for {clients?.find(c => c.clientId === selectedClientId)?.clientName}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                {isLoadingEntities && <DropdownMenuItem disabled>Loading...</DropdownMenuItem>}
                {!isLoadingEntities && entities && entities.map((entity) => (
                  <DropdownMenuItem 
                    key={entity.entityId} 
                    onSelect={() => onEntityChange(entity.entityId)}
                  >
                    {entity.entityName}
                  </DropdownMenuItem>
                ))}
                {!isLoadingEntities && (!entities || entities.length === 0) && selectedClientId && (
                  <DropdownMenuItem disabled>No entities found</DropdownMenuItem>
                )}
                {!selectedClientId && <DropdownMenuItem disabled>Select a client first</DropdownMenuItem>}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Status Filter Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="outline" 
                  className="flex-1 justify-between bg-white text-xs h-8"
                >
                  <div className="flex items-center gap-1">
                    <Settings2 className="h-3 w-3" />
                    Status ({selectedStatusFilters?.length || 0})
                  </div>
                  <ChevronDown className="ml-1 h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-64 z-50">
                <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                <DropdownMenuSeparator />
                
                <DropdownMenuCheckboxItem
                  checked={(selectedStatusFilters?.length || 0) === 0}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      onStatusFiltersChange([]);
                    }
                  }}
                >
                  All Statuses
                </DropdownMenuCheckboxItem>
                
                <DropdownMenuSeparator />
                
                <DropdownMenuCheckboxItem
                  checked={selectedStatusFilters?.includes('pending_configuration') || false}
                  onCheckedChange={(checked) => {
                    const current = selectedStatusFilters || [];
                    if (checked) {
                      onStatusFiltersChange([...current.filter(s => s !== 'pending_configuration'), 'pending_configuration']);
                    } else {
                      onStatusFiltersChange(current.filter(s => s !== 'pending_configuration'));
                    }
                  }}
                >
                  🔧 Pending Configuration
                </DropdownMenuCheckboxItem>
                
                <DropdownMenuCheckboxItem
                  checked={selectedStatusFilters?.includes('proposed') || false}
                  onCheckedChange={(checked) => {
                    const current = selectedStatusFilters || [];
                    if (checked) {
                      onStatusFiltersChange([...current.filter(s => s !== 'proposed'), 'proposed']);
                    } else {
                      onStatusFiltersChange(current.filter(s => s !== 'proposed'));
                    }
                  }}
                >
                  👀 Proposed
                </DropdownMenuCheckboxItem>
                
                <DropdownMenuCheckboxItem
                  checked={selectedStatusFilters?.includes('confirmed') || false}
                  onCheckedChange={(checked) => {
                    const current = selectedStatusFilters || [];
                    if (checked) {
                      onStatusFiltersChange([...current.filter(s => s !== 'confirmed'), 'confirmed']);
                    } else {
                      onStatusFiltersChange(current.filter(s => s !== 'confirmed'));
                    }
                  }}
                >
                  ✅ Confirmed
                </DropdownMenuCheckboxItem>
                
                <DropdownMenuCheckboxItem
                  checked={selectedStatusFilters?.includes('posted') || false}
                  onCheckedChange={(checked) => {
                    const current = selectedStatusFilters || [];
                    if (checked) {
                      onStatusFiltersChange([...current.filter(s => s !== 'posted'), 'posted']);
                    } else {
                      onStatusFiltersChange(current.filter(s => s !== 'posted'));
                    }
                  }}
                >
                  ✅ Posted
                </DropdownMenuCheckboxItem>
                
                <DropdownMenuCheckboxItem
                  checked={selectedStatusFilters?.includes('cancelled') || false}
                  onCheckedChange={(checked) => {
                    const current = selectedStatusFilters || [];
                    if (checked) {
                      onStatusFiltersChange([...current.filter(s => s !== 'cancelled'), 'cancelled']);
                    } else {
                      onStatusFiltersChange(current.filter(s => s !== 'cancelled'));
                    }
                  }}
                >
                  ❌ Cancelled
                </DropdownMenuCheckboxItem>
                
                <DropdownMenuCheckboxItem
                  checked={selectedStatusFilters?.includes('error') || false}
                  onCheckedChange={(checked) => {
                    const current = selectedStatusFilters || [];
                    if (checked) {
                      onStatusFiltersChange([...current.filter(s => s !== 'error'), 'error']);
                    } else {
                      onStatusFiltersChange(current.filter(s => s !== 'error'));
                    }
                  }}
                >
                  ⚠️ Error
                </DropdownMenuCheckboxItem>
                
                <DropdownMenuCheckboxItem
                  checked={selectedStatusFilters?.includes('skipped') || false}
                  onCheckedChange={(checked) => {
                    const current = selectedStatusFilters || [];
                    if (checked) {
                      onStatusFiltersChange([...current.filter(s => s !== 'skipped'), 'skipped']);
                    } else {
                      onStatusFiltersChange(current.filter(s => s !== 'skipped'));
                    }
                  }}
                >
                  ⏭️ Skipped
                </DropdownMenuCheckboxItem>
                
                <DropdownMenuCheckboxItem
                  checked={selectedStatusFilters?.includes('error') || false}
                  onCheckedChange={(checked) => {
                    const current = selectedStatusFilters || [];
                    if (checked) {
                      onStatusFiltersChange([...current.filter(s => s !== 'error'), 'error']);
                    } else {
                      onStatusFiltersChange(current.filter(s => s !== 'error'));
                    }
                  }}
                >
                  ⚠️ Error
                </DropdownMenuCheckboxItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Search Bar */}
          <div className="relative">
            <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search suppliers, invoices, line items..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      </div>


      {/* Bills List */}
      <ScrollArea className="flex-1">
        {isLoading ? (
          <div className="p-4 space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-1"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              </div>
            ))}
          </div>
        ) : suppliers.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <FileText className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm">No bills found</p>
            {searchTerm && (
              <p className="text-xs mt-1">Try adjusting your search term</p>
            )}
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {suppliers.map(renderSupplier)}
            
            {/* Load More Button */}
            {pagination && (pagination.hasMore || pagination.isLoadingMore) && (
              <div className="flex justify-center py-4 border-t border-gray-200 mt-4">
                <Button
                  variant="outline"
                  onClick={onLoadMore}
                  disabled={pagination.isLoadingMore}
                  className="min-w-32"
                >
                  {pagination.isLoadingMore ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    `Load More (${pagination.totalItems - suppliers.length} remaining)`
                  )}
                </Button>
              </div>
            )}
            
            {/* Pagination Info */}
            {pagination && pagination.totalItems > 0 && (
              <div className="text-center py-2 text-xs text-gray-500 border-t border-gray-100">
                Showing {suppliers.length} of {pagination.totalItems} suppliers
              </div>
            )}
          </div>
        )}
      </ScrollArea>
    </div>
  );
}