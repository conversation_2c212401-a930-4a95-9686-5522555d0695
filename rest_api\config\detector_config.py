"""
Prepayment Release Detector Configuration

Shared constants and configuration for the prepayment release detection system.
Centralizes magic numbers for easy tuning across cloud functions and services.
"""

# Detection scoring thresholds
CONFIDENCE_HIGH_THRESHOLD = 7    # asset_credit(3) + pnl_debit(3) + keyword(1)
CONFIDENCE_MEDIUM_THRESHOLD = 5  # asset_credit(3) + pnl_debit(3) - 1

# Chain detection parameters
CHAIN_LOOKBACK_MONTHS = 12       # How far back to look for initial deferral journals
AMOUNT_TOLERANCE_PERCENT = 5     # ±5% tolerance for amount matching in chain detection
BILL_MATCH_WINDOW_DAYS = 365     # How far back to look for originating bills (12 months)
MAX_BILL_LINK_DOCS = 10000       # Maximum bills to load in memory for linking (OOM protection)

# Performance parameters
DEFAULT_PAGE_SIZE = 500          # Default pagination size for Firestore queries
MAX_PREVIEW_CANDIDATES = 10      # Maximum candidates to return in preview
DEFAULT_JOURNAL_LOOKBACK_MONTHS = 24  # Default lookback period for candidate journals (months)
MAX_JOURNAL_LOOKBACK_MONTHS = 60      # Maximum allowed lookback period (5 years)
DEFAULT_FUTURE_JOURNAL_DAYS = 365     # Default future journal window (1 year)
MAX_FUTURE_JOURNAL_DAYS = 1095         # Maximum allowed future window (3 years)
JOURNAL_QUERY_LIMIT = 1000       # Maximum number of journals to process per entity

# Pattern detection parameters
MIN_PATTERN_MONTHS = 3           # Minimum consecutive months to detect schedule pattern

# Amortization grouping parameters
MIN_MONTHLY_GAP_DAYS = 5         # Minimum days between monthly entries
MAX_MONTHLY_GAP_DAYS = 60        # Maximum days between monthly entries
MAX_QUARTERLY_GAP_DAYS = 100     # Maximum days between quarterly entries
PERIOD_ADJACENCY_DAYS = 45       # Maximum days for period adjacency check
AMOUNT_SIMILARITY_TOLERANCE = 0.33  # ±33% tolerance for amount differences

# Narration similarity thresholds for grouping logic
NARRATION_SIMILARITY_HIGH_THRESHOLD = 0.90  # Immediate connection
NARRATION_SIMILARITY_MEDIUM_THRESHOLD = 0.70  # Enable relaxed checks
NARRATION_SIMILARITY_MIN_LENGTH = 8  # Minimum normalized narration length for similarity matching

# Bill matching configuration
BILL_MATCHER_MIN_CONFIDENCE = 0.60  # Minimum confidence threshold for bill linking (temporarily lowered for debugging)
USE_SUBTOTAL_FOR_MATCHING = True     # Use subtotal (pre-tax) instead of total amount for bill matching
NAME_MATCHING_MIN_LENGTH_RATIO = 0.6  # Minimum length ratio for prefix matching (prevents "Box" -> "Dropbox")

# Supported currency pairs for cross-currency bill matching
SUPPORTED_CURRENCY_PAIRS = {
    ('GBP', 'USD'), ('USD', 'GBP'),
    ('GBP', 'EUR'), ('EUR', 'GBP'),
    ('USD', 'EUR'), ('EUR', 'USD'),
    ('GBP', 'CAD'), ('CAD', 'GBP'),
    ('USD', 'CAD'), ('CAD', 'USD'),
}

# Default exchange rates for currency conversion in bill matching
# Note: These are approximate rates for matching purposes
# In production, should be replaced with live rate feeds
FX_CONVERSION_RATES = {
    # GBP conversions
    ('GBP', 'USD'): 1.27,    # 1 GBP = 1.27 USD
    ('USD', 'GBP'): 0.79,    # 1 USD = 0.79 GBP
    ('GBP', 'EUR'): 1.17,    # 1 GBP = 1.17 EUR
    ('EUR', 'GBP'): 0.85,    # 1 EUR = 0.85 GBP
    
    # USD conversions
    ('USD', 'EUR'): 0.92,    # 1 USD = 0.92 EUR
    ('EUR', 'USD'): 1.09,    # 1 EUR = 1.09 USD
    
    # Additional common pairs
    ('GBP', 'CAD'): 1.70,    # 1 GBP = 1.70 CAD
    ('CAD', 'GBP'): 0.59,    # 1 CAD = 0.59 GBP
    ('USD', 'CAD'): 1.35,    # 1 USD = 1.35 CAD
    ('CAD', 'USD'): 0.74,    # 1 CAD = 0.74 USD
}

# Firestore collection names
PREPAYMENT_RELEASES_DETECTED_COLLECTION = "PREPAYMENT_RELEASES_DETECTED"
MANUAL_JOURNALS_COLLECTION = "MANUAL_JOURNALS"
AMORTIZATION_SCHEDULES_COLLECTION = "AMORTIZATION_SCHEDULES"
ENTITY_SETTINGS_COLLECTION = "ENTITY_SETTINGS"
ENTITIES_COLLECTION = "ENTITIES"
ACCOUNTS_COLLECTION = "ACCOUNTS"

# Detection algorithm version for audit trails
DETECTION_ALGORITHM_VERSION = "1.1"  # Fixed critical production issues: status probing, memory usage, regex patterns, GST handling