"""
Prepayment Release Detector Configuration

Shared constants and configuration for the prepayment release detection system.
Centralizes magic numbers for easy tuning across cloud functions and services.
"""

# Detection scoring thresholds
CONFIDENCE_HIGH_THRESHOLD = 7    # asset_credit(3) + pnl_debit(3) + keyword(1)
CONFIDENCE_MEDIUM_THRESHOLD = 5  # asset_credit(3) + pnl_debit(3) - 1

# Chain detection parameters
CHAIN_LOOKBACK_MONTHS = 12       # How far back to look for initial deferral journals
AMOUNT_TOLERANCE_PERCENT = 5     # ±5% tolerance for amount matching in chain detection
BILL_MATCH_WINDOW_DAYS = 365     # How far back to look for originating bills (12 months)
MAX_BILL_LINK_DOCS = 10000       # Maximum bills to load in memory for linking (OOM protection)

# Performance parameters
DEFAULT_PAGE_SIZE = 500          # Default pagination size for Firestore queries
MAX_PREVIEW_CANDIDATES = 10      # Maximum candidates to return in preview
DEFAULT_JOURNAL_LOOKBACK_MONTHS = 24  # Default lookback period for candidate journals (months)
MAX_JOURNAL_LOOKBACK_MONTHS = 60      # Maximum allowed lookback period (5 years)
DEFAULT_FUTURE_JOURNAL_DAYS = 365     # Default future journal window (1 year)
MAX_FUTURE_JOURNAL_DAYS = 1095         # Maximum allowed future window (3 years)
JOURNAL_QUERY_LIMIT = 1000       # Maximum number of journals to process per entity

# Pattern detection parameters
MIN_PATTERN_MONTHS = 3           # Minimum consecutive months to detect schedule pattern

# Firestore collection names
PREPAYMENT_RELEASES_DETECTED_COLLECTION = "PREPAYMENT_RELEASES_DETECTED"
MANUAL_JOURNALS_COLLECTION = "MANUAL_JOURNALS"
AMORTIZATION_SCHEDULES_COLLECTION = "AMORTIZATION_SCHEDULES"
ENTITY_SETTINGS_COLLECTION = "ENTITY_SETTINGS"
ENTITIES_COLLECTION = "ENTITIES"
ACCOUNTS_COLLECTION = "ACCOUNTS"

# Detection algorithm version for audit trails
DETECTION_ALGORITHM_VERSION = "1.1"  # Fixed critical production issues: status probing, memory usage, regex patterns, GST handling