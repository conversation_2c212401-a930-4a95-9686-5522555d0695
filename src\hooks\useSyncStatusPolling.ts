import { useState, useEffect, useCallback, useRef } from 'react';
import { EntitiesService } from '@/services/entities.service';
import type { EntitySummary, SyncStatus } from '@/types/entity.types';

interface SyncStatusData {
  entityId: string;
  syncStatus: SyncStatus | null;
  lastSync?: string;
  connectionStatus?: string;
}

interface UseSyncStatusPollingOptions {
  pollingInterval?: number; // in milliseconds, default 30000 (30 seconds)
  enabled?: boolean; // allows pausing polling
}

interface UseSyncStatusPollingReturn {
  syncStatuses: Map<string, SyncStatusData>;
  isPolling: boolean;
  error: string | null;
  refreshSyncStatus: (entityId?: string) => Promise<void>;
  startPolling: () => void;
  stopPolling: () => void;
}

export function useSyncStatusPolling(
  entityIds: string[] = [],
  options: UseSyncStatusPollingOptions = {}
): UseSyncStatusPollingReturn {
  const { pollingInterval = 30000, enabled = true } = options;
  
  const [syncStatuses, setSyncStatuses] = useState<Map<string, SyncStatusData>>(new Map());
  const [isPolling, setIsPolling] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isPollingRef = useRef(false);

  const fetchSyncStatus = useCallback(async (entityId: string): Promise<SyncStatusData | null> => {
    try {
      // Try to get sync status first, but handle if endpoint doesn't exist
      let syncStatus: SyncStatus | null = null;
      try {
        const response = await EntitiesService.getSyncStatus(entityId);
        
        // Check if response is actually sync status or an error
        if (response && typeof response === 'object' && 'detail' in response) {
          // This is likely an auth error, not sync status data
          syncStatus = null;
        } else {
          syncStatus = response;
        }
      } catch (syncErr: any) {
        // If sync status endpoint fails, we'll rely on entity details
        console.warn(`Sync status endpoint error for entity ${entityId}:`, syncErr?.response?.status);
      }
      
      const entity = await EntitiesService.getEntity(entityId);
      
      return {
        entityId,
        syncStatus,
        lastSync: entity.last_sync,
        connectionStatus: entity.connection_details?.status || 'unknown'
      };
    } catch (err) {
      console.error(`Error fetching entity details for ${entityId}:`, err);
      return null;
    }
  }, []);

  const refreshSyncStatus = useCallback(async (specificEntityId?: string) => {
    const entitiesToUpdate = specificEntityId ? [specificEntityId] : entityIds;
    
    if (entitiesToUpdate.length === 0) return;

    try {
      setError(null);
      
      // Use Promise.allSettled to handle individual failures gracefully
      const statusPromises = entitiesToUpdate.map(entityId => fetchSyncStatus(entityId));
      const statusResults = await Promise.allSettled(statusPromises);
      
      setSyncStatuses(prev => {
        const newMap = new Map(prev);
        statusResults.forEach(result => {
          if (result.status === 'fulfilled' && result.value) {
            newMap.set(result.value.entityId, result.value);
          } else if (result.status === 'rejected') {
            console.warn('Failed to fetch sync status for an entity:', result.reason);
          }
        });
        return newMap;
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch sync statuses';
      console.warn('Error refreshing sync statuses (non-critical):', errorMessage);
      // Don't set error state for sync status failures - they're not critical
    }
  }, [entityIds, fetchSyncStatus]);

  const startPolling = useCallback(() => {
    if (isPollingRef.current || !enabled || entityIds.length === 0) return;
    
    isPollingRef.current = true;
    setIsPolling(true);
    
    // Initial fetch
    refreshSyncStatus();
    
    // Set up interval
    intervalRef.current = setInterval(() => {
      if (isPollingRef.current) {
        refreshSyncStatus();
      }
    }, pollingInterval);
  }, [enabled, entityIds.length, pollingInterval, refreshSyncStatus, entityIds]);

  const stopPolling = useCallback(() => {
    isPollingRef.current = false;
    setIsPolling(false);
    
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // Start/stop polling based on entityIds and enabled state
  useEffect(() => {
    if (enabled && entityIds.length > 0) {
      // Add a small delay to avoid immediate polling after page load
      const timer = setTimeout(() => {
        startPolling();
      }, 1000);
      
      return () => {
        clearTimeout(timer);
        stopPolling();
      };
    } else {
      stopPolling();
    }
  }, [entityIds, enabled, startPolling, stopPolling]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  return {
    syncStatuses,
    isPolling,
    error,
    refreshSyncStatus,
    startPolling,
    stopPolling
  };
}

// Convenience hook for single entity
export function useSingleEntitySyncStatus(
  entityId: string | null,
  options: UseSyncStatusPollingOptions = {}
): UseSyncStatusPollingReturn & {
  syncStatus: SyncStatusData | null;
} {
  const entityIds = entityId ? [entityId] : [];
  const result = useSyncStatusPolling(entityIds, options);
  
  const syncStatus = entityId ? result.syncStatuses.get(entityId) || null : null;
  
  return {
    ...result,
    syncStatus
  };
}