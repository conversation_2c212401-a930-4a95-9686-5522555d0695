# Firestore Indexes for Prepayment Release Detector

## Required Composite Indexes

The prepayment release detector requires the following Firestore composite indexes for optimal performance:

### 1. Manual Journals Query Index
**Collection**: `MANUAL_JOURNALS`
**Fields**: 
- `entity_id` (Ascending)
- `status` (Ascending)

**Purpose**: Efficiently query posted manual journals for specific entities during detection runs.

**Query Pattern**:
```javascript
db.collection("MANUAL_JOURNALS")
  .where("entity_id", "==", entity_id)
  .where("status", "==", "POSTED")
```

### 2. Accounts Query Index
**Collection**: `ACCOUNTS`
**Fields**:
- `entity_id` (Ascending)
- `accountType` (Ascending)

**Purpose**: Efficiently load expense account codes for GL movement analysis.

**Query Pattern**:
```javascript
db.collection("ACCOUNTS")
  .where("entity_id", "==", entity_id)
  .where("accountType", "==", "EXPENSE")
```

### 3. Amortization Schedules Idempotency Index
**Collection**: `AMORTIZATION_SCHEDULES`
**Fields**:
- `entity_id` (Ascending)
- `_system_linkedManualJournalID` (Ascending)

**Purpose**: Fast idempotency checks when creating synthetic schedules.

**Query Pattern**:
```javascript
db.collection("AMORTIZATION_SCHEDULES")
  .where("entity_id", "==", entity_id)
  .where("_system_linkedManualJournalID", "==", mj_id)
```

### 4. Chain Detection Index
**Collection**: `MANUAL_JOURNALS`
**Fields**:
- `entity_id` (Ascending)
- `journalDate` (Ascending)

**Purpose**: Efficiently find related deferral journals during chain detection.

**Query Pattern**:
```javascript
db.collection("MANUAL_JOURNALS")
  .where("entity_id", "==", entity_id)
  .where("journalDate", ">=", lookback_date)
  .where("journalDate", "<", current_journal_date)
```

## Creating Indexes

### Using Firebase CLI
```bash
# Create the indexes using firebase CLI
firebase firestore:indexes

# Or deploy from firestore.indexes.json
firebase deploy --only firestore:indexes
```

### Using Google Cloud Console
1. Navigate to Firestore in Google Cloud Console
2. Go to "Indexes" section
3. Click "Create Index"
4. Add the composite fields as specified above

### Using Firestore Rules/Admin SDK
```javascript
// Example for manual creation via Admin SDK
const indexDefinitions = [
  {
    collectionGroup: "MANUAL_JOURNALS",
    fields: [
      { fieldPath: "entity_id", order: "ASCENDING" },
      { fieldPath: "status", order: "ASCENDING" }
    ]
  },
  {
    collectionGroup: "ACCOUNTS", 
    fields: [
      { fieldPath: "entity_id", order: "ASCENDING" },
      { fieldPath: "accountType", order: "ASCENDING" }
    ]
  },
  {
    collectionGroup: "AMORTIZATION_SCHEDULES",
    fields: [
      { fieldPath: "entity_id", order: "ASCENDING" },
      { fieldPath: "_system_linkedManualJournalID", order: "ASCENDING" }
    ]
  },
  {
    collectionGroup: "MANUAL_JOURNALS",
    fields: [
      { fieldPath: "entity_id", order: "ASCENDING" },
      { fieldPath: "journalDate", order: "ASCENDING" }
    ]
  }
];
```

## Performance Considerations

### Query Optimization
- **Asset Credit Filtering**: Currently done in Python - consider adding a helper field `_assetCodesCredited` to `MANUAL_JOURNALS` for more efficient querying
- **Pagination**: Implement proper cursor-based pagination for large datasets
- **Batch Processing**: Process entities in batches to avoid memory issues

### Index Maintenance
- Monitor index usage in Firestore console
- Remove unused indexes to reduce storage costs
- Consider TTL policies for detection run summaries

## Future Optimizations

### Helper Field Strategy
Consider adding computed fields to reduce query complexity:

```javascript
// Example helper field in MANUAL_JOURNALS
{
  "journal_id": "mj_123",
  "entity_id": "entity_456", 
  "status": "POSTED",
  "lines": [...],
  "_assetCodesCredited": ["1610", "1620"],  // Computed field
  "_expenseCodesDebited": ["6200", "6300"], // Computed field
  "_hasAssetCredit": true,                  // Boolean flag
  "_hasPnLDebit": true                     // Boolean flag
}
```

This would enable more efficient queries:
```javascript
db.collection("MANUAL_JOURNALS")
  .where("entity_id", "==", entity_id)
  .where("_hasAssetCredit", "==", true)
  .where("_hasPnLDebit", "==", true)
```

### Materialized Views
For frequently accessed summary data, consider maintaining materialized views in separate collections updated via Cloud Functions triggers.