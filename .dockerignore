# Git
.git
.gitignore

# Environment files
.env
.env.local
.env.*.local
*.env

# Credentials and secrets
*.json
credentials/
secrets/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Documentation
docs/
*.md
README*

# Tests
tests/
test_*
*_test.py

# Development tools
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Terraform
*.tfstate
*.tfstate.*
.terraform/
*.tfvars

# Deployment
deployment/
scripts/
terraform/
local_utils/

# Temporary files
*.tmp
*.temp
temp/
tmp/