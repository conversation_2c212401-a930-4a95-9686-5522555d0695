# Firestore Collections

This document details each of the top-level Firestore collections used in the DRCR application.

## `FIRMS`

*   **Purpose**: Stores information about accounting firms that use the DRCR platform.
*   **Document ID Strategy**: Auto-generated UUID, stored in the `firm_id` field.
*   **Key Fields**:
    *   `firm_id` (String): Unique identifier for the firm (same as document ID).
    *   `name` (String): The official name of the accounting firm.
    *   `status` (String): Current status of the firm (e.g., "active", "inactive", "suspended").
    *   `created_at` (Timestamp): Firestore server timestamp of when the firm document was created.
    *   `updated_at` (Timestamp): Firestore server timestamp of when the firm document was last updated.
    *   *Other firm-specific details as needed (e.g., address, contact info, subscription level - TBD).* 
*   **Relationships**:
    *   Referenced by `FIRM_USERS` documents via their `firm_id` field.
    *   Referenced by `CLIENTS` documents via their `firm_id` field.
*   **Notes**: This collection forms the top-most organizational unit within DRCR for grouping users and their respective clients.

---

## `FIRM_USERS`

*   **Purpose**: Manages users associated with a specific accounting firm, detailing their roles and access permissions within that firm.
*   **Document ID Strategy**: Auto-generated Firestore document ID.
*   **Key Fields**:
    *   `user_id` (String): The Firebase Authentication User ID (UID) of the user.
    *   `firm_id` (String): The ID of the firm this user belongs to. Links to a document in the `FIRMS` collection.
    *   `email` (String): The user's email address (primary identifier for login).
    *   `display_name` (String, Optional): The user's preferred display name.
    *   `role` (String): The user's role within the firm (e.g., "firm_admin", "firm_staff"). Determines access capabilities.
    *   `assigned_client_ids` (Array of Strings, Optional): A list of `client_id`s that this user is specifically assigned to or has access to. Can be empty if access is governed purely by role (e.g., firm_admin has access to all firm clients).
    *   `status` (String): Current status of the user's association with the firm (e.g., "active", "invited", "disabled").
    *   `created_at` (Timestamp): Firestore server timestamp of when the user association was created.
    *   `updated_at` (Timestamp): Firestore server timestamp of when this record was last updated.
*   **Relationships**:
    *   Links to `FIRMS` via `firm_id`.
    *   `user_id` corresponds to a user in Firebase Authentication.
    *   `assigned_client_ids` (if populated) link to documents in the `CLIENTS` collection.
*   **Notes**: This collection is critical for authentication and authorization, determining what data a user can see and manipulate.

---

## `CLIENTS`

*   **Purpose**: Stores information about the clients (businesses or individuals) managed by an accounting firm using DRCR.
*   **Document ID Strategy**: Auto-generated UUID, stored in the `client_id` field.
*   **Key Fields**:
    *   `client_id` (String): Unique identifier for the client (same as document ID).
    *   `firm_id` (String): The ID of the firm that manages this client. Links to a document in the `FIRMS` collection.
    *   `name` (String): The name of the client business or individual.
    *   `status` (String): Current status of the client (e.g., "active", "inactive").
    *   `created_at` (Timestamp): Firestore server timestamp of creation.
    *   `updated_at` (Timestamp): Firestore server timestamp of last update.
    *   *Other client-specific details as needed (e.g., contact person, industry - TBD).* 
*   **Relationships**:
    *   Links to `FIRMS` via `firm_id`.
    *   Referenced by `ENTITIES` documents via their `client_id` field.
    *   Referenced by `TRANSACTIONS`, `COUNTERPARTIES`, `AMORTIZATION_SCHEDULES`, `PROPOSED_JOURNALS` (and potentially others) via their `client_id` field to scope data to a specific client.
*   **Notes**: Represents a primary data segregation boundary for most financial data within a firm.

---

## `ENTITIES`

*   **Purpose**: Represents the specific business entities (e.g., a company, a branch) for which financial data is being processed and managed. An entity often corresponds to a connection to an external accounting platform (like a Xero Organization).
*   **Document ID Strategy**: The unique identifier from the external accounting platform (e.g., Xero Tenant ID). This ID is also stored in the `entity_id` field.

### **Current Data Structure (Production)**

**Backend Storage Format:**
```json
{
  "client_id": "c8cc6cf1-920c-497e-b2cd-cef4cccf4005",
  "connection_details": {
    "connected_by": "bm43PZwmVAYFa1ifcSSlSS0nkIO2",
    "status": "active",
    "xero_tenant_id": "8ead108d-f6a2-41e4-b2a8-962024248a66"
  },
  "entity_id": "8ead108d-f6a2-41e4-b2a8-962024248a66",
  "entity_name": "Arua Test Prod",
  "status": "active",
  "type": "xero"
}
```

**Frontend Interface Requirements (`EntitySummary`):**
```typescript
interface EntitySummary {
  connection_status: ConnectionStatus; // Flattened from connection_details.status
  entity_id: string;
  entity_name: string;
  type: string;
  status: string;
  last_sync?: string;
  error_message?: string;
}
```

*   **Key Fields**:
    *   `entity_id` (String): Unique identifier for the entity (same as document ID; typically the ID from the source platform like Xero Tenant ID).
    *   `client_id` (String): The ID of the DRCR client this entity belongs to. Links to a document in the `CLIENTS` collection.
    *   `entity_name` (String): The name of the entity (e.g., company name from Xero). **Note:** Field name is `entity_name`, not `name`.
    *   `type` (String): The accounting platform this entity is associated with (e.g., "xero", "quickbooks"). **Note:** Lowercase values used.
    *   `status` (String): Current operational status of the entity (e.g., "active", "inactive").
    *   `connection_details` (Object): Nested object containing connection-specific information:
        *   `status` (String): Connection status (e.g., "active", "disconnected", "pending_auth").
        *   `connected_by` (String): Firebase UID of the user who established the connection.
        *   `xero_tenant_id` (String): For Xero entities, the tenant ID from Xero.
        *   `last_sync` (Timestamp, Optional): Last successful synchronization timestamp.
        *   `error_message` (String, Optional): Last error message if connection failed.
    *   `created_at` (Timestamp, Optional): Firestore server timestamp of creation.
    *   `updated_at` (Timestamp, Optional): Firestore server timestamp of last update.

### **Data Transformation Notes**

**Frontend-Backend Integration:**
- Backend API endpoint: `GET /clients/{client_id}/entities`
- Frontend service: `EntitiesService.getEntitiesForClient()`
- **Data transformation required:** Backend returns nested `connection_details` structure, but frontend expects flattened `connection_status` field
- **Status mapping:** Backend "active" → Frontend "connected"
- **Error handling:** Frontend includes defensive null checks for missing fields

**API Response Transformation:**
```typescript
// In EntitiesService.getEntitiesForClient()
const transformedEntity: EntitySummary = {
  entity_id: entity.entity_id,
  entity_name: entity.entity_name,
  type: entity.type || 'unknown',
  status: entity.status,
  connection_status: mapBackendStatusToFrontend(entity.connection_details?.status),
  last_sync: entity.connection_details?.last_sync,
  error_message: entity.connection_details?.error_message
};
```

*   **Relationships**:
    *   Links to `CLIENTS` via `client_id`.
    *   Directly corresponds to an `ENTITY_SETTINGS` document via its `entity_id` (which is the document ID for both).
    *   Referenced by `TRANSACTIONS`, `COUNTERPARTIES`, `AMORTIZATION_SCHEDULES`, `PROPOSED_JOURNALS` (and potentially others) via their `entity_id` field to scope data to a specific accounting entity/connection.
*   **Notes**: 
    *   This is a key collection for managing connections to external systems and segregating data that comes from different accounting instances.
    *   **Important:** The nested `connection_details` structure requires data transformation when consumed by the frontend to maintain API compatibility while supporting the UI's flattened interface requirements.
    *   **Field naming:** Uses `entity_name` (not `name`) and lowercase `type` values for consistency.

---

## `ENTITY_SETTINGS`

*   **Purpose**: Stores configuration settings, synchronization preferences, and accounting defaults specific to each `ENTITY`.
*   **Document ID Strategy**: The `entity_id` (same as the corresponding document ID in the `ENTITIES` collection).
*   **Key Fields**:
    *   `entity_id` (String): (Implicitly the document ID) Links to the `ENTITIES` collection.
    *   `client_id` (String): The `client_id` this entity belongs to (for context and easier querying, though `entity_id` is unique).
    *   `default_amortization_expense_account` (String, Optional): Default GL account code for amortization expenses.
    *   `default_prepayment_asset_account` (String, Optional): Default GL account code for prepayment assets.
    *   `_system_lastSyncTimestampUtc_[EndpointName]` (Timestamp, Optional): Stores the last successful sync timestamp for various data types/endpoints from the external platform (e.g., `_system_lastSyncTimestampUtc_Contacts`).
    *   `_system_lastSyncTimestampUtc_reason` (String, Optional): Reason for the last sync timestamp update.
    *   `_system_autoPostProposedJournalsToXero` (Boolean, Default: `false`): Setting to control automatic posting of proposed journals.
    *   `_system_updatedAt` (Timestamp): Firestore server timestamp of when these settings were last updated.
    *   *Other entity-specific settings as needed (e.g., fiscal year start, specific API keys/configurations if not managed elsewhere - TBD).* 
*   **Relationships**:
    *   One-to-one relationship with an `ENTITIES` document via `entity_id`.
*   **Notes**: Crucial for tailoring the behavior of data synchronization and processing for each connected accounting entity.

---

## `TRANSACTIONS`

*   **Purpose**: Stores detailed records of financial transactions, such as invoices, bills, and payments, sourced from external accounting platforms or created directly via the API.
*   **Document ID Strategy**: Auto-generated UUID, stored in the `transaction_id` field.
*   **Key Fields**:
    *   `transaction_id` (String): Unique identifier for the transaction (same as document ID).
    *   `client_id` (String): Links to the `CLIENTS` collection, identifying the client this transaction belongs to.
    *   `entity_id` (String): Links to the `ENTITIES` collection, identifying the specific accounting entity this transaction is associated with.
    *   `source_system` (String): Indicates the origin of the transaction data (e.g., "XERO", "API", "MANUAL").
    *   `source_system_id` (String, Optional): The unique identifier of this transaction in the `source_system` (e.g., Xero InvoiceID).
    *   `type` (String): The type of transaction (e.g., "ACCREC_INVOICE" for accounts receivable invoice, "ACCPAY_INVOICE" for accounts payable bill/invoice, "SPEND_MONEY", "RECEIVE_MONEY"). Standardized across sources.
    *   `status` (String): The current status of the transaction (e.g., "DRAFT", "SUBMITTED", "AUTHORISED", "PAID", "VOIDED"). Standardized across sources.
    *   `document_number` (String, Optional): The human-readable document number (e.g., Invoice #).
    *   `date_issued` (String/Timestamp/Date): The date the transaction was issued or occurred.
    *   `date_due` (String/Timestamp/Date, Optional): The due date for payment.
    *   `contact_id` (String, Optional): Identifier for the contact/vendor/customer associated with this transaction. Links to a document in the `COUNTERPARTIES` collection (typically the `source_system_id` of the counterparty).
    *   `line_items` (Array of Objects): Detailed line items for the transaction. Each object typically includes:
        *   `line_item_id` (String, Optional): Unique ID for the line item (e.g., from Xero).
        *   `description` (String).
        *   `quantity` (Number).
        *   `unit_amount` (Number).
        *   `amount` (Number): Total amount for the line (quantity * unit_amount).
        *   `account_code` (String, Optional): General ledger account code.
        *   `tax_rate_id` (String, Optional): Identifier for the tax rate applied.
        *   `tax_amount` (Number, Optional).
        *   *Other line-specific fields like tracking categories, job codes, etc.*
    *   `currency` (String): 3-letter ISO currency code (e.g., "USD", "CAD").
    *   `subtotal` (Number): The total amount before taxes.
    *   `tax_total` (Number): The total tax amount.
    *   `total` (Number): The grand total amount (subtotal + tax_total).
    *   `amount_due` (Number, Optional): The outstanding amount due for payment.
    *   `amount_paid` (Number, Optional): The amount already paid.
    *   `notes` (String, Optional): Additional notes or comments related to the transaction.
    *   `raw_xero_data` (Object, Optional): The complete raw data payload from Xero, if applicable, for reference and troubleshooting.
    *   `created_at` (Timestamp): Firestore server timestamp of when the DRCR record was created.
    *   `updated_at` (Timestamp): Firestore server timestamp of when the DRCR record was last updated.
    *   `source_updated_at_utc` (String/Timestamp, Optional): The timestamp of when this transaction was last updated in the `source_system`.
    *   `_system_amortizationScheduleIDs` (Array of Strings, Optional): List of `schedule_id`s from the `AMORTIZATION_SCHEDULES` collection that are linked to this transaction's line items.
    *   `_system_attachment_ids` (Array of Strings, Optional): List of `attachment_id`s (if an `ATTACHMENTS` collection exists or is planned).
*   **Relationships**:
    *   Links to `CLIENTS` via `client_id`.
    *   Links to `ENTITIES` via `entity_id`.
    *   `contact_id` links to a document in `COUNTERPARTIES`.
    *   `_system_amortizationScheduleIDs` link to documents in `AMORTIZATION_SCHEDULES`.
*   **Notes**: This is a central collection for all financial transactional data. Structure aims for a balance between standardized fields and preserving source-specific details (`raw_xero_data`).

---

## `COUNTERPARTIES`

*   **Purpose**: Stores information about contacts, vendors, and customers associated with transactions.
*   **Document ID Strategy**: The `source_system_id` of the counterparty from its original accounting platform (e.g., Xero ContactID). This ensures idempotency when syncing from the source.
*   **Key Fields**:
    *   `counterparty_id` (String): An internal DRCR identifier for the counterparty (can be the same as the document ID, or a separate DRCR-generated UUID if a global ID across different source systems for the same conceptual counterparty is needed later).
    *   `client_id` (String): The ID of the DRCR client this counterparty is associated with. Links to a document in the `CLIENTS` collection.
    *   `entity_id` (String): The ID of the DRCR entity (accounting platform connection) this counterparty belongs to. Links to a document in the `ENTITIES` collection.
    *   `source_system` (String): The system from which this counterparty data originated (e.g., "XERO").
    *   `source_system_id` (String): The unique identifier of this counterparty in the `source_system` (this is typically the Firestore document ID).
    *   `name` (String): The name of the counterparty.
    *   `email_address` (String, Optional): Primary email address.
    *   `phones` (Array of Objects, Optional): List of phone numbers, each object could have `type` (e.g., "DEFAULT", "MOBILE", "FAX") and `number`.
    *   `addresses` (Array of Objects, Optional): List of addresses, each object could have `type` (e.g., "STREET", "POBOX") and address fields (`line1`, `city`, `region`, `postal_code`, `country`).
    *   `status` (String, Optional): Status of the counterparty (e.g., "ACTIVE", "ARCHIVED" from Xero).
    *   `is_supplier` (Boolean, Default: `false`).
    *   `is_customer` (Boolean, Default: `false`).
    *   `contact_persons` (Array of Objects, Optional): Primary contact persons associated with this counterparty.
    *   `default_currency` (String, Optional): Default currency used with this counterparty.
    *   `_system_defaultAmortizationExpenseAccountCode` (String, Optional): A system field to store a default expense account for amortization purposes related to this vendor (used by `xero_sync_consumer`).
    *   `raw_xero_data` (Object, Optional): The complete raw data payload from Xero, if applicable.
    *   `created_at` (Timestamp): Firestore server timestamp of DRCR record creation.
    *   `updated_at` (Timestamp): Firestore server timestamp of DRCR record last update.
    *   `source_updated_at_utc` (String/Timestamp, Optional): Timestamp of last update in the `source_system`.
*   **Relationships**:
    *   Links to `CLIENTS` via `client_id`.
    *   Links to `ENTITIES` via `entity_id`.
    *   Referenced by `TRANSACTIONS` via `contact_id` (which should match the `source_system_id` or `counterparty_id` of a document here).
*   **Notes**: This collection was created by moving nested contact information to a top-level structure for better queryability and management.

---

## `AMORTIZATION_SCHEDULES`

*   **Purpose**: Stores detailed amortization schedules generated for specific transaction line items, outlining how an expense or revenue is to be recognized over time.
*   **Document ID Strategy**: Auto-generated UUID, stored in the `schedule_id` field.
*   **Key Fields**:
    *   `schedule_id` (String): Unique identifier for the schedule (same as document ID).
    *   `transaction_id` (String): The ID of the transaction this schedule pertains to. Links to a document in the `TRANSACTIONS` collection.
    *   `line_item_id` (String, Optional): The specific ID of the line item within the transaction that this schedule is for (if applicable, e.g., from Xero).
    *   `client_id` (String): Links to the `CLIENTS` collection.
    *   `entity_id` (String): Links to the `ENTITIES` collection.
    *   `status` (String): The current status of the schedule (e.g., "proposed", "active", "confirmed", "completed", "skipped", "error").
    *   `originalAmount` (Number): The total amount of the transaction line item to be amortized.
    *   `amortizationStartDate` (Date/String): The date when the amortization period begins.
    *   `amortizationEndDate` (Date/String): The date when the amortization period ends.
    *   `expenseAccountCode` (String): The GL account code where the amortized expense/revenue should be recognized.
    *   `assetAccountCode` (String): The GL account code for the prepayment asset or deferred revenue being amortized.
    *   `description` (String, Optional): A description for the schedule or the underlying item being amortized.
    *   `monthlyEntries` (Array of Objects): An array detailing each period's (typically monthly) amortization entry. Each object includes:
        *   `entry_id` (String, Optional): A unique identifier for this specific entry within the schedule.
        *   `month_date` (Date/String): The date representing the period (e.g., end of the month).
        *   `amount` (Number): The amount to be recognized in this period.
        *   `status` (String): Status of this specific entry (e.g., "proposed", "journal_proposed", "posted", "skipped").
        *   `_system_proposedJournalId` (String, Optional): ID of the journal entry in `PROPOSED_JOURNALS` related to this entry.
        *   `xero_journal_id` (String, Optional): ID of the journal entry in Xero if posted directly from the schedule confirmation (legacy or specific flows).
        *   `posted_at` (Timestamp, Optional).
    *   `created_at` (Timestamp): Firestore server timestamp.
    *   `updated_at` (Timestamp): Firestore server timestamp.
    *   `confirmed_at` (Timestamp, Optional): When the schedule was confirmed by a user.
    *   `confirmed_by` (String, Optional): UID of the user who confirmed the schedule.
    *   `notes` (String, Optional): Additional notes.
*   **Relationships**:
    *   Links to `TRANSACTIONS` via `transaction_id`.
    *   Links to `CLIENTS` via `client_id`.
    *   Links to `ENTITIES` via `entity_id`.
    *   Entries may link to `PROPOSED_JOURNALS` via `_system_proposedJournalId`.
*   **Notes**: This collection drives the core amortization logic.

---

## `PROPOSED_JOURNALS`

*   **Purpose**: Holds system-generated journal entries that are proposed based on due amortization schedule entries. These journals are typically reviewed or automatically processed before being posted to an external accounting system.
*   **Document ID Strategy**: Auto-generated UUID, stored in the `journal_id` field.
*   **Key Fields**:
    *   `journal_id` (String): Unique identifier for the proposed journal (same as document ID).
    *   `client_id` (String): Links to the `CLIENTS` collection.
    *   `entity_id` (String): Links to the `ENTITIES` collection.
    *   `amortization_schedule_id` (String): The ID of the `AMORTIZATION_SCHEDULES` document this journal entry is derived from.
    *   `amortization_entry_month_date` (Timestamp/Date): The specific month/period date from the amortization schedule entry this journal corresponds to.
    *   `status` (String): The status of this proposed journal (e.g., "proposed", "pending_approval", "approved_for_posting", "posted", "posting_failed", "skipped").
    *   `journal_date` (Date/String): The date to be used for the journal entry when posted to the accounting system.
    *   `narration` (String): A descriptive narration for the journal entry.
    *   `currencyCode` (String, Optional): Currency of the journal.
    *   `lines` (Array of Objects): The debit and credit lines for the journal. Each object includes:
        *   `account_code` (String): GL Account code.
        *   `amount` (Number): Amount for the line (positive for debit, negative for credit, or handled by a `type` field).
        *   `description` (String, Optional).
        *   *Other line-specific details like tracking categories.*
    *   `created_at` (Timestamp): Firestore server timestamp.
    *   `updated_at` (Timestamp): Firestore server timestamp.
    *   `posted_to_xero_at` (Timestamp, Optional): Timestamp of when this journal was successfully posted to Xero (if applicable).
    *   `xero_manual_journal_id` (String, Optional): The ID of the manual journal created in Xero (if applicable).
    *   `postingErrorDetails` (Object, Optional): Stores details if an error occurred during posting.
*   **Relationships**:
    *   Links to `CLIENTS` via `client_id`.
    *   Links to `ENTITIES` via `entity_id`.
    *   Links to `AMORTIZATION_SCHEDULES` via `amortization_schedule_id`.
*   **Notes**: This collection acts as an intermediary step for automated journal creation, allowing for review or batch processing.

---

## `AUDIT_LOG`

*   **Purpose**: Records significant events, actions, and errors that occur within the DRCR application for auditing, troubleshooting, and monitoring purposes.
*   **Document ID Strategy**: Auto-generated UUID.
*   **Key Fields**:
    *   `timestamp` (Timestamp): Firestore server timestamp indicating when the event occurred.
    *   `eventCategory` (String): Broad category of the event (e.g., "SYNC", "USER_MANAGEMENT", "AMORTIZATION_PROCESSING", "API_REQUEST").
    *   `eventType` (String): Specific type of event within the category (e.g., "XERO_CONTACTS_SYNC_STARTED", "USER_LOGIN_SUCCESS", "SCHEDULE_CONFIRMED", "TRANSACTION_CREATED_VIA_API").
    *   `client_id` (String, Optional): The `client_id` relevant to the event, if applicable.
    *   `entity_id` (String, Optional): The `entity_id` relevant to the event, if applicable.
    *   `user_id` (String, Optional): The Firebase UID of the user who initiated or was involved in the event, if applicable.
    *   `source_ip` (String, Optional): IP address from which a user-initiated action originated (for API requests).
    *   `status` (String): Outcome of the event (e.g., "SUCCESS", "FAILURE", "INFO", "WARNING", "STARTED").
    *   `details` (Object): A flexible field to store structured information specific to the event (e.g., request parameters, error messages, counts of processed items).
*   **Relationships**:
    *   May implicitly relate to records in other collections via IDs stored in the `details` field or the top-level `client_id`, `entity_id`, `user_id` fields.
*   **Notes**: Essential for maintaining a trail of system activities and diagnosing issues.

---

## `XERO_APP_TENANT_CONNECTIONS`

*   **Purpose**: Stores OAuth 2.0 tokens (access token, refresh token) and related metadata for each Xero organization (tenant) connected to DRCR. This collection is primarily managed by the Xero client logic.
*   **Document ID Strategy**: Xero Tenant ID (which corresponds to an `entity_id` in the `ENTITIES` collection).
*   **Key Fields**:
    *   `xero_tenant_id` (String): The Xero Tenant ID (same as document ID and the `entity_id`).
    *   `access_token` (String): The encrypted Xero access token.
    *   `refresh_token` (String): The encrypted Xero refresh token.
    *   `expires_at` (Timestamp/Number): Timestamp or seconds indicating when the access token expires.
    *   `scopes` (Array of Strings): List of OAuth scopes granted by Xero (e.g., "accounting.transactions", "offline_access").
    *   `client_id` (String): The DRCR `client_id` associated with this Xero connection (for context).
    *   `entity_id` (String): The DRCR `entity_id` (should be the same as `xero_tenant_id` and document ID).
    *   `connection_status` (String, Optional): e.g., "active", "needs_reauthentication".
    *   `created_at` (Timestamp): Firestore server timestamp.
    *   `updated_at` (Timestamp): Firestore server timestamp.
*   **Relationships**:
    *   One-to-one relationship with an `ENTITIES` document where `platform` is "XERO", linked via the Xero Tenant ID (`entity_id`).
    *   Links to `CLIENTS` via `client_id`.
*   **Notes**: This collection is sensitive as it holds authentication tokens. OAuth tokens are now stored encrypted in Firestore using Fernet encryption for cost efficiency (replacing expensive Secret Manager). Ensure appropriate Firestore security rules are in place to protect this data. The actual token storage is handled by the `FirestoreTokenStorage` service in the `oauth_tokens/{app_tenant_id}/platforms/{platform_org_id}` collection structure.

---

## `oauth_tokens` (Collection Group)

*   **Purpose**: Stores encrypted OAuth 2.0 tokens for all connected accounting platforms in a cost-effective manner. This replaces the expensive Secret Manager approach with encrypted Firestore storage.
*   **Collection Structure**: `oauth_tokens/{app_tenant_id}/platforms/{platform_org_id}`
*   **Document ID Strategy**: 
    *   Parent document: `app_tenant_id` (DRCR client ID)
    *   Sub-collection document: `platform_org_id` (e.g., Xero Tenant ID)
*   **Key Fields**:
    *   `access_token` (String): Fernet-encrypted OAuth access token.
    *   `refresh_token` (String): Fernet-encrypted OAuth refresh token.
    *   `expires_at` (Timestamp): When the access token expires.
    *   `updated_at` (Timestamp): Firestore server timestamp of last update.
    *   `platform` (String): Platform identifier (e.g., "xero") for future multi-platform support.
*   **Security**:
    *   All sensitive token data is encrypted using Fernet encryption before storage.
    *   Encryption key is managed separately (typically in Secret Manager for production).
    *   Firestore security rules should restrict access to authenticated users for their own tenant data only.
*   **Cost Benefits**:
    *   Reduces token storage costs by 80-90% compared to Secret Manager.
    *   Firestore reads are ~$0.18 per 100,000 vs Secret Manager ~$0.03 per 10,000 accesses.
*   **Relationships**:
    *   Parent document ID (`app_tenant_id`) links to `CLIENTS` collection.
    *   Sub-document ID (`platform_org_id`) links to `ENTITIES` collection.
*   **Notes**: Managed by the `FirestoreTokenStorage` service. This collection structure supports multiple accounting platforms per client while maintaining security and cost efficiency.

--- 